// 个人信息相关类型定义

/**
 * 用户基础信息
 */
export interface UserProfile {
  user_id: number;
  work_no: string;               // 工号
  name: string;                  // 姓名
  name_kana?: string;            // 假名（日文）
  email: string;                 // 邮箱
  phone?: string;                // 电话
  department: string;            // 部门
  position: string;              // 职位
  hire_date: string;             // 入职日期 YYYY-MM-DD
  birth_date?: string;           // 生日 YYYY-MM-DD
  gender?: 'male' | 'female' | 'other'; // 性别
  nationality?: string;          // 国籍
  address?: string;              // 地址
  emergency_contact?: EmergencyContact; // 紧急联系人
  avatar_url?: string;           // 头像URL
  status: 'active' | 'inactive' | 'suspended'; // 状态
  created_at: string;
  updated_at: string;
}

/**
 * 紧急联系人信息
 */
export interface EmergencyContact {
  name: string;
  relationship: string;          // 关系
  phone: string;
  email?: string;
}

/**
 * 更新个人信息请求参数
 */
export interface UpdateProfileRequest {
  name?: string;
  name_kana?: string;
  email?: string;
  phone?: string;
  birth_date?: string;
  gender?: 'male' | 'female' | 'other';
  nationality?: string;
  address?: string;
  emergency_contact?: EmergencyContact;
}

/**
 * 更改密码请求参数
 */
export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

/**
 * 获取个人信息响应
 */
export interface GetProfileResponse {
  status: 'OK' | 'ERROR';
  profile: UserProfile;
  message?: string;
}

/**
 * 更新个人信息响应
 */
export interface UpdateProfileResponse {
  status: 'OK' | 'ERROR';
  message: string;
  updated_profile?: UserProfile;
}

/**
 * 更改密码响应
 */
export interface ChangePasswordResponse {
  status: 'OK' | 'ERROR';
  message: string;
}

/**
 * 上传头像请求参数
 */
export interface UploadAvatarRequest {
  file: File;
}

/**
 * 上传头像响应
 */
export interface UploadAvatarResponse {
  status: 'OK' | 'ERROR';
  message: string;
  avatar_url?: string;
}

/**
 * 工作履历
 */
export interface WorkHistory {
  id: string;
  user_id: number;
  department: string;
  position: string;
  start_date: string;            // YYYY-MM-DD
  end_date?: string;             // YYYY-MM-DD，null表示当前职位
  description?: string;
  created_at: string;
}

/**
 * 技能信息
 */
export interface Skill {
  id: string;
  name: string;
  category: string;              // 技能分类
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert'; // 技能水平
  years_of_experience?: number;  // 经验年数
  certification?: string;        // 相关认证
}

/**
 * 教育背景
 */
export interface Education {
  id: string;
  institution: string;          // 学校/机构
  degree: string;                // 学位
  major: string;                 // 专业
  start_date: string;            // YYYY-MM-DD
  end_date: string;              // YYYY-MM-DD
  gpa?: number;                  // 成绩
  description?: string;
}

/**
 * 语言能力
 */
export interface Language {
  id: string;
  language: string;              // 语言名称
  proficiency: 'basic' | 'conversational' | 'business' | 'fluent' | 'native'; // 熟练程度
  certification?: string;        // 相关认证
  test_score?: string;           // 考试成绩
}

/**
 * 完整的个人档案
 */
export interface CompleteProfile {
  basic_info: UserProfile;
  work_history: WorkHistory[];
  skills: Skill[];
  education: Education[];
  languages: Language[];
}

/**
 * 个人设置
 */
export interface UserSettings {
  user_id: number;
  language: 'zh' | 'ja' | 'en';  // 界面语言
  timezone: string;              // 时区
  date_format: string;           // 日期格式
  time_format: '12h' | '24h';    // 时间格式
  notifications: NotificationSettings; // 通知设置
  privacy: PrivacySettings;      // 隐私设置
}

/**
 * 通知设置
 */
export interface NotificationSettings {
  email_notifications: boolean;
  push_notifications: boolean;
  attendance_reminders: boolean;
  approval_notifications: boolean;
  system_updates: boolean;
}

/**
 * 隐私设置
 */
export interface PrivacySettings {
  profile_visibility: 'public' | 'department' | 'private';
  show_contact_info: boolean;
  show_work_history: boolean;
  allow_search: boolean;
}
