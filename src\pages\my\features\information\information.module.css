/* 日系用户信息页面样式 */
.container {
  width: 95%;
  height: 100%;
  min-height: 600px;
  background: #fff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  margin: 24px auto;
  padding: 32px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 页面标题区域 */
.page_header {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e9ecef;
}

.page_title {
  font-size: 28px;
  font-weight: 700;
  color: #3498db;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.title_icon {
  font-size: 24px;
  color: #3498db;
}

.page_description {
  font-size: 16px;
  color: #6c757d;
  margin: 0;
  line-height: 1.5;
}

/* 内容包装器 */
.content_wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-y: auto;
}

/* 用户头像卡片 */
.profile_card {
  width: 100% !important;
  border-radius: 12px !important;
  border: 1px solid #e9ecef !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease;
}

.profile_card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  transform: translateY(-2px);
  border-color: #3498db !important;
}

.profile_header {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 16px;
}

.user_avatar {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
  border: 3px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile_info {
  flex: 1;
}

.user_name {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 8px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user_meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.user_tag {
  border-radius: 12px !important;
  font-weight: 600 !important;
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
  padding: 4px 8px !important;
  border: none !important;
  transition: all 0.3s ease !important;
}

.user_tag:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 信息卡片 */
.info_card,
.security_card {
  width: 100% !important;
  border-radius: 12px !important;
  border: 1px solid #e9ecef !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease;
}

.info_card:hover,
.security_card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  transform: translateY(-1px);
  border-color: #3498db !important;
}

.card_title {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card_icon {
  color: #3498db;
}

/* 信息网格 */
.info_grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  padding: 16px;
}

.info_item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.info_item:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info_label {
  font-size: 14px;
  font-weight: 600;
  color: #6c757d;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
}

.info_icon {
  color: #3498db;
  font-size: 14px;
}

.info_value {
  font-size: 16px;
  font-weight: 500;
  color: #2c3e50;
  word-break: break-all;
}

.type_tag {
  border-radius: 12px !important;
  font-weight: 600 !important;
  border: none !important;
  transition: all 0.3s ease !important;
}

.type_tag:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 安全设置 */
.security_info {
  padding: 16px;
}

.security_item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.security_icon {
  font-size: 20px;
  color: #3498db;
}

.security_text {
  flex: 1;
}

.security_title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.security_desc {
  font-size: 14px;
  color: #6c757d;
}

.security_status {
  font-size: 20px;
  color: #27ae60;
}

.change_password_btn {
  border-radius: 12px !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
}

.change_password_btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

/* 模态框样式 */
.password_modal :global(.ant-modal-content) {
  border-radius: 12px !important;
  overflow: hidden;
}

.modal_title {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal_icon {
  color: #3498db;
}

.modal_content {
  padding: 16px 0;
}

.modal_form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 32px;
}

.form_item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form_label {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.form_input {
  border-radius: 12px !important;
  border: 1px solid #e9ecef !important;
  transition: all 0.3s ease !important;
}

.form_input:hover {
  border-color: #3498db !important;
}

.form_input:focus {
  border-color: #3498db !important;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2) !important;
}

.modal_submit_btn {
  border-radius: 12px !important;
  font-weight: 600 !important;
}

/* 提示信息 */
.modal_notice {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.notice_item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 4px;
}

.notice_item:last-child {
  margin-bottom: 0;
}

.notice_icon_success {
  color: #27ae60;
}

.notice_icon_warning {
  color: #f39c12;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    width: 98%;
    padding: 24px;
  }
  
  .info_grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .container {
    width: 100%;
    margin: 8px auto;
    padding: 16px;
    border-radius: 4px;
  }
  
  .page_title {
    font-size: 20px;
  }
  
  .profile_header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .user_meta {
    justify-content: center;
  }
  
  .info_grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .info_item {
    padding: 8px;
  }
  
  .security_item {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
  
  .modal_form {
    gap: 16px;
  }
}
