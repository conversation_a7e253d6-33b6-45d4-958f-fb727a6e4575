/* 响应式日系用户信息页面样式 */
.container {
  width: 100%;
  max-width: none; /* 移除最大宽度限制，确保完全显示 */
  min-height: 600px;
  background: var(--bg-primary);
  box-shadow: var(--shadow-lg);
  border-radius: var(--border-radius-lg);
  margin: var(--spacing-lg) auto;
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

/* 页面标题区域 */
.page_header {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.page_title {
  font-size: calc(var(--font-size-xl) + 4px);
  font-weight: 700;
  color: var(--color-primary);
  margin: 0 0 var(--spacing-sm) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.title_icon {
  font-size: var(--font-size-xl);
  color: var(--color-primary);
}

.page_description {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  margin: 0;
  line-height: var(--line-height-relaxed);
}

/* 响应式内容包装器 */
.content_wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  min-width: 0; /* 确保可以收缩 */
}

/* 移动端页面优化 */
@media (max-width: 768px) {
  .container {
    margin: var(--spacing-md) auto;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
  }

  .page_title {
    font-size: var(--font-size-lg);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .title_icon {
    font-size: var(--font-size-lg);
  }

  .content_wrapper {
    gap: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .container {
    margin: var(--spacing-sm) auto;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
  }

  .page_header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
  }
}

/* 响应式用户头像卡片 */
.profile_card {
  width: 100% !important;
  border-radius: var(--border-radius-lg) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: var(--shadow-md) !important;
  transition: var(--transition-normal);
  background: var(--bg-primary) !important;
}

.profile_card:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: translateY(-2px);
  border-color: var(--color-primary) !important;
}

.profile_header {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  flex-wrap: wrap;
}

.user_avatar {
  background: var(--bg-gradient-primary) !important;
  border: 3px solid var(--border-color);
  box-shadow: var(--shadow-md);
  flex-shrink: 0;
}

.profile_info {
  flex: 1;
  min-width: 200px;
}

.user_name {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  word-break: break-word;
}

.user_meta {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

/* 移动端头像卡片优化 */
@media (max-width: 768px) {
  .profile_header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .profile_info {
    width: 100%;
    min-width: auto;
  }

  .user_name {
    font-size: var(--font-size-lg);
  }

  .user_meta {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .profile_header {
    padding: var(--spacing-sm);
    gap: var(--spacing-sm);
  }

  .user_name {
    font-size: var(--font-size-md);
  }
}

/* 响应式用户标签 */
.user_tag {
  border-radius: var(--border-radius-lg) !important;
  font-weight: 600 !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--spacing-xs) !important;
  padding: var(--spacing-xs) var(--spacing-sm) !important;
  border: none !important;
  transition: var(--transition-normal) !important;
  font-size: var(--font-size-sm) !important;
}

.user_tag:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-sm);
}

/* 响应式信息卡片 */
.info_card,
.security_card {
  width: 100% !important;
  border-radius: var(--border-radius-lg) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: var(--shadow-md) !important;
  transition: var(--transition-normal);
  background: var(--bg-primary) !important;
  overflow: hidden;
}

.info_card:hover,
.security_card:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: translateY(-1px);
  border-color: var(--color-primary) !important;
}

.card_title {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid var(--border-color);
  margin: 0;
}

.card_icon {
  color: var(--color-primary);
  font-size: var(--font-size-lg);
}

/* 移动端卡片标题优化 */
@media (max-width: 768px) {
  .card_title {
    font-size: var(--font-size-md);
    padding: var(--spacing-md);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .card_icon {
    font-size: var(--font-size-md);
  }
}

@media (max-width: 480px) {
  .card_title {
    padding: var(--spacing-sm);
    font-size: var(--font-size-sm);
  }

  .user_tag {
    font-size: var(--font-size-xs) !important;
    padding: var(--spacing-xs) !important;
  }
}

/* 响应式信息网格 - 确保列表项完全显示 */
.info_grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  width: 100%;
  box-sizing: border-box;
}

.info_item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  transition: var(--transition-normal);
  min-width: 0; /* 确保可以收缩 */
  word-wrap: break-word;
}

.info_item:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.info_label {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
  flex-shrink: 0;
}

.info_icon {
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  flex-shrink: 0;
}

.info_value {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
  word-break: break-word;
  overflow-wrap: break-word;
  line-height: var(--line-height-relaxed);
}

.type_tag {
  border-radius: var(--border-radius-lg) !important;
  font-weight: 600 !important;
  border: none !important;
  transition: var(--transition-normal) !important;
  font-size: var(--font-size-sm) !important;
}

.type_tag:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-sm);
}

/* 移动端信息网格优化 */
@media (max-width: 768px) {
  .info_grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .info_item {
    padding: var(--spacing-md);
  }

  .info_label {
    font-size: var(--font-size-xs);
  }

  .info_value {
    font-size: var(--font-size-sm);
  }
}

@media (max-width: 480px) {
  .info_grid {
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
  }

  .info_item {
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
  }

  .type_tag {
    font-size: var(--font-size-xs) !important;
  }
}

/* 响应式安全设置 */
.security_info {
  padding: var(--spacing-lg);
}

.security_item {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  transition: var(--transition-normal);
  margin-bottom: var(--spacing-md);
}

.security_item:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.security_icon {
  font-size: var(--font-size-lg);
  color: var(--color-primary);
  flex-shrink: 0;
}

.security_text {
  flex: 1;
  min-width: 0;
}

.security_title {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  word-break: break-word;
}

.security_desc {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

.security_status {
  font-size: var(--font-size-lg);
  color: var(--color-success);
  flex-shrink: 0;
}

.change_password_btn {
  border-radius: var(--border-radius-lg) !important;
  font-weight: 600 !important;
  box-shadow: var(--shadow-md) !important;
  transition: var(--transition-normal) !important;
  height: 40px !important;
}

.change_password_btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-lg) !important;
}

/* 移动端安全设置优化 */
@media (max-width: 768px) {
  .security_info {
    padding: var(--spacing-md);
  }

  .security_item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .security_text {
    width: 100%;
  }

  .change_password_btn {
    width: 100% !important;
    height: 44px !important;
  }
}

@media (max-width: 480px) {
  .security_info {
    padding: var(--spacing-sm);
  }

  .security_item {
    padding: var(--spacing-sm);
    gap: var(--spacing-sm);
  }

  .change_password_btn {
    height: 48px !important; /* 移动端更大的触摸目标 */
  }
}

/* 响应式模态框样式 */
.password_modal :global(.ant-modal-content) {
  border-radius: var(--border-radius-lg) !important;
  overflow: hidden;
}

.modal_title {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.modal_icon {
  color: var(--color-primary);
  font-size: var(--font-size-lg);
}

.modal_content {
  padding: var(--spacing-lg) 0;
}

.modal_form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.form_item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.form_label {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.form_input {
  border-radius: var(--border-radius-lg) !important;
  border: 1px solid var(--border-color) !important;
  transition: var(--transition-normal) !important;
  height: 40px !important;
  padding: 0 var(--spacing-md) !important;
}

.form_input:hover {
  border-color: var(--color-primary) !important;
}

.form_input:focus {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.modal_submit_btn {
  border-radius: var(--border-radius-lg) !important;
  font-weight: 600 !important;
  height: 40px !important;
}

/* 移动端模态框优化 */
@media (max-width: 768px) {
  .modal_title {
    font-size: var(--font-size-md);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .modal_content {
    padding: var(--spacing-md) 0;
  }

  .modal_form {
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
  }

  .form_input {
    height: 44px !important;
  }

  .modal_submit_btn {
    height: 44px !important;
    width: 100% !important;
  }
}

@media (max-width: 480px) {
  .form_input {
    height: 48px !important; /* 移动端更大的触摸目标 */
  }

  .modal_submit_btn {
    height: 48px !important;
  }
}

/* 响应式提示信息 */
.modal_notice {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color);
  margin-bottom: var(--spacing-lg);
}

.notice_item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  line-height: var(--line-height-relaxed);
}

.notice_item:last-child {
  margin-bottom: 0;
}

.notice_icon_success {
  color: var(--color-success);
  flex-shrink: 0;
}

.notice_icon_warning {
  color: var(--color-warning);
  flex-shrink: 0;
}

/* 移动端提示信息优化 */
@media (max-width: 768px) {
  .modal_notice {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
  }

  .notice_item {
    font-size: var(--font-size-xs);
    gap: var(--spacing-xs);
  }
}

@media (max-width: 480px) {
  .modal_notice {
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
  }
}

/* ========================================
   响应式工具类 - 确保列表项完全显示
   ======================================== */

/* 卡片容器横向滚动 */
.cards_container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary-light) var(--bg-secondary);
}

.cards_container::-webkit-scrollbar {
  height: 8px;
}

.cards_container::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 4px;
}

.cards_container::-webkit-scrollbar-thumb {
  background: var(--color-primary-light);
  border-radius: 4px;
}

.cards_container::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

/* 响应式文字大小工具类 */
.text_responsive_large {
  font-size: var(--font-size-lg);
}

.text_responsive_medium {
  font-size: var(--font-size-md);
}

.text_responsive_small {
  font-size: var(--font-size-sm);
}

@media (max-width: 768px) {
  .text_responsive_large {
    font-size: var(--font-size-md);
  }

  .text_responsive_medium {
    font-size: var(--font-size-sm);
  }

  .text_responsive_small {
    font-size: var(--font-size-xs);
  }
}

/* 响应式间距工具类 */
.spacing_responsive_large {
  padding: var(--spacing-xl);
}

.spacing_responsive_medium {
  padding: var(--spacing-lg);
}

.spacing_responsive_small {
  padding: var(--spacing-md);
}

@media (max-width: 768px) {
  .spacing_responsive_large {
    padding: var(--spacing-lg);
  }

  .spacing_responsive_medium {
    padding: var(--spacing-md);
  }

  .spacing_responsive_small {
    padding: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .spacing_responsive_large {
    padding: var(--spacing-md);
  }

  .spacing_responsive_medium {
    padding: var(--spacing-sm);
  }

  .spacing_responsive_small {
    padding: var(--spacing-xs);
  }
}
