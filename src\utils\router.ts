// 路由工具函数 - 替代Next.js的useRouter功能

import { shortUrlMapping } from '../config/routes'

// 获取完整路径（处理短链接映射）
export function getFullPath(path: string): string {
  return shortUrlMapping[path as keyof typeof shortUrlMapping] || path
}

// 检查路径是否匹配
export function isPathMatch(currentPath: string, targetPath: string): boolean {
  const fullCurrentPath = getFullPath(currentPath)
  const fullTargetPath = getFullPath(targetPath)
  return fullCurrentPath === fullTargetPath
}

// 构建查询字符串
export function buildQueryString(params: Record<string, any>): string {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.append(key, String(value))
    }
  })
  
  const queryString = searchParams.toString()
  return queryString ? `?${queryString}` : ''
}

// 解析查询字符串
export function parseQueryString(search: string): Record<string, string> {
  const params = new URLSearchParams(search)
  const result: Record<string, string> = {}
  
  params.forEach((value, key) => {
    result[key] = value
  })
  
  return result
}

// 路由重定向配置（对应原middleware.ts的逻辑）
export const redirectConfig = {
  continue: { value: 'continue' },
  login: { value: 'login', linkTo: '/login' },
  user_info: { value: 'user_info', linkTo: '/user/information' },
  pass_forget: { value: 'pass_forget', linkTo: '/reset' },
}
