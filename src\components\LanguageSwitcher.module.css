/* 多语言切换器样式 */
.language_switcher {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.language_switcher:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transform: translateY(-1px);
}

/* Select组件样式覆盖 */
.language_switcher :global(.ant-select-selector) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.language_switcher :global(.ant-select-selection-item) {
  color: #fff !important;
  font-weight: 500 !important;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.language_switcher :global(.ant-select-arrow) {
  color: #fff !important;
}

/* 下拉菜单样式 */
:global(.ant-select-dropdown.language-dropdown) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

:global(.ant-select-dropdown.language-dropdown .ant-select-item) {
  color: #fff !important;
  background: transparent !important;
}

:global(.ant-select-dropdown.language-dropdown .ant-select-item:hover) {
  background: rgba(255, 255, 255, 0.1) !important;
}

:global(.ant-select-dropdown.language-dropdown .ant-select-item-option-selected) {
  background: rgba(255, 255, 255, 0.2) !important;
  font-weight: 600 !important;
}

/* 图标样式 */
.globe_icon {
  color: #fff;
  font-size: 16px;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));
}

/* 自定义箭头 */
.custom_arrow {
  color: #fff;
  font-size: 12px;
  margin-right: 4px;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));
  transition: transform 0.3s ease;
}

.custom_arrow.open {
  transform: rotate(180deg);
}
