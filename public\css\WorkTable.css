.all {
    background-image: url(../../public/background/22397179.png );
    height: 100%;
    width: 100%;
    background-repeat: norepeat;
    background-size: cover;
    background-position: center;
    position: absolute;
    left: 0;
    top: 0;
    min-width: 1700px;
}

.table_kaoqin {
    position: absolute;
    top: 60px;
    right: 35%;
    padding-top: 20px;
    background: #fff;
    width: 38%;
    height: 23%;
    border-radius: 20px;
    min-width: 700px;
}

.table_member {
    position: absolute;
    top: 350px;
    right: 35%;
    padding-top: 20px;
    background: #fff;
    width: 38%;
    height: 23%;
    border-radius: 20px;
    min-width: 700px;
}

.table_setting {
    position: absolute;
    top: 640px;
    right: 35%;
    padding-top: 20px;
    background: #fff;
    width: 38%;
    height: 23%;
    border-radius: 20px;
    min-width: 700px;
}

.table_record {
    position: absolute;
    top: 60px;
    right: 8%;
    padding-top: 20px;
    background: #fff;
    width: 23%;
    height: 43%;
    border-radius: 20px;
}

.table_title {
    margin-top: 5px;
    margin-left: 30px;
    margin-right: 10px;
    width: 30px;
    height: 30px;
}

.table_item_name{
    margin-left: 40px;
    width: 100%;
    height: 20px;
    font-size: large;
}

.table_item{
    width: 18%;
    height: 20%;
    float: left;
}

.table_item_record{
    width: 30%;
    height: 20%;
    float: left;
}

.table_item_img{
    margin-top: 30px;
    width: 50px;
    height: 50px;
    float: left;
    margin-left: 50px;
    margin-bottom: 10px;
}

.table_item_name1{
    margin-left: 55px;
}

.table_item_name2{
    margin-left: 45px;
}