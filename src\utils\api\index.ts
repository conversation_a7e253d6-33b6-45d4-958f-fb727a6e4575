// API工具导出文件

// 导出API客户端
export { ApiClient, apiClient, ApiError } from './client';
export type { ApiRequestOptions, ApiResponse } from './client';

// 导出API配置
export { 
  API_CONFIG, 
  API_ENDPOINTS, 
  HTTP_STATUS, 
  API_STATUS, 
  REQUEST_HEADERS, 
  ERROR_MESSAGES, 
  DEV_CONFIG 
} from './config';

// 导出各模块API
export * as authApi from '../../pages/login/api/authApi';
export * as applicationApi from '../../pages/application/features/transportationExpense/api/applicationApi';
export * as recordApi from '../../pages/statistics/features/transportation/api/recordApi';
export * as approvalApi from '../../pages/approval/features/transportationExpense/api/approvalApi';
export * as attendanceApi from '../../pages/attendance/features/my/api/attendanceApi';
export * as profileApi from '../../pages/my/features/information/api/profileApi';

// 导出类型定义
export type { LoginRequest, LoginResponse, TokenValidationResponse } from '../../pages/login/types';
export type {
  TransportationExpenseApplication,
  CreateApplicationRequest,
  UpdateApplicationRequest,
  DeleteApplicationRequest,
  GetApplicationListResponse,
  CreateApplicationResponse,
  UpdateApplicationResponse,
  DeleteApplicationResponse
} from '../../pages/application/features/transportationExpense/types';
export type {
  PersonalTransportationRequest,
  CollectiveTransportationRequest,
  OverrideTransportationRequest,
  PersonalTransportationResponse,
  CollectiveTransportationResponse,
  OverrideTransportationResponse
} from '../../pages/statistics/features/transportation/types';
export type {
  PendingApproval,
  ApprovalRequest,
  BatchApprovalRequest,
  GetPendingApprovalsResponse,
  ApprovalResponse,
  BatchApprovalResponse,
  ApprovalStatistics
} from '../../pages/approval/features/transportationExpense/types';
export type {
  AttendanceRecord,
  ClockRecord,
  GetAttendanceRecordsRequest,
  GetAttendanceRecordsResponse,
  ClockInOutRequest,
  ClockInOutResponse,
  AttendanceCorrectionRequest,
  AttendanceCorrectionResponse
} from '../../pages/attendance/features/my/types';
export type {
  UserProfile,
  UpdateProfileRequest,
  ChangePasswordRequest,
  GetProfileResponse,
  UpdateProfileResponse,
  ChangePasswordResponse,
  CompleteProfile,
  UserSettings
} from '../../pages/my/features/information/types';
