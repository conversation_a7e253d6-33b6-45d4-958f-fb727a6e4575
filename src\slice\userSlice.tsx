import { createSlice, configureStore } from '@reduxjs/toolkit';  
import cookie from 'cookie';  
  
const userSlice = createSlice({  
  name: 'user',  
  initialState: {  
    isAuthenticated: false,  
    token: null,  
  },  
  reducers: {  
    loginSuccess: (state, action) => {  
      state.isAuthenticated = true;  
      state.token = action.payload.token;  
    },  
    loginFailure: (state) => {  
      state.isAuthenticated = false;  
      state.token = null;  
    },  
  },  
});  
  
export const { loginSuccess, loginFailure } = userSlice.actions;  