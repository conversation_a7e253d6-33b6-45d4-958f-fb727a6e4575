import React, { useState } from 'react';
import Layout from './components/Layout';
import AuthContainer from './components/auth/AuthContainer';
import type { User } from './types/auth';

function App() {
  const [user, setUser] = useState<User | null>(null);

  const handleAuthSuccess = (authenticatedUser: User) => {
    setUser(authenticatedUser);
  };

  const handleLogout = () => {
    setUser(null);
  };

  if (!user) {
    return <AuthContainer onAuthSuccess={handleAuthSuccess} />;
  }

  return <Layout user={user} onLogout={handleLogout} />;
}

export default App;