#!/usr/bin/env node

/**
 * 扫描项目中的硬编码中文字符串
 */

const fs = require('fs');
const path = require('path');

const chineseRegex = /[一-龥]+/g;
const excludeDirs = ['node_modules', '.next', 'dist', '.git', 'public/locales'];
const includeExts = ['.tsx', '.ts', '.js', '.jsx'];

function scanDirectory(dir, results = []) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !excludeDirs.includes(file)) {
      scanDirectory(filePath, results);
    } else if (stat.isFile() && includeExts.includes(path.extname(file))) {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      lines.forEach((line, index) => {
        const matches = line.match(chineseRegex);
        if (matches) {
          results.push({
            file: filePath,
            line: index + 1,
            content: line.trim(),
            matches: matches
          });
        }
      });
    }
  }
  
  return results;
}

console.log('🔍 扫描硬编码中文字符串...');
const results = scanDirectory('.');

if (results.length > 0) {
  console.log(`\n找到 ${results.length} 处硬编码中文：\n`);
  results.forEach(result => {
    console.log(`📁 ${result.file}:${result.line}`);
    console.log(`   ${result.content}`);
    console.log(`   匹配: ${result.matches.join(', ')}\n`);
  });
} else {
  console.log('✅ 未找到硬编码中文字符串');
}
