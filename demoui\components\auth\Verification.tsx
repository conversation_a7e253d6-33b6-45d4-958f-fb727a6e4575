import React, { useState, useEffect } from 'react';
import { Shield, ArrowLeft, AlertCircle, RefreshCw } from 'lucide-react';
import type { VerificationForm } from '../../types/auth';

interface VerificationProps {
  onSubmit: (form: VerificationForm) => void;
  onBack: () => void;
  onResendCode: () => void;
  email: string;
  error?: string;
  isLoading?: boolean;
  isResending?: boolean;
}

const Verification: React.FC<VerificationProps> = ({ 
  onSubmit, 
  onBack, 
  onResendCode, 
  email, 
  error, 
  isLoading,
  isResending 
}) => {
  const [form, setForm] = useState<VerificationForm>({
    code: '',
  });
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [countdown]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(form);
  };

  const handleResend = () => {
    onResendCode();
    setCountdown(60);
    setCanResend(false);
  };

  const handleCodeChange = (value: string) => {
    // Only allow numbers and limit to 6 digits
    const numericValue = value.replace(/\D/g, '').slice(0, 6);
    setForm({ code: numericValue });
  };

  const maskedEmail = email.replace(/(.{2})(.*)(@.*)/, '$1***$3');

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 to-orange-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
            <Shield className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-slate-800 mb-2">
            認証コード入力
          </h1>
          <p className="text-slate-600">
            {maskedEmail} に送信された<br />
            6桁の認証コードを入力してください
          </p>
        </div>

        {/* Form */}
        <div className="bg-white rounded-2xl shadow-xl border border-amber-100 p-8">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6 flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-red-800 font-medium mb-1">認証エラー</p>
                <p className="text-red-700 text-sm">{error}</p>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Verification Code */}
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                認証コード
              </label>
              <input
                type="text"
                value={form.code}
                onChange={(e) => handleCodeChange(e.target.value)}
                placeholder="123456"
                className="w-full px-4 py-4 text-center text-2xl font-mono border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 tracking-widest"
                maxLength={6}
                required
              />
              <p className="text-xs text-slate-500 mt-2 text-center">
                6桁の数字を入力してください
              </p>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading || form.code.length !== 6}
              className="w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed text-white py-4 px-6 rounded-xl font-bold text-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
            >
              {isLoading ? '認証中...' : '認証する'}
            </button>

            {/* Resend Code */}
            <div className="text-center">
              {canResend ? (
                <button
                  type="button"
                  onClick={handleResend}
                  disabled={isResending}
                  className="flex items-center justify-center gap-2 text-green-600 hover:text-green-800 text-sm font-medium underline transition-colors duration-200 mx-auto"
                >
                  <RefreshCw className={`w-4 h-4 ${isResending ? 'animate-spin' : ''}`} />
                  {isResending ? '送信中...' : '認証コードを再送信'}
                </button>
              ) : (
                <p className="text-slate-500 text-sm">
                  再送信まで {countdown} 秒
                </p>
              )}
            </div>

            {/* Back Button */}
            <button
              type="button"
              onClick={onBack}
              className="w-full flex items-center justify-center gap-2 text-slate-600 hover:text-slate-800 py-3 px-6 rounded-xl border border-gray-300 hover:border-gray-400 transition-all duration-200"
            >
              <ArrowLeft className="w-4 h-4" />
              前の画面に戻る
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Verification;