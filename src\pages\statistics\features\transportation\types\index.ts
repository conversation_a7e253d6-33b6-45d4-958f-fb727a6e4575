// 交通费统计记录相关类型定义

/**
 * 个人交通费记录查询请求
 */
export interface PersonalTransportationRequest {
  date: string; // YYYY-MM 格式
}

/**
 * 总交通费集计查询请求
 */
export interface CollectiveTransportationRequest {
  date: string; // YYYY-MM 格式
}

/**
 * 修改个人数据请求
 */
export interface OverrideTransportationRequest {
  date: string;                    // YYYY-MM-DD 格式
  method_type: 'single' | 'monthly'; // 单次券或定期券
  amount: number;                  // 价格
  route_from?: string;             // 可选：出发地
  route_to?: string;               // 可选：目的地
  user_id?: number;                // 可选：用户ID（管理员使用）
}

/**
 * 报销方式
 */
export interface ReimbursementMethod {
  single?: number;   // 单次票总费用
  monthly?: number;  // 月票总费用
}

/**
 * 个人交通费记录项
 */
export interface PersonalTransportationRecord {
  date: string;                    // 日期 YYYY-MM-DD
  clock_in_time: string;           // 打卡上班时间
  clock_out_time: string;          // 打卡下班时间
  attendance_location: string;     // 打卡点
  applied_from: string;            // 申请住宿点
  applied_to: string;              // 申请出勤点
  reimbursement_method: ReimbursementMethod; // 报销方式
}

/**
 * 个人交通费记录响应
 */
export interface PersonalTransportationResponse {
  status: 'OK' | 'ERROR';
  records: PersonalTransportationRecord[];
  message?: string;
}

/**
 * 报销天数详情
 */
export interface ReimbursementDay {
  type: 'single' | 'monthly';
  amount: number;
  route_from: string;
  route_to: string;
}

/**
 * 总交通费集计记录项
 */
export interface CollectiveTransportationRecord {
  work_no: string;                 // 工号
  name: string;                    // 姓名
  actual_work_days: number;        // 实际出勤天数
  absent_days: number;             // 缺勤天数
  attendance_locations: string[];  // 实际打卡的所有地点
  reimbursement_method: ReimbursementMethod | null; // 报销方式汇总
  reimbursement_days: Record<string, ReimbursementDay>; // 每日报销详情
}

/**
 * 总交通费集计响应
 */
export interface CollectiveTransportationResponse {
  status: 'OK' | 'ERROR';
  records: CollectiveTransportationRecord[];
  query_period: string;            // 查询期间
  message?: string;
}

/**
 * 修改个人数据响应
 */
export interface OverrideTransportationResponse {
  status: 'ok' | 'error';
  message?: string;
}

/**
 * 员工基础信息
 */
export interface StaffInfo {
  user_id: number;
  work_no: string;
  name: string;
  department?: string;
  position?: string;
}

/**
 * 考勤记录
 */
export interface AttendanceRecord {
  date: string;
  clock_in_time: string;
  clock_out_time: string;
  attendance_location: string;
  status: 'present' | 'absent' | 'late' | 'early_leave';
}

/**
 * 交通费统计汇总
 */
export interface TransportationSummary {
  total_employees: number;         // 总员工数
  total_work_days: number;         // 总出勤天数
  total_single_amount: number;     // 单次票总费用
  total_monthly_amount: number;    // 月票总费用
  total_amount: number;            // 总费用
  average_amount_per_employee: number; // 人均费用
  average_amount_per_day: number;  // 日均费用
}

/**
 * 路线使用统计
 */
export interface RouteUsageStats {
  route: string;                   // 路线（from → to）
  usage_count: number;             // 使用次数
  total_amount: number;            // 总费用
  employees: string[];             // 使用该路线的员工
}

/**
 * 月度统计数据
 */
export interface MonthlyStats {
  month: string;                   // YYYY-MM
  summary: TransportationSummary;
  route_stats: RouteUsageStats[];
  top_routes: RouteUsageStats[];   // 使用最多的路线
}

/**
 * 交通费类型枚举
 */
export enum TransportationType {
  SINGLE = 'single',
  MONTHLY = 'monthly'
}

/**
 * 查询状态枚举
 */
export enum QueryStatus {
  SUCCESS = 'OK',
  ERROR = 'ERROR'
}
