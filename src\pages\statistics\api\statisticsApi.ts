import { getApi, postApi, createAxios } from '@/api/fetch-api'
import ApiUrlVars from '@/api/common/url-vars'
import { statusActions } from '@/slice/statusSlice'
import dayjs from 'dayjs'

/**
 * 统计查询参数接口
 */
export interface StatisticsQueryParams {
  ym?: string // 年月 YYYY-MM
  dl?: number // 下载标志
  department?: string
  month?: string
  user_id?: string
}

/**
 * 部门统计数据接口
 */
export interface DepartmentStats {
  id: string
  employeeName: string
  department: string
  attendanceDays: number
  leaveDays: number
  overtimeHours: number
  transportationExpense: number
}

/**
 * 统计汇总数据接口
 */
export interface StatsSummary {
  totalEmployees: number
  totalAttendanceDays: number
  totalLeaveDays: number
  totalOvertimeHours: number
  totalTransportationExpense: number
}

/**
 * 统计响应接口
 */
export interface StatisticsResponse {
  status: string
  message?: string
  data?: any
  details?: DepartmentStats[]
  summary?: StatsSummary
}

/**
 * 获取部门数据汇总
 * @param params 查询参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<StatisticsResponse>
 */
export async function getDepartmentStatistics(
  params: StatisticsQueryParams,
  dispatch: any
): Promise<StatisticsResponse> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.summary_depart_info_get
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取部门统计失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      details: response.data.details,
      summary: response.data.summary
    }
  } catch (error: any) {
    console.error('Get department statistics API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 下载部门统计报告
 * @param targetYm 目标年月
 * @param dispatch Redux dispatch函数
 * @returns Promise<void>
 */
export async function downloadDepartmentReport(
  targetYm: string,
  dispatch: any
): Promise<void> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.summary_depart_info_get
    const params = {
      dl: 1,
      ym: targetYm
    }
    
    const axios = createAxios()
    const response = await axios.post(url, params, {
      responseType: 'blob'
    })
    
    // 创建下载链接
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = downloadUrl
    a.download = '部门统计报告_' + dayjs().format('YYYYMMDD') + '.xlsx'
    a.click()
    window.URL.revokeObjectURL(downloadUrl)
    
  } catch (error: any) {
    console.error('Download department report API error:', error)
    if (error.response?.data?.status === 'NG') {
      if (error.response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(error.response.data.message))
      }
    }
    throw error
  }
}

/**
 * 获取部门报销数据汇总
 * @param params 查询参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<StatisticsResponse>
 */
export async function getDepartmentExpenseStatistics(
  params: StatisticsQueryParams,
  dispatch: any
): Promise<StatisticsResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.summary_depart_info_expense_get
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取报销统计失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get department expense statistics API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 下载部门报销报告
 * @param targetYm 目标年月
 * @param dispatch Redux dispatch函数
 * @returns Promise<void>
 */
export async function downloadDepartmentExpenseReport(
  targetYm: string,
  dispatch: any
): Promise<void> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.summary_depart_info_expense_get
    const params = {
      ym: targetYm
    }
    
    const axios = createAxios()
    const response = await axios.post(url, params, {
      responseType: 'blob'
    })
    
    // 创建下载链接
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = downloadUrl
    a.download = '报销费用详细表_' + dayjs().format('YYYYMMDD') + '.xlsx'
    a.click()
    window.URL.revokeObjectURL(downloadUrl)
    
  } catch (error: any) {
    console.error('Download department expense report API error:', error)
    if (error.response?.data?.status === 'NG') {
      if (error.response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(error.response.data.message))
      }
    }
    throw error
  }
}

/**
 * 获取个人数据汇总
 * @param params 查询参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<StatisticsResponse>
 */
export async function getPersonalStatistics(
  params: StatisticsQueryParams,
  dispatch: any
): Promise<StatisticsResponse> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.summary_personal_info_get
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取个人统计失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get personal statistics API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取组织数据汇总
 * @param params 查询参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<StatisticsResponse>
 */
export async function getCompanyStatistics(
  params: StatisticsQueryParams,
  dispatch: any
): Promise<StatisticsResponse> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.summary_company_info_get
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取组织统计失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get company statistics API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取调休管理数据
 * @param params 查询参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<StatisticsResponse>
 */
export async function getCompensatoryStatistics(
  params: StatisticsQueryParams,
  dispatch: any
): Promise<StatisticsResponse> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.summary_compensatory_info_get
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取调休统计失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get compensatory statistics API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 触发调休管理重新计算
 * @param dispatch Redux dispatch函数
 * @returns Promise<StatisticsResponse>
 */
export async function triggerCompensatoryRecalc(dispatch: any): Promise<StatisticsResponse> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.summary_compensatory_trigger
    const response = await postApi(url, {}, dispatch)
    
    return {
      status: response.data.status || 'OK',
      data: response.data,
      message: response.data.message
    }
  } catch (error: any) {
    console.error('Trigger compensatory recalc API error:', error)
    return {
      status: 'NG',
      message: error.message || '触发重新计算失败'
    }
  }
}

/**
 * 获取周末/节假日加班申请统计
 * @param params 查询参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<StatisticsResponse>
 */
export async function getOvertimeApplicationStatistics(
  params: StatisticsQueryParams,
  dispatch: any
): Promise<StatisticsResponse> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.summary_application_get
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取加班申请统计失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get overtime application statistics API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}
