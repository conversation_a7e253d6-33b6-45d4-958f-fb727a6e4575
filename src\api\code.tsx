import * as CryptoJS from 'crypto-js';
import Globals from '../../public/strings/Strings'

// Declare this key and iv values in declaration
const key = CryptoJS.enc.Utf8.parse(Globals.SECRET_KEY);
const iv = CryptoJS.enc.Utf8.parse(Globals.SECRET_KEY);

// Methods for the encrypt and decrypt Using AES
export function encryptAES(encString:string) {
    const src = CryptoJS.enc.Utf8.parse(encString);
    const encrypted = CryptoJS.AES.encrypt(src, key, { iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 });
    return encrypted.ciphertext.toString().toUpperCase();
}

export function decryptAES(decString:string) {
    const encryptedHexStr = CryptoJS.enc.Hex.parse(decString);
    const src = CryptoJS.enc.Base64.stringify(encryptedHexStr);
    const decrypt = CryptoJS.AES.decrypt(src, key, { iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 });
    const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
    return decryptedStr.toString();
}

// Methods for the encrypt and decrypt Using MD5
export function encryptMD5(encString:string) {
	const md5String = CryptoJS.MD5(encString);
    return md5String;
}
