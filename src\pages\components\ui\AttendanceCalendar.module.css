/* 考勤日历组件样式 */
.attendance_calendar {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  width: 100%;
  height: 100%;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 日历主体样式 */
.calendar {
  width: 100%;
}

/* 日历头部样式 */
.calendar_header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 12px;
  background: #fafafa;
  border-radius: 6px 6px 0 0;
  flex-shrink: 0; /* 防止头部被压缩 */
}

.header_navigation {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header_title {
  font-size: 24px;
  font-weight: 700;
  color: #1890ff;
  min-width: 180px;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.nav_button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  border: 2px solid #1890ff;
  color: #1890ff;
  background: white;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  transition: all 0.3s ease;
}

.nav_button:hover {
  background: #1890ff;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* 日期单元格样式 */
.calendar_cell {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 100px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.attendance_info {
  min-height: 80px;
  max-height: 100px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.time_info {
  background: rgba(24, 144, 255, 0.15);
  border-radius: 6px;
  padding: 4px 6px;
  font-size: 14px;
  line-height: 1.4;
  border: 1px solid rgba(24, 144, 255, 0.3);
}

.time_text {
  color: #1890ff;
  font-weight: 600;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.status_badge {
  position: absolute;
  bottom: 4px;
  right: 4px;
  font-size: 12px;
  font-weight: 600;
}

/* 休假和加班信息样式 */
.leave_overtime_info {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  margin-top: 3px;
  align-items: flex-start;
}

/* 调休标签 - 蓝色 */
.compensatory_tag {
  background: #e6f7ff;
  border: 1px solid #40a9ff;
  border-radius: 4px;
  padding: 3px 8px;
  margin: 2px 0;
  display: inline-block;
  font-size: 12px;
  line-height: 1.4;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 事假标签 - 橙色 */
.personal_leave_tag {
  background: #fff2e8;
  border: 1px solid #ffb366;
  border-radius: 4px;
  padding: 3px 8px;
  margin: 2px 0;
  display: inline-block;
  font-size: 12px;
  line-height: 1.4;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 病假标签 - 红色 */
.sick_leave_tag {
  background: #fff1f0;
  border: 1px solid #ff7875;
  border-radius: 4px;
  padding: 3px 8px;
  margin: 2px 0;
  display: inline-block;
  font-size: 12px;
  line-height: 1.4;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.overtime_tag {
  background: #f6ffed;
  border: 1px solid #73d13d;
  border-radius: 4px;
  padding: 3px 8px;
  margin: 2px 0;
  display: inline-block;
  font-size: 12px;
  line-height: 1.4;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.tag_text {
  color: #333;
  font-weight: 700;
  white-space: nowrap;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

/* 时间和出勤地容器 */
.time_location_container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 2px;
  gap: 4px;
}

.time_section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.location_section {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
}

.work_from_home_tag {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  display: inline-block;
  white-space: nowrap;
}

.work_from_home_tag .tag_text {
  color: #1890ff;
  font-size: 9px;
  font-weight: 600;
}

.business_trip_tag {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  display: inline-block;
  white-space: nowrap;
}

.business_trip_tag .tag_text {
  color: #fa8c16;
  font-size: 9px;
  font-weight: 600;
}

.office_tag {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  display: inline-block;
  white-space: nowrap;
}

.office_tag .tag_text {
  color: #52c41a;
  font-size: 9px;
  font-weight: 600;
}

/* 自定义日期网格样式 */
.date_grid {
  width: 100%;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：允许flex子元素收缩 */
}

.week_header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0; /* 防止表头被压缩 */
  box-sizing: border-box; /* 确保padding计算正确 */
  transition: padding-right 0.2s ease; /* 平滑过渡效果 */
}

/* 可滚动的日期内容容器 */
.date_content_container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0; /* 重要：允许flex子元素收缩 */
}

/* 滚动条样式优化 */
.date_content_container::-webkit-scrollbar {
  width: 8px;
}

.date_content_container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.date_content_container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.date_content_container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.day_header {
  padding: 12px 8px;
  text-align: center;
  font-weight: 600;
  color: #595959;
  border-right: 1px solid #f0f0f0;
}

.day_header:last-child {
  border-right: none;
}

.week_row {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  border-bottom: 1px solid #f0f0f0;
}

.week_row:last-child {
  border-bottom: none;
}

.date_cell {
  min-height: 140px;
  border-right: 1px solid #f0f0f0;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background: #fff;
}

.date_cell:last-child {
  border-right: none;
}

.date_cell:hover {
  background: #f5f5f5;
}

.empty_cell {
  min-height: 120px;
  border-right: 1px solid #f0f0f0;
  background: #fafafa;
}

.empty_cell:last-child {
  border-right: none;
}

.date_number {
  font-size: 20px;
  font-weight: 700;
  color: #262626;
  margin-bottom: 6px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.date_content {
  min-height: 80px;
  max-height: 100px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.record_item {
  margin-bottom: 6px;
  padding: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.record_item:last-child {
  margin-bottom: 0;
}

/* 没有时间记录的样式 */
.no_time_record {
  background: transparent !important;
  border: none !important;
  padding: 2px !important;
}

/* 时间行样式 */
.time_row {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
  font-size: 12px;
  line-height: 1.3;
}

.time_label {
  font-weight: 600;
  color: #666;
  margin-right: 3px;
  min-width: 28px;
  font-size: 11px;
}

.time_normal {
  color: #1890ff;
  font-weight: 600;
  font-size: 13px;
}

.time_changed {
  color: #ff4d4f;
  font-weight: 600;
  font-size: 12px;
}

.time_crossed {
  text-decoration: line-through;
  color: #999;
}

.time_arrow {
  color: #ff4d4f;
  font-weight: bold;
}

/* 日期状态样式 */
.date_cell.today {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%) !important;
  border: 2px solid #1890ff !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2) !important;
}

/* 周末/节假日 - 淡蓝色背景 */
.date_cell.weekend_holiday {
  background-color: #e6f7ff !important;
  border: 1px solid #91d5ff !important;
}

/* 异常状态 */
.date_cell.exception {
  border: 2px solid #ff4d4f !important;
  background-color: #fff2f0 !important;
}

/* 确认状态 */
.date_cell.confirm {
  border: 2px solid #fa8c16 !important;
  background-color: #fff7e6 !important;
}

/* 正常工作日 - 绿色背景 */
.date_cell.normal {
  background-color: #f6ffed !important;
  border: 1px solid #b7eb8f !important;
}

/* 工作日无记录 - 绿色背景 */
.date_cell.workday_no_record {
  background-color: #f6ffed !important;
  border: 1px solid #d9f7be !important;
}

/* 日期范围标题 */
.date_range_title {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
  text-align: center;
  padding: 8px 0;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 8px;
  background: #fafafa;
  border-radius: 6px;
}

/* 超出范围的日期样式 */
.date_cell.out_of_range {
  background-color: #f5f5f5 !important;
  cursor: not-allowed !important;
}

.date_number.disabled {
  color: #bfbfbf !important;
}

/* 今天日期数字样式 */
.date_number.today_number {
  color: #1890ff !important;
  font-weight: bold !important;
  position: relative;
}

/* 今天指示器样式 */
.today_indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  background: #1890ff;
  color: white;
  font-size: 10px;
  padding: 1px 3px;
  border-radius: 8px;
  font-weight: bold;
  line-height: 1;
}

/* 月份汇总样式 */
.month_summary {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px;
}

.summary_item {
  font-size: 12px;
}

/* 弹窗内容样式 */
.modal_content {
  max-height: 400px;
  overflow-y: auto;
}

.record_detail {
  padding: 12px 0;
}

.detail_row {
  display: flex;
  margin-bottom: 8px;
  align-items: center;
}

.detail_label {
  font-weight: 500;
  color: #595959;
  width: 100px;
  flex-shrink: 0;
}

.detail_value {
  color: #262626;
  flex: 1;
}

.record_divider {
  border: none;
  border-top: 1px solid #f0f0f0;
  margin: 12px 0;
}

/* 日历单元格状态样式 */
.attendance_calendar :global(.ant-picker-calendar-date-content) {
  height: 100px !important;
  font-size: 14px;
}

/* 周末和祝日样式 */
.attendance_calendar :global(.ant-picker-cell) {
  position: relative;
}

.attendance_calendar :global(.ant-picker-cell[data-weekend="true"]) {
  background-color: #f0f8ff !important;
}

.attendance_calendar :global(.ant-picker-cell[data-holiday="true"]) {
  background-color: #f0f8ff !important;
}

/* 异常状态样式 */
.attendance_calendar :global(.ant-picker-cell[data-exception="true"]) {
  border: 2px solid #ff4d4f !important;
}

.attendance_calendar :global(.ant-picker-cell[data-exception="true"] .ant-picker-calendar-date-value) {
  color: #ff4d4f !important;
  font-weight: bold !important;
}

/* 确认状态样式 */
.attendance_calendar :global(.ant-picker-cell[data-confirm="true"]) {
  border: 2px solid #fa8c16 !important;
}

.attendance_calendar :global(.ant-picker-cell[data-confirm="true"] .ant-picker-calendar-date-value) {
  color: #fa8c16 !important;
  font-weight: bold !important;
}

/* 正常状态样式 */
.attendance_calendar :global(.ant-picker-cell[data-normal="true"]) {
  border: 1px solid #52c41a !important;
}

.attendance_calendar :global(.ant-picker-cell[data-normal="true"] .ant-picker-calendar-date-value) {
  color: #52c41a !important;
}

/* 选中日期样式 */
.attendance_calendar :global(.ant-picker-cell-selected) {
  background-color: #e6f7ff !important;
}

.attendance_calendar :global(.ant-picker-cell-selected .ant-picker-calendar-date-value) {
  background-color: #1890ff !important;
  color: #fff !important;
}

/* 今天的样式 */
.attendance_calendar :global(.ant-picker-cell-today .ant-picker-calendar-date-value) {
  border: 2px solid #1890ff !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .attendance_calendar {
    padding: 8px;
  }
  
  .calendar_header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .header_left,
  .header_right {
    justify-content: center;
  }
  
  .calendar_cell {
    min-height: 60px;
  }
  
  .time_info {
    font-size: 9px;
  }
  
  .status_badge {
    font-size: 9px;
  }
}

@media (max-width: 480px) {
  .calendar_cell {
    min-height: 50px;
    padding: 2px;
  }
  
  .time_info {
    font-size: 8px;
    padding: 1px 2px;
  }
  
  .header_title {
    font-size: 16px;
  }
}

/* 加载状态样式 */
.calendar:global(.ant-spin-container) {
  min-height: 400px;
}

/* 自定义日历主题 */
.attendance_calendar :global(.ant-picker-calendar) {
  background: transparent;
}

.attendance_calendar :global(.ant-picker-calendar .ant-picker-panel) {
  background: transparent;
}

.attendance_calendar :global(.ant-picker-calendar-header) {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
}

/* 日历表格样式优化 */
.attendance_calendar :global(.ant-picker-calendar-date) {
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.attendance_calendar :global(.ant-picker-calendar-date:hover) {
  background-color: #f5f5f5;
  cursor: pointer;
}

/* 月视图和年视图切换按钮样式 */
.attendance_calendar :global(.ant-picker-calendar-mode-switch) {
  display: none;
}

/* 自定义日期值样式 */
.attendance_calendar :global(.ant-picker-calendar-date-value) {
  width: 32px;
  height: 32px;
  line-height: 30px;
  border-radius: 50%;
  margin: 0 auto 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

/* 禁用日期样式 */
.attendance_calendar :global(.ant-picker-cell-disabled) {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.attendance_calendar :global(.ant-picker-cell-disabled .ant-picker-calendar-date-value) {
  color: #bfbfbf;
}

/* 日历内容区域样式 */
.attendance_calendar :global(.ant-picker-calendar-date-content) {
  position: relative;
  padding: 4px;
  min-height: 60px;
  display: flex;
  flex-direction: column;
}

/* 优化日历网格线 */
.attendance_calendar :global(.ant-picker-calendar table) {
  border-collapse: separate;
  border-spacing: 1px;
}

.attendance_calendar :global(.ant-picker-calendar td) {
  border: none;
  background-color: #fff;
}

.attendance_calendar :global(.ant-picker-calendar th) {
  background-color: #fafafa;
  font-weight: 600;
  color: #262626;
  border: none;
  padding: 12px 8px;
}

/* 星期标题样式 */
.attendance_calendar :global(.ant-picker-calendar-header .ant-picker-calendar-column-header) {
  text-align: center;
  font-weight: 700;
  color: #262626;
  font-size: 16px;
  padding: 16px 8px;
}

/* 周末列标题特殊样式 */
.attendance_calendar :global(.ant-picker-calendar-header .ant-picker-calendar-column-header:first-child),
.attendance_calendar :global(.ant-picker-calendar-header .ant-picker-calendar-column-header:last-child) {
  color: #ff4d4f;
  font-weight: 800;
}
