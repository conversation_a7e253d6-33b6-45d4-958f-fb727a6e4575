import React from 'react'
import { Card, Typography } from 'antd'
import { useTranslation } from '../../../hooks/useTranslation'

const { Title } = Typography

const DepartmentStatisticsPage: React.FC = () => {
  const { t } = useTranslation()

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Title level={2}>{t('statistics.department.title', '部门统计')}</Title>
        <p>这里是部门统计页面的内容</p>
        <p>路径: /statistics/departmentStatistics</p>
        <p>短链接: /sum/dep</p>
        {/* TODO: 从原 pages/statistics/departmentStatistics 迁移具体内容 */}
      </Card>
    </div>
  )
}

export default DepartmentStatisticsPage
