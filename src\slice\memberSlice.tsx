import { createSlice, current, PayloadAction, } from "@reduxjs/toolkit";
import { RootState ,store } from "../store";
import ApiFetchVars from "../api/common/fetch-api-vars";
import ApiTasksTypes from "../api/common/task-types";
import ApiUrlVars from "../api/common/url-vars";
// import { createApiAsyncThunk } from "../api/fetch-api";
import dayjs from 'dayjs';

export interface departmentState {
    keyword: any,
    pageSize: number,
    departmentList: any[],
    departmentTrueList: any[],
    currentData: any[],
    role_list: {
        role_id: string,
        role_name: string,
        disable: boolean
    }[],
    memberList: {
        name: any,
    }[],
    departList: {
        id: any,
        name: any,
    }[],
    memberAddInf: {
        member_list: {
            user_id: any,
            name: any,
            sex: any,
            mail: any,
        }[],
        depart_id: any,
        start_time: any
    }
    memberAdd: {
        user_list: {
            key: any,
            user_id: any,
            name: string,
            sex: any,
            mail: any,
        }[],
        role_id: string,
        start_time: string,
    },
    simpleChangeList: {
        key: any,
        user: any,
        role: any,
        depart: any,
    },
    addCommit: {
        use_type: any,
        user_list: { 
            user_id: any,
            name: string,
            sex: string,
            mail: string
        }[],
        role_id: any,
        start_time: string,
    },
    currentPage: number,
    chooseKey: any,
    confirmText: string,
    multipleType: string,
    multipleRole: string,
    keyList: any[],
    isConfirmed: boolean,
    isMultipleForm: boolean,
    isMultipleMove: boolean,
    isAddFormShow: boolean,
    isSimpleChange: boolean,
    isMultipleChange: boolean,
}
export interface organizationState {
    keyword: any,
    pageSize: number,
    organizationList: any[],
    role_list: {
        role_id: string,
        role_name: string,
        disable: boolean
    }[],
    organizationTrueList: any[],
    currentData: any[], 
    depart_list: {
        depart_id: number,
        depart_name: string,
    }[],
    memberAdd: {
        name: string,
        work_no: string,
        sex: any,
        mail: string,
        password: string,
        role_id: any,
        depart_id: any,
        start_time: any,
    },
    simpleChangeList: {
        key: any,
        name: any,
        work_no: any,
        email: any,
        role: any,
        depart: any,
    },
    workList: {
        "JS": string,
        "JSXP": string,
        "JSSX": string,
    },
    currentPage: number,
    chooseKey: any,
    confirmText: string,
    multipleType: string,
    multipleRole: string,
    keyList: any[],
    isConfirmed: boolean,
    isMultipleArea: boolean,
    isMultipleForm: boolean,
    isMultipleMove: boolean,
    isAddFormShow: boolean,
    isSimpleChange: boolean,
    isMultipleChange: boolean,
}

const departmentState = {
    keyword: '',
    pageSize: 100,
    departmentList: [],
    departmentTrueList: [],
    currentData: [],
    role_list: [],
    memberList: [],
    departList: [],
    memberAdd: {
        user_list: [{
            key: 0,
            user_id: 0,
            name: '',
            sex: '',
            mail: '',
        }],
        role_id: '',
        start_time: '',
    },
    memberAddInf: {
        member_list: [{
            user_id: 0,
            name: "",
            sex: "",
            mail: "",
        }],
        depart_id: 0,
        start_time: "",
    },
    simpleChangeList: {
        key: 0,
        user: '',
        role: '',
        depart: '',
    },
    addCommit: {
        use_type: 1,
        user_list: [{ 
            user_id: 0,
            name: '',
            sex: '',
            mail: ''
        }],
        role_id: '',
        start_time: dayjs().format('YYYY-MM-DD').toString() + ' 00:00:00',
    },
    currentPage: 1,
    confirmText: '',
    multipleType: '',
    multipleRole: '',
    keyList: [],
    chooseKey: 0,
    isConfirmed: false,
    isMultipleForm: false,
    isMultipleMove: false,
    isAddFormShow: false,
    isSimpleChange: false,
    isMultipleChange: false,
}
const organizationState = {
    keyword: '',
    pageSize: 100,
    organizationList: [],
    role_list: [],
    organizationTrueList: [],
    currentData: [],
    depart_list: [],
    memberAdd: {
        name: '',
        work_no: '',
        sex: 1,
        mail: '',
        password: '',
        role_id: '',
        depart_id: 0,
        start_time: dayjs().format('YYYY-MM-DD').toString() + ' 00:00:00',
    },
    simpleChangeList: {
        key: 0,
        name: '',
        work_no: 0,
        email: '',
        role: '',
        depart: '',
    },
    confirmText: '',
    multipleType: '',
    multipleRole: '',
    keyList: [],
    workList: {
        "JS": '',
        "JSXP": '',
        "JSSX": '',
    },
    currentPage: 1,
    chooseKey: 0,
    isConfirmed: false,
    isMultipleArea: false,
    isMultipleForm: false,
    isMultipleMove: false,
    isAddFormShow: false,
    isSimpleChange: false,
    isMultipleChange: false,
}
// 定义要保存到Store的数据格式
export interface memberState {
    department: departmentState,
    organization: organizationState,
}

// 初始化数据
const initialState: memberState = {
    department: departmentState,
    organization: organizationState,
};

export const memberSlice = createSlice({
    name: 'member',
    initialState,
    // reducers 集合了reducer和action的功能
    // 每个case冒号之前的为action名
    // 每个case整体为reducer
    reducers: {
        // 使用PayloadAction来展示action.payload的详细内容
        setDepartDataWhenInit: (state: memberState, action: PayloadAction<any>) => {
            if(action.payload.staff_list && Array.isArray(action.payload.staff_list) && action.payload.staff_list.length > 0){
                state.department.departmentList = action.payload.staff_list;
                state.department.departmentList = state.department.departmentList.map(item=>{
                    return{
                        ...item,
                        "comment": ''
                    }
                })
                let list: any[] = []
                state.department.departmentList.map((item:any, index:any)=>{
                    if(index>0){
                        let array:number[] = []
                        list.map(a=>{
                            array.push(a.user_id)
                        })
                        if(array.includes(item.user_id)){
                            const regex = /\(([^)]*)\)/;
                            const target = state.department.departmentList.filter(object=>object.user_id === item.user_id)
                            let match = ''
                            target.map(b=>{
                                const string = regex.exec(b.depart_name)
                                if (string && string.length > 1) {
                                    match += (string[1] + ';')
                                }
                            })
                            list.filter(object=>object.user_id === item.user_id)[0].comment = match;
                        }else{
                            list.push(item)
                        }
                    }else{
                        list.push(item)
                    }
                })
                state.department.departmentList = list.map((item:any,index:any)=>({...item, ['id']: index+1}));
                state.department.departmentTrueList = state.department.departmentList;
                const startIndex = (state.department.currentPage - 1) * state.department.pageSize;
                const endIndex = startIndex + state.department.pageSize;
                state.department.currentData = state.department.departmentTrueList.slice(startIndex, endIndex);
                if(action.payload.role_list && Array.isArray(action.payload.role_list)){
                    state.department.role_list = action.payload.role_list;
                    state.department.role_list = state.department.role_list.map(item=>{
                        return{
                            ...item,
                            "disabled": false
                        }
                    })
                }
            }else{
                state.department.departmentList = [{
                    "id":1,
                    "user_id": 0,
                    "name": '',
                    "sex": '',
                    "depart_id": 0,
                    "depart_name": '',
                    "mail": '',
                    "comment": ''
                }];
                state.department.departmentTrueList = state.department.departmentList;
            }
        },
        departmentMultipleMove: (state: memberState) => {
            state.department.isMultipleMove = !state.department.isMultipleMove;
            state.department.keyList = [];
        },
        departmentPageSize: (state: memberState, action: PayloadAction<any>) => {
            state.department.pageSize = action.payload;
            const startIndex = (state.department.currentPage - 1) * state.department.pageSize;
            const endIndex = startIndex + state.department.pageSize;
            state.department.currentData = state.department.departmentTrueList.slice(startIndex, endIndex);
        },
        departmentCheckboxAll: (state: memberState) => {
            let all:any[] = [];
            state.department.departmentList.map((value) => {
                all.push(value.user_id);
            })
            if(state.department.keyList.length === all.length){
                state.department.keyList = [];
            }else{
                state.department.keyList = all;
            }
        },
        departmentCheckboxClick: (state: memberState, action: PayloadAction<any>) => {
            if(state.department.keyList.includes(action.payload)){
                const keyList = state.department.keyList.filter((item:any) => item !== action.payload)
                state.department.keyList = keyList;
            }else{
                state.department.keyList.push(action.payload);
            }
        },
        departmentSimpleChangeClick: (state: memberState, action: PayloadAction<any>) => {
            state.department.isSimpleChange = true;
            const regex = /\(([^)]*)\)/;
            let role_name = ''
            const string = regex.exec(state.department.departmentList.find((item:any) => item.user_id == action.payload)?.depart_name)
            if (string && string.length > 1) {
                role_name = string[1]
            }
            const comment = state.department.departmentList.find((item:any) => item.user_id == action.payload)?.comment
            state.department.simpleChangeList.key = action.payload;
            state.department.simpleChangeList.user = state.department.departmentList.find((item:any) => item.user_id == action.payload)?.name;
            state.department.simpleChangeList.depart = state.department.departmentList.find((item:any) => item.user_id == action.payload)?.depart_name.split('(')[0];
            let roles:any
            if(comment){
                const matchResult = comment
                let role_id:any[] = []
                const stringArray = matchResult.split(';').filter((str:any)=>str !== '')
                stringArray.map((a:any)=>{
                    const name = state.department.role_list.filter((b:any)=>b.role_name === a)[0].role_id
                    role_id.push([name])
                })
                roles = role_id
            }else{
                const role_id = state.department.role_list.filter((b:any)=>b.role_name === role_name)[0].role_id
                roles = [role_id]
            }
            state.department.simpleChangeList.role = roles;
        },
        departmentDelete: (state: memberState, action: PayloadAction<any>) => {
            state.department.departmentList = state.department.departmentList.filter(item => item.user_id !== action.payload.user_id);

            state.department.confirmText = '';
            state.department.isConfirmed = false;
            state.department.chooseKey = 0;
            state.department.departmentTrueList = state.department.departmentList;

            const startIndex = (state.department.currentPage - 1) * state.department.pageSize;
            const endIndex = startIndex + state.department.pageSize;
            state.department.currentData = state.department.departmentTrueList.slice(startIndex, endIndex);
        },
        departmentAdd: (state: memberState, action: PayloadAction<any>) => {
            state.department.departmentList.push({
                id: state.department.departmentList.length + 1,
                user_id: action.payload.user_id,
                name: (action.payload.name.substring(action.payload.name.indexOf('-')+1) + '-' + action.payload.name.substring(0,action.payload.name.indexOf('-'))),
                sex: action.payload.sex,
                depart_id: action.payload.depart_id,
                depart_name: action.payload.depart_name,
                mail: action.payload.mail,
                "comment": ''
            })
            state.department.departmentTrueList = state.department.departmentList;

            const startIndex = (state.department.currentPage - 1) * state.department.pageSize;
            const endIndex = startIndex + state.department.pageSize;
            state.department.currentData = state.department.departmentTrueList.slice(startIndex, endIndex);
            
            state.department.confirmText = '';
            state.department.isConfirmed = false;
            state.department.chooseKey = 0;
        },
        departmentSimpleDeleteClick: (state: memberState) => {
            state.department.isConfirmed = false;
        },
        departmentSimpleRoleChange: (state: memberState, action: PayloadAction<any>) => {
            state.department.simpleChangeList.role = action.payload
        },
        departmentChange: (state: memberState, action: PayloadAction<any>) => {
            state.department.isSimpleChange = false;
            state.department.keyword = ''
            state.department.departmentList = state.department.departmentList.map(item => {
                if(item.user_id === action.payload.user_id){
                    return {
                        ...item,
                        user_id: action.payload.user_id,
                        name: action.payload.name,
                        sex: action.payload.sex,
                        depart_id: action.payload.depart_id,
                        depart_name: action.payload.depart_name,
                        mail: action.payload.mail,
                        comment: action.payload.comment
                    }
                }
                return item;
            })
            state.department.departmentTrueList = state.department.departmentList;

            state.department.simpleChangeList.key = 0;
            state.department.simpleChangeList.user = '';
            state.department.simpleChangeList.role = '';
            state.department.simpleChangeList.depart = '';

            const startIndex = (state.department.currentPage - 1) * state.department.pageSize;
            const endIndex = startIndex + state.department.pageSize;
            state.department.currentData = state.department.departmentTrueList.slice(startIndex, endIndex);
        },
        departmentSingleChange: (state: memberState, action: PayloadAction<any>) => {
            state.department.keyword = ''
            state.department.isSimpleChange = false;
            const comment = action.payload.comment.replaceAll('&', ';')

            state.department.departmentList = state.department.departmentList.map(item => {
                if(item.user_id === action.payload.user_id){
                    return {
                        ...item,
                        user_id: action.payload.user_id,
                        name: action.payload.name,
                        sex: action.payload.sex,
                        depart_id: action.payload.depart_id,
                        depart_name: action.payload.depart_name,
                        mail: action.payload.mail,
                        comment: comment
                    }
                }
                return item;
            })
            state.department.departmentTrueList = state.department.departmentList;

            state.department.simpleChangeList.key = 0;
            state.department.simpleChangeList.user = '';
            state.department.simpleChangeList.role = '';
            state.department.simpleChangeList.depart = '';

            const startIndex = (state.department.currentPage - 1) * state.department.pageSize;
            const endIndex = startIndex + state.department.pageSize;
            state.department.currentData = state.department.departmentTrueList.slice(startIndex, endIndex);
        },
        departmentSimpleListSave: (state: memberState) => {
            state.department.isSimpleChange = false;
        },
        departmentSimpleCancel: (state: memberState) => {
            state.department.isSimpleChange = false;
            state.department.simpleChangeList.key = 0;
            state.department.simpleChangeList.user = '';
            state.department.simpleChangeList.role = '';
            state.department.simpleChangeList.depart = '';
        },
        departmentMultipleForm: (state: memberState) => {
            state.department.isMultipleForm = true;
        },
        departmentMultipleType: (state: memberState, action: PayloadAction<any>) => {
            if(state.department.multipleType == action.payload){
                state.department.multipleType = '';
            }else{
                state.department.multipleType = action.payload;
            }
        },
        departmentMultipleRole: (state: memberState, action: PayloadAction<any>) => {
            state.department.multipleRole = action.payload;
        },
        departmentMultipleListSave: (state: memberState) => {
            state.department.multipleRole = '';
            state.department.isMultipleForm = false;
        },
        departmentMultipleCancel: (state: memberState) => {
            state.department.multipleRole = '';
            state.department.isMultipleForm = false;
        },
        departmentAddForm: (state: memberState) => {
            state.department.isAddFormShow = true;
        },
        departmentAddFormPlus: (state: memberState) => {
            state.department.memberAdd.user_list.push({
                key: state.department.memberAdd.user_list.length,
                user_id: '',
                name: '',
                sex: '',
                mail: '',
            });
            state.department.memberAdd.user_list = state.department.memberAdd.user_list.map((item,index) => {
                return {
                    ...item,
                    key: index,
                };
            })
        },
        departmentAddFormMinus: (state: memberState, action: PayloadAction<any>) => {
            state.department.memberAdd.user_list = state.department.memberAdd.user_list.filter(item => item.key !== action.payload);
            state.department.memberAdd.user_list = state.department.memberAdd.user_list.map((item,index) => {
                return {
                    ...item,
                    key: index,
                };
            })
        },
        departmentAddFormSelect: (state: memberState, action: PayloadAction<any>) => {
            state.department.memberAdd.user_list = state.department.memberAdd.user_list.map(item => {
                if (item.key === action.payload.key && action.payload.value!='') {
                  return {
                    ...item,
                    name: action.payload.value,
                  };
                }
                return item;
              });
              state.department.memberAdd.user_list = state.department.memberAdd.user_list.map((member,index) => ({
                key: index,
                user_id: state.department.memberAddInf.member_list.find((item=> item.name == member.name))?.user_id,
                name: member.name,
                sex: state.department.memberAddInf.member_list.find((item=> item.name == member.name))?.sex,
                mail: state.department.memberAddInf.member_list.find((item=> item.name == member.name))?.mail,
            }))
            state.department.addCommit.user_list = state.department.memberAdd.user_list.map(({ key, ...rest }) => rest).filter(item => item.name!='0-0');
            state.department.addCommit.role_id = state.department.role_list.find(item => item.role_name == state.department.memberAdd.role_id)?.role_id;
        },
        departmentAddFormRole: (state: memberState, action: PayloadAction<any>) => {
            state.department.memberAdd.role_id = action.payload;
            state.department.memberAdd.user_list = state.department.memberAdd.user_list.map((member,index) => ({
                key: index,
                user_id: state.department.memberAddInf.member_list.find((item=> item.name == member.name))?.user_id,
                name: member.name,
                sex: state.department.memberAddInf.member_list.find((item=> item.name == member.name))?.sex,
                mail: state.department.memberAddInf.member_list.find((item=> item.name == member.name))?.mail,
            }))
            state.department.addCommit.user_list = state.department.memberAdd.user_list.map(({ key, ...rest }) => rest).filter(item => item.name!='0-0');
            state.department.addCommit.role_id = state.department.role_list.find(item => item.role_name == state.department.memberAdd.role_id)?.role_id;
        },
        departmentAddFormTime: (state: memberState, action: PayloadAction<any>) => {
            state.department.memberAdd.user_list = state.department.memberAdd.user_list.map((member,index) => ({
                key: index,
                user_id: state.department.memberAddInf.member_list.find((item=> item.name == member.name))?.user_id,
                name: member.name,
                sex: state.department.memberAddInf.member_list.find((item=> item.name == member.name))?.sex,
                mail: state.department.memberAddInf.member_list.find((item=> item.name == member.name))?.mail,
            }))
            state.department.addCommit.user_list = state.department.memberAdd.user_list.map(({ key, ...rest }) => rest).filter(item => item.name!='0-0');
            state.department.addCommit.role_id = state.department.role_list.find(item => item.role_name == state.department.memberAdd.role_id)?.role_id;
            state.department.addCommit.start_time = action.payload.format('YYYY-MM-DD').toString() + ' 00:00:00';
        },
        departmentAddFormSave: (state: memberState, action: PayloadAction<any>) => {
            state.department.memberAdd = {
                user_list: [{
                    key: 0,
                    user_id: 0,
                    name: '',
                    sex: '',
                    mail: '',
                }],
                role_id: '',
                start_time: '',
            }
            state.department.isAddFormShow = false;
            state.department.addCommit = {
                use_type: 1,
                user_list: [{ 
                    user_id: 0,
                    name: '',
                    sex: '',
                    mail: ''
                }],
                role_id: '',
                start_time: dayjs().format('YYYY-MM-DD').toString() + ' 00:00:00',
            }
        },
        departmentAddFormCancel: (state: memberState) => {
            state.department.memberAdd = {
                user_list: [{
                    key: 0,
                    user_id: 0,
                    name: '',
                    sex: '',
                    mail: '',
                }],
                role_id: '',
                start_time: '',
            }
            state.department.isAddFormShow = false;
            state.department.addCommit = {
                use_type: 1,
                user_list: [{ 
                    user_id: 0,
                    name: '',
                    sex: '',
                    mail: ''
                }],
                role_id: '',
                start_time: dayjs().format('YYYY-MM-DD').toString() + ' 00:00:00',
            }
        },
        departmentKeyWordInput: (state: memberState, action: PayloadAction<any>) => {
            if(action.payload!=null){
                state.department.keyword = action.payload;
            }
            const startIndex = (state.department.currentPage - 1) * state.department.pageSize;
            const endIndex = startIndex + state.department.pageSize;
            state.department.departmentTrueList = state.department.departmentList;
            if(state.department.keyword != ''){
                state.department.departmentTrueList = state.department.departmentTrueList.filter(item => 
                    (item.name.includes(state.department.keyword) || 
                    item.depart_id.toString().includes(state.department.keyword) || 
                    item.depart_name.includes(state.department.keyword) ||
                    item.comment.includes(state.department.keyword))
                );
            }else {
                state.department.departmentTrueList = state.department.departmentList
            }
            state.department.currentData = state.department.departmentTrueList.slice(startIndex, endIndex);
            state.department.currentPage = 1;
        },
        //点击显示撤回栏
        departmentHandleDelete: (state: memberState, action: PayloadAction<any>) => {
            state.department.isConfirmed = true;
            state.department.chooseKey = action.payload;
        },
        //输入栏输入'我确认'
        departmentHandleDeleteFormInput: (state: memberState, action: PayloadAction<any>) => {
            state.department.confirmText = action.payload.target.value;
        },
        //一键输入'我确认'
        departmentHandleDeleteAutoInput: (state: memberState) => {
            state.department.confirmText = '我确认';
        },
        //点击显示撤回栏
        departmentHandleDeleteCancel: (state: memberState) => {
            state.department.confirmText = '';
            state.department.isConfirmed = false;
            state.department.chooseKey = 0;
        },
        departmentPageChange: (state: memberState, action: PayloadAction<any>) => {
            if(action.payload.target.value!=null){
                state.department.keyword = action.payload.target.value;
            }
            const startIndex = (state.department.currentPage - 1) * state.department.pageSize;
            const endIndex = startIndex + state.department.pageSize;
            state.department.departmentTrueList = state.department.departmentList;
            if(state.department.keyword != ''){
                state.department.departmentTrueList = state.department.departmentTrueList.filter(item => (item.name.includes(state.department.keyword)));
            }else {
                state.department.departmentTrueList = state.department.departmentList
            }
            state.department.currentData = state.department.departmentTrueList.slice(startIndex, endIndex);
            state.department.currentPage = 1;
        },
        handleDepartPageChange: (state: memberState, action: PayloadAction<any>) => {
            state.department.currentPage = action.payload;
            const startIndex = (state.department.currentPage - 1) * state.department.pageSize;
            const endIndex = startIndex + state.department.pageSize;
            state.department.currentData = state.department.departmentTrueList.slice(startIndex, endIndex);
        },
        


        //organization
        setStaffDataWhenInit: (state: memberState, action: PayloadAction<any>) => {
            if(action.payload.staff_list && Array.isArray(action.payload.staff_list) && action.payload.staff_list.length > 0){
                state.organization.organizationList = action.payload.staff_list
                // state.organization.organizationList = action.payload.staff_list.map((item:any,index:any)=>({...item, ['id']: index+1}));
                state.organization.organizationList = state.organization.organizationList.map(item=>{
                    return{
                        ...item,
                        "comment": ''
                    }
                })
                let list: any[] = []
                state.organization.organizationList.map((item:any, index:any)=>{
                    if(index>0){
                        let array:number[] = []
                        list.map(a=>{
                            array.push(a.user_id)
                        })
                        if(array.includes(item.user_id)){
                            const target = state.organization.organizationList.filter(object=>object.user_id === item.user_id)
                            let match = ''
                            target.map(b=>{
                                match += (b.role_name + ';')
                            })
                            list.filter(object=>object.user_id === item.user_id)[0].comment = match;
                        }else{
                            list.push(item)
                        }
                    }else{
                        list.push(item)
                    }
                })
                state.organization.organizationList = list.map((item:any,index:any)=>({...item, ['id']: index+1}));
                state.organization.organizationTrueList = state.organization.organizationList;
                const startIndex = (state.organization.currentPage - 1) * state.organization.pageSize;
                const endIndex = startIndex + state.organization.pageSize;
                state.organization.currentData = state.organization.organizationTrueList.slice(startIndex, endIndex);
                state.organization.role_list = action.payload.role_list;
                state.organization.depart_list = action.payload.depart_list;
                state.organization.workList = {
                    "JS": action.payload.JS[0]?.work_no,
                    "JSXP": action.payload.JSXP[0]?.work_no,
                    "JSSX": action.payload.JSSX[0]?.work_no,
                };
            }else{
                state.organization.organizationList = [{
                    "id":1,
                    "user_id":0,
                    "name":"",
                    "work_no":"",
                    "sex":"",
                    "depart_id":0,
                    "depart_name":"",
                    "role_name":"",
                    "mail":"",
                    "start_time":""
                }];
                state.organization.organizationTrueList = state.organization.organizationList;
            }
        },
        organizationPageSize: (state: memberState, action: PayloadAction<any>) => {
            state.organization.pageSize = action.payload;
            const startIndex = (state.organization.currentPage - 1) * state.organization.pageSize;
            const endIndex = startIndex + state.organization.pageSize;
            state.organization.currentData = state.organization.organizationTrueList.slice(startIndex, endIndex);
        },
        organizationMultipleMove: (state: memberState) => {
            state.organization.isMultipleMove = !state.organization.isMultipleMove;
            state.organization.isMultipleArea = false;
            state.organization.keyList = [];
            state.organization.isAddFormShow = false;
        },
        organizationDataImport: (state: memberState) => {
            state.organization.isMultipleArea = !state.organization.isMultipleArea;
            state.organization.isMultipleMove = !state.organization.isMultipleMove;
            state.organization.isAddFormShow = false;
        },
        organizationCheckboxAll: (state: memberState) => {
            let all:any[] = [];
            state.organization.organizationTrueList.map((value) => {
                all.push(value.user_id);
            })
            if(state.organization.keyList.length === all.length){
                state.organization.keyList = [];
            }else{
                state.organization.keyList = all;
            }
        },
        organizationCheckboxClick: (state: memberState, action: PayloadAction<any>) => {
            if(state.organization.keyList.includes(action.payload)){
                const keyList = state.organization.keyList.filter((item:any) => item !== action.payload)
                state.organization.keyList = keyList;
            }else{
                state.organization.keyList.push(action.payload);
            }
        },
        organizationSimpleChangeClick: (state: memberState, action: PayloadAction<any>) => {
            state.organization.isSimpleChange = true;
            const comment = state.organization.organizationList.find((item:any) => item.user_id == action.payload)?.comment;
            const role_name = state.organization.organizationList.find((item:any) => item.user_id == action.payload)?.role_name;
            state.organization.simpleChangeList.key = action.payload;
            state.organization.simpleChangeList.name = state.organization.organizationList.find((item:any) => item.user_id == action.payload)?.name;
            state.organization.simpleChangeList.work_no = state.organization.organizationList.find((item:any) => item.user_id == action.payload)?.work_no;
            state.organization.simpleChangeList.email = state.organization.organizationList.find((item:any) => item.user_id == action.payload)?.mail;
            state.organization.simpleChangeList.depart = state.organization.organizationList.find((item:any) => item.user_id == action.payload)?.depart_name;
            let roles:any
            if(comment){
                const matchResult = comment
                let role_id:any[] = []
                const stringArray = matchResult.split(';').filter((str:any)=>str !== '')
                stringArray.map((a:any)=>{
                    const name = state.organization.role_list.filter((b:any)=>b.role_name === a)[0].role_id
                    role_id.push([name])
                })
                roles = role_id
            }else{
                const role_id = state.organization.role_list.filter((b:any)=>b.role_name === role_name)[0].role_id
                roles = [role_id]
            }
            state.organization.simpleChangeList.role = roles;
        },
        organizationSimpleDeleteClick: (state: memberState) => {
            state.organization.confirmText = '';
            state.organization.isConfirmed = false;
            state.organization.chooseKey = 0;
        },
        organizationSimpleRoleChange: (state: memberState, action: PayloadAction<any>) => {
            state.organization.simpleChangeList.role = action.payload
        },
        organizationSimpleSave: (state: memberState) => {
            state.organization.isSimpleChange = false;
            state.organization.simpleChangeList.key = 0;
            state.organization.simpleChangeList.name = '';
            state.organization.simpleChangeList.work_no = '';
            state.organization.simpleChangeList.email = '';
            state.organization.simpleChangeList.role = '';
            state.organization.simpleChangeList.depart = '';
            const startIndex = (state.organization.currentPage - 1) * state.organization.pageSize;
            const endIndex = startIndex + state.organization.pageSize;
            state.organization.currentData = state.organization.organizationTrueList.slice(startIndex, endIndex);
        },
        organizationSimpleCancel: (state: memberState) => {
            state.organization.isSimpleChange = false;
            state.organization.simpleChangeList.key = 0;
            state.organization.simpleChangeList.name = '';
            state.organization.simpleChangeList.work_no = '';
            state.organization.simpleChangeList.email = '';
            state.organization.simpleChangeList.role = '';
            state.organization.simpleChangeList.depart = '';
        },
        organizationMultipleForm: (state: memberState) => {
            state.organization.isMultipleForm = true;
            state.organization.isAddFormShow = false;
        },
        organizationMultipleCommit: (state: memberState) => {
            state.organization.isMultipleArea = false;
        },
        organizationMultipleCancel: (state: memberState) => {
            state.organization.isMultipleArea = false;
        },
        organizationMultipleType: (state: memberState, action: PayloadAction<any>) => {
            if(state.organization.multipleType == action.payload){
                state.organization.multipleType = '';
            }else{
                state.organization.multipleType = action.payload;
            }
        },
        organizationMultipleRole: (state: memberState, action: PayloadAction<any>) => {
            state.organization.multipleRole = action.payload;
        },
        organizationMultipleSave: (state: memberState) => {
            state.organization.isMultipleForm = false;
        },
        organizationMultipleFormCancel: (state: memberState) => {
            state.organization.multipleRole = '';
            state.organization.isMultipleForm = false;
        },
        organizationAddForm: (state: memberState) => {
            state.organization.isAddFormShow = true;
            state.organization.isMultipleForm = false;
            state.organization.isMultipleArea = false;
        },
        organizationAddFormName: (state: memberState, action: PayloadAction<any>) => {
            state.organization.memberAdd.name = action.payload.target.value;
        },
        organizationAddFormId: (state: memberState, action: PayloadAction<any>) => {
            state.organization.memberAdd.work_no = action.payload.target.value;
        },
        organizationAddFormEmail: (state: memberState, action: PayloadAction<any>) => {
            state.organization.memberAdd.mail = action.payload.target.value;
        },
        organizationAddFormRole: (state: memberState, action: PayloadAction<any>) => {
            state.organization.memberAdd.role_id = action.payload;
        },
        organizationAddFormSex: (state: memberState, action: PayloadAction<any>) => {
            state.organization.memberAdd.sex = action.payload;
        },
        organizationAddFormDepart: (state: memberState, action: PayloadAction<any>) => {
            state.organization.memberAdd.depart_id = action.payload;
        },
        organizationAddFormPassword: (state: memberState, action: PayloadAction<any>) => {
            state.organization.memberAdd.password = action.payload.target.value;
        },
        organizationAddFormTime: (state: memberState, action: PayloadAction<any>) => {
            state.organization.memberAdd.start_time = action.payload.format('YYYY-MM-DD').toString() + ' 00:00:00';
        },
        organizationAddFormSave: (state: memberState) => {
            state.organization.memberAdd = {
                name: '',
                work_no: '',
                sex: 1,
                mail: '',
                password: '',
                role_id: '',
                depart_id: 0,
                start_time: dayjs().format('YYYY-MM-DD').toString() + ' 00:00:00',
            };
            state.organization.isAddFormShow = false;
            const startIndex = (state.organization.currentPage - 1) * state.organization.pageSize;
            const endIndex = startIndex + state.organization.pageSize;
            state.organization.currentData = state.organization.organizationTrueList.slice(startIndex, endIndex);
        },
        // 选择角色
        handleRolesSelectedDeparts: (state: memberState, action: PayloadAction<any>) => {
            state.department.simpleChangeList.role = action.payload;
        },
        organizationAdd: (state: memberState, action: PayloadAction<any>) => {
            state.organization.organizationList.push({
                id: state.organization.organizationList.length + 1,
                user_id: action.payload.user_id,
                name: action.payload.name,
                work_no: action.payload.work_no,
                sex: action.payload.sex,
                depart_id: action.payload.depart_id,
                depart_name: action.payload.depart_name,
                role_name: action.payload.role_name,
                mail: action.payload.mail,
            });
            state.organization.organizationTrueList = state.organization.organizationList;
            const startIndex = (state.organization.currentPage - 1) * state.organization.pageSize;
            const endIndex = startIndex + state.organization.pageSize;
            state.organization.currentData = state.organization.organizationTrueList.slice(startIndex, endIndex);
        },
        organizationDelete: (state: memberState, action: PayloadAction<any>) => {
            state.organization.organizationList = state.organization.organizationList.filter(item => (item.user_id != action.payload.user_id));
            state.organization.organizationTrueList = state.organization.organizationList;
            const startIndex = (state.organization.currentPage - 1) * state.organization.pageSize;
            const endIndex = startIndex + state.organization.pageSize;
            state.organization.currentData = state.organization.organizationTrueList.slice(startIndex, endIndex);
        },
        organizationChange: (state: memberState, action: PayloadAction<any>) => {
            state.organization.keyword = ''
            state.organization.organizationList = state.organization.organizationList.map(item => {
                if (item.user_id === action.payload.user_id) {
                    return {
                        ...item,
                        role_name: action.payload.role_name,
                        comment: action.payload.comment
                    };
                    }
                return item;
            });
            state.organization.organizationTrueList = state.organization.organizationList;
            const startIndex = (state.organization.currentPage - 1) * state.organization.pageSize;
            const endIndex = startIndex + state.organization.pageSize;
            state.organization.currentData = state.organization.organizationTrueList.slice(startIndex, endIndex);
        },
        organizationSingleChange: (state: memberState, action: PayloadAction<any>) => {
            state.organization.keyword = ''
            const comment = action.payload.comment.replaceAll('&', ';')
            state.organization.organizationList = state.organization.organizationList.map(item => {
                if (item.user_id === action.payload.user_id) {
                    return {
                        ...item,
                        role_name: action.payload.role_name,
                        comment: comment
                    };
                    }
                return item;
            });
            state.organization.organizationTrueList = state.organization.organizationList;
            const startIndex = (state.organization.currentPage - 1) * state.organization.pageSize;
            const endIndex = startIndex + state.organization.pageSize;
            state.organization.currentData = state.organization.organizationTrueList.slice(startIndex, endIndex);
        },
        organizationAddFormCancel: (state: memberState) => {
            state.organization.memberAdd = {
                name: '',
                work_no: '',
                sex: 1,
                mail: '',
                password: '',
                role_id: '',
                depart_id: 0,
                start_time: '',
            };
            state.organization.isAddFormShow = false;
        },
        organizationPageChange: (state: memberState, action: PayloadAction<any>) => {
            if(action.payload!=null){
                state.organization.keyword = action.payload;
            }
            const startIndex = (state.organization.currentPage - 1) * state.organization.pageSize;
            const endIndex = startIndex + state.organization.pageSize;
            state.organization.organizationTrueList = state.organization.organizationList;
            if(state.organization.keyword != ''){
                state.organization.organizationTrueList = state.organization.organizationTrueList.filter(item => 
                    (item.name.toLowerCase().includes(state.organization.keyword.toString().toLowerCase()) || 
                    item.depart_id.toString().toLowerCase().includes(state.organization.keyword.toString().toLowerCase()) || 
                    (item.depart_name+'('+item.depart_code+')').toLowerCase().includes(state.organization.keyword.toString().toLowerCase()) || 
                    item.mail.toLowerCase().includes(state.organization.keyword.toString().toLowerCase()) ||
                    item.start_time.slice(0,10).toLowerCase().includes(state.organization.keyword.toString().toLowerCase()) ||
                    item.work_no.toString().toLowerCase().includes(state.organization.keyword.toString().toLowerCase()) ||
                    item.role_name.toLowerCase().includes(state.organization.keyword.toString().toLowerCase()) || 
                    item.comment.toLowerCase().includes(state.organization.keyword.toString().toLowerCase())));
            }else {
                state.organization.organizationTrueList = state.organization.organizationList
            }
            state.organization.currentData = state.organization.organizationTrueList.slice(startIndex, endIndex);
            state.organization.currentPage = 1;
        },
        handlePageChange: (state: memberState, action: PayloadAction<any>) => {
            state.organization.currentPage = action.payload;
            const startIndex = (state.organization.currentPage - 1) * state.organization.pageSize;
            const endIndex = startIndex + state.organization.pageSize;
            state.organization.currentData = state.organization.organizationTrueList.slice(startIndex, endIndex);
        },
        //点击显示撤回栏
        organizationHandleDelete: (state: memberState, action: PayloadAction<any>) => {
            state.organization.isConfirmed = true;
            state.organization.chooseKey = action.payload;
        },
        //输入栏输入'我确认'
        organizationHandleDeleteFormInput: (state: memberState, action: PayloadAction<any>) => {
            state.organization.confirmText = action.payload.target.value;
        },
        //一键输入'我确认'
        organizationHandleDeleteAutoInput: (state: memberState) => {
            state.organization.confirmText = '我确认';
        },
        //点击显示撤回栏
        organizationHandleDeleteCancel: (state: memberState) => {
            state.organization.confirmText = '';
            state.organization.isConfirmed = false;
            state.organization.chooseKey = 0;
        },
        handleDepartMemberAdd: (state: memberState, action: PayloadAction<any>) => {
            const response = action.payload;
            state.department.memberAddInf = response.data;
            let all:any[] = [];
            state.department.memberAddInf.member_list?.map((value) => {
                if(value.user_id != 0){
                    all.push(
                        {
                            name: value.name
                        }
                    );
                }
            })
            state.department.memberList = all;
        }
    },
    }
);

//以下内容必须要有
export const { actions: memberActions } = memberSlice;

export default memberSlice.reducer;

//state 后面的为store中数据名称
export const memberData = (state: RootState) => state.member;