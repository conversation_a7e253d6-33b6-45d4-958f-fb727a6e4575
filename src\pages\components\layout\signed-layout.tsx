import { ReactElement, useEffect, useState, useCallback } from "react"
import { useRouter } from "next/router"
import { Spin } from "antd";
import { subPermissionIsGrantedByPath } from "@/utils/permissions"
import errorStyles from '../css/error.module.css'
import layoutStyles from '../css/signed-layout.module.css'
import { is_login } from "@/utils/login"
import { getCookie, key_for_token, key_for_token_aes } from "@/utils/cookies"
import Head from "next/head";
import dynamic from 'next/dynamic';
import { useInterval } from '@/hook/useInterval';
import { useTranslation } from '@/hooks/useTranslation';

// 创建客户端专用组件
const LoadingSpinner = dynamic(() => import('./LoadingSpinner'), {
  ssr: false,
  loading: () => null
});

const LeftCornor = dynamic(() => import('@/components/ui/left-cornor'), {
  ssr: false,
  loading: () => <div>Loading...</div>
});

export default function SignedLayout(children: ReactElement) {
  // 初始化状态时使用更安全的默认值
  const [granted, changePermissionGrantedByPath] = useState<string>('')
  const [scaleRatio, setScaleRatio] = useState<number>(0.0)
  const [screenHeight, setScreenHeight] = useState<number>(960.0)
  const [isClient, setIsClient] = useState<boolean>(false)
  const [translationInitialized, setTranslationInitialized] = useState<boolean>(false)

  const router = useRouter()
  const path = router.pathname

  // 启用翻译功能用于菜单按钮
  const translation = useTranslation(['common', 'menu']);
  const { t: translateFn, currentLanguage, isClient: translationReady } = translation || {
    t: (key: string) => key,
    currentLanguage: 'zh',
    isClient: false
  };

  // 使用简单的后备翻译
  const t = (key: string) => {
    const fallbackTranslations: Record<string, string> = {
      'permissions.noAccess': '您没有权限访问该页面'
    };
    return translateFn ? translateFn(key) : (fallbackTranslations[key] || key);
  };

  // 使用 useCallback 优化回调函数
  const checkLoginStatus = useCallback(() => {
    if (typeof window === 'undefined') return;

    try {
      const token = getCookie(key_for_token)
      const token_aes = getCookie(key_for_token_aes)
      const logined: boolean = is_login(token, token_aes)

      if(!logined){
        // 清除可能残留的本地数据
        localStorage.removeItem('login');
        router.replace('/login');
      }
    } catch (error) {
      console.error('Login status check failed:', error);
      // 检查失败，跳转到登录页面
      router.replace('/login');
    }
  }, [router]);

  // 暂时禁用定时器，避免冲突
  // useInterval(checkLoginStatus, isClient ? 60000 : null); // 只在客户端启用定时器

  // 客户端初始化
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsClient(true);
      setTranslationInitialized(true);
    }
  }, []);

  // 权限检查 - 只在客户端执行，并且要更加谨慎
  useEffect(() => {
    if (isClient && typeof window !== 'undefined') {
      // 添加延迟，确保登出操作有时间完成
      const checkPermissions = () => {
        try {
          // 首先检查登录状态
          const token = getCookie(key_for_token);
          const token_aes = getCookie(key_for_token_aes);
          const isLoggedIn = is_login(token, token_aes);

          if (!isLoggedIn) {
            // 用户未登录，直接跳转，不进行权限检查
            router.replace('/login');
            return;
          }

          // 检查本地存储
          const loginData = localStorage.getItem('login');
          if (!loginData) {
            // 没有登录数据，直接跳转到登录页面，不显示权限错误
            router.replace('/login');
            return;
          }

          // 使用改进的权限检查
          const permissionResult = subPermissionIsGrantedByPath(router.pathname);

          if (permissionResult === 'not_logged_in') {
            // 用户未登录，跳转到登录页面
            router.replace('/login');
            return;
          }

          changePermissionGrantedByPath(permissionResult ? 'true' : 'false');
        } catch (error) {
          console.error('Permission check failed:', error);
          // 权限检查失败，跳转到登录页面
          router.replace('/login');
        }
      };

      // 延迟执行权限检查，给登出操作留出时间
      const timer = setTimeout(checkPermissions, 100);
      return () => clearTimeout(timer);
    }
  }, [path, isClient, router]);

  // 安全的渲染逻辑
  let childrenEle = (
    <div>
      <div style={{fontSize:'13px'}}>
          <div className={errorStyles.no_permission}>
              <div className={errorStyles.title}>加载中...</div>
          </div>
      </div>
    </div>);

  // 只在客户端完全初始化后进行条件渲染
  if (isClient && translationInitialized) {
    if (granted === 'true') {
      childrenEle = children;
    } else if (granted === 'false') {
      // 在显示权限错误前，再次检查登录状态
      const token = getCookie(key_for_token);
      const token_aes = getCookie(key_for_token_aes);
      const isLoggedIn = is_login(token, token_aes);

      if (!isLoggedIn) {
        // 用户未登录，显示加载状态而不是权限错误
        childrenEle = (
          <div>
            <div style={{fontSize:'13px'}}>
                <div className={errorStyles.no_permission}>
                    <div className={errorStyles.title}>正在跳转到登录页面...</div>
                </div>
            </div>
          </div>
        );
      } else {
        // 用户已登录但无权限，显示权限错误
        childrenEle = (
          <div>
            <div style={{fontSize:'13px'}}>
                <div className={errorStyles.no_permission}>
                    <div className={errorStyles.title}>
                      {translationReady && t ? t('permissions.noAccess') : '您没有权限访问该页面'}
                    </div>
                </div>
            </div>
          </div>
        );
      }
    }
  }
  return (
    <>
        {/* <Head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=0" />
          <style>
            {`
            body{
              transform: ${`scale(${scaleRatio})`};
            }
            html {
              height:  ${`${screenHeight}px`};
            }`}
          </style>
        </Head> */}
    <div className={layoutStyles.main_layout}>
      <LoadingSpinner />
      <aside className={layoutStyles.sidebar}>
        <LeftCornor></LeftCornor>
      </aside>
      <main className={layoutStyles.main_content}>
        {childrenEle}
      </main>
    </div>
        </>
  )
}