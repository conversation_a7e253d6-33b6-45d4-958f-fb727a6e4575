/* 统计详情组件样式 - 使用全局CSS变量 */

/* Modal样式 - 增加宽度，适配小屏幕 */
.detail_modal {
  width: 1200px;
  max-width: 95vw;
  max-height: 80vh;
}

.detail_modal :global(.ant-modal-content) {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.detail_modal :global(.ant-modal-header) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 50%, #f0f8ff 100%);
  border-bottom: none;
  padding: 16px var(--spacing-xl);
}

.detail_modal :global(.ant-modal-title) {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
}

.detail_modal :global(.ant-modal-close) {
  color: #fff;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
}

.detail_modal :global(.ant-modal-close:hover) {
  color: #f0f0f0;
}

.detail_modal :global(.ant-modal-body) {
  padding: var(--spacing-lg);
  max-height: 60vh;
  overflow-y: auto;
}

.detail_modal :global(.ant-modal-footer) {
  border-top: 1px solid #f0f0f0;
  padding: var(--spacing-lg) var(--spacing-xl);
  text-align: right;
}

/* Modal标题 */
.modal_title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: #fff;
}

.title_icon {
  font-size: 20px;
}

/* Modal内容 */
.modal_content {
  padding: 0;
}

/* 用户信息卡片 */
.user_info_card {
  margin-bottom: var(--spacing-lg);
  border: 1px solid #e8e8e8;
  border-radius: var(--border-radius-md);
}

.info_item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) 0;
}

.info_icon {
  color: #1890ff;
  font-size: 16px;
}

/* 区域标题 */
.section_title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #262626;
  font-weight: 600;
}

.section_icon {
  color: #1890ff;
  font-size: 16px;
}

/* 请假记录卡片 */
.leave_card {
  margin-bottom: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}

.leave_list {
  max-height: 200px;
  overflow-y: auto;
}

.leave_item {
  padding: 12px;
  border-bottom: 1px solid #f5f5f5;
  transition: background 0.3s ease;
}

.leave_item:last-child {
  border-bottom: none;
}

.leave_item:hover {
  background: #f8f9fa;
}

.leave_date {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.date_icon {
  color: #1890ff;
  font-size: 14px;
}

.leave_types {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.leave_tag {
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 请假类型标签颜色配置 */
.tag_compensatory {
  background-color: #f6ffed;
  border-color: #52c41a;
  color: #52c41a;
}

.tag_personal {
  background-color: #fff7e6;
  border-color: #fa8c16;
  color: #fa8c16;
}

.tag_sick {
  background-color: #fff2f0;
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.tag_other {
  background-color: #f9f0ff;
  border-color: #722ed1;
  color: #722ed1;
}

.tag_trip {
  background-color: #e6fffb;
  border-color: #13c2c2;
  color: #13c2c2;
}

/* 加班记录卡片 */
.overtime_card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}

.overtime_list {
  max-height: 200px;
  overflow-y: auto;
}

.overtime_item {
  padding: 12px;
  border-bottom: 1px solid #f5f5f5;
  transition: background 0.3s ease;
}

.overtime_item:last-child {
  border-bottom: none;
}

.overtime_item:hover {
  background: #f8f9fa;
}

.overtime_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.overtime_date {
  display: flex;
  align-items: center;
  gap: 8px;
}

.overtime_time {
  display: flex;
  gap: 16px;
}

.time_text {
  font-size: 12px;
  color: #666;
}

.overtime_types {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.overtime_tag {
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 加班类型标签颜色配置 */
.tag_weekday_overtime {
  background-color: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.tag_weekend_overtime {
  background-color: #fff7e6;
  border-color: #fa8c16;
  color: #fa8c16;
}

.tag_holiday_overtime {
  background-color: #fff2f0;
  border-color: #ff4d4f;
  color: #ff4d4f;
}

/* 无数据状态 */
.no_data {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

/* 关闭按钮 */
.close_btn {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  color: #666;
  border-radius: 6px;
  height: 36px;
  padding: 0 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.close_btn:hover {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: var(--breakpoint-lg)) {
  .detail_modal {
    width: 700px;
  }
}

@media (max-width: var(--breakpoint-md)) {
  .detail_modal {
    width: 95vw;
    margin: 0;
    max-width: 100vw;
  }

  .overtime_header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .overtime_time {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}

@media (max-width: var(--breakpoint-sm)) {
  .detail_modal {
    width: 100vw;
    height: 100vh;
    max-width: 100vw;
    max-height: 100vh;
  }
}

.btn {
    margin-left: 5px;
    display: inline-block;
    width: 40px;
    height: 22px;
    color: white;
    text-align: center;
    text-decoration: none;
    font-size: 13px;
    line-height: 22px;
    text-indent: 0px;
    border-radius: 5px;
    font-weight: bold;
}