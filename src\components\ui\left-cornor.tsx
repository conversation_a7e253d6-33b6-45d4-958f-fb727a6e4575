import React, { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useApplicationDispatch, useApplicationSelector } from '@/hook/hooks'
import { tabData, tabActions } from '@/slice/tabSlice'
import { setUserActions, loginData } from '@/slice/authSlice'
import { Button, message, Tooltip } from 'antd'
import { UserOutlined, IdcardOutlined, MailOutlined, LogoutOutlined } from '@ant-design/icons'
import { onLogout } from '@/utils/cookies'
import { performLogout } from '@/utils/logout'
import { useTranslation } from '@/hooks/useTranslation'
import LanguageSwitcher from '@/components/LanguageSwitcher'
import styles from '@/pages/components/css/left-cornor.module.css'

const LeftCornor: React.FC = () => {
  const [localStorageName, setLocalStorageName] = useState<string>('')
  const [localStorageDepart_name, setLocalStorageDepart_name] = useState<string>('')
  const [localStorageMail, setLocalStorageMail] = useState<string>('')
  const [localStorageWorkNo, setLocalStorageWorkNo] = useState<string>('')
  const [messageApi, contextHolder] = message.useMessage()
  const [expandedMenus, setExpandedMenus] = useState<Set<string>>(new Set(['profile']))

  const navigate = useNavigate()
  const location = useLocation()
  const { t } = useTranslation('common')
  
  const tabType = useApplicationSelector((state) => state.tab.tabType)
  const menuData = useApplicationSelector(tabData)
  const data = useApplicationSelector(loginData)
  const dispatch = useApplicationDispatch()

  // 初始化用户信息
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const loginDataStr = localStorage.getItem('login')
      if (loginDataStr) {
        try {
          const loginInfo = JSON.parse(loginDataStr)
          setLocalStorageName(loginInfo.name || '')
          setLocalStorageDepart_name(loginInfo.depart_name || '')
          setLocalStorageMail(loginInfo.mail || '')
          setLocalStorageWorkNo(loginInfo.work_no || '')
        } catch (error) {
          console.error('Failed to parse login data:', error)
        }
      }
    }
  }, [])



  // 处理登出
  const handleLogout = async () => {
    try {
      await performLogout()
      onLogout()
      navigate('/login', { replace: true })
      messageApi.success(t('logout.success', '已成功登出'))
    } catch (error) {
      console.error('Logout error:', error)
      messageApi.error(t('logout.error', '登出失败'))
    }
  }

  // 菜单项配置
  const menuItems = [
    {
      key: 'profile',
      title: t('menuItems.profile', '我的'),
      icon: '👤',
      iconColor: '#4F46E5',
      items: [
        { key: 'information', title: t('menuItems.userInfo', '个人信息'), path: '/my/information' },
      ]
    },
    {
      key: 'attendance',
      title: t('menuItems.attendance', '考勤功能'),
      icon: '📅',
      iconColor: '#059669',
      items: [
        { key: 'my-record', title: t('menuItems.myRecord', '查询'), path: '/attendance/my' },
      ]
    },
    {
      key: 'application',
      title: t('menuItems.application', '申请功能'),
      icon: '📝',
      iconColor: '#DC2626',
      items: [
        { key: 'transportation', title: t('menuItems.transportationExpense', '交通费申请'), path: '/application/transportationExpense' },
      ]
    },
    {
      key: 'approval',
      title: t('menuItems.approval', '审批承认'),
      icon: '✅',
      iconColor: '#7C3AED',
      items: [
        { key: 'transportation', title: t('menuItems.transportationApproval', '交通费个人审批'), path: '/approval/transportationExpense' },
      ]
    },
    {
      key: 'statistics',
      title: t('menuItems.statistics', '数据统计'),
      icon: '📊',
      iconColor: '#EA580C',
      items: [
        { key: 'department', title: t('menuItems.departmentStats', '部门统计'), path: '/statistics/department' },
      ]
    }
  ]

  const handleMenuClick = (path: string) => {
    navigate(path)
  }

  // 切换菜单展开/收起状态
  const toggleMenu = (menuKey: string) => {
    setExpandedMenus(prev => {
      const newSet = new Set(prev)
      if (newSet.has(menuKey)) {
        newSet.delete(menuKey)
      } else {
        newSet.add(menuKey)
      }
      return newSet
    })
  }



  return (
    <>
      {contextHolder}
      <div className={styles.sidebar_container}>
        <div className={styles.sidebar_main_content}>
          {/* 日系欢迎标题 */}
          <div className={styles.welcome_header}>
            <h2 className={styles.welcome_title}>おかえりなさい</h2>
            <p className={styles.welcome_subtitle}>欢迎回来</p>
          </div>

          {/* 用户信息 */}
          <div className={styles.user_message}>
            <div className={styles.user_info}>
              <UserOutlined className={styles.user_info_icon} />
              <span className={styles.user_info_title}>
                {localStorageName}({localStorageWorkNo})
              </span>
            </div>
            <div className={styles.user_info}>
              <IdcardOutlined className={styles.user_info_icon} />
              <span className={styles.user_info_title}>{localStorageDepart_name}</span>
            </div>
            <div className={styles.user_info}>
              <MailOutlined className={styles.user_info_icon} />
              <span className={styles.user_info_title}>{localStorageMail}</span>
            </div>
          </div>

          {/* 分隔线 */}
          <div className={styles.divider_section}>
            <div className={styles.divider_line}></div>
            <div className={styles.divider_text}>{t('menu', 'メニュー')}</div>
            <div className={styles.divider_line}></div>
          </div>

          {/* 菜单 */}
          <div className={styles.user_menu}>
            {menuItems.map((section) => {
              const isExpanded = expandedMenus.has(section.key)
              return (
                <div key={section.key} style={{ marginBottom: '8px' }}>
                  {/* 1级菜单标题 - 可点击展开/收起 */}
                  <div
                    style={{
                      fontSize: '14px',
                      fontWeight: 'bold',
                      color: '#fff',
                      padding: '12px 16px',
                      backgroundColor: 'rgba(255,255,255,0.1)',
                      borderRadius: '8px',
                      cursor: 'pointer',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      transition: 'all 0.3s ease',
                      border: '1px solid rgba(255,255,255,0.2)'
                    }}
                    onClick={() => toggleMenu(section.key)}
                  >
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      <span style={{
                        fontSize: '18px',
                        background: section.iconColor,
                        borderRadius: '6px',
                        padding: '4px 6px',
                        display: 'inline-block',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                        filter: 'brightness(1.1)'
                      }}>
                        {section.icon}
                      </span>
                      <span>{section.title}</span>
                    </div>
                    <span style={{
                      transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
                      transition: 'transform 0.3s ease',
                      fontSize: '12px'
                    }}>
                      ▶
                    </span>
                  </div>

                  {/* 2级菜单项 - 可折叠 */}
                  {isExpanded && (
                    <div style={{
                      marginTop: '8px',
                      paddingLeft: '16px',
                      borderLeft: '2px solid rgba(255,255,255,0.2)'
                    }}>
                      {section.items.map((item) => (
                        <Button
                          key={item.key}
                          type="text"
                          block
                          style={{
                            textAlign: 'left',
                            color: '#fff',
                            marginBottom: '4px',
                            backgroundColor: location.pathname === item.path ? 'rgba(255,255,255,0.2)' : 'transparent',
                            borderRadius: '6px',
                            fontSize: '13px',
                            height: '36px',
                            transition: 'all 0.3s ease'
                          }}
                          onClick={() => handleMenuClick(item.path)}
                        >
                          {item.title}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              )
            })}
          </div>

          {/* 底部区域 */}
          <div style={{ 
            marginTop: 'auto', 
            padding: '16px', 
            borderTop: '1px solid rgba(255,255,255,0.1)' 
          }}>
            {/* 语言切换器 */}
            <div style={{ marginBottom: '12px' }}>
              <LanguageSwitcher />
            </div>
            
            {/* 登出按钮 */}
            <Button
              type="primary"
              danger
              block
              icon={<LogoutOutlined />}
              onClick={handleLogout}
            >
              {t('buttons.logout', '登出')}
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}

export default LeftCornor
