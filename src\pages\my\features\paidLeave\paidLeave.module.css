/* 响应式有薪假期页面样式 */
.container {
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  min-height: 100vh;
  width: 100%;
  max-width: none; /* 移除最大宽度限制，确保完全显示 */
  box-sizing: border-box;
}

/* 响应式头部样式 */
.header {
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.title_icon {
  font-size: calc(var(--font-size-xl) + 4px);
  color: var(--color-primary);
}

/* 响应式加载和错误状态 */
.loading_container,
.error_container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  text-align: center;
}

/* 响应式概要统计区域 */
.summary_section {
  margin-bottom: var(--spacing-lg);
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.stat_card {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  padding: var(--spacing-lg);
  min-width: 200px; /* 确保卡片最小宽度 */
}

.stat_card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--color-primary);
}

/* 响应式进度卡片 */
.progress_card {
  margin-bottom: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  padding: var(--spacing-lg);
  width: 100%;
  overflow: hidden;
}

.progress_card h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.legal_reminder {
  margin-top: var(--spacing-lg);
}

/* 响应式进度条包装器 */
.progress_wrapper {
  position: relative;
  margin-bottom: var(--spacing-lg);
  width: 100%;
}

.progress_detail {
  text-align: center;
  margin-top: var(--spacing-sm);
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
}

/* 响应式表格卡片 */
.table_card {
  margin-bottom: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  overflow: hidden;
  width: 100%;
}

.table_card h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid var(--border-color);
  margin: 0;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .container {
    padding: var(--spacing-md);
  }

  .title {
    font-size: var(--font-size-lg);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .title_icon {
    font-size: var(--font-size-lg);
  }

  .stat_card {
    height: auto;
    min-height: 100px;
    padding: var(--spacing-md);
  }

  .progress_card,
  .table_card {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .progress_card h3,
  .table_card h3 {
    font-size: var(--font-size-md);
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .container {
    padding: var(--spacing-sm);
  }

  .title {
    font-size: var(--font-size-md);
  }

  .stat_card {
    padding: var(--spacing-sm);
    min-height: 80px;
  }

  .progress_card,
  .table_card {
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
  }

  .progress_card h3,
  .table_card h3 {
    font-size: var(--font-size-sm);
    padding: var(--spacing-sm);
  }
}

/* 响应式表格样式 - 确保列表项完全显示 */
.available_table,
.history_table {
  border-radius: var(--border-radius-md);
  overflow: hidden;
  width: 100%;
}

/* 表格容器 - 支持横向滚动 */
.table_container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary-light) var(--bg-secondary);
}

.table_container::-webkit-scrollbar {
  height: 8px;
}

.table_container::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 4px;
}

.table_container::-webkit-scrollbar-thumb {
  background: var(--color-primary-light);
  border-radius: 4px;
}

.table_container::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

.available_table {
  min-width: 800px; /* 减少最小宽度但保持可读性 */
}

.available_table :global(.ant-table-thead > tr > th),
.history_table :global(.ant-table-thead > tr > th) {
  background: var(--bg-gradient-header) !important;
  font-weight: 600;
  color: var(--text-primary) !important;
  border-bottom: 2px solid var(--border-color) !important;
  padding: var(--spacing-md) var(--spacing-lg) !important;
  font-size: var(--font-size-sm) !important;
}

.available_table :global(.ant-table-tbody > tr > td),
.history_table :global(.ant-table-tbody > tr > td) {
  padding: var(--spacing-md) var(--spacing-lg) !important;
  border-bottom: 1px solid var(--border-color-light) !important;
  font-size: var(--font-size-sm) !important;
  word-break: break-word;
}

.available_table :global(.ant-table-tbody > tr:hover > td),
.history_table :global(.ant-table-tbody > tr:hover > td) {
  background: var(--bg-hover) !important;
}

/* 响应式状态标签样式 */
.available_table :global(.ant-tag),
.history_table :global(.ant-tag) {
  border-radius: var(--border-radius-lg) !important;
  padding: var(--spacing-xs) var(--spacing-sm) !important;
  font-size: var(--font-size-xs) !important;
  font-weight: 500;
  border: none !important;
  white-space: nowrap;
}

/* 响应式统计卡片内的数字样式 */
.stat_card :global(.ant-statistic) {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat_card :global(.ant-statistic-content) {
  font-size: var(--font-size-xl) !important;
  font-weight: 700 !important;
  color: var(--text-primary) !important;
}

.stat_card :global(.ant-statistic-title) {
  font-size: var(--font-size-sm) !important;
  color: var(--text-secondary) !important;
  margin-bottom: var(--spacing-sm) !important;
}

/* 响应式卡片提示样式 */
.card_tip {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-top: auto;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
  border-left: 3px solid var(--color-primary);
  line-height: var(--line-height-relaxed);
  flex-shrink: 0;
}

/* 响应式卡片占位符样式 */
.card_placeholder {
  height: 27px;
  margin-top: auto;
  flex-shrink: 0;
}

/* 移动端表格优化 */
@media (max-width: 768px) {
  .available_table {
    min-width: 600px;
  }

  .available_table :global(.ant-table-thead > tr > th),
  .history_table :global(.ant-table-thead > tr > th) {
    padding: var(--spacing-sm) !important;
    font-size: var(--font-size-xs) !important;
  }

  .available_table :global(.ant-table-tbody > tr > td),
  .history_table :global(.ant-table-tbody > tr > td) {
    padding: var(--spacing-sm) !important;
    font-size: var(--font-size-xs) !important;
  }

  .stat_card :global(.ant-statistic-content) {
    font-size: var(--font-size-lg) !important;
  }

  .stat_card :global(.ant-statistic-title) {
    font-size: var(--font-size-xs) !important;
  }

  .card_tip {
    font-size: 10px;
    padding: var(--spacing-xs);
  }
}

@media (max-width: 480px) {
  .available_table {
    min-width: 400px;
  }

  .available_table :global(.ant-table-thead > tr > th),
  .history_table :global(.ant-table-thead > tr > th),
  .available_table :global(.ant-table-tbody > tr > td),
  .history_table :global(.ant-table-tbody > tr > td) {
    padding: var(--spacing-xs) !important;
    font-size: 10px !important;
  }

  .stat_card :global(.ant-statistic-content) {
    font-size: var(--font-size-md) !important;
  }
}

/* 响应式进度条样式 */
.progress_card :global(.ant-progress-text) {
  font-weight: 600 !important;
  color: var(--text-primary) !important;
  font-size: var(--font-size-sm) !important;
}

.progress_card :global(.ant-progress-bg) {
  border-radius: var(--border-radius-lg) !important;
  height: 12px !important;
}

.progress_card :global(.ant-progress-inner) {
  border-radius: var(--border-radius-lg) !important;
  background: var(--bg-secondary) !important;
}

/* 响应式警告框样式 */
.legal_reminder :global(.ant-alert) {
  border-radius: var(--border-radius-md) !important;
  border: none !important;
  box-shadow: var(--shadow-sm) !important;
  margin-bottom: var(--spacing-md) !important;
}

.legal_reminder :global(.ant-alert-success) {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%) !important;
  border-left: 4px solid var(--color-success) !important;
}

.legal_reminder :global(.ant-alert-warning) {
  background: linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%) !important;
  border-left: 4px solid var(--color-warning) !important;
}

.legal_reminder :global(.ant-alert-message) {
  font-size: var(--font-size-sm) !important;
  color: var(--text-primary) !important;
  font-weight: 600 !important;
}

.legal_reminder :global(.ant-alert-description) {
  font-size: var(--font-size-xs) !important;
  color: var(--text-secondary) !important;
  line-height: var(--line-height-relaxed) !important;
}

/* ========================================
   响应式工具类 - 确保列表项完全显示
   ======================================== */

/* 统计卡片网格容器 */
.stats_grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  width: 100%;
  margin-bottom: var(--spacing-lg);
}

/* 表格响应式容器 */
.responsive_table_wrapper {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
}

/* 移动端卡片视图 */
.mobile_card_view {
  display: none;
}

@media (max-width: 768px) {
  .stats_grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
  }

  .progress_card :global(.ant-progress-bg) {
    height: 10px !important;
  }

  .legal_reminder :global(.ant-alert-message) {
    font-size: var(--font-size-xs) !important;
  }

  .legal_reminder :global(.ant-alert-description) {
    font-size: 10px !important;
  }

  /* 移动端显示卡片视图 */
  .mobile_card_view {
    display: block;
  }

  .desktop_table_view {
    display: none;
  }
}

@media (max-width: 480px) {
  .stats_grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .progress_card :global(.ant-progress-bg) {
    height: 8px !important;
  }

  .progress_card :global(.ant-progress-text) {
    font-size: 10px !important;
  }
}

/* 深色主题适配 - 使用CSS变量 */
@media (prefers-color-scheme: dark) {
  .container {
    background: var(--bg-dark, #141414);
  }

  .stat_card,
  .progress_card,
  .table_card {
    background: var(--bg-dark-card, #1f1f1f);
    border: 1px solid var(--border-dark, #303030);
  }

  .title {
    color: var(--color-primary);
  }

  .progress_card h3,
  .table_card h3 {
    color: var(--text-dark, #fff);
  }
}

/* 打印样式优化 */
@media print {
  .container {
    background: white !important;
    padding: 0 !important;
    box-shadow: none !important;
  }

  .stat_card,
  .progress_card,
  .table_card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
    break-inside: avoid;
    background: white !important;
  }

  .title {
    color: #000 !important;
  }

  .progress_card h3,
  .table_card h3 {
    color: #000 !important;
  }

  /* 隐藏不必要的元素 */
  .legal_reminder {
    display: none !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .stat_card,
  .progress_card,
  .table_card {
    border: 2px solid var(--text-primary) !important;
  }

  .card_tip {
    border-left: 4px solid var(--color-primary) !important;
  }
}
