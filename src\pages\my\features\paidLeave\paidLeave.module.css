/* 容器样式 */
.container {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 头部样式 */
.header {
  margin-bottom: 24px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title_icon {
  font-size: 28px;
}

/* 加载和错误状态 */
.loading_container,
.error_container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 16px;
}

/* 概要统计区域 */
.summary_section {
  margin-bottom: 24px;
}

.stat_card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.stat_card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 进度卡片 */
.progress_card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress_card h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.legal_reminder {
  margin-top: 16px;
}

/* 进度条包装器 */
.progress_wrapper {
  position: relative;
  margin-bottom: 16px;
}

.progress_detail {
  text-align: center;
  margin-top: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* 表格卡片 */
.table_card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table_card h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

/* 表格样式 */
.available_table,
.history_table {
  border-radius: 6px;
  overflow: hidden;
}

.available_table {
  min-width: 900px;
}

.available_table :global(.ant-table-thead > tr > th),
.history_table :global(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e8e8e8;
}

.available_table :global(.ant-table-tbody > tr > td),
.history_table :global(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.available_table :global(.ant-table-tbody > tr:hover > td),
.history_table :global(.ant-table-tbody > tr:hover > td) {
  background: #f8f9fa;
}

/* 状态标签样式 */
.available_table :global(.ant-tag),
.history_table :global(.ant-tag) {
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  border: none;
}

/* 统计卡片内的数字样式 */
.stat_card :global(.ant-statistic) {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat_card :global(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 700;
}

.stat_card :global(.ant-statistic-title) {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

/* 卡片提示样式 */
.card_tip {
  font-size: 11px;
  color: #999;
  margin-top: auto;
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
  line-height: 1.3;
  flex-shrink: 0;
}

/* 卡片占位符样式 */
.card_placeholder {
  height: 27px;
  margin-top: auto;
  flex-shrink: 0;
}

/* 进度条样式 */
.progress_card :global(.ant-progress-text) {
  font-weight: 600;
  color: #333;
}

.progress_card :global(.ant-progress-bg) {
  border-radius: 10px;
}

.progress_card :global(.ant-progress-inner) {
  border-radius: 10px;
  background: #f0f0f0;
}

/* 警告框样式 */
.legal_reminder :global(.ant-alert) {
  border-radius: 6px;
  border: none;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.legal_reminder :global(.ant-alert-success) {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
  border-left: 4px solid #52c41a;
}

.legal_reminder :global(.ant-alert-warning) {
  background: linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%);
  border-left: 4px solid #faad14;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .title {
    font-size: 20px;
  }

  .stat_card {
    height: 110px;
  }

  .stat_card :global(.ant-statistic-content) {
    font-size: 20px;
  }

  .table_card {
    overflow-x: auto;
  }
}

@media (max-width: 576px) {
  .container {
    padding: 12px;
  }

  .title {
    font-size: 18px;
  }

  .stat_card {
    height: 100px;
  }

  .card_placeholder {
    height: 23px;
  }

  .summary_section {
    margin-bottom: 16px;
  }

  .progress_card,
  .table_card {
    margin-bottom: 16px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .container {
    background: #141414;
  }
  
  .stat_card,
  .progress_card,
  .table_card {
    background: #1f1f1f;
    border: 1px solid #303030;
  }
  
  .title {
    color: #1890ff;
  }
  
  .progress_card h3,
  .table_card h3 {
    color: #fff;
  }
}

/* 打印样式 */
@media print {
  .container {
    background: white;
    padding: 0;
  }
  
  .stat_card,
  .progress_card,
  .table_card {
    box-shadow: none;
    border: 1px solid #ddd;
    break-inside: avoid;
  }
  
  .title {
    color: #000;
  }
}
