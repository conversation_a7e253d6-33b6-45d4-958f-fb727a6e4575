import { getApi, postApi } from '@/api/fetch-api'
import ApiUrlVars from '@/api/common/url-vars'
import { statusActions } from '@/slice/statusSlice'

/**
 * 部门信息接口
 */
export interface DepartmentInfo {
  id: string
  name: string
  parentId?: string
  level: number
  description?: string
  managerId?: string
  managerName?: string
  memberCount?: number
  status: 'active' | 'inactive'
  createDate: string
}

/**
 * 部门参数接口
 */
export interface DepartmentParams {
  id?: string
  name: string
  parent_id?: string
  description?: string
  manager_id?: string
  status?: 'active' | 'inactive'
}

/**
 * 部门结构响应接口
 */
export interface StructureResponse {
  status: string
  message?: string
  data?: any
}

/**
 * 获取部门结构数据
 * @param dispatch Redux dispatch函数
 * @returns Promise<StructureResponse>
 */
export async function getDepartmentStructure(dispatch: any): Promise<StructureResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.structure_info_get
    const response = await getApi(url, {}, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取部门结构失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get department structure API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 新增部门
 * @param params 部门参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<StructureResponse>
 */
export async function addDepartment(params: DepartmentParams, dispatch: any): Promise<StructureResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.structure_departAdd_post
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '新增部门失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '新增部门成功'
    }
  } catch (error: any) {
    console.error('Add department API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 删除部门
 * @param departmentId 部门ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<StructureResponse>
 */
export async function deleteDepartment(departmentId: string, dispatch: any): Promise<StructureResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.structure_departDelete_post
    const params = { department_id: departmentId }
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '删除部门失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '删除部门成功'
    }
  } catch (error: any) {
    console.error('Delete department API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 修改部门数据
 * @param params 部门参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<StructureResponse>
 */
export async function changeDepartment(params: DepartmentParams, dispatch: any): Promise<StructureResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.structure_departChange_post
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '修改部门失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '修改部门成功'
    }
  } catch (error: any) {
    console.error('Change department API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取部门层级结构
 * @param dispatch Redux dispatch函数
 * @returns Promise<StructureResponse>
 */
export async function getDepartmentHierarchy(dispatch: any): Promise<StructureResponse> {
  try {
    const response = await getDepartmentStructure(dispatch)
    
    if (response.status === 'OK' && response.data) {
      // 处理部门数据，构建层级结构
      const departments = response.data.departments || response.data
      const hierarchy = buildDepartmentTree(departments)
      
      return {
        status: 'OK',
        data: {
          ...response.data,
          hierarchy
        }
      }
    }
    
    return response
  } catch (error: any) {
    console.error('Get department hierarchy API error:', error)
    return {
      status: 'NG',
      message: error.message || '获取部门层级结构失败'
    }
  }
}

/**
 * 构建部门树形结构
 * @param departments 部门列表
 * @returns 树形结构数据
 */
function buildDepartmentTree(departments: any[]): any[] {
  const departmentMap = new Map()
  const rootDepartments: any[] = []
  
  // 创建部门映射
  departments.forEach(dept => {
    departmentMap.set(dept.id, { ...dept, children: [] })
  })
  
  // 构建树形结构
  departments.forEach(dept => {
    const department = departmentMap.get(dept.id)
    
    if (dept.parent_id && departmentMap.has(dept.parent_id)) {
      // 有父部门，添加到父部门的children中
      const parent = departmentMap.get(dept.parent_id)
      parent.children.push(department)
    } else {
      // 没有父部门，是根部门
      rootDepartments.push(department)
    }
  })
  
  return rootDepartments
}

/**
 * 获取部门成员统计
 * @param departmentId 部门ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<StructureResponse>
 */
export async function getDepartmentMemberStats(departmentId: string, dispatch: any): Promise<StructureResponse> {
  try {
    // TODO: 实现部门成员统计API，暂时使用部门成员获取API
    const url = ApiUrlVars.api_domain + ApiUrlVars.departMember_get
    const params = { department_id: departmentId }
    const response = await getApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取部门成员统计失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get department member stats API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 移动部门位置
 * @param departmentId 部门ID
 * @param newParentId 新父部门ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<StructureResponse>
 */
export async function moveDepartment(
  departmentId: string, 
  newParentId: string | null, 
  dispatch: any
): Promise<StructureResponse> {
  try {
    const params: DepartmentParams = {
      id: departmentId,
      parent_id: newParentId || undefined,
      name: '' // 这里需要传入部门名称，实际使用时需要获取
    }
    
    return await changeDepartment(params, dispatch)
  } catch (error: any) {
    console.error('Move department API error:', error)
    return {
      status: 'NG',
      message: error.message || '移动部门失败'
    }
  }
}

/**
 * 批量操作部门
 * @param operations 操作列表
 * @param dispatch Redux dispatch函数
 * @returns Promise<StructureResponse>
 */
export async function batchDepartmentOperations(
  operations: Array<{
    action: 'add' | 'delete' | 'change'
    params: DepartmentParams
  }>, 
  dispatch: any
): Promise<StructureResponse> {
  try {
    const results = []
    
    for (const operation of operations) {
      let result: StructureResponse
      
      switch (operation.action) {
        case 'add':
          result = await addDepartment(operation.params, dispatch)
          break
        case 'delete':
          result = await deleteDepartment(operation.params.id!, dispatch)
          break
        case 'change':
          result = await changeDepartment(operation.params, dispatch)
          break
        default:
          result = { status: 'NG', message: '未知操作类型' }
      }
      
      results.push(result)
      
      // 如果有操作失败，停止后续操作
      if (result.status === 'NG') {
        break
      }
    }
    
    const failedOperations = results.filter(r => r.status === 'NG')
    
    if (failedOperations.length > 0) {
      return {
        status: 'NG',
        message: `批量操作失败，${failedOperations.length} 个操作未成功`,
        data: results
      }
    }
    
    return {
      status: 'OK',
      message: '批量操作成功',
      data: results
    }
  } catch (error: any) {
    console.error('Batch department operations API error:', error)
    return {
      status: 'NG',
      message: error.message || '批量操作失败'
    }
  }
}
