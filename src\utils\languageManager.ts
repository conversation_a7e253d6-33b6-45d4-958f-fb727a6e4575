/**
 * 全局语言管理器
 * 确保语言偏好在整个应用中保持一致
 */

type LanguageChangeListener = (language: string) => void;

class LanguageManager {
  private listeners: LanguageChangeListener[] = [];
  private currentLanguage: string = 'zh';

  constructor() {
    // 初始化时读取保存的语言偏好
    if (typeof window !== 'undefined') {
      this.currentLanguage = localStorage.getItem('preferred-language') || 'zh';
      console.log('LanguageManager: Initialized with language:', this.currentLanguage);
    }
  }

  /**
   * 获取当前语言
   */
  getCurrentLanguage(): string {
    return this.currentLanguage;
  }

  /**
   * 设置语言
   */
  setLanguage(language: string): void {
    if (this.currentLanguage !== language) {
      console.log('LanguageManager: Changing language from', this.currentLanguage, 'to', language);
      this.currentLanguage = language;
      
      // 保存到本地存储
      if (typeof window !== 'undefined') {
        localStorage.setItem('preferred-language', language);
      }
      
      // 通知所有监听器
      this.notifyListeners(language);
    }
  }

  /**
   * 添加语言变化监听器
   */
  addListener(listener: LanguageChangeListener): void {
    this.listeners.push(listener);
  }

  /**
   * 移除语言变化监听器
   */
  removeListener(listener: LanguageChangeListener): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(language: string): void {
    console.log('LanguageManager: Notifying', this.listeners.length, 'listeners of language change');
    this.listeners.forEach(listener => {
      try {
        listener(language);
      } catch (error) {
        console.error('LanguageManager: Error in listener:', error);
      }
    });
  }

  /**
   * 强制刷新所有监听器（用于初始化时）
   */
  refreshListeners(): void {
    this.notifyListeners(this.currentLanguage);
  }
}

// 创建全局实例
export const languageManager = new LanguageManager();

// 导出便捷函数
export const getCurrentLanguage = () => languageManager.getCurrentLanguage();
export const setGlobalLanguage = (language: string) => languageManager.setLanguage(language);
export const addLanguageListener = (listener: LanguageChangeListener) => languageManager.addListener(listener);
export const removeLanguageListener = (listener: LanguageChangeListener) => languageManager.removeListener(listener);
export const refreshLanguageListeners = () => languageManager.refreshListeners();
