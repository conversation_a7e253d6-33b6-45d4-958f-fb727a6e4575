/**
 * API测试文件 - 验证所有引用是否正确
 * 这个文件仅用于开发时验证，不会在生产环境中使用
 */

import { authApi } from './index';
import { LoginRequest, LoginResponse } from './types';
import { apiClient, ApiError } from '../client';
import { API_ENDPOINTS, HTTP_STATUS } from '../config';

/**
 * 测试基础API功能
 */
export async function testBasicApi() {
  console.log('🧪 测试基础API功能...');
  
  try {
    // 测试类型定义
    const loginParams: LoginRequest = {
      user_account: 'test_user',
      user_password: 'test_password'
    };
    
    console.log('✅ 类型定义正常');
    console.log('✅ API端点配置正常:', API_ENDPOINTS.AUTH.LOGIN);
    console.log('✅ HTTP状态码正常:', HTTP_STATUS.OK);
    console.log('✅ API客户端正常');
    
    return true;
  } catch (error) {
    console.error('❌ 基础API测试失败:', error);
    return false;
  }
}

/**
 * 测试认证API
 */
export async function testAuthApi() {
  console.log('🧪 测试认证API...');
  
  try {
    // 测试API对象存在
    console.log('✅ authApi对象:', Object.keys(authApi));
    
    // 测试函数存在
    console.log('✅ login函数:', typeof authApi.login);
    console.log('✅ loginWithStateManagement函数:', typeof authApi.loginWithStateManagement);
    console.log('✅ validateToken函数:', typeof authApi.validateToken);
    console.log('✅ logout函数:', typeof authApi.logout);
    console.log('✅ getCurrentUser函数:', typeof authApi.getCurrentUser);
    console.log('✅ checkLoginStatus函数:', typeof authApi.checkLoginStatus);
    console.log('✅ autoLogin函数:', typeof authApi.autoLogin);
    
    return true;
  } catch (error) {
    console.error('❌ 认证API测试失败:', error);
    return false;
  }
}

/**
 * 测试错误处理
 */
export async function testErrorHandling() {
  console.log('🧪 测试错误处理...');
  
  try {
    // 测试ApiError类
    const error = new ApiError('测试错误', HTTP_STATUS.BAD_REQUEST);
    console.log('✅ ApiError类正常:', error.message, error.status);
    
    return true;
  } catch (error) {
    console.error('❌ 错误处理测试失败:', error);
    return false;
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('🚀 开始API引用测试...\n');
  
  const results = await Promise.all([
    testBasicApi(),
    testAuthApi(),
    testErrorHandling()
  ]);
  
  const allPassed = results.every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 所有API引用测试通过！');
    console.log('✅ 类型定义正确');
    console.log('✅ 函数导出正确');
    console.log('✅ 配置引用正确');
    console.log('✅ 错误处理正确');
  } else {
    console.log('\n❌ 部分测试失败，请检查API引用');
  }
  
  return allPassed;
}

// 开发环境自动运行测试
if (import.meta.env.MODE === 'development') {
  // 延迟执行，避免影响应用启动
  setTimeout(() => {
    runAllTests().catch(console.error);
  }, 1000);
}
