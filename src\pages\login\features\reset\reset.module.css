/* 日系风格重置密码页面样式 */

/* 主容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

/* 装饰元素1 */
.decoration1 {
  position: absolute;
  top: 10%;
  left: 10%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(52, 152, 219, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 8s ease-in-out infinite;
  z-index: 1;
}

/* 装饰元素2 */
.decoration2 {
  position: absolute;
  bottom: 15%;
  right: 15%;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(155, 89, 182, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 10s ease-in-out infinite reverse;
  z-index: 1;
}

/* 重置密码卡片 */
.card {
  width: 100%;
  max-width: 420px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(139, 125, 107, 0.15), 0 8px 16px rgba(139, 125, 107, 0.1);
  border: 1px solid rgba(212, 197, 169, 0.3);
  overflow: hidden;
  z-index: 10;
}

/* 卡片头部 */
.header {
  background: linear-gradient(135deg, #8b7d6b 0%, #a0927d 50%, #b5a68f 100%);
  padding: 32px 32px 24px;
  text-align: center;
  color: white;
}

/* 图标容器 */
.iconContainer {
  width: 64px;
  height: 64px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  font-size: 24px;
}

/* 标题 */
.title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

/* 副标题 */
.subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

/* 表单容器 */
.formContainer {
  padding: 32px;
}

/* 输入框样式 */
.input {
  border-radius: 8px !important;
  border: 1px solid #d9d9d9 !important;
  transition: all 0.3s ease !important;
}

/* 发送按钮 */
.sendButton {
  height: 48px !important;
  border-radius: 8px !important;
  background: linear-gradient(135deg, #8b7d6b 0%, #a0927d 50%, #b5a68f 100%) !important;
  border: none !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 12px rgba(139, 125, 107, 0.4) !important;
  transition: all 0.3s ease !important;
}

.sendButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(139, 125, 107, 0.5) !important;
}

/* 返回按钮 */
.backButton {
  color: #8b7d6b !important;
  font-size: 14px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
}

.backButton:hover {
  color: #a0927d !important;
}

/* 语言切换器位置 */
.languageSwitcher {
  position: absolute;
  top: 24px;
  right: 24px;
  z-index: 1000;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }
  
  .card {
    max-width: 90%;
  }
  
  .decoration1,
  .decoration2 {
    display: none;
  }
  
  .languageSwitcher {
    top: 16px;
    right: 16px;
  }
}
