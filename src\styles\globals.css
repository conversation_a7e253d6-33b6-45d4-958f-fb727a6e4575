/* 全局CSS变量定义 - 统一响应式布局系统 */
:root {
  /* 响应式断点变量 - 基于现代设备尺寸优化 */
  --breakpoint-xs: 320px;   /* 小屏手机 */
  --breakpoint-sm: 480px;   /* 大屏手机 */
  --breakpoint-md: 768px;   /* 平板竖屏 */
  --breakpoint-lg: 1024px;  /* 平板横屏/小笔记本 */
  --breakpoint-xl: 1200px;  /* 桌面显示器 */
  --breakpoint-xxl: 1600px; /* 大屏显示器 */

  /* 容器最大宽度 */
  --container-xs: 100%;
  --container-sm: 100%;
  --container-md: 100%;
  --container-lg: 1024px;
  --container-xl: 1200px;
  --container-xxl: 1400px;

  /* 网格系统 */
  --grid-columns: 12;
  --grid-gutter: 16px;
  --grid-gutter-sm: 8px;
  --grid-gutter-lg: 24px;

  /* 标准间距变量 - 扩展移动端适配 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
  --spacing-xxl: 32px;
  --spacing-mobile: 12px;   /* 移动端标准间距 */
  --spacing-desktop: 24px;  /* 桌面端标准间距 */

  /* 标准圆角变量 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;

  /* 标准阴影变量 - 日系风格 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.12);

  /* 主题颜色变量 - 日系蓝白渐变 */
  --color-primary: #1890ff;
  --color-primary-light: #40a9ff;
  --color-primary-dark: #096dd9;
  --color-success: #52c41a;
  --color-warning: #fa8c16;
  --color-error: #ff4d4f;
  --color-info: #13c2c2;
  --color-purple: #722ed1;

  /* 背景颜色变量 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #f5f5f5;
  --bg-dark: #2c3e50;
  --bg-gradient-primary: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  --bg-gradient-secondary: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);

  /* 文本颜色变量 */
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --text-light: #a0aec0;
  --text-white: #ffffff;

  /* 边框颜色变量 */
  --border-color: #e2e8f0;
  --border-color-light: #f0f0f0;
  --border-color-dark: #d1d5db;

  /* 字体大小变量 - 响应式字体 */
  --font-size-xs: 11px;
  --font-size-sm: 13px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-xxl: 20px;

  /* 行高变量 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.8;

  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* 全局样式 */
html, body {
  font-family: "Helvetica", "Microsoft YaHei", "Helvetica Rounded", Arial, sans-serif;
  margin: 0;
  padding: 0;
  height: 100%;
  background-color: var(--bg-tertiary);
  font-size: var(--font-size-md);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
}

#root {
  height: 100%;
}

* {
  box-sizing: border-box;
}

/* Antd 样式覆盖 */
.ant-layout {
  min-height: 100vh;
}

.ant-layout-sider {
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
}

.ant-menu {
  background: transparent;
  border: none;
}

.ant-menu-item {
  color: #fff !important;
}

.ant-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.ant-menu-item-selected {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
}

/* 按钮样式 */
.ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 卡片样式 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 表格样式 */
.ant-table-thead > tr > th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  font-weight: 600;
}

/* 分页样式 - 日系设计风格，统一蓝白渐变 */
.ant-pagination-item {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s ease;
  margin: 0 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ant-pagination-item:hover {
  border-color: #40a9ff;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.15);
}

.ant-pagination-item a {
  color: #4a5568;
  font-weight: 500;
  transition: color 0.3s ease;
}

.ant-pagination-item:hover a {
  color: #1890ff;
}

/* 当前选中页样式 - 蓝白渐变替代紫蓝色 */
.ant-pagination-item-active {
  border-color: #1890ff !important;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25);
  transform: translateY(-1px);
}

.ant-pagination-item-active a {
  color: #ffffff !important;
  font-weight: 600;
}

.ant-pagination-item-active:hover {
  border-color: #1890ff !important;
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.3);
}

/* 上一页/下一页按钮样式 */
.ant-pagination-prev,
.ant-pagination-next {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s ease;
  margin: 0 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ant-pagination-prev:hover,
.ant-pagination-next:hover {
  border-color: #40a9ff;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.15);
}

.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link {
  color: #4a5568;
  transition: color 0.3s ease;
}

.ant-pagination-prev:hover .ant-pagination-item-link,
.ant-pagination-next:hover .ant-pagination-item-link {
  color: #1890ff;
}

/* 禁用状态样式 */
.ant-pagination-disabled {
  border-color: #f0f0f0 !important;
  background: #fafafa !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

.ant-pagination-disabled .ant-pagination-item-link {
  color: #bfbfbf !important;
}

/* 快速跳转输入框样式 */
.ant-pagination-options-quick-jumper input {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ant-pagination-options-quick-jumper input:focus {
  border-color: #40a9ff;
  background: #ffffff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

/* 总数显示样式 */
.ant-pagination-total-text {
  color: #4a5568;
  font-weight: 500;
  margin-right: 16px;
}

/* 导入响应式工具类 */
@import './responsive-utilities.css';

/* 响应式表格全局样式 - 确保列表项完全显示 */
:global(.ant-table-wrapper) {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

:global(.ant-table) {
  min-width: 800px;
}

:global(.ant-table-tbody > tr > td) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

/* 移动端表格优化 - iOS风格列表 */
@media (max-width: 768px) {
  /* 隐藏表头 */
  :global(.ant-table-thead) {
    display: none !important;
  }

  /* 表格容器优化 */
  :global(.ant-table-wrapper) {
    overflow-x: visible;
  }

  :global(.ant-table) {
    min-width: 100%;
    border: none;
  }

  /* 表格行转换为卡片样式 - iOS风格 */
  :global(.ant-table-tbody > tr) {
    display: block !important;
    background: var(--bg-primary) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: var(--border-radius-md) !important;
    margin-bottom: var(--spacing-md) !important;
    padding: var(--spacing-md) !important;
    box-shadow: var(--shadow-sm) !important;
    transition: var(--transition-normal) !important;
  }

  :global(.ant-table-tbody > tr:hover) {
    box-shadow: var(--shadow-md) !important;
    transform: translateY(-1px) !important;
  }

  /* 表格单元格转换为块级元素 */
  :global(.ant-table-tbody > tr > td) {
    display: block !important;
    border: none !important;
    padding: var(--spacing-xs) 0 !important;
    white-space: normal !important;
    max-width: none !important;
    overflow: visible !important;
    text-overflow: unset !important;
    position: relative !important;
    padding-left: 120px !important;
    min-height: 24px !important;
    line-height: 1.5 !important;
  }

  /* 为每个单元格添加标签 */
  :global(.ant-table-tbody > tr > td::before) {
    content: attr(data-label) !important;
    position: absolute !important;
    left: 0 !important;
    top: var(--spacing-xs) !important;
    width: 110px !important;
    font-weight: 600 !important;
    color: var(--text-secondary) !important;
    font-size: var(--font-size-xs) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
  }

  /* 隐藏空单元格 */
  :global(.ant-table-tbody > tr > td:empty) {
    display: none !important;
  }
}

/* 响应式分页样式优化 - 移动端保留 */
:global(.ant-pagination) {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin: var(--spacing-lg) 0 !important;
  padding: var(--spacing-md) !important;
  background: var(--bg-primary) !important;
  border-radius: var(--border-radius-md) !important;
  box-shadow: var(--shadow-sm) !important;
  flex-wrap: wrap !important;
  gap: var(--spacing-xs) !important;
}

/* 移动端分页优化 */
@media (max-width: 768px) {
  :global(.ant-pagination) {
    padding: var(--spacing-sm) !important;
    margin: var(--spacing-md) 0 !important;
  }

  :global(.ant-pagination-item),
  :global(.ant-pagination-prev),
  :global(.ant-pagination-next) {
    min-width: 40px !important;
    height: 40px !important;
    line-height: 38px !important;
    margin: 0 2px !important;
  }

  /* 隐藏页码范围显示，只保留核心分页功能 */
  :global(.ant-pagination-total-text) {
    display: none !important;
  }

  :global(.ant-pagination-options) {
    display: none !important;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed;
    height: 100vh;
    z-index: 1000;
  }

  .ant-layout-content {
    margin-left: 0;
  }
}
