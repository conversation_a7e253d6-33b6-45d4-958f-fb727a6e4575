/* 全局样式 */
html, body {
  font-family: "Helvetica", "Microsoft YaHei", "Helvetica Rounded", Arial, sans-serif;
  margin: 0;
  padding: 0;
  height: 100%;
  background-color: #f5f5f5;
}

#root {
  height: 100%;
}

* {
  box-sizing: border-box;
}

/* Antd 样式覆盖 */
.ant-layout {
  min-height: 100vh;
}

.ant-layout-sider {
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
}

.ant-menu {
  background: transparent;
  border: none;
}

.ant-menu-item {
  color: #fff !important;
}

.ant-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.ant-menu-item-selected {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
}

/* 按钮样式 */
.ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 卡片样式 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 表格样式 */
.ant-table-thead > tr > th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  font-weight: 600;
}

/* 分页样式 - 日系设计风格，统一蓝白渐变 */
.ant-pagination-item {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s ease;
  margin: 0 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ant-pagination-item:hover {
  border-color: #40a9ff;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.15);
}

.ant-pagination-item a {
  color: #4a5568;
  font-weight: 500;
  transition: color 0.3s ease;
}

.ant-pagination-item:hover a {
  color: #1890ff;
}

/* 当前选中页样式 - 蓝白渐变替代紫蓝色 */
.ant-pagination-item-active {
  border-color: #1890ff !important;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25);
  transform: translateY(-1px);
}

.ant-pagination-item-active a {
  color: #ffffff !important;
  font-weight: 600;
}

.ant-pagination-item-active:hover {
  border-color: #1890ff !important;
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.3);
}

/* 上一页/下一页按钮样式 */
.ant-pagination-prev,
.ant-pagination-next {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s ease;
  margin: 0 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ant-pagination-prev:hover,
.ant-pagination-next:hover {
  border-color: #40a9ff;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.15);
}

.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link {
  color: #4a5568;
  transition: color 0.3s ease;
}

.ant-pagination-prev:hover .ant-pagination-item-link,
.ant-pagination-next:hover .ant-pagination-item-link {
  color: #1890ff;
}

/* 禁用状态样式 */
.ant-pagination-disabled {
  border-color: #f0f0f0 !important;
  background: #fafafa !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

.ant-pagination-disabled .ant-pagination-item-link {
  color: #bfbfbf !important;
}

/* 快速跳转输入框样式 */
.ant-pagination-options-quick-jumper input {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ant-pagination-options-quick-jumper input:focus {
  border-color: #40a9ff;
  background: #ffffff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

/* 总数显示样式 */
.ant-pagination-total-text {
  color: #4a5568;
  font-weight: 500;
  margin-right: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed;
    height: 100vh;
    z-index: 1000;
  }
  
  .ant-layout-content {
    margin-left: 0;
  }
}
