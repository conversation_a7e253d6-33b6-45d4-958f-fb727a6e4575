import React, { useState } from 'react';
import { X, Calendar, Clock, User, Phone, Briefcase, FileText, ChevronDown } from 'lucide-react';

interface LeaveApplicationDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const LeaveApplicationDialog: React.FC<LeaveApplicationDialogProps> = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState({
    leaveType: '',
    specialLeaveSubtype: '',
    startDate: '',
    endDate: '',
    relatedWorkDate: '', // 振替休日和代休的关联工作日期
    startTime: '09:00',
    endTime: '18:00',
    isHalfDay: false,
    halfDayPeriod: 'morning',
    reason: '',
    emergencyContact: '',
    emergencyPhone: '',
    workHandover: '',
    additionalNotes: ''
  });

  const leaveTypes = [
    { value: '', label: '休暇種類を選択してください (Please select leave type)', color: 'text-gray-500' },
    { value: 'annual', label: '年次有給休暇 (Annual Leave)', color: 'text-blue-600' },
    { value: 'compensatory', label: '振替休日 (Compensatory Day Off)', color: 'text-orange-600', needsRelatedDate: true, noHalfDay: true },
    { value: 'substitute', label: '代休 (Substitute Holiday)', color: 'text-amber-600', needsRelatedDate: true, noHalfDay: true },
    { value: 'summer', label: '夏季休暇 (Summer Holiday)', color: 'text-cyan-600' },
    { value: 'leave_absence', label: '休職 (Leave of Absence)', color: 'text-gray-600' },
    { value: 'special', label: '特別休暇 (Special Leave)', color: 'text-purple-600', hasSubtypes: true }
  ];

  const specialLeaveSubtypes = [
    { value: '', label: '特別休暇の種類を選択してください (Please select special leave type)', color: 'text-gray-500' },
    { value: 'maternity', label: '産前産後休暇 (Maternity Leave)' },
    { value: 'childcare', label: '育児休業 (Childcare Leave)' },
    { value: 'nursing_care', label: '介護休業 (Nursing Care Leave)' },
    { value: 'bereavement', label: '忌引休暇 (Bereavement Leave)' },
    { value: 'marriage', label: '結婚休暇 (Marriage Leave)' },
    { value: 'moving', label: '転居休暇 (Moving Leave)' },
    { value: 'volunteer', label: 'ボランティア休暇 (Volunteer Leave)' },
    { value: 'disaster', label: '災害休暇 (Disaster Leave)' },
    { value: 'court', label: '裁判員休暇 (Jury Duty Leave)' },
    { value: 'other', label: 'その他 (Other)' }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Leave application submitted:', formData);
    onClose();
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Reset special leave subtype and related date when changing main leave type
    if (field === 'leaveType') {
      setFormData(prev => ({
        ...prev,
        specialLeaveSubtype: '',
        relatedWorkDate: '',
        isHalfDay: false // Reset half day when changing leave type
      }));
    }
  };

  const selectedLeaveType = leaveTypes.find(type => type.value === formData.leaveType);
  const needsRelatedDate = selectedLeaveType?.needsRelatedDate;
  const hasSubtypes = selectedLeaveType?.hasSubtypes;
  const noHalfDay = selectedLeaveType?.noHalfDay; // 振替休日和代休不支持半日休假
  const canSelectTime = selectedLeaveType?.noHalfDay; // 振替休日和代休可以选择时刻

  const getRelatedDateLabel = () => {
    if (formData.leaveType === 'compensatory') {
      return '振替対象日 (Original Work Date)';
    } else if (formData.leaveType === 'substitute') {
      return '代休対象日 (Overtime Work Date)';
    }
    return '';
  };

  const getRelatedDatePlaceholder = () => {
    if (formData.leaveType === 'compensatory') {
      return '振替元の勤務日を選択してください';
    } else if (formData.leaveType === 'substitute') {
      return '残業・休日出勤した日を選択してください';
    }
    return '';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                <Calendar className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-800">休暇申請書</h2>
                <p className="text-sm text-gray-600">Leave Application Form</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="w-8 h-8 rounded-full bg-white shadow-md flex items-center justify-center hover:bg-gray-50 transition-colors"
            >
              <X className="w-4 h-4 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Form Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-200px)]">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Leave Type Selection - Dropdown */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-gray-700 flex items-center space-x-2">
                <FileText className="w-4 h-4" />
                <span>休暇種類 (Leave Type) *</span>
              </label>
              <div className="relative">
                <select
                  value={formData.leaveType}
                  onChange={(e) => handleInputChange('leaveType', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white pr-10"
                  required
                >
                  {leaveTypes.map((type) => (
                    <option key={type.value} value={type.value} disabled={type.value === ''}>
                      {type.label}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
              </div>
              
              {/* Display selected leave type with color */}
              {formData.leaveType && formData.leaveType !== '' && (
                <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg">
                  <div className={`w-3 h-3 rounded-full ${selectedLeaveType?.color?.replace('text-', 'bg-')}`}></div>
                  <span className={`text-sm font-medium ${selectedLeaveType?.color}`}>
                    {selectedLeaveType?.label}
                  </span>
                </div>
              )}
            </div>

            {/* Special Leave Subtype Dropdown */}
            {hasSubtypes && formData.leaveType === 'special' && (
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  特別休暇の詳細 (Special Leave Details) *
                </label>
                <div className="relative">
                  <select
                    value={formData.specialLeaveSubtype}
                    onChange={(e) => handleInputChange('specialLeaveSubtype', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent appearance-none bg-white pr-10"
                    required
                  >
                    {specialLeaveSubtypes.map((subtype) => (
                      <option key={subtype.value} value={subtype.value} disabled={subtype.value === ''}>
                        {subtype.label}
                      </option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                </div>
              </div>
            )}

            {/* Related Work Date for Compensatory/Substitute Leave */}
            {needsRelatedDate && (
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span>{getRelatedDateLabel()} *</span>
                </label>
                <input
                  type="date"
                  value={formData.relatedWorkDate}
                  onChange={(e) => handleInputChange('relatedWorkDate', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={getRelatedDatePlaceholder()}
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  {getRelatedDatePlaceholder()}
                </p>
              </div>
            )}

            {/* Date Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span>開始日 (Start Date) *</span>
                </label>
                <input
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span>終了日 (End Date) *</span>
                </label>
                <input
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => handleInputChange('endDate', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
            </div>

            {/* Half Day Option - Only for non-compensatory/substitute leave */}
            {!noHalfDay && (
              <div className="space-y-3">
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.isHalfDay}
                    onChange={(e) => handleInputChange('isHalfDay', e.target.checked)}
                    className="w-4 h-4 text-blue-600 focus:ring-blue-500 rounded"
                  />
                  <span className="text-sm font-medium text-gray-700">半日休暇 (Half Day Leave)</span>
                </label>
                
                {formData.isHalfDay && (
                  <div className="ml-7 space-y-2">
                    <div className="flex space-x-4">
                      <label className="flex items-center space-x-2">
                        <input
                          type="radio"
                          name="halfDayPeriod"
                          value="morning"
                          checked={formData.halfDayPeriod === 'morning'}
                          onChange={(e) => handleInputChange('halfDayPeriod', e.target.value)}
                          className="w-4 h-4 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700">午前 (Morning)</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input
                          type="radio"
                          name="halfDayPeriod"
                          value="afternoon"
                          checked={formData.halfDayPeriod === 'afternoon'}
                          onChange={(e) => handleInputChange('halfDayPeriod', e.target.value)}
                          className="w-4 h-4 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700">午後 (Afternoon)</span>
                      </label>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Time Selection - For Half Day or Compensatory/Substitute Leave */}
            {(formData.isHalfDay || canSelectTime) && (
              <div className="space-y-3">
                <div className="flex items-center space-x-2 mb-2">
                  <Clock className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-700">
                    {canSelectTime ? '時刻指定 (Time Selection)' : '時刻 (Time)'}
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      開始時刻 (Start Time)
                    </label>
                    <input
                      type="time"
                      value={formData.startTime}
                      onChange={(e) => handleInputChange('startTime', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      終了時刻 (End Time)
                    </label>
                    <input
                      type="time"
                      value={formData.endTime}
                      onChange={(e) => handleInputChange('endTime', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
                {canSelectTime && (
                  <p className="text-xs text-gray-500 mt-1">
                    振替休日・代休では具体的な時刻を指定できます (You can specify exact times for compensatory day off and substitute holiday)
                  </p>
                )}
              </div>
            )}

            {/* Reason */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 flex items-center space-x-2">
                <FileText className="w-4 h-4" />
                <span>理由 (Reason) *</span>
              </label>
              <textarea
                value={formData.reason}
                onChange={(e) => handleInputChange('reason', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                placeholder="休暇の理由を記入してください (Please enter the reason for your leave)"
                required
              />
            </div>

            {/* Emergency Contact */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 flex items-center space-x-2">
                  <User className="w-4 h-4" />
                  <span>緊急連絡先 (Emergency Contact)</span>
                </label>
                <input
                  type="text"
                  value={formData.emergencyContact}
                  onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="氏名 (Name)"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 flex items-center space-x-2">
                  <Phone className="w-4 h-4" />
                  <span>電話番号 (Phone Number)</span>
                </label>
                <input
                  type="tel"
                  value={formData.emergencyPhone}
                  onChange={(e) => handleInputChange('emergencyPhone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="090-1234-5678"
                />
              </div>
            </div>

            {/* Work Handover */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 flex items-center space-x-2">
                <Briefcase className="w-4 h-4" />
                <span>業務引継ぎ (Work Handover)</span>
              </label>
              <textarea
                value={formData.workHandover}
                onChange={(e) => handleInputChange('workHandover', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                placeholder="休暇中の業務引継ぎについて記入してください (Please describe work handover arrangements during your leave)"
              />
            </div>

            {/* Additional Notes */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                備考 (Additional Notes)
              </label>
              <textarea
                value={formData.additionalNotes}
                onChange={(e) => handleInputChange('additionalNotes', e.target.value)}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                placeholder="その他ご連絡事項があれば記入してください (Please add any other relevant information)"
              />
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-100">
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              キャンセル (Cancel)
            </button>
            <button
              type="submit"
              form="leave-form"
              onClick={handleSubmit}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm"
            >
              申請する (Submit Application)
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeaveApplicationDialog;