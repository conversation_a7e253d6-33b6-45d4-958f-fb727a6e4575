import React from 'react'
import ReactD<PERSON> from 'react-dom/client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { ConfigProvider } from 'antd'
import App from './App'
import ReduxProvider from './components/ReduxProvider'
import ErrorBoundary from './components/ErrorBoundary'
import I18nProvider from './components/I18nProvider'
import './styles/globals.css'
import './styles/layout-fixes.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ErrorBoundary>
      <I18nProvider>
        <ReduxProvider>
          <ConfigProvider>
            <BrowserRouter>
              <App />
            </BrowserRouter>
          </ConfigProvider>
        </ReduxProvider>
      </I18nProvider>
    </ErrorBoundary>
  </React.StrictMode>
)
