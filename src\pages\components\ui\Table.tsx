import React from 'react';
import { Pagination } from 'antd';
import styles from '../css/table.module.css';
import { useTranslation } from '@/hooks/useTranslation';

// 表格列定义接口
export interface TableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  render?: (value: any, record: any, index: number) => React.ReactNode;
  ellipsis?: boolean;
}

// 表格属性接口
export interface TableProps {
  columns: TableColumn[];
  dataSource: any[];
  loading?: boolean;
  empty?: {
    icon?: React.ReactNode;
    text?: string;
    description?: string;
  };
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize?: number) => void;
    simple?: boolean;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
    pageSizeOptions?: string[];
    showTotal?: (total: number, range: [number, number]) => string;
    size?: "small" | "default";
  };
  onRowClick?: (record: any, index: number) => void;
  expandedRowKeys?: string[];
  expandedRowRender?: (record: any) => React.ReactNode;
  rowClassName?: (record: any, index: number) => string;
  className?: string;
  style?: React.CSSProperties;
}

// 状态标签组件
export const StatusTag: React.FC<{
  status: 'pending' | 'approved' | 'rejected' | 'processing';
  children: React.ReactNode;
}> = ({ status, children }) => {
  return (
    <span className={`${styles.table_status_tag} ${styles[`table_status_${status}`]}`}>
      {children}
    </span>
  );
};

// 操作按钮组件
export const ActionButton: React.FC<{
  type?: 'primary' | 'secondary' | 'danger' | 'warning';
  onClick?: (e?: React.MouseEvent) => void;
  children: React.ReactNode;
  disabled?: boolean;
}> = ({ type = 'primary', onClick, children, disabled = false }) => {
  return (
    <button
      className={`${styles.table_action_btn} ${styles[`table_action_btn_${type}`]}`}
      onClick={(e) => {
        e.stopPropagation(); // 阻止事件冒泡
        onClick?.(e);
      }}
      disabled={disabled}
      style={{ opacity: disabled ? 0.5 : 1, cursor: disabled ? 'not-allowed' : 'pointer' }}
    >
      {children}
    </button>
  );
};

// 主表格组件
const Table: React.FC<TableProps> = ({
  columns,
  dataSource,
  loading = false,
  empty,
  pagination,
  onRowClick,
  expandedRowKeys = [],
  expandedRowRender,
  rowClassName,
  className = '',
  style = {}
}) => {
  const { t } = useTranslation(['common']);

  // 设置默认的空数据配置
  const defaultEmpty = {
    text: t('table.noData', '暂无数据'),
    description: t('table.noDisplayData', '当前没有可显示的数据')
  };

  const emptyConfig = empty || defaultEmpty;
  // 渲染表格头部
  const renderHeader = () => (
    <div className={styles.table_header}>
      <div className={styles.table_header_row}>
        {columns?.map((column) => (
          <div
            key={column.key}
            className={styles.table_header_cell}
            style={{ 
              width: column.width,
              flex: column.width ? 'none' : 1
            }}
          >
            <span className={styles.table_header_text}>
              {column.title}
            </span>
          </div>
        ))}
      </div>
    </div>
  );

  // 渲染表格内容
  const renderContent = () => {
    if (loading) {
      return (
        <div className={styles.table_loading}>
          <div>{t('messages.loading', '加载中...')}</div>
        </div>
      );
    }

    if (!dataSource || dataSource.length === 0) {
      return (
        <div className={styles.table_empty}>
          {emptyConfig.icon && <div className={styles.table_empty_icon}>{emptyConfig.icon}</div>}
          <div className={styles.table_empty_text}>{emptyConfig.text}</div>
          {emptyConfig.description && (
            <div className={styles.table_empty_description}>{emptyConfig.description}</div>
          )}
        </div>
      );
    }

    return (
      <div className={styles.table_content}>
        {dataSource.map((record, index) => {
          const isExpanded = expandedRowKeys.includes(record.key || index.toString());
          const customRowClassName = rowClassName ? rowClassName(record, index) : '';
          // 添加默认的奇偶行样式
          const defaultRowClassName = index % 2 === 0 ? 'table_row_even' : 'table_row_odd';

          return (
            <React.Fragment key={record.key || index}>
              <div
                className={`${styles.table_row} ${isExpanded ? styles.table_row_expanded : ''} ${defaultRowClassName} ${customRowClassName}`}
                onClick={() => onRowClick?.(record, index)}
              >
                {columns.map((column) => {
                  const value = record[column.dataIndex];
                  const cellContent = column.render
                    ? column.render(value, record, index)
                    : value;

                  return (
                    <div
                      key={column.key}
                      className={`${styles.table_cell} ${
                        column.align === 'left' ? styles.table_cell_left :
                        column.align === 'right' ? styles.table_cell_right :
                        styles.table_cell_center
                      }`}
                      style={{
                        width: column.width,
                        flex: column.width ? 'none' : 1
                      }}
                    >
                      <div className={`${styles.table_cell_content} ${
                        column.ellipsis ? '' : styles.table_cell_multiline
                      }`}>
                        {cellContent}
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* 展开行内容 */}
              {isExpanded && expandedRowRender && (
                <div className={styles.table_expanded_row}>
                  {expandedRowRender(record)}
                </div>
              )}
            </React.Fragment>
          );
        })}
      </div>
    );
  };

  // 渲染分页
  const renderPagination = () => {
    if (!pagination) return null;

    return (
      <div className={styles.table_pagination}>
        <Pagination
          current={pagination.current}
          pageSize={pagination.pageSize}
          total={pagination.total}
          onChange={pagination.onChange}
          simple={pagination.simple}
          showSizeChanger={pagination.showSizeChanger || false}
          showQuickJumper={pagination.showQuickJumper || false}
          pageSizeOptions={pagination.pageSizeOptions}
          showTotal={pagination.showTotal}
          size={pagination.size || "small"}
        />
      </div>
    );
  };

  return (
    <div
      className={`${styles.table_container} ${className}`}
      style={style}
    >
      {/* 表格主体容器 */}
      <div className={styles.table_main_container}>
        {/* 固定表头容器 */}
        <div className={styles.fixed_header_container}>
          {renderHeader()}
        </div>

        {/* 表格滚动区域 */}
        <div className={styles.table_body_container}>
          <div className={styles.table_content_wrapper}>
            {renderContent()}
          </div>
        </div>
      </div>

      {/* 固定分页区域 */}
      {renderPagination()}
    </div>
  );
};

export default Table;
