/**
 * 认证模块API - 使用最新的API客户端架构
 */

import {
  LoginRequest,
  LoginResponse,
  TokenValidationResponse,
  ApiResponse
} from './types';
import { apiClient, ApiError, ApiClient } from '../client';
import { API_ENDPOINTS, HTTP_STATUS, API_CONFIG } from '../config';
import { getCookie, key_for_token, onLogin, onLogout } from '@/utils/cookies';
import { get_token_aes } from '@/utils/login';

/**
 * 登录验证 - 获取token
 * GET /in/login/check?user_account=JS1873&user_password=Song13@14
 */
export async function login(params: LoginRequest): Promise<LoginResponse> {
  try {
    // 创建专门用于登录的API客户端实例，使用登录专用域名
    const loginClient = new (await import('../client')).ApiClient(API_CONFIG.LOGIN_BASE_URL);

    // 使用查询参数发送GET请求
    const queryParams = new URLSearchParams({
      user_account: params.user_account,
      user_password: params.user_password,
    });

    const response = await loginClient.get<LoginResponse>(
      `${API_ENDPOINTS.AUTH.LOGIN}?${queryParams.toString()}`
    );

    // 检查响应状态
    if (response.status === 'OK' && response.data) {
      return response.data;
    } else {
      throw new ApiError(
        response.message || '登录失败',
        HTTP_STATUS.UNAUTHORIZED
      );
    }
  } catch (error) {
    console.error('Login error:', error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      '登录失败，请检查用户名和密码',
      HTTP_STATUS.UNAUTHORIZED
    );
  }
}

/**
 * Token验证
 */
export async function validateToken(token: string): Promise<TokenValidationResponse> {
  try {
    const response = await apiClient.post<TokenValidationResponse>(
      API_ENDPOINTS.AUTH.VALIDATE,
      { token },
      {
        headers: {
          'Authorization': token,
        }
      }
    );

    if (response.status === 'OK' && response.data) {
      return response.data;
    } else {
      throw new ApiError(
        response.message || 'Token验证失败',
        HTTP_STATUS.UNAUTHORIZED
      );
    }
  } catch (error) {
    console.error('Token validation error:', error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      'Token验证失败',
      HTTP_STATUS.UNAUTHORIZED
    );
  }
}

/**
 * 登出
 */
export async function logout(token: string): Promise<ApiResponse> {
  try {
    onLogout();
  } catch (error) {
    console.error('Logout error:', error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      '登出失败',
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );
  }
}

/**
 * 获取当前用户信息
 */
export async function getCurrentUser(token: string): Promise<LoginResponse> {
  try {
    const response = await apiClient.get<LoginResponse>(
      API_ENDPOINTS.PROFILE.GET, // 使用配置中的用户信息端点
      {
        headers: {
          'Authorization': token,
        }
      }
    );

    if (response.status === 'OK' && response.data) {
      return response.data;
    } else {
      throw new ApiError(
        response.message || '获取用户信息失败',
        HTTP_STATUS.NOT_FOUND
      );
    }
  } catch (error) {
    console.error('Get current user error:', error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      '获取用户信息失败',
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );
  }
}

/**
 * 增强版登录函数 - 集成Redux状态管理和cookie处理
 * 兼容现有的登录流程
 */
export async function loginWithStateManagement(
  params: LoginRequest,
  dispatch?: any
): Promise<LoginResponse> {
  try {
    // 设置加载状态
    if (dispatch) {
      const { setUserActions } = await import('@/slice/authSlice');
      dispatch(setUserActions.setLoading(true));
    }

    const response = await login(params);

    if (response.token) {
      // 处理登录成功的状态管理
      if (dispatch) {
        const { setUserActions } = await import('@/slice/authSlice');
        dispatch(setUserActions.setUser(response));
      }

      // 保存token到cookie
      const token = response.token;
      const token_aes = get_token_aes(token);
      onLogin(token, token_aes);

      // 保存到localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('login', JSON.stringify(response));
      }

      return response;
    } else {
      throw new ApiError(
        response.message || '登录失败',
        HTTP_STATUS.UNAUTHORIZED
      );
    }
  } catch (error) {
    console.error('Login with state management error:', error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      '登录失败，请检查用户名和密码',
      HTTP_STATUS.UNAUTHORIZED
    );
  } finally {
    // 重置加载状态
    if (dispatch) {
      const { setUserActions } = await import('@/slice/authSlice');
      dispatch(setUserActions.setLoading(false));
    }
  }
}

/**
 * 检查当前登录状态
 */
export async function checkLoginStatus(): Promise<boolean> {
  try {
    const token = getCookie(key_for_token);

    if (!token) {
      return false;
    }

    // 验证token有效性
    const validationResult = await validateToken(token);
    return validationResult.valid === true;
  } catch (error) {
    console.error('Check login status error:', error);
    return false;
  }
}

/**
 * 自动登录 - 从localStorage恢复登录状态
 */
export async function autoLogin(dispatch?: any): Promise<LoginResponse | null> {
  try {
    if (typeof window === 'undefined') {
      return null;
    }

    const loginData = localStorage.getItem('login');
    if (!loginData) {
      return null;
    }

    const parsedData = JSON.parse(loginData) as LoginResponse;
    const isValid = await checkLoginStatus();

    if (isValid && dispatch) {
      const { setUserActions } = await import('@/slice/authSlice');
      dispatch(setUserActions.setUser(parsedData));
      return parsedData;
    }

    return null;
  } catch (error) {
    console.error('Auto login error:', error);
    return null;
  }
}

// 导出所有认证相关的API函数
export const authApi = {
  login,
  loginWithStateManagement,
  validateToken,
  logout,
  getCurrentUser,
  checkLoginStatus,
  autoLogin,
};

export default authApi;
