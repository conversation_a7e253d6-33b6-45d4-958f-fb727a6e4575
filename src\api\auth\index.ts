/**
 * 认证模块API
 */

import {
  LoginRequest,
  LoginResponse,
  TokenValidationRequest,
  TokenValidationResponse,
  ApiResponse
} from './types';
import { API_CONFIG, AUTH_ENDPOINTS } from '../config';

// API基础URL - 从配置文件获取
const API_BASE_URL = API_CONFIG.BASE_URL;

/**
 * 通用API请求函数
 */
async function apiRequest<T>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  };

  const response = await fetch(url, { ...defaultOptions, ...options });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  return response.json();
}

/**
 * 登录验证 - 获取token
 * GET /in/login/check?user_account=JS1873&user_password=Song13@14
 */
export async function login(params: LoginRequest): Promise<LoginResponse> {
  const queryParams = new URLSearchParams({
    user_account: params.user_account,
    user_password: params.user_password,
  });

  try {
    const response = await apiRequest<LoginResponse>(
      `${AUTH_ENDPOINTS.LOGIN}?${queryParams.toString()}`,
      {
        method: 'GET',
      }
    );
    
    return response;
  } catch (error) {
    console.error('Login error:', error);
    throw new Error('登录失败，请检查用户名和密码');
  }
}

/**
 * Token验证
 */
export async function validateToken(token: string): Promise<TokenValidationResponse> {
  try {
    const response = await apiRequest<TokenValidationResponse>(
      AUTH_ENDPOINTS.VALIDATE,
      {
        method: 'POST',
        headers: {
          'Authorization': token,
        },
        body: JSON.stringify({ token }),
      }
    );
    
    return response;
  } catch (error) {
    console.error('Token validation error:', error);
    throw new Error('Token验证失败');
  }
}

/**
 * 登出
 */
export async function logout(token: string): Promise<ApiResponse> {
  try {
    const response = await apiRequest<ApiResponse>(
      AUTH_ENDPOINTS.LOGOUT,
      {
        method: 'POST',
        headers: {
          'Authorization': token,
        },
      }
    );
    
    return response;
  } catch (error) {
    console.error('Logout error:', error);
    throw new Error('登出失败');
  }
}

/**
 * 获取当前用户信息
 */
export async function getCurrentUser(token: string): Promise<LoginResponse> {
  try {
    const response = await apiRequest<LoginResponse>(
      AUTH_ENDPOINTS.CURRENT_USER,
      {
        method: 'GET',
        headers: {
          'Authorization': token,
        },
      }
    );
    
    return response;
  } catch (error) {
    console.error('Get current user error:', error);
    throw new Error('获取用户信息失败');
  }
}

// 导出所有认证相关的API函数
export const authApi = {
  login,
  validateToken,
  logout,
  getCurrentUser,
};

export default authApi;
