namespace Globals {
  export let MY_KAOQIN: string;
  export let KAOQIN_REMINDER: string;
  export let NORMAL_STATE: string;
  export let ABNORMAL_STATE: string;
  export let DATE_UNIT: string;
  export let LOGOFF: string;
  export let MY_WORKTABLE: string;
  export let RECENT_MISSION: string;
  export let MORE: string;
  export let QUICK_ACCESS: string;
  export let DIRECTED_QUERY: string;
  export let DATA_IMPORT: string;
  export let REPORT_MANAGER: string;
  export let MY_RECORDS: string;
  export let WORKTABLE: string;
  export let RECENT_RECORDS: string;
  export let BOX_TITLE: string;
  export let BOX_REMAIN: string;
  export let LOGIN_ACCOUNT: string;
  export let LOGIN_PASSWORD: string;
  export let LOGIN_REMAIN_SUCCESS: string;
  export let UPDATE_REMAIN_SUCCESS: string;
  export let BOX_REMAIN_MISS: string;
  export let UPDATE_REMAIN_MISS: string;
  export let BOX_REMAIN_ERROR: string;
  export let BOX_REMAIN_USERNAME: string;
  export let BOX_REMAIN_PASSWORD: string;
  export let BOX_USERNAME: string;
  export let BOX_PASSWORD: string;
  export let BOX_RESET: string;
  export let BOX_REMAIN_NUMBER: string;
  export let BOX_REMAIN_MAIL: string;
  export let BOX_RESET_SUCCESS: string;
  export let BOX_REMAIN_SUCCESS: string;
  export let BOX_RESET_FAIL: string;
  export let BOX_REMAIN_FAIL: string;
  export let CONFIRM: string;
  export let BOX_REMEMBER: string;
  export let BOX_FORGET: string;
  export let LOGIN: string;
  export let RESET_RETURN: string;
  export let WORKTABLE_KAOQIN: string;
  export let WORKTABLE_KAOQIN_FIND: string;
  export let WORKTABLE_KAOQIN_CHANGE: string;
  export let WORKTABLE_KAOQIN_IMPORT: string;
  export let WORKTABLE_KAOQIN_TABLE: string;
  export let WORKTABLE_KAOQIN_STATICS: string;
  export let WORKTABLE_MEMBER: string;
  export let WORKTABLE_MEMBER_ALL: string;
  export let WORKTABLE_MEMBER_MANAGER: string;
  export let WORKTABLE_MEMBER_EDIT: string;
  export let WORKTABLE_MEMBER_DYNAMIC: string;
  export let WORKTABLE_SETTING: string;
  export let WORKTABLE_SETTING_DEPARTMENT: string;
  export let WORKTABLE_SETTING_CALENDAR: string;
  export let WORKTABLE_SETTING_MAIL: string;
  export let WORKTABLE_SETTING_OTHER: string;
  export let WORKTABLE_RECORD: string;
  export let WORKTABLE_RECORD_RECENT: string;
  export let WORKTABLE_RECORD_HISTORY: string;
  export let OTHER: string;
  export let KAOQIN_SELECTED_DAYS: string;
  export let KAOQIN_SELECTED_DAYS_KEEP: string;
  export let DEVELOPER_LIST: string;
  export let DEVELOPER_CONNECT: string;
  export let QUERY_FIND: string;
  export let QUERY_FIND_BTN: string;
  export let RELATIVE: string;
  export let BATCHADJUST_FILE: string;
  export let BATCHADJUST_START: string;
  export let BATCHADJUST_ATTENTION: string;
  export let BATCHADJUST_ATTENTION_CONTENT1: string;
  export let BATCHADJUST_ATTENTION_CONTENT2: string;
  export let BATCHADJUST_ATTENTION_BTN: string;
  export let BATCHADJUST_PROGRESS: string;
  export let BATCHADJUST_PROGRESS_CONTENT: string;
  export let BATCHADJUST_EXAMPLE: string;
  export let SECRET_KEY: string;
  export let INFORMATION_TITLE: string;
  export let INFORMATION_NAME: string;
  export let INFORMATION_SEX: string;
  export let INFORMATION_WORK_NO: string;
  export let INFORMATION_DEPARTMENT: string;
  export let INFORMATION_MAIL: string;
  export let INFORMATION_ROLE: string;
  export let INFORMATION_PASS: string;
  export let INFORMATION_CHANGE_PASS: string;
  export let INFORMATION_REMAIN: string;
  export let INFORMATION_OLD_PASS: string;
  export let INFORMATION_NEW_PASS: string;
  export let INFORMATION_CANCEL_CHANGE: string;
  export let IMPORT_OBJECTS: string;
  export let ACCESS_CONTROL_DATA: string;
  export let HOME_OFFICE_DATA: string;
  export let PAPER_DOCUMENT_DATA: string;
  export let LASTEST_DATA_TIME: string;
  export let IMPORT_NEW_DATE: string;
  export let BATCHADJUST_ATTENDENCE_IMPORT_PROMPT1: string;
  export let BATCHADJUST_ATTENDENCE_IMPORT_PROMPT1_1: string;
  export let BATCHADJUST_ATTENDENCE_IMPORT_PROMPT1_2: string;
  export let BATCHADJUST_ATTENDENCE_IMPORT_PROMPT1_3: string;
  export let BATCHADJUST_ATTENDENCE_IMPORT_PROMPT1_4: string;
  export let BATCHADJUST_ATTENDENCE_IMPORT_PROMPT2: string;
  export let BATCHADJUST_ATTENDENCE_IMPORT_PROMPT2_1: string;
  export let BATCHADJUST_ATTENDENCE_IMPORT_PROMPT2_2: string;
  export let APPROVAL_TITLE: string;
  export let APPROVAL_LEAVE_PROGRESS: string;
  export let APPROVAL_EVECTION_PROGRESS: string;
  export let APPROVAL_OVERTIME_PROGRESS: string;
  export let APPROVAL_CONFIRMATION_PROGRESS: string;
  export let APPROVAL_KEY_FIND: string;
  export let APPROVAL_DATE_FIND: string;
  export let APPROVAL_BATCH_MANAGE: string;
  export let APPROVAL_BATCH_AGREE: string;
  export let APPROVAL_BATCH_REJECT: string;
  export let APPROVAL_ALL: string;
  export let APPROVAL_UNAPPROVED: string;
  export let APPROVAL_APPROVED: string;
  export let APPROVAL_REJECTED: string;
  export let APPROVAL_WAIT: string;
  export let APPROVAL_START: string;
  export let APPROVAL_END: string;
  export let APPROVAL_TIAOXIU: string;
  export let APPROVAL_BINGJIA: string;
  export let APPROVAL_SHIJIA: string;
  export let APPROVAL_QITA: string;
  export let APPROVAL_REASON: string;
  export let APPROVAL_STATE: string;
  export let APPROVAL_UPDATE: string;
  export let APPROVAL_EVECTION_UPDATE: string;
  export let APPROVAL_AGREE: string;
  export let APPROVAL_REJECT: string;
  export let APPLICATION_NEW: string;
  export let APPLICATION_PEOPLE: string;
  export let APPLICATION_DATE: string;
  export let APPLICATION_AREA: string;
  export let APPLICATION_REASON: string;
  export let APPLICATION_START_TIME: string;
  export let APPLICATION_START_CARD: string;
  export let APPLICATION_START_CARD_CHANGE: string;
  export let APPLICATION_END_TIME: string;
  export let APPLICATION_END_CARD: string;
  export let APPLICATION_END_CARD_CHANGE: string;
  export let APPLICATION_PROJECT: string;
  export let APPLICATION_PROJECT_CHOOSE: string;
  export let APPLICATION_DEVELOPMENT: string;
  export let APPLICATION_COMMIT: string;
  export let CANCEL: string;
  export let APPLICATION_OVERVIEW: string;
  export let APPLICATION_NAME_NO: string;
  export let APPLICATION_B_AREA: string;
  export let OPERATION: string;
  export let APPLICATION_A_CHANGE: string;
  export let APPLICATION_INPUT: string;
  export let APPLICATION_SURE: string;
  export let APPLICATION_C_TEXT: string;
  export let APPLICATION_ONE_INPUT: string;
  export let DELETE: string;
  export let APPLICATION_ASK: string;
  export let APPLICATION_PREDICT: string;
  export let APPLICATION_CHANGE: string;
  export let APPLICATION_CHANGE_ASK: string;
  export let APPLICATION_CHANGE_SURE: string;
  export let APPLICATION_NO_CARD: string;
  export let APPLICATION_SURE_DATE: string;
  export let APPLICATION_CARD_TIME: string;
  export let APPLICATION_SURE_TIME: string;
  export let APPLICATION_TYPE: string;
  export let APPLICATION_VOCATION_TIME: string;
  export let APPLICATION_PREDICT_TIME: string;
  export let MEMBERS_OVERVIEW: string;
  export let MEMBERS_UPDATE: string;
  export let MEMBERS_NEW: string;
  export let MEMBERS_FORM_DUE: string;
  export let CHANGE: string;
  export let MEMBERS_DELETE: string;
  export let MEMBERS_MEMBER_DELETE: string;
  export let MEMBERS_DATA_CHANGE: string;
  export let MEMBERS_NAME: string;
  export let MEMBERS_WORK_NO: string;
  export let MEMBERS_SEX: string;
  export let MEMBERS_MAIL: string;
  export let MEMBERS_PASSWORD: string;
  export let MEMBERS_DEVELOPMENT: string;
  export let MEMBERS_DATA_MULTI: string;
  export let MEMBERS_ROLES_TEXT: string;
  export let MEMBERS_CHOOSE_DELETE: string;
  export let MEMBERS_MEMBER_NAME: string;
  export let MEMBERS_ROLES: string;
  export let SAVE: string;
  export let MEMBERS_DATA_MULTI_DUE: string;
  export let MEMBERS_MAIL_AUTO: string;
  export let MEMBERS_INPUT_TEXT: string;
}

Globals.MY_KAOQIN = "我的考勤";
Globals.KAOQIN_REMINDER = "考勤提醒"
Globals.NORMAL_STATE = "正常"
Globals.ABNORMAL_STATE = "异常"
Globals.DATE_UNIT = "日"
Globals.LOGOFF = "注销"
Globals.MY_WORKTABLE = "我的工作台"
Globals.RECENT_MISSION = "最近任务"
Globals.MORE = "查看更多"
Globals.QUICK_ACCESS = "快捷访问"
Globals.DIRECTED_QUERY = "考勤|定向查询"
Globals.DATA_IMPORT = "考勤|数据导入"
Globals.REPORT_MANAGER = "考勤|报表管理"
Globals.MY_RECORDS = "操作记录|我的记录"
Globals.WORKTABLE = "工作台"
Globals.RECENT_RECORDS = "近期记录"
Globals.BOX_TITLE = "立即登录"
Globals.BOX_REMAIN = "考勤管理系统"
Globals.LOGIN_ACCOUNT = "账号"
Globals.LOGIN_PASSWORD = "密码"
Globals.LOGIN_REMAIN_SUCCESS = "登录成功！"
Globals.UPDATE_REMAIN_SUCCESS = "修改成功！"
Globals.BOX_REMAIN_MISS = "您输入的账号或密码不正确，请重新输入"
Globals.BOX_REMAIN_ERROR = "您的账号或密码格式不正确，请重新输入"
Globals.UPDATE_REMAIN_MISS = "您输入的两次新密码不一致，请重新输入"
Globals.BOX_REMAIN_USERNAME = "请输入账号/工号/公司邮箱！"
Globals.BOX_REMAIN_PASSWORD = "请输入密码！"
Globals.BOX_USERNAME = " 请输入工号/公司邮箱"
Globals.BOX_PASSWORD = " 请输入密码"
Globals.BOX_RESET = "请输入工号和邮箱来重置您的密码"
Globals.BOX_REMAIN_NUMBER = "请输入工号！"
Globals.BOX_REMAIN_MAIL = "请输入公司邮箱！"
Globals.BOX_RESET_SUCCESS = "重置成功"
Globals.BOX_REMAIN_SUCCESS = "您的新密码已发送至您的邮箱。稍后请使用新密码登录考勤系统"
Globals.BOX_RESET_FAIL = "重置失败"
Globals.BOX_REMAIN_FAIL = "工号和邮箱不匹配，请确认后重试"
Globals.CONFIRM = "确定"
Globals.BOX_REMEMBER = "记住账号"
Globals.BOX_FORGET = "重置密码"
Globals.LOGIN = "登录"
Globals.RESET_RETURN = "返回登录"
Globals.WORKTABLE_KAOQIN = "考勤";
Globals.WORKTABLE_KAOQIN_FIND = "定向查询";
Globals.WORKTABLE_KAOQIN_CHANGE = "批量调整";
Globals.WORKTABLE_KAOQIN_IMPORT = "数据导入";
Globals.WORKTABLE_KAOQIN_TABLE = "报表管理";
Globals.WORKTABLE_KAOQIN_STATICS = "考勤汇总";
Globals.WORKTABLE_MEMBER = "员工";
Globals.WORKTABLE_MEMBER_ALL = "员工一览";
Globals.WORKTABLE_MEMBER_MANAGER = "管理员";
Globals.WORKTABLE_MEMBER_EDIT = "批量操作";
Globals.WORKTABLE_MEMBER_DYNAMIC = "员工动态";
Globals.WORKTABLE_SETTING = "系统设置";
Globals.WORKTABLE_SETTING_DEPARTMENT = "部门";
Globals.WORKTABLE_SETTING_CALENDAR = "节假日";
Globals.WORKTABLE_SETTING_MAIL = "邮件";
Globals.WORKTABLE_SETTING_OTHER = "其它";
Globals.WORKTABLE_RECORD = "操作记录";
Globals.WORKTABLE_RECORD_RECENT = "近期记录";
Globals.WORKTABLE_RECORD_HISTORY = "我的记录";
Globals.OTHER = "其他";
Globals.KAOQIN_SELECTED_DAYS = "考勤默认查询天数";
Globals.KAOQIN_SELECTED_DAYS_KEEP = "保存修改";
Globals.DEVELOPER_LIST = "开发人员列表";
Globals.DEVELOPER_CONNECT = "联系开发人员";
Globals.QUERY_FIND = "考勤/定向查询";
Globals.QUERY_FIND_BTN = "查询";
Globals.RELATIVE = "联系开发人员";
Globals.BATCHADJUST_FILE = "选择文件";
Globals.BATCHADJUST_START = "开始处理";
Globals.BATCHADJUST_ATTENTION = "注意";
Globals.BATCHADJUST_ATTENTION_CONTENT1 = "1.考勤文件中最大日期不能超过";
Globals.BATCHADJUST_ATTENTION_CONTENT2 = "2.请下载模板文件后按照规则输入考勤数据，否则批量处理将会出错";
Globals.BATCHADJUST_ATTENTION_BTN = "下载模板";
Globals.BATCHADJUST_PROGRESS = "处理进度";
Globals.BATCHADJUST_PROGRESS_CONTENT = "正在处理，请稍后......";
Globals.BATCHADJUST_EXAMPLE = "输入规则";
Globals.SECRET_KEY = "911eb4bccc4ee613";
Globals.INFORMATION_TITLE = "您的个人信息";
Globals.INFORMATION_NAME = "姓名";
Globals.INFORMATION_SEX = "性别";
Globals.INFORMATION_WORK_NO = "工号";
Globals.INFORMATION_MAIL = "邮箱";
Globals.INFORMATION_DEPARTMENT = "部门";
Globals.INFORMATION_ROLE = "角色";
Globals.INFORMATION_PASS = "密码";
Globals.INFORMATION_CHANGE_PASS = "修改密码";
Globals.INFORMATION_REMAIN = "请输入8~20位新密码，须包含大小写英文、数字、特殊字符（$、@、#、%、!）";
Globals.INFORMATION_OLD_PASS = "旧密码";
Globals.INFORMATION_NEW_PASS = "新密码";
Globals.INFORMATION_CANCEL_CHANGE = "取消修改";
Globals.IMPORT_OBJECTS = "导入对象";
Globals.ACCESS_CONTROL_DATA = "门禁数据";
Globals.HOME_OFFICE_DATA = "居家办公数据";
Globals.PAPER_DOCUMENT_DATA = "纸质单据数据";
Globals.LASTEST_DATA_TIME = "当前数据已更新至";
Globals.IMPORT_NEW_DATE = "导入新数据";
Globals.BATCHADJUST_ATTENDENCE_IMPORT_PROMPT1 = "■导入新数据前请先提取门禁数据"
Globals.BATCHADJUST_ATTENDENCE_IMPORT_PROMPT1_1 = "1.进门禁系统，选取所有门，并“提取数据”"
Globals.BATCHADJUST_ATTENDENCE_IMPORT_PROMPT1_2 = "2.确保所有门禁数据提取成功"
Globals.BATCHADJUST_ATTENDENCE_IMPORT_PROMPT1_3 = "3.进本考勤系统，点“导入新数据”"
Globals.BATCHADJUST_ATTENDENCE_IMPORT_PROMPT1_4 = "※新旧门禁系统的数据都需要提取。如果只提取一份数据将无法导入。"
Globals.BATCHADJUST_ATTENDENCE_IMPORT_PROMPT2 = "■工号变更"
Globals.BATCHADJUST_ATTENDENCE_IMPORT_PROMPT2_1 = "1.确保每位员工只有一个门禁卡有工号信息"
Globals.BATCHADJUST_ATTENDENCE_IMPORT_PROMPT2_2 = "2.如果员工更换了门禁卡，请及时更新新旧门禁系统中对应门禁卡的工号信息"
Globals.APPROVAL_LEAVE_PROGRESS = "请假审批进程";
Globals.APPROVAL_EVECTION_PROGRESS = "出差审批进程";
Globals.APPROVAL_OVERTIME_PROGRESS = "加班审批进程";
Globals.APPROVAL_CONFIRMATION_PROGRESS = "确认单审批进程";
Globals.APPROVAL_TITLE = "申请一览";
Globals.APPROVAL_KEY_FIND = "关键字查询：";
Globals.APPROVAL_DATE_FIND = "日期查询：";
Globals.APPROVAL_BATCH_MANAGE = "批量管理";
Globals.APPROVAL_BATCH_AGREE = "批量同意";
Globals.APPROVAL_BATCH_REJECT = "批量驳回";
Globals.APPROVAL_ALL = "全部";
Globals.APPROVAL_UNAPPROVED = "未审批";
Globals.APPROVAL_APPROVED = "已同意";
Globals.APPROVAL_REJECTED = "已驳回";
Globals.APPROVAL_WAIT = "等待上级批准";
Globals.APPROVAL_START = "开始";
Globals.APPROVAL_END = "结束";
Globals.APPROVAL_TIAOXIU = "调休";
Globals.APPROVAL_BINGJIA = "病假";
Globals.APPROVAL_SHIJIA = "事假";
Globals.APPROVAL_QITA = "其他";
Globals.APPROVAL_REASON = "原因";
Globals.APPROVAL_STATE = "状态";
Globals.APPROVAL_UPDATE = "修改请假时长";
Globals.APPROVAL_EVECTION_UPDATE = "修改出差时长";
Globals.APPROVAL_AGREE = "同意";
Globals.APPROVAL_REJECT = "驳回";
Globals.APPLICATION_NEW = "新建申请";
Globals.APPLICATION_PEOPLE = "申请人：";
Globals.APPLICATION_DATE = "日期：";
Globals.APPLICATION_AREA = "地点：";
Globals.APPLICATION_REASON = "事由：";
Globals.APPLICATION_START_TIME = "开始时间";
Globals.APPLICATION_START_CARD = "上班打卡：";
Globals.APPLICATION_START_CARD_CHANGE = "上班修正：";
Globals.APPLICATION_END_TIME = "结束时间";
Globals.APPLICATION_END_CARD = "下班打卡：";
Globals.APPLICATION_END_CARD_CHANGE = "下班修正：";
Globals.APPLICATION_PROJECT = "项目审批：";
Globals.APPLICATION_PROJECT_CHOOSE = "请选择项目审批人";
Globals.APPLICATION_DEVELOPMENT = "部门审批：";
Globals.APPLICATION_COMMIT = "提交";
Globals.CANCEL = "取消";
Globals.APPLICATION_OVERVIEW = "申请一览";
Globals.APPLICATION_NAME_NO = "工号/姓名";
Globals.APPLICATION_B_AREA = "出差地点";
Globals.OPERATION = "操作";
Globals.APPLICATION_A_CHANGE = "申请修改";
Globals.APPLICATION_INPUT = "输入";
Globals.APPLICATION_SURE = "我确认";
Globals.APPLICATION_C_TEXT = "才能点击删除键撤回当前申请";
Globals.APPLICATION_ONE_INPUT = "一键输入";
Globals.DELETE = "删除";
Globals.APPLICATION_ASK = "您确定要申请修改预定时间吗？";
Globals.APPLICATION_PREDICT = "预定时间：";
Globals.APPLICATION_PREDICT_TIME = "预订时长";
Globals.APPLICATION_CHANGE = "修正时间：";
Globals.APPLICATION_CHANGE_ASK = "确定申请修改吗？";
Globals.APPLICATION_CHANGE_SURE = "确认申请";
Globals.APPLICATION_NO_CARD = "上班打卡： 当日无打卡记录";
Globals.APPLICATION_SURE_DATE = "确认日期";
Globals.APPLICATION_CARD_TIME = "打卡时长";
Globals.APPLICATION_SURE_TIME = "确认时长";
Globals.APPLICATION_TYPE = "请假类型：";
Globals.APPLICATION_VOCATION_TIME = "请假时长：";
Globals.MEMBERS_OVERVIEW = "成员一览";
Globals.MEMBERS_UPDATE = "批量更新";
Globals.MEMBERS_NEW = "添加新成员";
Globals.MEMBERS_FORM_DUE = "处理表格选中项";
Globals.CHANGE = "修改";
Globals.MEMBERS_DELETE = "从本部门删除";
Globals.MEMBERS_MEMBER_DELETE = "才能点击删除对象成员:";
Globals.MEMBERS_DATA_CHANGE = "数据修改";
Globals.MEMBERS_NAME = "姓名:";
Globals.MEMBERS_WORK_NO = "工号:";
Globals.MEMBERS_SEX = "性别:";
Globals.MEMBERS_MAIL = "邮箱:";
Globals.MEMBERS_PASSWORD = "密码:";
Globals.MEMBERS_DEVELOPMENT = "部门:";
Globals.MEMBERS_DATA_MULTI = "数据批量修改";
Globals.MEMBERS_ROLES_TEXT = "修改成员角色为";
Globals.MEMBERS_CHOOSE_DELETE = "删除所选成员";
Globals.MEMBERS_MEMBER_NAME = "成员名:";
Globals.MEMBERS_ROLES = "角色:";
Globals.SAVE = "保存";
Globals.MEMBERS_DATA_MULTI_DUE = "数据批量处理";
Globals.MEMBERS_MAIL_AUTO = "自动发送邮件";
Globals.MEMBERS_INPUT_TEXT = "输入说明";
export default Globals;