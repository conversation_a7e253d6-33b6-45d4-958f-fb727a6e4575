

import { useApplicationSelector, useApplicationDispatch } from "@/hook/hooks";
import React, { useEffect, useState } from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import ApiUrlVars from '@/api/common/url-vars';

import { Pagination, message, Spin, Tooltip, Tag, Button, Space, Table } from 'antd';
import { TableOutlined, CalendarOutlined } from '@ant-design/icons';
import dayjs, { Dayjs } from 'dayjs';
import styles from './my.module.css'
import ItemImage from '@/pages/components/ui/itemImage';
import MobileTable from '@/components/ui/MobileTable';
import { tabActions } from '@/slice/tabSlice';
import { menuConfig, menuTypes } from '@/utils/menulist';
import { queryAttendanceRecords } from '@/pages/attendance/api/attendanceApi';

import { statusData, statusActions } from '@/slice/statusSlice';
import ErrorPart from '@/pages/components/ui/ErrorPart';

import AttendanceCalendar from '@/pages/components/ui/AttendanceCalendar';

let dispatch: any = null

const queryUrl = ApiUrlVars.loginApi_domain + ApiUrlVars.record_query

const MyAttendance: React.FC = (props) => {
    const { t, isClient } = useTranslation(['attendance', 'common']);
    const tCommon = (key: string, fallback?: string) => t(`common:${key}`, fallback);
    const [queryResult, resetQueryResult] = useState({})
    const [currentPage, setCurrentPage] = useState(1)
    const [messageApi, contextHolder] = message.useMessage();
    const [pageSize, setPageSize] = useState(50);
    // 移动端默认显示列表模式，PC端默认显示日历模式
    const [viewMode, setViewMode] = useState<'table' | 'calendar'>(() => {
        if (typeof window !== 'undefined') {
            return window.innerWidth <= 768 ? 'table' : 'calendar';
        }
        return 'calendar';
    });

    const infData = useApplicationSelector(statusData);
    const loginDataFromStorage = typeof window !== 'undefined' ? localStorage.getItem('login') : null
    const localStorageData = loginDataFromStorage? JSON.parse(loginDataFromStorage) : {}

    const startIndex = (currentPage -1) * pageSize
    const endIndex = currentPage * pageSize

    dispatch = useApplicationDispatch()

    // 渲染时间显示
    const renderTimeDisplay = (item: any, timeType: 'start' | 'end') => {
        const isStart = timeType === 'start';
        const currentTime = isStart ? item?.start : item?.end;
        const initTime = isStart ? item?.init_start : item?.init_end;
        const hasChanged = isStart ? item?.change_start : item?.change_end;

        if (item?.et_code === '999999') {
            if (initTime === currentTime) {
                return <span>{currentTime}</span>;
            } else {
                return (
                    <span style={{color:'red'}}>
                        <span style={{textDecorationLine:'line-through'}}>{initTime}</span>
                        &nbsp;&nbsp;⇨&nbsp;&nbsp;
                        {currentTime}
                    </span>
                );
            }
        } else {
            if (hasChanged) {
                return (
                    <span style={{color:'red'}}>
                        <span style={{textDecorationLine:'line-through'}}>{initTime}</span>
                        &nbsp;&nbsp;⇨&nbsp;&nbsp;
                        {currentTime}
                    </span>
                );
            } else {
                return <span>{currentTime}</span>;
            }
        }
    };

    // 渲染请假标签
    const renderLeaveTags = (item: any) => {
        const tags = [];

        if (item?.c_hours > 0) {
            tags.push(
                <div key="compensatory" className={styles.btn} style={{position:'relative', marginBottom: '4px'}}>
                    <ItemImage type='调休' time={item?.c_hours}></ItemImage>
                </div>
            );
        }
        if (item?.t_hours > 0) {
            tags.push(
                <div key="normal" className={styles.btn} style={{position:'relative', marginBottom: '4px'}}>
                    <ItemImage type='事假' time={item?.t_hours}></ItemImage>
                </div>
            );
        }
        if (item?.s_hours > 0) {
            tags.push(
                <div key="sick" className={styles.btn} style={{position:'relative', marginBottom: '4px'}}>
                    <ItemImage type='病假' time={item?.s_hours}></ItemImage>
                </div>
            );
        }

        return <div style={{display: 'flex', flexDirection: 'column', gap: '2px'}}>{tags}</div>;
    };

    // 渲染备注信息
    const renderComments = (item: any) => {
        const comments = [];

        if (item?.l_hours > 0) {
            comments.push(
                <div key="absent" className={styles.comment_edit_apply}>
                    {t('myAttendance.absentHours', { hours: item?.l_hours })}
                </div>
            );
        }

        if (item?.err == 1 && item?.code != '999999' && item?.code != 0 && item.l_hours > 0) {
            comments.push(
                <div key="insufficient" className={styles.comment_edit_apply}>
                    {t('myAttendance.insufficientLeave')}
                </div>
            );
        }

        if (item?.err == 1 && item?.code == 0 && dayjs(item?.day?.slice(0,10)).add(7,'day') >= dayjs()) {
            comments.push(
                <div key="no-leave" className={styles.comment_edit_apply}>
                    {t('myAttendance.noLeave')}
                </div>
            );
        }

        if (item?.err == 2 && item?.code) {
            comments.push(
                <div key="pending" className={styles.comment_edit_apply}>
                    {t('myAttendance.pendingApproval')}
                </div>
            );
        }

        if (item?.err == 0 && item?.home == 1) {
            comments.push(
                <div key="home" className={styles.comment_ok}>
                    ※{tCommon('workFromHome', '居家办公')}
                </div>
            );
        }

        if (item?.err == 0 && item?.home == 0 && item?.o_code != 0 && item?.o_hours > 0) {
            comments.push(
                <div key="other-leave" className={styles.comment_ok}>
                    ※{tCommon('otherLeave', '其他')}
                </div>
            );
        }

        if (item?.err == 0 && item?.home == 0 && item?.bt_code != 0 && item?.bt_hours > 0) {
            comments.push(
                <div key="business-trip" className={styles.comment_ok}>
                    ※{tCommon('businessTrip', '出差')}
                </div>
            );
        }

        return <div>{comments}</div>;
    };

    // 表格列配置
    const getTableColumns = () => [
        {
            key: 'day',
            title: tCommon('date', '日期'),
            mobileLabel: tCommon('date', '日期'),
            mobilePosition: 'title' as const,
            dataIndex: 'day',
            width: '11%',
            type: 'text' as const,
            render: (text: string, record: any) => {
                // 确定文字样式
                let textStyle: React.CSSProperties = { fontWeight: '500' };

                if (record?.err == 1) {
                    textStyle.color = '#ff4d4f';
                } else if (record?.err == 2) {
                    textStyle.color = '#fa8c16';
                }

                return <span style={textStyle}>{text}</span>;
            },
            // 移动端自定义渲染 - 左上角日期
            mobileRender: (value: any, record: any) => {
                let textStyle: React.CSSProperties = {
                    fontWeight: '600',
                    fontSize: '16px'
                };

                if (record?.err == 1) {
                    textStyle.color = '#ff4d4f';
                } else if (record?.err == 2) {
                    textStyle.color = '#fa8c16';
                } else {
                    textStyle.color = '#1890ff';
                }

                return <span style={textStyle}>{value}</span>;
            }
        },
        {
            key: 'start_time',
            title: t('workTime.clockIn'),
            mobileLabel: t('workTime.clockIn'),
            mobilePosition: 'content' as const,
            dataIndex: 'start_time',
            width: '14%',
            type: 'text' as const,
            render: (text: string, record: any) => renderTimeDisplay(record, 'start'),
            // 移动端自定义渲染 - 左下角上班打卡
            mobileRender: (value: any, record: any) => {
                const timeDisplay = renderTimeDisplay(record, 'start');
                return (
                    <div style={{ textAlign: 'left' }}>
                        <div style={{
                            fontSize: '12px',
                            color: '#666',
                            marginBottom: '4px',
                            fontWeight: '500'
                        }}>
                            上班打卡
                        </div>
                        <div style={{
                            fontSize: '14px',
                            fontWeight: '600',
                            color: '#1890ff'
                        }}>
                            {timeDisplay}
                        </div>
                    </div>
                );
            }
        },
        {
            key: 'end_time',
            title: t('workTime.clockOut'),
            mobileLabel: t('workTime.clockOut'),
            mobilePosition: 'meta' as const,
            dataIndex: 'end_time',
            width: '14%',
            type: 'text' as const,
            render: (text: string, record: any) => renderTimeDisplay(record, 'end'),
            // 移动端自定义渲染 - 右下角下班打卡
            mobileRender: (value: any, record: any) => {
                const timeDisplay = renderTimeDisplay(record, 'end');
                return (
                    <div style={{ textAlign: 'right' }}>
                        <div style={{
                            fontSize: '12px',
                            color: '#666',
                            marginBottom: '4px',
                            fontWeight: '500'
                        }}>
                            下班打卡
                        </div>
                        <div style={{
                            fontSize: '14px',
                            fontWeight: '600',
                            color: '#52c41a'
                        }}>
                            {timeDisplay}
                        </div>
                    </div>
                );
            }
        },
        {
            key: 'work_location',
            title: tCommon('workLocation', '出勤地'),
            mobileLabel: tCommon('workLocation', '出勤地'),
            mobilePosition: 'subtitle' as const,
            dataIndex: 'work_location',
            width: '12%',
            type: 'text' as const,
            render: (text: string, record: any) => {
                // 根据不同的工作地点类型显示不同的标签
                if (record?.home == 1) {
                    return (
                        <div style={{ textAlign: 'center' }}>
                            <span style={{
                                background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
                                color: '#1890ff',
                                padding: '2px 8px',
                                borderRadius: '12px',
                                fontSize: '11px',
                                fontWeight: '600',
                                border: '1px solid #91d5ff'
                            }}>
                                🏠 {tCommon('workFromHome', '居家办公')}
                            </span>
                        </div>
                    );
                } else if (record?.bt_code != 0 && record?.bt_hours > 0) {
                    return (
                        <div style={{ textAlign: 'center' }}>
                            <span style={{
                                background: 'linear-gradient(135deg, #fff7e6 0%, #ffd591 100%)',
                                color: '#fa8c16',
                                padding: '2px 8px',
                                borderRadius: '12px',
                                fontSize: '11px',
                                fontWeight: '600',
                                border: '1px solid #ffb84d'
                            }}>
                                ✈️ {tCommon('businessTrip', '出差')}
                            </span>
                        </div>
                    );
                } else {
                    return (
                        <div style={{ textAlign: 'center' }}>
                            <span style={{
                                background: 'linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)',
                                color: '#52c41a',
                                padding: '2px 8px',
                                borderRadius: '12px',
                                fontSize: '11px',
                                fontWeight: '600',
                                border: '1px solid #95de64'
                            }}>
                                🏢 {tCommon('office', '办公室')}
                            </span>
                        </div>
                    );
                }
            },
            // 移动端自定义渲染 - 右上角出勤地点，右侧对齐
            mobileRender: (value: any, record: any) => {
                let locationContent;
                if (record?.home == 1) {
                    locationContent = (
                        <span style={{
                            background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
                            color: '#1890ff',
                            padding: '2px 8px',
                            borderRadius: '12px',
                            fontSize: '11px',
                            fontWeight: '600',
                            border: '1px solid #91d5ff'
                        }}>
                            🏠 {tCommon('workFromHome', '居家办公')}
                        </span>
                    );
                } else if (record?.bt_code != 0 && record?.bt_hours > 0) {
                    locationContent = (
                        <span style={{
                            background: 'linear-gradient(135deg, #fff7e6 0%, #ffd591 100%)',
                            color: '#fa8c16',
                            padding: '2px 8px',
                            borderRadius: '12px',
                            fontSize: '11px',
                            fontWeight: '600',
                            border: '1px solid #ffb84d'
                        }}>
                            ✈️ {tCommon('businessTrip', '出差')}
                        </span>
                    );
                } else {
                    locationContent = (
                        <span style={{
                            background: 'linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)',
                            color: '#52c41a',
                            padding: '2px 8px',
                            borderRadius: '12px',
                            fontSize: '11px',
                            fontWeight: '600',
                            border: '1px solid #95de64'
                        }}>
                            🏢 {tCommon('office', '办公室')}
                        </span>
                    );
                }

                return (
                    <div style={{ textAlign: 'right' }}>
                        {locationContent}
                    </div>
                );
            }
        },
        {
            key: 'leave',
            title: tCommon('leave', '请假'),
            mobileLabel: tCommon('leave', '请假'),
            mobilePosition: 'meta' as const,
            dataIndex: 'leave',
            width: '15%',
            type: 'text' as const,
            render: (text: string, record: any) => renderLeaveTags(record)
        },

        {
            key: 'comments',
            title: tCommon('remarks', '备注'),
            mobileLabel: tCommon('remarks', '备注'),
            mobilePosition: 'meta' as const,
            dataIndex: 'comments',
            width: '38%',
            type: 'text' as const,
            render: (text: string, record: any) => {
                const hasTooltip = (record?.update_from != '' && record.update_from != null && record.update_from != 'Batch') ||
                                 (record?.update_name != '' && record.update_name != null && record.update_name != 'Batch');

                if (hasTooltip) {
                    let toolTipInf_name = '';
                    let toolTipInf_from = '';
                    let toolTipInf_warn = '';

                    if (record?.update_name != '' && record?.update_name != null && record?.update_name != 'Batch') {
                        toolTipInf_name = "modified by " + record?.update_name;
                    }
                    if (record?.update_from != '' && record?.update_from != null && record?.update_from != 'Batch') {
                        toolTipInf_from = record?.update_from;
                    }
                    if (toolTipInf_from) {
                        toolTipInf_from = tCommon('remarks', '备注') + '：' + toolTipInf_from;
                    } else {
                        toolTipInf_from = tCommon('remarks', '备注') + '：' + tCommon('none', '无')
                    }

                    // 构建警告信息
                    if (record?.l_hours > 0) {
                        toolTipInf_warn += t('myAttendance.absentHours', { hours: record?.l_hours })
                    }
                    if (record?.err == 1 && record?.code != '999999' && record?.code != 0 && record.l_hours > 0) {
                        toolTipInf_warn += t('myAttendance.insufficientLeave')
                    }
                    if (record?.err == 1 && record?.code == 0 && dayjs(record?.day?.slice(0,10)).add(7,'day') >= dayjs()) {
                        toolTipInf_warn += t('myAttendance.noLeave')
                    }
                    if (record?.err == 2 && record?.code) {
                        toolTipInf_warn += t('myAttendance.pendingApproval')
                    }
                    if (record?.err == 0 && record?.home == 1) {
                        toolTipInf_warn += '※' + tCommon('workFromHome', '居家办公')
                    }
                    if (record?.err == 0 && record?.home == 0 && record?.o_code != 0 && record?.o_hours > 0) {
                        toolTipInf_warn += '※' + tCommon('otherLeave', '其他')
                    }
                    if (record?.err == 0 && record?.home == 0 && record?.bt_code != 0 && record?.bt_hours > 0) {
                        toolTipInf_warn += '※' + tCommon('businessTrip', '出差')
                    }
                    if (toolTipInf_warn) {
                        toolTipInf_warn = t('myAttendance.abnormal', '异常') + '：' + toolTipInf_warn
                    } else {
                        toolTipInf_warn = t('myAttendance.abnormal', '异常') + '：' + tCommon('none', '无')
                    }

                    const toolTipInf = <span>{toolTipInf_name}<br/>{toolTipInf_from}<br/>{toolTipInf_warn}</span>;

                    return (
                        <Tooltip title={toolTipInf} placement="top">
                            {renderComments(record)}
                        </Tooltip>
                    );
                }

                return renderComments(record);
            }
        }
    ];

    // 获取表格数据（PC端分页，移动端全部数据）
    const getTableData = () => {
        const dataList = (queryResult as any).data_list || [];
        const filteredData = dataList
            .filter((item: any) => dayjs(item?.day?.slice(0,10)) <= dayjs((queryResult as any).max_day));

        // 检测是否为移动端
        const isMobile = typeof window !== 'undefined' && window.innerWidth <= 768;

        // 移动端显示全部数据，PC端使用分页
        const displayData = isMobile ? filteredData : filteredData.slice(startIndex, endIndex);

        return displayData.map((item: any, index: number) => {
            const actualIndex = isMobile ? index : startIndex + index;
            return {
                key: `table_${actualIndex}`,
                day: item?.day,
                start_time: item?.start,
                end_time: item?.end,
                leave: '',
                comments: '',
                ...item
            };
        });
    };

    // 获取日历数据（不分页，返回所有数据）
    const getCalendarData = () => {
        const dataList = (queryResult as any).data_list || [];
        return dataList
            .filter((item: any) => dayjs(item?.day?.slice(0,10)) <= dayjs((queryResult as any).max_day))
            .map((item: any, index: number) => {
                return {
                    key: `calendar_${index}`,
                    day: item?.day,
                    start: item?.start,
                    end: item?.end,
                    day_type: item?.day_type,
                    err: item?.err,
                    work_no: item?.work_no || localStorageData.work_no,
                    name: item?.name || localStorageData.name,
                    update_name: item?.update_name,
                    update_from: item?.update_from,
                    ...item
                };
            });
    };

    // 处理日历日期选择
    const handleCalendarDateSelect = (date: Dayjs, records: any[]) => {
        console.log('Selected date:', date.format('YYYY-MM-DD'));
        console.log('Records for date:', records);
    };

    function createJson(){
        return {
            "keyword": localStorageData.user_id + '',
            "query_type":1,
            "start_dt":"",
            "end_dt":""
        }
    }
    useEffect(() => {
        dispatch(tabActions.onTabChange(menuTypes.record))
        dispatch(tabActions.onSubChange(menuConfig[0].list[0].index))
        // getApi(ApiUrlVars.api_domain + ApiUrlVars.apply_day_get, null, dispatch).then((res:any) => {
        //     if(res.data?.status == 'OK'){
        //       setPageSize(Number(res.data?.search_days[0][0]))
        //     }
        //   })

        const json = createJson();
        queryAttendanceRecords(json, dispatch).then((res) => {
            if (res.status == 'OK') {
                resetQueryResult(res.data)
            }else if (res?.status == 'NG') {
                messageApi.open({
                  type: 'error',
                  content: res?.message,
                });
              }
          })
    }, [])

    // 响应式viewMode处理
    useEffect(() => {
        const handleResize = () => {
            const isMobile = window.innerWidth <= 768;
            if (isMobile && viewMode === 'calendar') {
                setViewMode('table');
            }
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [viewMode]);

    // 如果还在客户端初始化中，显示加载状态
    if (!isClient) {
        return (
            <>
                <div>Loading...</div>
            </>
        );
    }

    return (
        <>
        {infData.status=='OK'&&<div style={{
            padding: '24px',
            background: '#f5f5f5',
            minHeight: '100vh',
            width: '100%',
            height: '100vh',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column'
        }}>
            {contextHolder}

            {/* 页面标题 - 固定在顶部，不滚动 */}
            <div style={{
                marginBottom: '32px',
                padding: '20px 0',
                borderBottom: '3px solid #e2e8f0',
                position: 'relative',
                flexShrink: 0  // 防止标题被压缩
            }}>
                <div style={{
                    position: 'absolute',
                    bottom: '-3px',
                    left: '0',
                    width: '120px',
                    height: '3px',
                    background: 'linear-gradient(90deg, #1890ff 0%, #40a9ff 100%)',
                    borderRadius: '2px'
                }}></div>
                <h1 style={{
                    fontSize: '26px',
                    fontWeight: '600',
                    color: '#2d3748',
                    margin: '0',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    letterSpacing: '1px'
                }}>
                    <span style={{
                        fontSize: '28px',
                        color: '#667eea',
                        filter: 'drop-shadow(0 2px 4px rgba(102, 126, 234, 0.2))'
                    }}>📅</span>
                    {t('myAttendance.title')}
                </h1>
            </div>

            {/* 考勤查询区域 */}
            <div className={styles.attendance_query_container}>
                {/* 视图切换区域 */}
                <div className={styles.view_selector_area} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '16px 20px',
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    color: '#fff'
                }}>
                    <div style={{
                        fontSize: '18px',
                        fontWeight: '600',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px'
                    }}>
                        <span>📊</span>
                        考勤数据视图
                    </div>
                    <Space size="middle">
                        <Button
                            type={viewMode === 'table' ? 'primary' : 'default'}
                            icon={<TableOutlined />}
                            onClick={() => setViewMode('table')}
                            style={{
                                height: '36px',
                                fontSize: '14px',
                                fontWeight: '500',
                                borderRadius: '6px',
                                backgroundColor: viewMode === 'table' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.1)',
                                borderColor: 'rgba(255, 255, 255, 0.3)',
                                color: '#fff',
                                boxShadow: viewMode === 'table' ? '0 2px 8px rgba(255, 255, 255, 0.2)' : 'none'
                            }}
                        >
                            {t('viewMode.table', '表格视图')}
                        </Button>
                        <Button
                            type={viewMode === 'calendar' ? 'primary' : 'default'}
                            icon={<CalendarOutlined />}
                            onClick={() => setViewMode('calendar')}
                            style={{
                                height: '36px',
                                fontSize: '14px',
                                fontWeight: '500',
                                borderRadius: '6px',
                                backgroundColor: viewMode === 'calendar' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.1)',
                                borderColor: 'rgba(255, 255, 255, 0.3)',
                                color: '#fff',
                                boxShadow: viewMode === 'calendar' ? '0 2px 8px rgba(255, 255, 255, 0.2)' : 'none'
                            }}
                        >
                            {t('viewMode.calendar', '日历视图')}
                        </Button>
                    </Space>
                </div>

                {/* 内容显示区域 */}
                <div className={styles.content_display_area}>
                {viewMode === 'table' ? (
                    <div className={styles.table_container}>
                        {/* 表格主体容器 */}
                        <div className={styles.table_main_container}>
                            {/* 固定表头容器 */}
                            <div className={`${styles.fixed_header_container} ${styles.table_header_area}`}>
                                <div className={styles.custom_table_header}>
                                    <div className={styles.custom_table_header_row}>
                                        {getTableColumns().map((column, index) => (
                                            <div
                                                key={column.key}
                                                className={`${styles.custom_table_header_cell}`}
                                                style={{ width: column.width }}
                                            >
                                                {column.title}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>

                            {/* 表格滚动区域 */}
                            <div className={styles.table_body_container}>
                                <div className={styles.table_content_wrapper}>
                                    <MobileTable
                                        columns={getTableColumns()
                                            .filter(col => col.key !== 'leave') // 移动端隐藏请假列
                                            .map((col, index) => ({
                                                ...col,
                                                width: col.width
                                            }))}
                                        dataSource={getTableData()}
                                        loading={false}
                                        locale={{
                                            emptyText: tCommon('table.noData', '暂无考勤记录')
                                        }}
                                        pagination={false}
                                        rowClassName={(record: any, index: number) => {
                                            let className = '';
                                            // 添加奇偶行样式 - 参考部门统计页面
                                            className += index % 2 === 0 ? 'table_row_even ' : 'table_row_odd ';

                                            if (record?.day_type == 1 || record?.day_type == 2 || record?.day_type == 3) {
                                                className += 'weekend-holiday ';
                                            }
                                            if (record?.err == 1) {
                                                className += 'exception ';
                                            } else if (record?.err == 2) {
                                                className += 'confirm ';
                                            }
                                            return className.trim();
                                        }}
                                        className="attendance-table-data"
                                        style={{
                                            background: '#ffffff',
                                            border: 'none',
                                            margin: 0,
                                            padding: 0,
                                            width: '100%'
                                        }}
                                        size="small"
                                        bordered={false}
                                        showHeader={false}
                                        tableLayout="fixed"
                                    />
                                </div>
                            </div>
                        </div>

                        {/* PC端分页区域 */}
                        <div className={`${styles.pagination_container} ${styles.pc_only_pagination}`}>
                            <Pagination
                                current={currentPage}
                                pageSize={pageSize}
                                total={(queryResult as any).data_list?.length || 0}
                                onChange={(page) => setCurrentPage(page)}
                                showSizeChanger={false}
                                showQuickJumper={true}
                                pageSizeOptions={['50', '100', '200']}
                                showTotal={(total, range) =>
                                    `${range[0]}-${range[1]} / ${total} ${tCommon('records', '条记录')}`
                                }
                                size="default"
                            />
                        </div>

                    </div>
                ) : (
                    <div style={{
                        height: '100%',
                        overflow: 'hidden',
                        display: 'flex',
                        flexDirection: 'column'
                    }}>
                        <AttendanceCalendar
                            dataSource={getCalendarData()}
                            loading={false}
                            onDateSelect={handleCalendarDateSelect}
                            style={{
                                height: '100%',
                                minHeight: '400px',
                                overflow: 'hidden'
                            }}
                        />
                    </div>
                )}
                </div>
            </div>
        </div>}
        {infData.status=='NG'&&
            <ErrorPart type='NG' message={infData.text} ></ErrorPart>
        }
        </>
    )
}

export default MyAttendance