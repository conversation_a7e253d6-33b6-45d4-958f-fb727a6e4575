// 登录认证相关类型定义

/**
 * 登录请求参数
 */
export interface LoginRequest {
  user_account: string;
  user_password: string;
}

/**
 * 登录响应数据
 */
export interface LoginResponse {
  status: string;
  token?: string;
  message?: string;
  user_info?: {
    user_id: number;
    work_no: string;
    name: string;
    department?: string;
    position?: string;
    email?: string;
  };
}

/**
 * Token验证响应
 */
export interface TokenValidationResponse {
  status: string;
  valid: boolean;
  user_info?: {
    user_id: number;
    work_no: string;
    name: string;
    department?: string;
    position?: string;
    email?: string;
  };
}

/**
 * API响应基础类型
 */
export interface ApiResponse<T = any> {
  status: 'OK' | 'ERROR';
  message?: string;
  data?: T;
}
