// API配置文件

/**
 * API基础配置
 */
export const API_CONFIG = {
  // 基础URL
  BASE_URL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:3000',
  
  // API版本
  VERSION: 'v1',
  
  // 请求超时时间（毫秒）
  TIMEOUT: 30000,
  
  // 重试次数
  RETRY_COUNT: 3,
  
  // 重试延迟（毫秒）
  RETRY_DELAY: 1000
};

/**
 * API端点配置
 */
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/in/login/check',
    LOGOUT: '/in/auth/logout',
    VALIDATE: '/in/auth/validate',
    REFRESH: '/in/auth/refresh'
  },
  
  // 交通费申请相关
  TRANSPORTATION_APPLICATION: {
    LIST: '/in/apply/transportation_fee/get',
    CREATE: '/in/apply/transportation_fee/add',
    UPDATE: '/in/apply/transportation_fee/update',
    DELETE: '/in/apply/transportation_fee/delete'
  },
  
  // 交通费记录相关
  TRANSPORTATION_RECORD: {
    PERSONAL: '/in/record/transportation/personal',
    COLLECTIVE: '/in/record/transportation/collective',
    OVERRIDE: '/in/record/transportation/override'
  },
  
  // 审批相关
  APPROVAL: {
    PENDING: '/in/approval/transportation_fee/pending',
    APPROVE: '/in/approval/transportation_fee/approve',
    BATCH_APPROVE: '/in/approval/transportation_fee/batch_approve',
    STATISTICS: '/in/approval/transportation_fee/statistics',
    HISTORY: '/in/approval/transportation_fee/history'
  },
  
  // 考勤相关
  ATTENDANCE: {
    MY_RECORDS: '/in/attendance/my/records',
    CLOCK: '/in/attendance/my/clock',
    CORRECTION: '/in/attendance/my/correction',
    MONTHLY_SUMMARY: '/in/attendance/my/monthly_summary',
    TODAY: '/in/attendance/my/today'
  },
  
  // 个人信息相关
  PROFILE: {
    GET: '/in/my/profile',
    UPDATE: '/in/my/profile',
    CHANGE_PASSWORD: '/in/my/change_password',
    UPLOAD_AVATAR: '/in/my/upload_avatar',
    COMPLETE_PROFILE: '/in/my/complete_profile',
    SETTINGS: '/in/my/settings'
  }
};

/**
 * HTTP状态码
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
} as const;

/**
 * API响应状态
 */
export const API_STATUS = {
  SUCCESS: 'OK',
  ERROR: 'ERROR'
} as const;

/**
 * 请求头配置
 */
export const REQUEST_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  'X-Requested-With': 'XMLHttpRequest'
};

/**
 * 错误消息映射
 */
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'ネットワークエラーが発生しました',
  TIMEOUT_ERROR: 'リクエストがタイムアウトしました',
  UNAUTHORIZED: '認証が必要です',
  FORBIDDEN: 'アクセス権限がありません',
  NOT_FOUND: 'リソースが見つかりません',
  SERVER_ERROR: 'サーバーエラーが発生しました',
  UNKNOWN_ERROR: '不明なエラーが発生しました'
};

/**
 * 开发环境配置
 */
export const DEV_CONFIG = {
  // 是否启用模拟数据
  ENABLE_MOCK: process.env.NODE_ENV === 'development',
  
  // 是否启用API日志
  ENABLE_LOGGING: process.env.NODE_ENV === 'development',
  
  // 是否启用调试模式
  DEBUG_MODE: process.env.NODE_ENV === 'development'
};
