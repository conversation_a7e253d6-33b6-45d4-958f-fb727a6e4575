{"timestamp": "2025-06-14T03:11:14.098Z", "criticalFiles": [{"path": "src/lib/i18n.ts", "exists": true}, {"path": "src/hooks/useTranslation.ts", "exists": true}, {"path": "pages/_app.tsx", "exists": true}, {"path": "next.config.js", "exists": true}, {"path": "public/locales/zh/errors.json", "exists": true}, {"path": "public/locales/ja/errors.json", "exists": true}], "dependencies": [{"name": "react-i18next", "required": "^13.5.0", "installed": "^13.5.0"}, {"name": "i18next", "required": "^23.7.6", "installed": "^23.7.6"}, {"name": "next-i18next", "required": "^15.2.0", "installed": "^15.2.0"}], "nextConfig": {"exists": true, "hasI18nConfig": true, "strictModeDisabled": true}}