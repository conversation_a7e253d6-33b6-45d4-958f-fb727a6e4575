/**
 * API模块统一导出
 * 整合所有API模块，提供统一的访问接口
 */

// 导出认证模块
export * from './auth';
export { default as authApi } from './auth';

// 导出交通费模块
export * from './transportation';
export { default as transportationApi } from './transportation';

// 导出现有的API工具函数
export * from './fetch-api';

// 导出API配置
export { default as ApiUrlVars } from './common/url-vars';
export { default as ApiFetchVars } from './common/fetch-api-vars';

// 统一的API对象
import authApi from './auth';
import transportationApi from './transportation';

export const api = {
  auth: authApi,
  transportation: transportationApi,
};

export default api;

/**
 * API使用示例：
 * 
 * // 认证相关
 * import { authApi } from '@/api';
 * const loginResult = await authApi.login({ user_account: 'JS1873', user_password: 'password' });
 * 
 * // 交通费申请相关
 * import { transportationApi } from '@/api';
 * const applications = await transportationApi.application.getApplicationList(token);
 * 
 * // 交通费记录相关
 * const records = await transportationApi.records.getPersonalTransportationRecords(token, { date: '2025-06' });
 * 
 * // 使用统一API对象
 * import api from '@/api';
 * const result = await api.auth.login(credentials);
 */
