/**
 * API模块统一导出
 * 整合所有API模块，提供统一的访问接口
 */

// 导出API客户端
export { ApiClient, apiClient, ApiError } from './client';
export type { ApiRequestOptions, ApiResponse } from './client';

// 导出API配置
export {
  API_CONFIG,
  API_ENDPOINTS,
  HTTP_STATUS,
  API_STATUS,
  ERROR_CODES,
  ERROR_MESSAGES,
  ENV_CONFIG,
  DEV_CONFIG,
  // 保持向后兼容
  TRANSPORTATION_ENDPOINTS,
  AUTH_ENDPOINTS
} from './config';

// 导出认证模块
export * from './auth';
export { default as authApi } from './auth';

// 导出交通费模块
export * from './transportation';
export { default as transportationApi } from './transportation';

// 导出现有的API工具函数
export * from './fetch-api';

// 导出API配置（保持向后兼容）
export { default as ApiUrlVars } from './common/url-vars';
export { default as ApiFetchVars } from './common/fetch-api-vars';

// 导出各模块API（新的API结构）
export * as applicationApi from '../pages/application/features/transportationExpense/api/applicationApi';
export * as recordApi from '../pages/statistics/features/transportation/api/recordApi';
export * as approvalApi from '../pages/approval/features/transportationExpense/api/approvalApi';
export * as attendanceApi from '../pages/attendance/features/my/api/attendanceApi';
export * as profileApi from '../pages/my/features/information/api/profileApi';

// 导出类型定义
export type { LoginRequest, LoginResponse, TokenValidationResponse } from '../pages/login/types';
export type {
  TransportationExpenseApplication,
  CreateApplicationRequest,
  UpdateApplicationRequest,
  DeleteApplicationRequest,
  GetApplicationListResponse,
  CreateApplicationResponse,
  UpdateApplicationResponse,
  DeleteApplicationResponse
} from '../pages/application/features/transportationExpense/types';
export type {
  PersonalTransportationRequest,
  CollectiveTransportationRequest,
  OverrideTransportationRequest,
  PersonalTransportationResponse,
  CollectiveTransportationResponse,
  OverrideTransportationResponse
} from '../pages/statistics/features/transportation/types';
export type {
  PendingApproval,
  ApprovalRequest,
  BatchApprovalRequest,
  GetPendingApprovalsResponse,
  ApprovalResponse,
  BatchApprovalResponse,
  ApprovalStatistics
} from '../pages/approval/features/transportationExpense/types';
export type {
  AttendanceRecord,
  ClockRecord,
  GetAttendanceRecordsRequest,
  GetAttendanceRecordsResponse,
  ClockInOutRequest,
  ClockInOutResponse,
  AttendanceCorrectionRequest,
  AttendanceCorrectionResponse
} from '../pages/attendance/features/my/types';
export type {
  UserProfile,
  UpdateProfileRequest,
  ChangePasswordRequest,
  GetProfileResponse,
  UpdateProfileResponse,
  ChangePasswordResponse,
  CompleteProfile,
  UserSettings
} from '../pages/my/features/information/types';

// 统一的API对象
import authApi from './auth';
import transportationApi from './transportation';
import { apiClient } from './client';

export const api = {
  // 现有API模块
  auth: authApi,
  transportation: transportationApi,

  // 新的API客户端
  client: apiClient,
};

export default api;

/**
 * API使用示例：
 *
 * // 现有API模块（保持向后兼容）
 * import { authApi, transportationApi } from '@/api';
 * const loginResult = await authApi.login({ user_account: 'JS1873', user_password: 'password' });
 * const applications = await transportationApi.application.getApplicationList(token);
 *
 * // 新的API模块
 * import { applicationApi, recordApi, approvalApi, attendanceApi, profileApi } from '@/api';
 * const newApplications = await applicationApi.getApplicationList(token, params);
 * const records = await recordApi.getPersonalTransportationRecords(token, params);
 * const approvals = await approvalApi.getPendingApprovals(token, params);
 * const attendance = await attendanceApi.getMyAttendanceRecords(token, params);
 * const profile = await profileApi.getMyProfile(token);
 *
 * // 使用API客户端
 * import { apiClient } from '@/api';
 * const response = await apiClient.get('/some/endpoint');
 *
 * // 使用统一API对象
 * import api from '@/api';
 * const result = await api.auth.login(credentials);
 * const clientResponse = await api.client.post('/endpoint', data);
 */
