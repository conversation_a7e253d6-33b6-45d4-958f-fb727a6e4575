import React, { useState } from 'react';
import { Select } from 'antd';
import { GlobalOutlined } from '@ant-design/icons';
import { useTranslation } from '@/hooks/useTranslation';
import { setGlobalLanguage } from '@/utils/languageManager';
import styles from './LanguageSwitcher.module.css';

interface LanguageSwitcherProps {
  size?: 'small' | 'middle' | 'large';
  style?: React.CSSProperties;
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  size = 'small',
  style = {}
}) => {
  const { currentLanguage, changeLanguage } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const handleLanguageChange = async (value: string) => {
    try {
      console.log('LanguageSwitcher: Changing language to:', value);

      // 使用语言管理器设置全局语言
      setGlobalLanguage(value);

      // 同时更新 i18n
      await changeLanguage(value);

      console.log('LanguageSwitcher: Language change completed');
    } catch (error) {
      console.error('Language change failed:', error);
    }
  };

  return (
    <div className={styles.language_switcher} style={style}>
      <GlobalOutlined className={styles.globe_icon} />
      <Select
        value={currentLanguage}
        onChange={handleLanguageChange}
        size={size}
        bordered={false}
        onDropdownVisibleChange={setIsOpen}
        suffixIcon={
          <span className={`${styles.custom_arrow} ${isOpen ? styles.open : ''}`}>
            ▼
          </span>
        }
        popupClassName="language-dropdown"
        style={{
          minWidth: 90,
          background: 'transparent',
          color: '#fff'
        }}
        options={[
          {
            value: 'zh',
            label: <span style={{ color: '#fff', fontWeight: '500' }}>中文</span>
          },
          {
            value: 'ja',
            label: <span style={{ color: '#fff', fontWeight: '500' }}>日本語</span>
          },
        ]}
      />
    </div>
  );
};

export default LanguageSwitcher;
