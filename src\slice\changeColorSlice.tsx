import { createSlice, PayloadAction, } from "@reduxjs/toolkit";
import { RootState } from "../store";
import ApiTasksTypes from "../api/common/task-types";

// 定义要保存到Store的数据格式
export interface colorState {
    color: string;
  }
   
// 初始化数据
const initialState: colorState = {
    color: 'blue'
};



export const colorSlice = createSlice({
    name: 'color',
    initialState,
    // reducers 集合了reducer和action的功能
    // 每个case冒号之前的为action名
    // 每个case整体为reducer
    reducers: {
        // 使用PayloadAction来展示action.payload的详细内容
        colorChange: (state: colorState, action: PayloadAction<any>) => {
            state.color = action.payload.color;
        },
    },
});

//以下内容必须要有
export const { actions: colorChange } = colorSlice;

export default colorSlice.reducer;

//state 后面的为store中数据名称
export const colorData = (state: RootState) => (state as any).color;