import { getApi, postApi } from '@/api/fetch-api'
import ApiUrlVars from '@/api/common/url-vars'
import { statusActions } from '@/slice/statusSlice'

/**
 * 角色权限信息接口
 */
export interface RolePermission {
  id: string
  name: string
  permissions: string[]
  description?: string
}

/**
 * 节假日信息接口
 */
export interface HolidayInfo {
  id: string
  date: string
  name: string
  type: 'holiday' | 'workday'
  description?: string
}

/**
 * 特殊安排接口
 */
export interface SpecialArrangement {
  id: string
  date: string
  type: string
  description: string
}

/**
 * 邮箱设置接口
 */
export interface MailSettings {
  host: string
  port: number
  username: string
  password: string
  ssl: boolean
}

/**
 * 设置响应接口
 */
export interface SettingResponse {
  status: string
  message?: string
  data?: any
}

/**
 * 获取角色权限信息
 * @param dispatch Redux dispatch函数
 * @returns Promise<SettingResponse>
 */
export async function getRolePermissions(dispatch: any): Promise<SettingResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.setting_roles_get
    const response = await getApi(url, {}, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取角色权限失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get role permissions API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 修改角色权限信息
 * @param roleData 角色权限数据
 * @param dispatch Redux dispatch函数
 * @returns Promise<SettingResponse>
 */
export async function updateRolePermissions(roleData: RolePermission[], dispatch: any): Promise<SettingResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.setting_roles_post
    const params = { roles: roleData }
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '修改角色权限失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '修改角色权限成功'
    }
  } catch (error: any) {
    console.error('Update role permissions API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取节假日信息
 * @param dispatch Redux dispatch函数
 * @returns Promise<SettingResponse>
 */
export async function getHolidayList(dispatch: any): Promise<SettingResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.setting_holiday_list_get
    const response = await getApi(url, {}, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取节假日信息失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get holiday list API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 新增节假日信息
 * @param holidayData 节假日数据
 * @param dispatch Redux dispatch函数
 * @returns Promise<SettingResponse>
 */
export async function addHoliday(holidayData: HolidayInfo, dispatch: any): Promise<SettingResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.setting_holiday_add_post
    const response = await postApi(url, holidayData, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '新增节假日失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '新增节假日成功'
    }
  } catch (error: any) {
    console.error('Add holiday API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 删除节假日信息
 * @param holidayId 节假日ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<SettingResponse>
 */
export async function deleteHoliday(holidayId: string, dispatch: any): Promise<SettingResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.setting_holiday_delete_post
    const params = { holiday_id: holidayId }
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '删除节假日失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '删除节假日成功'
    }
  } catch (error: any) {
    console.error('Delete holiday API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取特殊安排
 * @param dispatch Redux dispatch函数
 * @returns Promise<SettingResponse>
 */
export async function getSpecialArrangements(dispatch: any): Promise<SettingResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.setting_special_list_get
    const response = await getApi(url, {}, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取特殊安排失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get special arrangements API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 新增特殊安排
 * @param arrangementData 特殊安排数据
 * @param dispatch Redux dispatch函数
 * @returns Promise<SettingResponse>
 */
export async function addSpecialArrangement(arrangementData: SpecialArrangement, dispatch: any): Promise<SettingResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.setting_special_add_post
    const response = await postApi(url, arrangementData, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '新增特殊安排失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '新增特殊安排成功'
    }
  } catch (error: any) {
    console.error('Add special arrangement API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 删除特殊安排
 * @param arrangementId 特殊安排ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<SettingResponse>
 */
export async function deleteSpecialArrangement(arrangementId: string, dispatch: any): Promise<SettingResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.setting_special_delete_post
    const params = { arrangement_id: arrangementId }
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '删除特殊安排失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '删除特殊安排成功'
    }
  } catch (error: any) {
    console.error('Delete special arrangement API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取考勤默认查询天数
 * @param dispatch Redux dispatch函数
 * @returns Promise<SettingResponse>
 */
export async function getSearchDays(dispatch: any): Promise<SettingResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.setting_search_day_get
    const response = await getApi(url, {}, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取查询天数设置失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get search days API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 设置考勤默认查询天数
 * @param days 查询天数
 * @param dispatch Redux dispatch函数
 * @returns Promise<SettingResponse>
 */
export async function setSearchDays(days: number, dispatch: any): Promise<SettingResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.setting_search_day_set
    const params = { search_days: days }
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '设置查询天数失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '设置查询天数成功'
    }
  } catch (error: any) {
    console.error('Set search days API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取邮箱设置
 * @param dispatch Redux dispatch函数
 * @returns Promise<SettingResponse>
 */
export async function getMailSettings(dispatch: any): Promise<SettingResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.setting_mail_get
    const response = await getApi(url, {}, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取邮箱设置失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get mail settings API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 修改邮箱设置
 * @param mailSettings 邮箱设置
 * @param dispatch Redux dispatch函数
 * @returns Promise<SettingResponse>
 */
export async function updateMailSettings(mailSettings: MailSettings, dispatch: any): Promise<SettingResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.setting_change_mail_post
    const response = await postApi(url, mailSettings, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '修改邮箱设置失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '修改邮箱设置成功'
    }
  } catch (error: any) {
    console.error('Update mail settings API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}
