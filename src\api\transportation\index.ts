/**
 * 交通费模块统一导出
 */

// 导出类型定义
export * from './types';

// 导出申请相关API
export * from './application';
export { default as transportationApplicationApi } from './application';

// 导出记录相关API
export * from './records';
export { default as transportationRecordsApi } from './records';

// 统一的交通费API对象
import transportationApplicationApi from './application';
import transportationRecordsApi from './records';

export const transportationApi = {
  application: transportationApplicationApi,
  records: transportationRecordsApi,
};

export default transportationApi;
