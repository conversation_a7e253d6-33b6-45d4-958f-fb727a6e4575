import React from "react";
import { useApplicationSelector, useApplicationDispatch } from "@/hook/hooks";
import { tabData, tabActions } from "@/slice/tabSlice";
import styles from '../css/left-cornor.module.css';
import { Link } from "react-router-dom";
import { menuConfig, menuTypes } from "@/utils/menulist";

interface ListItemProps {
  linkTo: string,
  textContent: string,
  liIndex: string,
  tabType : string
}

const ListItem = ({ linkTo, textContent, liIndex, tabType }: ListItemProps) =>  {

  const currentTab = useApplicationSelector(tabData)
  const dispatch = useApplicationDispatch();

  let liColor = styles.menu_item_not_selected

  let tabColor = styles.menu_item_selected_blue

  if (tabType == menuTypes.record){
    tabColor = styles.menu_item_selected_blue
  } else if (tabType == menuTypes.apply){
    tabColor = styles.menu_item_selected_yellow
  }  else if (tabType == menuTypes.approvel){
    tabColor = styles.menu_item_selected_orange
  }  else if (tabType == menuTypes.member){
    tabColor = styles.menu_item_selected_yellow_green
  }  else if (tabType == menuTypes.report){
    tabColor = styles.menu_item_selected_green
  }  else if (tabType == menuTypes.depart){
    tabColor = styles.menu_item_selected_red
  }  else if (tabType == menuTypes.history){
    tabColor = styles.menu_item_selected_light_purple
  }  else if (tabType == menuTypes.setting){
    tabColor = styles.menu_item_selected_purple
  } 

  if(currentTab.subTabIndex == liIndex){
    liColor = tabColor
  }

  const handleClick = () => {
    dispatch(tabActions.onSubChange(liIndex));
  };

  return (
      <Link to={linkTo}>
        <li className={liColor} onClick={() => handleClick()}>
          <div style={{display: 'flex', alignItems: 'center', flex: 1}}>
            <span style={{
              width: '3px',
              height: '16px',
              backgroundColor: currentTab.subTabIndex == liIndex ? 'currentColor' : 'transparent',
              marginRight: '8px',
              borderRadius: '2px'
            }}></span>
            <span style={{flex: 1}}>{textContent}</span>
          </div>
          <span style={{fontSize: '14px', opacity: 0.6}}>&raquo;</span>
        </li>
      </Link>
  );
};

export default ListItem;