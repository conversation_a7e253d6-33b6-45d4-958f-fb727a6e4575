/* 布局修复CSS - 专门解决列表页面布局崩溃问题 */

/* 审批页面布局修复 */
.approval-page-container {
  width: 100% !important;
  height: calc(100vh - 120px) !important;
  min-height: 600px !important;
  min-width: 1080px !important;
  display: flex !important;
  flex-direction: column !important;
  background: #f5f5f5 !important;
  box-sizing: border-box !important;
}

/* 考勤页面布局修复 */
.attendance-page-container {
  width: 100% !important;
  height: 92% !important;
  min-width: 1440px !important;
  box-sizing: border-box !important;
}

/* Tab容器修复 */
.tab-container {
  background: #fff !important;
  width: 98% !important;
  min-width: 1080px !important;
  height: auto !important;
  max-height: calc(100vh - 160px) !important;
  min-height: 600px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  border-radius: 0 0 8px 8px !important;
  line-height: 35px !important;
  margin-top: -16px !important;
  padding-top: 16px !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
}

/* 查询栏修复 */
.search-bar {
  height: 40px !important;
  width: 100% !important;
  display: flex !important;
  box-sizing: border-box !important;
}

.search-keyword {
  width: 20% !important;
  height: 40px !important;
  display: flex !important;
  box-sizing: border-box !important;
}

.search-date {
  width: 60% !important;
  height: 40px !important;
  display: flex !important;
  box-sizing: border-box !important;
}

.search-batch {
  width: 18% !important;
  min-width: 270px !important;
  height: 40px !important;
  display: flex !important;
  box-sizing: border-box !important;
}

/* 弹窗修复 */
.modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  z-index: 1000 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  box-sizing: border-box !important;
}

/* 表格行修复 */
.table-row {
  width: 96% !important;
  border: 1px solid rgb(187, 186, 186) !important;
  border-radius: 5px !important;
  display: flex !important;
  justify-content: center !important;
  text-align: center !important;
  margin-bottom: 5px !important;
  min-height: 40px !important;
  box-sizing: border-box !important;
}

/* 记录容器修复 */
.record-container {
  margin-left: 0px !important;
  width: 100% !important;
  flex: 1 !important;
  min-height: 0 !important;
  margin-top: 10px !important;
  padding-left: 0px !important;
  overflow-y: auto !important;
  box-sizing: border-box !important;
}

/* 导入页面修复 */
.import-container {
  width: 100% !important;
  height: 92% !important;
  min-width: 1440px !important;
  box-sizing: border-box !important;
}

.import-tab-container {
  width: 70% !important;
  background: #fff !important;
  border-radius: 0 5px 5px 5px !important;
  height: 45px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  padding: 0 !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  margin: 0 !important;
  position: relative !important;
  z-index: 1 !important;
  box-sizing: border-box !important;
}

/* 数据导入区域修复 */
.import-data-area {
  border-radius: 5px !important;
  margin-top: 30px !important;
  width: 70% !important;
  height: 50px !important;
  background: white !important;
  display: flex !important;
  align-items: center !important;
  box-sizing: border-box !important;
}

/* 注意事项区域修复 */
.attention-area {
  border-radius: 5px !important;
  margin-top: 10px !important;
  padding-top: 20px !important;
  width: 70% !important;
  height: 470px !important;
  background: white !important;
  box-sizing: border-box !important;
}

/* 非门禁数据容器修复 */
.non-access-container {
  display: flex !important;
  align-items: flex-start !important;
  margin-top: 10px !important;
  box-sizing: border-box !important;
}

.non-access-div {
  width: 500px !important;
  display: inline-block !important;
  margin-top: 20px !important;
  box-sizing: border-box !important;
}

/* 输入示例区域修复 */
.input-example-area {
  width: 800px !important;
  height: 480px !important;
  margin-top: 30px !important;
  background-color: white !important;
  display: inline-block !important;
  border-radius: 5px !important;
  box-sizing: border-box !important;
}

/* 按钮修复 */
.btn-primary {
  background: linear-gradient(135deg, #2B99FF 0%, #59C2FD 100%) !important;
  font-size: 14px !important;
  font-weight: bold !important;
  color: white !important;
  border: none !important;
  border-radius: 5px !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-sizing: border-box !important;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3) !important;
}

.btn-primary:disabled {
  background: linear-gradient(135deg, #4f5052 0%, #3e7b9e 100%) !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

/* 文件选择修复 */
.file-input-hidden {
  display: none !important;
}

/* 考勤页面表格特殊修复 */
.attendance-table-container {
  background: #fff !important;
  border-radius: 6px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* 考勤表格头部强制样式 */
.attendance-table-container .ant-table-thead > tr > th {
  background: linear-gradient(135deg, #4f81ee 0%, #1890ff 100%) !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  text-align: center !important;
  border-bottom: 2px solid #1890ff !important;
  border-right: 1px solid rgba(255, 255, 255, 0.3) !important;
  padding: 14px 16px !important;
  height: 50px !important;
  line-height: 1.4 !important;
  vertical-align: middle !important;
}

.attendance-table-container .ant-table-thead > tr > th:last-child {
  border-right: none !important;
}

/* 考勤表格行样式 - 增加高度和间距 */
.attendance-table-container .ant-table-tbody > tr > td {
  border-bottom: 1px solid #e0e0e0 !important;
  border-right: 1px solid #f0f0f0 !important;
  padding: 14px 16px !important;
  background-color: #ffffff !important;
  font-size: 13px !important;
  line-height: 1.6 !important;
  min-height: 48px !important;
  vertical-align: middle !important;
}

.attendance-table-container .ant-table-tbody > tr > td:last-child {
  border-right: none !important;
}

/* 周末/假日行样式 */
.attendance-table-container .ant-table-tbody > tr.weekend-holiday > td {
  background: linear-gradient(to bottom, #f8f8f8 0%, #f0f0f0 100%) !important;
  border-bottom: 2px solid #d0d0d0 !important;
  border-top: 1px solid #e0e0e0 !important;
}

/* 异常行样式 */
.attendance-table-container .ant-table-tbody > tr.exception > td {
  background-color: #fff2f0 !important;
  border-bottom: 1px solid #ffccc7 !important;
}

/* 确认行样式 */
.attendance-table-container .ant-table-tbody > tr.confirm > td {
  background-color: #fff7e6 !important;
  border-bottom: 1px solid #ffd591 !important;
}

/* 悬停效果 */
.attendance-table-container .ant-table-tbody > tr:hover > td {
  background-color: #e6f7ff !important;
}

/* 表格边框 */
.attendance-table-container .ant-table {
  border: 1px solid #d9d9d9 !important;
  border-radius: 6px !important;
}

.attendance-table-container .ant-table-container {
  border-left: 1px solid #d9d9d9 !important;
  border-right: 1px solid #d9d9d9 !important;
}

.attendance-table-container .ant-table-tbody {
  border-left: 1px solid #d9d9d9 !important;
  border-right: 1px solid #d9d9d9 !important;
}

/* 最后一行 */
.attendance-table-container .ant-table-tbody > tr:last-child > td {
  border-bottom: 2px solid #d9d9d9 !important;
}

/* 分页器样式 */
.attendance-table-container .ant-pagination {
  margin-top: 16px !important;
  text-align: center !important;
}

/* 响应式修复 */
@media (max-width: 1600px) {
  .approval-page-container,
  .tab-container {
    width: 99% !important;
    min-width: 1200px !important;
  }
}

@media (max-width: 1366px) {
  .approval-page-container,
  .tab-container {
    width: 100% !important;
    min-width: 1080px !important;
    margin: 10px auto !important;
    border-radius: 6px !important;
  }
}

@media (max-width: 1200px) {
  .approval-page-container,
  .tab-container {
    width: 100% !important;
    min-width: 1000px !important;
    margin: 8px auto !important;
    padding: 16px !important;
  }

  .search-keyword {
    width: 25% !important;
  }

  .search-date {
    width: 55% !important;
  }

  .search-batch {
    width: 20% !important;
    min-width: 250px !important;
  }
}
