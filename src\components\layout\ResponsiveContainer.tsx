import React from 'react';
import styles from './ResponsiveContainer.module.css';

interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  fluid?: boolean;
}

/**
 * 响应式容器组件
 * 提供统一的容器布局，支持不同的最大宽度和内边距设置
 */
const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className = '',
  maxWidth = 'xl',
  padding = 'md',
  fluid = false
}) => {
  const containerClasses = [
    styles.container,
    styles[`maxWidth_${maxWidth}`],
    styles[`padding_${padding}`],
    fluid ? styles.fluid : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {children}
    </div>
  );
};

export default ResponsiveContainer;
