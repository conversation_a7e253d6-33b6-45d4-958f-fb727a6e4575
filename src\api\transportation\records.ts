/**
 * 交通费记录模块API
 */

import {
  PersonalRecordRequest,
  PersonalRecordResponse,
  CollectiveRecordRequest,
  CollectiveRecordResponse,
  OverrideRecordRequest,
  OverrideRecordResponse,
} from './types';
import { API_CONFIG, TRANSPORTATION_ENDPOINTS } from '../config';

// API基础URL - 从配置文件获取
const API_BASE_URL = API_CONFIG.BASE_URL;

/**
 * 通用API请求函数
 */
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;

  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  };

  const response = await fetch(url, { ...defaultOptions, ...options });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
}

/**
 * 个人交通费报销集计（按照月份 YYYY-MM）
 * POST /in/record/transportation/personal
 */
export async function getPersonalTransportationRecords(
  token: string,
  data: PersonalRecordRequest
): Promise<PersonalRecordResponse> {
  try {
    const response = await apiRequest<PersonalRecordResponse>(
      TRANSPORTATION_ENDPOINTS.RECORDS.PERSONAL,
      {
        method: 'POST',
        headers: {
          'Authorization': token,
        },
        body: JSON.stringify(data),
      }
    );

    return response;
  } catch (error) {
    console.error('Get personal transportation records error:', error);
    throw new Error('获取个人交通费记录失败');
  }
}

/**
 * 总交通费报销集计（按照自然月，如果选择的当前月，只计算到当前时间 YYYY-MM）
 * POST /in/record/transportation/collective
 */
export async function getCollectiveTransportationRecords(
  token: string,
  data: CollectiveRecordRequest
): Promise<CollectiveRecordResponse> {
  try {
    const response = await apiRequest<CollectiveRecordResponse>(
      TRANSPORTATION_ENDPOINTS.RECORDS.COLLECTIVE,
      {
        method: 'POST',
        headers: {
          'Authorization': token,
        },
        body: JSON.stringify(data),
      }
    );

    return response;
  } catch (error) {
    console.error('Get collective transportation records error:', error);
    throw new Error('获取总交通费记录失败');
  }
}

/**
 * 修改个人数据
 * POST /in/record/transportation/override
 */
export async function overrideTransportationRecord(
  token: string,
  data: OverrideRecordRequest
): Promise<OverrideRecordResponse> {
  try {
    const response = await apiRequest<OverrideRecordResponse>(
      TRANSPORTATION_ENDPOINTS.RECORDS.OVERRIDE,
      {
        method: 'POST',
        headers: {
          'Authorization': token,
        },
        body: JSON.stringify(data),
      }
    );

    return response;
  } catch (error) {
    console.error('Override transportation record error:', error);
    throw new Error('修改交通费记录失败');
  }
}

/**
 * 获取用户交通费统计摘要
 * 基于个人记录计算统计信息
 */
export async function getTransportationSummary(
  token: string,
  date: string
): Promise<{
  total_amount: number;
  monthly_amount: number;
  single_amount: number;
  work_days: number;
  monthly_days: number;
  single_days: number;
}> {
  try {
    const records = await getPersonalTransportationRecords(token, { date });
    
    let totalAmount = 0;
    let monthlyAmount = 0;
    let singleAmount = 0;
    let workDays = records.records.length;
    let monthlyDays = 0;
    let singleDays = 0;

    records.records.forEach(record => {
      if (record.reimbursement_method.monthly) {
        monthlyAmount += record.reimbursement_method.monthly;
        monthlyDays++;
      }
      if (record.reimbursement_method.single) {
        singleAmount += record.reimbursement_method.single;
        singleDays++;
      }
    });

    totalAmount = monthlyAmount + singleAmount;

    return {
      total_amount: totalAmount,
      monthly_amount: monthlyAmount,
      single_amount: singleAmount,
      work_days: workDays,
      monthly_days: monthlyDays,
      single_days: singleDays,
    };
  } catch (error) {
    console.error('Get transportation summary error:', error);
    throw new Error('获取交通费统计摘要失败');
  }
}

// 导出所有交通费记录相关的API函数
export const transportationRecordsApi = {
  getPersonalTransportationRecords,
  getCollectiveTransportationRecords,
  overrideTransportationRecord,
  getTransportationSummary,
};

export default transportationRecordsApi;
