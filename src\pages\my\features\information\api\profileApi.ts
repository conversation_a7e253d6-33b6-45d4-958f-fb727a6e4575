// 个人信息API实现

import {
  UserProfile,
  UpdateProfileRequest,
  ChangePasswordRequest,
  GetProfileResponse,
  UpdateProfileResponse,
  ChangePasswordResponse,
  UploadAvatarRequest,
  UploadAvatarResponse,
  CompleteProfile,
  UserSettings
} from '../types';

/**
 * 获取个人信息
 * GET /in/my/profile
 */
export const getMyProfile = async (token: string): Promise<GetProfileResponse> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/my/profile', {
  //   method: 'GET',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    profile: {
      user_id: 553,
      work_no: 'JS1640',
      name: '朱国威',
      name_kana: 'シュ コクイ',
      email: '<EMAIL>',
      phone: '+81-90-1234-5678',
      department: '技术部',
      position: '高级开发工程师',
      hire_date: '2022-04-01',
      birth_date: '1990-05-15',
      gender: 'male',
      nationality: '中国',
      address: '大阪府大阪市中央区难波1-1-1',
      emergency_contact: {
        name: '朱某某',
        relationship: '配偶',
        phone: '+81-90-8765-4321',
        email: '<EMAIL>'
      },
      avatar_url: '/avatars/user_553.jpg',
      status: 'active',
      created_at: '2022-04-01T09:00:00Z',
      updated_at: '2024-06-01T10:30:00Z'
    }
  };
};

/**
 * 更新个人信息
 * PUT /in/my/profile
 */
export const updateMyProfile = async (
  token: string,
  data: UpdateProfileRequest
): Promise<UpdateProfileResponse> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/my/profile', {
  //   method: 'PUT',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  //   body: JSON.stringify(data),
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    message: '个人信息更新成功'
  };
};

/**
 * 更改密码
 * POST /in/my/change_password
 */
export const changePassword = async (
  token: string,
  data: ChangePasswordRequest
): Promise<ChangePasswordResponse> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/my/change_password', {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  //   body: JSON.stringify(data),
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    message: '密码修改成功'
  };
};

/**
 * 上传头像
 * POST /in/my/upload_avatar
 */
export const uploadAvatar = async (
  token: string,
  data: UploadAvatarRequest
): Promise<UploadAvatarResponse> => {
  // TODO: 实现实际的API调用
  // const formData = new FormData();
  // formData.append('avatar', data.file);
  
  // const response = await fetch('/in/my/upload_avatar', {
  //   method: 'POST',
  //   headers: {
  //     'Authorization': token,
  //   },
  //   body: formData,
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    message: '头像上传成功',
    avatar_url: '/avatars/user_553_new.jpg'
  };
};

/**
 * 获取完整个人档案
 * GET /in/my/complete_profile
 */
export const getCompleteProfile = async (
  token: string
): Promise<{ status: 'OK' | 'ERROR'; profile: CompleteProfile; message?: string }> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/my/complete_profile', {
  //   method: 'GET',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    profile: {
      basic_info: {
        user_id: 553,
        work_no: 'JS1640',
        name: '朱国威',
        name_kana: 'シュ コクイ',
        email: '<EMAIL>',
        phone: '+81-90-1234-5678',
        department: '技术部',
        position: '高级开发工程师',
        hire_date: '2022-04-01',
        birth_date: '1990-05-15',
        gender: 'male',
        nationality: '中国',
        address: '大阪府大阪市中央区难波1-1-1',
        status: 'active',
        created_at: '2022-04-01T09:00:00Z',
        updated_at: '2024-06-01T10:30:00Z'
      },
      work_history: [
        {
          id: 'wh_001',
          user_id: 553,
          department: '技术部',
          position: '高级开发工程师',
          start_date: '2023-04-01',
          description: '负责前端开发和系统架构设计',
          created_at: '2023-04-01T09:00:00Z'
        },
        {
          id: 'wh_002',
          user_id: 553,
          department: '技术部',
          position: '开发工程师',
          start_date: '2022-04-01',
          end_date: '2023-03-31',
          description: '参与项目开发和维护',
          created_at: '2022-04-01T09:00:00Z'
        }
      ],
      skills: [
        {
          id: 'skill_001',
          name: 'React',
          category: '前端开发',
          level: 'advanced',
          years_of_experience: 3
        },
        {
          id: 'skill_002',
          name: 'TypeScript',
          category: '编程语言',
          level: 'advanced',
          years_of_experience: 2
        }
      ],
      education: [
        {
          id: 'edu_001',
          institution: '某某大学',
          degree: '学士',
          major: '计算机科学',
          start_date: '2008-09-01',
          end_date: '2012-06-30',
          gpa: 3.8
        }
      ],
      languages: [
        {
          id: 'lang_001',
          language: '中文',
          proficiency: 'native'
        },
        {
          id: 'lang_002',
          language: '日语',
          proficiency: 'business',
          certification: 'JLPT N2'
        },
        {
          id: 'lang_003',
          language: '英语',
          proficiency: 'conversational'
        }
      ]
    }
  };
};

/**
 * 获取个人设置
 * GET /in/my/settings
 */
export const getMySettings = async (
  token: string
): Promise<{ status: 'OK' | 'ERROR'; settings: UserSettings; message?: string }> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/my/settings', {
  //   method: 'GET',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    settings: {
      user_id: 553,
      language: 'zh',
      timezone: 'Asia/Tokyo',
      date_format: 'YYYY-MM-DD',
      time_format: '24h',
      notifications: {
        email_notifications: true,
        push_notifications: true,
        attendance_reminders: true,
        approval_notifications: true,
        system_updates: false
      },
      privacy: {
        profile_visibility: 'department',
        show_contact_info: true,
        show_work_history: true,
        allow_search: true
      }
    }
  };
};

/**
 * 更新个人设置
 * PUT /in/my/settings
 */
export const updateMySettings = async (
  token: string,
  settings: Partial<UserSettings>
): Promise<{ status: 'OK' | 'ERROR'; message: string }> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/my/settings', {
  //   method: 'PUT',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  //   body: JSON.stringify(settings),
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    message: '设置更新成功'
  };
};
