import { createSlice, PayloadAction, createAsyncThunk } from "@reduxjs/toolkit";
import { RootState } from "../../store";
import ApiTasksTypes from "../../api/common/task-types";
import ApiFetchVars from "../../api/common/fetch-api-vars";
import ApiUrlVars from "../../api/common/url-vars";

// 定义要保存到Store的数据格式
export interface recentHistoryState {
    value: number;
    status: string;
    tasks: string;
    list: [];
  }
   
// 初始化数据
const initialState: recentHistoryState = {
    value: 0,
    status: ApiFetchVars.todo,
    tasks: '',
    list: []
};

//定义需要的变量

export const recentHistorySlice = createSlice({
    name: 'recentHistory',
    initialState,
    // reducers 集合了reducer和action的功能
    // 每个case冒号之前的为action名
    // 每个case整体为reducer
    reducers: {
        // 使用PayloadAction来展示action.payload的详细内容
        setDataWhenInit: (state: recentHistoryState, action: PayloadAction<any>) => {
            state.value = 200
            state.status = action.payload.status;
            state.list = action.payload.task_inodes;
        },
    }
});

//以下内容必须要有
export const { actions: recentHistoryActions } = recentHistorySlice;

export default recentHistorySlice.reducer;

//state 后面的为store中数据名称
export const recentHistoryData = (state: RootState) => state.recentHistory;