import React, { useState } from 'react';
import Image from 'next/image';

const HoverImage = ({ src, hoverSrc }: { src: string; hoverSrc: string }) => {
  const [imageSrc, setImageSrc] = useState(src);

  const handleMouseOver = () => {
    setImageSrc(hoverSrc);
  };

  const handleMouseOut = () => {
    setImageSrc(src);
  };

  return (
    <Image
      width={18} height={18} 
      src={imageSrc}
      onMouseOver={handleMouseOver}
      onMouseOut={handleMouseOut}
      alt=""
      priority
    />
  );
};

export default HoverImage