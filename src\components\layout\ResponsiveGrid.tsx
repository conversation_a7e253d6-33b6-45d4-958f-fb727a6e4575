import React from 'react';
import styles from './ResponsiveGrid.module.css';

export interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  columns?: number;
  gap?: 'sm' | 'md' | 'lg';
  responsive?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
}

export interface ResponsiveGridItemProps {
  children: React.ReactNode;
  className?: string;
  span?: number;
  responsive?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
}

/**
 * 响应式网格容器组件
 */
export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className = '',
  columns = 12,
  gap = 'md',
  responsive
}) => {
  const gridClasses = [
    styles.grid,
    styles[`gap_${gap}`],
    className
  ].filter(Boolean).join(' ');

  const gridStyle: React.CSSProperties = {
    '--grid-columns': columns.toString(),
    ...(responsive && {
      '--grid-columns-xs': responsive.xs?.toString() || columns.toString(),
      '--grid-columns-sm': responsive.sm?.toString() || columns.toString(),
      '--grid-columns-md': responsive.md?.toString() || columns.toString(),
      '--grid-columns-lg': responsive.lg?.toString() || columns.toString(),
      '--grid-columns-xl': responsive.xl?.toString() || columns.toString(),
    })
  } as React.CSSProperties;

  return (
    <div className={gridClasses} style={gridStyle}>
      {children}
    </div>
  );
};

/**
 * 响应式网格项组件
 */
export const ResponsiveGridItem: React.FC<ResponsiveGridItemProps> = ({
  children,
  className = '',
  span = 1,
  responsive
}) => {
  const itemClasses = [
    styles.gridItem,
    className
  ].filter(Boolean).join(' ');

  const itemStyle: React.CSSProperties = {
    '--item-span': span.toString(),
    ...(responsive && {
      '--item-span-xs': responsive.xs?.toString() || span.toString(),
      '--item-span-sm': responsive.sm?.toString() || span.toString(),
      '--item-span-md': responsive.md?.toString() || span.toString(),
      '--item-span-lg': responsive.lg?.toString() || span.toString(),
      '--item-span-xl': responsive.xl?.toString() || span.toString(),
    })
  } as React.CSSProperties;

  return (
    <div className={itemClasses} style={itemStyle}>
      {children}
    </div>
  );
};

export default ResponsiveGrid;
