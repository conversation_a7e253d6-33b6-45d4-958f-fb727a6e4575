{"buttons": {"save": "保存", "cancel": "キャンセル", "confirm": "確定", "delete": "削除", "edit": "編集", "add": "追加", "search": "検索", "reset": "リセット", "submit": "送信", "back": "戻る", "contactDeveloper": "開発者に連絡", "logout": "ログアウト"}, "labels": {"user": "ユーザー", "department": "部門", "email": "メール", "name": "氏名", "workNo": "社員番号", "role": "役割", "status": "ステータス", "date": "日付", "time": "時間", "employeeInfo": "氏名/社員番号", "attendanceDate": "出勤日付", "month": "月", "actions": "操作"}, "date": "日付", "leave": "休暇", "overtime": "残業", "remarks": "備考", "nameWorkNo": "氏名/社員番号", "workNoName": "社員番号-氏名", "startDate": "開始日付", "endDate": "終了日付", "attendanceDate": "勤怠日付", "startTime": "開始時間", "endTime": "終了時間", "dataModification": "データ修正", "attendanceDataNotImported": "勤怠データが未インポート", "workLocation": "勤務地", "office": "オフィス", "workFromHome": "在宅勤務", "businessTrip": "出張", "otherLeave": "その他休暇", "none": "なし", "compensatoryLeave": "振替休暇", "personalLeave": "事由休暇", "sickLeave": "病気休暇", "leaveExceedsLimit": "休暇合計が8時間を超えています", "updateAttendanceInfo": "勤怠情報更新", "processFailed": "処理失敗、再実行するか開発者にお問い合わせください", "messages": {"loading": "読み込み中...", "noData": "データがありません", "success": "操作が成功しました", "error": "操作が失敗しました", "confirm": "この操作を実行しますか？", "noAttendanceRecords": "現在勤怠記録がありません", "noDisplayData": "現在表示可能なデータがありません"}, "table": {"noData": "データがありません", "noDisplayData": "現在表示可能なデータがありません"}, "placeholders": {"selectMonth": "月を選択"}, "common": {"none": "なし", "remarks": "備考", "date": "日付", "leave": "休暇", "overtime": "残業", "workFromHome": "在宅勤務", "businessTrip": "出張", "otherLeave": "その他の休暇。資料が完全に提出されているかご注意ください", "startDate": "開始日付", "endDate": "終了日付", "nameWorkNo": "氏名/社員番号", "attendanceDataNotImported": "勤怠データが未インポート", "workNoName": "社員番号-氏名", "attendanceDate": "勤怠日付", "startTime": "開始時間", "endTime": "終了時間", "dataModification": "データ修正", "leaveExceedsLimit": "休暇合計が8時間を超えています", "save": "保存", "cancel": "キャンセル", "updateAttendanceInfo": "勤怠情報更新", "processFailed": "処理失敗、再実行するか開発者にお問い合わせください", "compensatoryLeave": "振替休日", "personalLeave": "事務休暇", "sickLeave": "病気休暇", "paperDocumentErrorData": "紙文書エラーデータ", "homeOfficeErrorData": "在宅勤務エラーデータ", "homeOfficeTemplate": "在宅勤務テンプレート", "batchProcessingTemplate": "一括処理テンプレート"}, "statistics": {"detailInfo": "詳細情報", "leaveHours": "休暇(単位:時間)", "overtimeHours": "残業(単位:時間)", "start": "開始", "end": "終了", "departmentStatistics": "部門統計データ", "statisticsMonth": "統計月", "statisticsData": "統計データ", "export": "エクスポート", "workNoName": "工号/姓名", "department": "部門", "absence": "欠勤", "workDays": "出勤日数", "compensatoryHours": "振替休暇時間", "personalLeaveHours": "事由休暇時間", "sickLeaveHours": "病気休暇時間", "weekdayOvertime": "平日残業", "weekendOvertime": "休日残業", "holidayOvertime": "祝日残業", "other": "その他", "detail": "詳細", "employeeInfo": "社員情報", "leaveRecords": "休暇記録", "overtimeRecords": "残業記録", "noLeaveRecords": "休暇記録がありません", "noOvertimeRecords": "残業記録がありません", "close": "閉じる", "records": "件"}, "details": "詳細", "departmentManager": "部門責任者", "seniorManagement": "上級管理層", "noData": "データなし", "login": {"title": "勤怠管理システム", "usernameRequired": "ユーザー名を入力してください", "usernamePlaceholder": "ユーザー名", "passwordRequired": "パスワードを入力してください", "passwordPlaceholder": "パスワード", "rememberMe": "ログイン状態を保持", "login": "ログイン", "forgotPassword": "パスワードを忘れましたか？"}, "menu": "メニュー", "menuItems": {"profile": "マイページ", "attendance": "勤怠機能", "application": "申請機能", "approval": "承認確認", "statistics": "データ統計", "userInfo": "個人情報", "myRecord": "照会", "transportationExpense": "交通費申請", "transportationApproval": "交通費個人承認", "departmentStats": "部門統計"}, "transportationExpenseDetails": "交通費詳細", "userDetails": "ユーザー詳細", "save": "保存", "close": "閉じる", "loading": "読み込み中", "userInfo": "ユーザー情報", "workNoAndName": "社員番号氏名", "position": "職位", "transportationExpenseManagement": "交通費管理", "commuterPassAndSingleTicketClassification": "定期券と単発券の分類", "commuterPass": "定期券", "singleTicket": "単発券", "convertToCommuterPass": "定期券に変換", "convertToSingleTicket": "単発券に変換", "selectRoute": "路線を選択", "moveToSingleTicket": "単発券に移動", "moveToCommuterPass": "定期券に移動", "commuterPassRecordCount": "定期券記録数", "commuterPassTotalCost": "定期券総費用", "singleTicketRecordCount": "単発券記録数", "singleTicketTotalCost": "単発券総費用", "totalRecordCount": "総記録数", "totalCost": "総費用"}