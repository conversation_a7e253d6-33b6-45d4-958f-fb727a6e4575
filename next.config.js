/** @type {import('next').NextConfig} */
module.exports = {
  async rewrites() {
    return routerConfig
  },
  output: 'standalone',

  // 禁用 React Strict Mode 以避免多语言初始化问题
  reactStrictMode: false,

  // 国际化配置
  i18n: {
    // 支持的语言列表
    locales: ['zh', 'ja'],
    // 默认语言
    defaultLocale: 'zh',
    // 禁用自动语言检测，由用户手动选择
    localeDetection: false,
  },
}

const routerConfig = [
  {
      source: '/record/my',
      destination: '/attendance/attendanceimport1',
  },
  {
      source: '/record/query',
      destination: '/attendance/attendanceimport2',
  },
  {
      source: '/record/edit',
      destination: '/attendance/attendanceimport3',
  },
  {
      source: '/record/import',
      destination: '/attendance/attendanceimport',
  },
  {
      source: '/approve/tpc',
      destination: '/approval/tpc',
  },
  {
      source: '/apply/l',
      destination: '/application/leaveApplication',
  },
  {
      source: '/apply/o',
      destination: '/application/overtimeApplication',
  },
  {
      source: '/apply/b',
      destination: '/application/businessTripApplication',
  },
  {
      source: '/apply/s',
      destination: '/application/confirmationApplication',
  },
  {
      source: '/apply/tf',
      destination: '/application/transportationExpenseApplication',
  },
  {
      source: '/approve/l',
      destination: '/approval/leaveApproval',
  },
  {
      source: '/approve/o',
      destination: '/approval/overtimeApproval',
  },
  {
      source: '/approve/b',
      destination: '/approval/evectionApproval',
  },
  {
      source: '/approve/s',
      destination: '/approval/confirmationApproval',
  },
  {
      source: '/mem/dep',
      destination: '/members/department',
  },
  {
      source: '/mem/org',
      destination: '/members/organization',
  },  
  {
      source: '/sum/app',
      destination: '/statistics/applicationStatistics',
  },  
  {
      source: '/sum/emp',
      destination: '/statistics/personalStatistics',
  },  
  {
      source: '/sum/dep',
      destination: '/statistics/departmentStatistics',
  },  
  {
      source: '/sum/org',
      destination: '/statistics/tissueStatistics',
  },  
  {
      source: '/sum/com',
      destination: '/statistics/paidLeaveStatistics',
  },  
  {
      source: '/task/my',
      destination: '/operationRecords/myRecords',
  },  
  {
      source: '/task/all',
      destination: '/operationRecords/allRecords',
  },  
  {
      source: '/set/nwd',
      destination: '/settings/holidaySettings',
  },  
  {
      source: '/set/ml',
      destination: '/settings/mailSettings',
  },  
  {
      source: '/set/rl',
      destination: '/settings/roleLimitsSettings',
  },  
  {
      source: '/set/wf',
      destination: '/settings/approvalProcessSettings',
  },  
  {
      source: '/set/oth',
      destination: '/settings/otherSettings',
  },
  {
      source: '/profile/paidLeave',
      destination: '/profile/paidLeave',
  },
]
