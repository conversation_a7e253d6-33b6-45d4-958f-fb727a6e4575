/* 响应式表格样式 */
.responsiveTable {
  width: 100%;
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.tableWrapper {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
}

.table {
  width: 100%;
  min-width: 100%;
}

/* 桌面端样式 */
.desktop .table {
  min-width: auto;
}

/* 移动端样式 */
.mobile .tableWrapper {
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary-light) var(--bg-secondary);
}

.mobile .tableWrapper::-webkit-scrollbar {
  height: 6px;
}

.mobile .tableWrapper::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 3px;
}

.mobile .tableWrapper::-webkit-scrollbar-thumb {
  background: var(--color-primary-light);
  border-radius: 3px;
}

.mobile .tableWrapper::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

/* 移动端卡片视图 */
.mobileCards {
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.mobileCard {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
}

.mobileCard:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.cardRow {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid var(--border-color-light);
}

.cardRow:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.cardLabel {
  font-weight: 600;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  flex: 0 0 40%;
  margin-right: var(--spacing-md);
}

.cardValue {
  color: var(--text-primary);
  font-size: var(--font-size-md);
  flex: 1;
  text-align: right;
  word-break: break-word;
}

/* 表格全局样式覆盖 */
.responsiveTable :global(.ant-table) {
  border: none !important;
  background: var(--bg-primary) !important;
}

.responsiveTable :global(.ant-table-container) {
  border: none !important;
  background: var(--bg-primary) !important;
}

.responsiveTable :global(.ant-table-thead > tr > th) {
  background: var(--bg-gradient-primary) !important;
  color: var(--text-white) !important;
  font-weight: 600 !important;
  font-size: var(--font-size-sm) !important;
  text-align: center !important;
  border-bottom: 2px solid var(--color-primary) !important;
  border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
  padding: var(--spacing-lg) var(--spacing-md) !important;
  height: 60px !important;
  line-height: var(--line-height-tight) !important;
  vertical-align: middle !important;
  letter-spacing: 0.5px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.responsiveTable :global(.ant-table-thead > tr > th:last-child) {
  border-right: none !important;
}

.responsiveTable :global(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid var(--border-color-light) !important;
  border-right: 1px solid var(--border-color-light) !important;
  padding: var(--spacing-md) !important;
  background-color: var(--bg-primary) !important;
  font-size: var(--font-size-sm) !important;
  line-height: var(--line-height-tight) !important;
  height: 48px !important;
  vertical-align: middle !important;
  text-align: center !important;
  color: var(--text-primary) !important;
  transition: var(--transition-normal) !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.responsiveTable :global(.ant-table-tbody > tr > td:last-child) {
  border-right: none !important;
}

.responsiveTable :global(.ant-table-tbody > tr:hover > td) {
  background: linear-gradient(135deg, #e8f2ff 0%, #f0f4ff 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-sm) !important;
  border-color: #d6e3ff !important;
}

/* 响应式断点 */
@media (max-width: 480px) {
  .mobileCards {
    padding: var(--spacing-sm);
    gap: var(--spacing-sm);
  }
  
  .mobileCard {
    padding: var(--spacing-md);
  }
  
  .cardLabel {
    flex: 0 0 35%;
    font-size: var(--font-size-xs);
  }
  
  .cardValue {
    font-size: var(--font-size-sm);
  }
}
