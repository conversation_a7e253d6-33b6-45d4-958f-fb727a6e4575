import React from "react";
import { useApplicationSelector, useApplicationDispatch } from "@/hook/hooks";
import { approvalData, approvalActions } from "@/slice/approvalSlice";
import styles from '../css/approvalList.module.css';
import { getApprovalConfig } from '@/utils/approvallist';
import Image from 'next/image';
import { useTranslation } from '@/hooks/useTranslation';

interface ApprovalFormProps {
  title: string;
}
const ApprovalList = ({ title }: ApprovalFormProps) => {
  const currentTab = useApplicationSelector(approvalData)

  const dispatch = useApplicationDispatch();
  const handleTabClick = (key: string) => {
    dispatch(approvalActions.onTabChange(key));
  };

  // 多语言支持
  const { t } = useTranslation(['approval']);
  const tApproval = (key: string, fallback?: string) => t(`approval:${key}`, fallback);

  // 获取多语言审批配置
  const approvalConfig = getApprovalConfig();

  return (
    <div className={styles.record_progress}>
      <div className={styles.progress_title}>
        <span className={styles.progress_title_font}>{title}</span>
      </div>
      <div>
        <ul className={styles.user_menu_tab_ul}>
          <li onClick={() => handleTabClick(approvalConfig.approvalAll.title)}>
            <Image src={currentTab.title === approvalConfig.approvalAll.title ? "/image/icon/all-application-blue.png" : "/image/icon/all-application.png"} alt={tApproval('tabs.all', '全部')} width={19} height={19} priority></Image>
            {
              currentTab.title === approvalConfig.approvalAll.title ?<div style={{color:'#2A82E4'}}>{approvalConfig.approvalAll.title}</div> :<div>{approvalConfig.approvalAll.title}</div>
            }
          </li>
          <li onClick={() => handleTabClick(approvalConfig.approvalNull.title)}>
            <Image src={currentTab.title === approvalConfig.approvalNull.title ? "/image/icon/hourglass-null-blue.png" : "/image/icon/hourglass-null.png"} alt={tApproval('tabs.pending', '待审批')} width={18} height={18} priority></Image>
            {
              currentTab.title === approvalConfig.approvalNull.title ?<div style={{color:'#2A82E4'}}>{approvalConfig.approvalNull.title}</div> :<div>{approvalConfig.approvalNull.title}</div>
            }
          </li>
          <li onClick={() => handleTabClick(approvalConfig.approvalFull.title)}>
            <Image src={currentTab.title === approvalConfig.approvalFull.title ? "/image/icon/hourglass-full-blue.png" : "/image/icon/hourglass-full.png"} alt={tApproval('tabs.approved', '已同意')} width={18} height={18} priority></Image>
            {
              currentTab.title === approvalConfig.approvalFull.title ?<div style={{color:'#2A82E4'}}>{approvalConfig.approvalFull.title}</div> :<div>{approvalConfig.approvalFull.title}</div>
            }
          </li>
          <li onClick={() => handleTabClick(approvalConfig.approvalReject.title)}>
            <Image src={currentTab.title === approvalConfig.approvalReject.title ? "/image/icon/reject-blue.png" : "/image/icon/reject.png"} alt={tApproval('tabs.rejected', '已驳回')} width={18} height={18} priority></Image>
            {
              currentTab.title === approvalConfig.approvalReject.title ?<div style={{color:'#2A82E4'}}>{approvalConfig.approvalReject.title}</div> :<div>{approvalConfig.approvalReject.title}</div>
            }
          </li>
        </ul>
      </div>
    </div>

  );
};

export default ApprovalList;