import { createSlice, PayloadAction, } from "@reduxjs/toolkit";
import { RootState } from "../store";
import { getTabMenuByType, menuConfig, menuTypes } from "../utils/menulist";
import { getCurrentLanguage } from "../utils/languageManager";

// 定义要保存到Store的数据格式
export interface tabState {
    tabType: string,
    subTabIndex: string,
    selectedTabMenu: any,
    columns: {
        key: any,
        content: any,
        target: any,
        count: any,
    }[]
  }
   
// 获取安全的初始菜单数据
const getInitialTabMenu = () => {
    try {
        // 尝试获取当前语言的菜单，如果失败则使用默认中文
        return getTabMenuByType(menuTypes.record);
    } catch (error) {
        console.warn('Failed to get initial tab menu, using fallback');
        return getTabMenuByType(menuTypes.record);
    }
};

// 初始化数据
const initialState: tabState = {
    tabType: menuTypes.record,
    subTabIndex : "1",
    selectedTabMenu: getInitialTabMenu(),
    columns: [],
};

export const tabSlice = createSlice({
    name: 'tab',
    initialState,
    // reducers 集合了reducer和action的功能
    // 每个case冒号之前的为action名
    // 每个case整体为reducer
    reducers: {
        // 使用PayloadAction来展示action.payload的详细内容
        onTabChange: (state: tabState, action: PayloadAction<any>) => {
            state.tabType = action.payload;
            state.subTabIndex = "1";
            // 注意：不在这里更新 selectedTabMenu，让 left-cornor 组件负责更新多语言菜单
            // 这样可以避免覆盖已经设置好的多语言菜单数据
            // state.selectedTabMenu = getTabMenuByType(action.payload)
        },
        onSubChange: (state: tabState, action: PayloadAction<any>) => {
            state.subTabIndex = action.payload;
        },
        onTabMenuChange: (state: tabState, action: PayloadAction<any>) => {
            state.selectedTabMenu = action.payload;
        },
        onTabColumns: (state: tabState, action: PayloadAction<any>) => {
            if(action.payload?.message!=null){
                let count = 0;
                state.columns.map(item => {
                    if(item.content == action.payload.message){
                        count++;
                        item.count++;
                    }
                })
                if(count == 0){
                    state.columns.push({
                        key: state.columns.length,
                        content: action.payload.message,
                        target: action.payload.target,
                        count: 1,
                    });
                }
            }
            
        },
        dynamicClick: (state: tabState, action: PayloadAction<any>) => {
            state.columns.map(item => {
                if(item.key == action.payload){
                    item.count = 0;
                }
            })
        }
    },
});

//以下内容必须要有
export const { actions: tabActions } = tabSlice;

export default tabSlice.reducer;

//state 后面的为store中数据名称
export const tabData = (state: RootState) => state.tab;
