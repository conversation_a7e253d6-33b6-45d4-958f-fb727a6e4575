# React应用的Dockerfile
ARG BASE_BUILD_IMAGE=node:18-alpine

FROM $BASE_BUILD_IMAGE AS base

RUN npm config set registry https://registry.npmmirror.com/

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package-react.json package.json
COPY package-lock.json* ./
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# 复制环境变量文件
COPY .env* ./

RUN npm run build

# Production image, copy all the files and run the app
FROM nginx:alpine AS runner
WORKDIR /app

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

ENV NODE_ENV production

CMD ["nginx", "-g", "daemon off;"]
