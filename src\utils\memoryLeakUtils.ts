/**
 * 内存泄漏预防工具
 * 提供一些实用函数来帮助避免常见的内存泄漏问题
 */

import { useEffect, useRef } from 'react';

/**
 * 安全的 setTimeout hook
 * 自动在组件卸载时清理定时器
 */
export function useSafeTimeout() {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const setSafeTimeout = (callback: () => void, delay: number) => {
    // 清除之前的定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(callback, delay);
    return timeoutRef.current;
  };

  const clearSafeTimeout = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };

  // 组件卸载时自动清理
  useEffect(() => {
    return () => {
      clearSafeTimeout();
    };
  }, []);

  return { setSafeTimeout, clearSafeTimeout };
}

/**
 * 安全的事件监听器 hook
 * 自动在组件卸载时移除事件监听器
 */
export function useSafeEventListener(
  target: EventTarget | null,
  event: string,
  handler: EventListener,
  options?: boolean | AddEventListenerOptions
) {
  useEffect(() => {
    if (!target) return;

    target.addEventListener(event, handler, options);

    return () => {
      target.removeEventListener(event, handler, options);
    };
  }, [target, event, handler, options]);
}

/**
 * 安全的 WebSocket hook
 * 自动在组件卸载时关闭连接
 */
export function useSafeWebSocket(url: string | null) {
  const socketRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    if (!url) return;

    const socket = new WebSocket(url);
    socketRef.current = socket;

    return () => {
      if (socketRef.current) {
        socketRef.current.close();
      }
    };
  }, [url]);

  return socketRef.current;
}

/**
 * 检查组件是否已卸载的 hook
 * 用于在异步操作完成后检查组件状态
 */
export function useIsMounted() {
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return () => isMountedRef.current;
}

/**
 * 内存泄漏检查清单
 * 开发时可以使用这个清单检查组件
 */
export const MEMORY_LEAK_CHECKLIST = {
  timers: [
    'setInterval 是否有对应的 clearInterval',
    'setTimeout 是否有对应的 clearTimeout',
    'requestAnimationFrame 是否有对应的 cancelAnimationFrame'
  ],
  events: [
    'addEventListener 是否有对应的 removeEventListener',
    'window/document 事件监听器是否正确清理',
    'resize/scroll 等全局事件是否清理'
  ],
  connections: [
    'WebSocket 连接是否在组件卸载时关闭',
    'EventSource 连接是否正确关闭',
    'HTTP 请求是否可以取消'
  ],
  subscriptions: [
    'Redux 订阅是否正确取消',
    '第三方库的订阅是否清理',
    'Observable 订阅是否取消'
  ],
  references: [
    '是否存在循环引用',
    '闭包是否持有不必要的引用',
    'DOM 节点引用是否及时清理'
  ]
};

/**
 * 开发环境下的内存泄漏警告
 */
export function warnMemoryLeak(componentName: string, leakType: string) {
  if (process.env.NODE_ENV === 'development') {
    console.warn(
      `⚠️ 潜在内存泄漏警告: ${componentName} 组件中的 ${leakType} 可能没有正确清理`
    );
  }
}
