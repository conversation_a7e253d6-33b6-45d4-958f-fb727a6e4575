import React, { useState } from 'react';
import Login from './Login';
import ForgotPassword from './ForgotPassword';
import Verification from './Verification';
import ResetPassword from './ResetPassword';
import type { AuthStep, LoginForm, ForgotPasswordForm, VerificationForm, ResetPasswordForm, User } from '../../types/auth';

interface AuthContainerProps {
  onAuthSuccess: (user: User) => void;
}

const AuthContainer: React.FC<AuthContainerProps> = ({ onAuthSuccess }) => {
  const [currentStep, setCurrentStep] = useState<AuthStep>('login');
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [error, setError] = useState<string>('');
  const [userEmail, setUserEmail] = useState<string>('');

  // Mock user data - in real app, this would come from API
  const mockUser: User = {
    id: '1',
    name: '田中 太郎',
    nameKana: 'たなか たろう',
    email: '<EMAIL>',
    department: '開発部門',
    position: 'シニアエンジニア',
    employeeId: 'EMP-2024-001',
  };

  const handleLogin = async (form: LoginForm) => {
    setIsLoading(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock validation
      if (form.employeeId === 'EMP-2024-001' && form.password === 'password123') {
        onAuthSuccess(mockUser);
      } else {
        setError('社員番号またはパスワードが正しくありません');
      }
    } catch (err) {
      setError('ログインに失敗しました。しばらく後でお試しください。');
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async (form: ForgotPasswordForm) => {
    setIsLoading(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock validation
      if (form.employeeId === 'EMP-2024-001' && form.email === '<EMAIL>') {
        setUserEmail(form.email);
        setCurrentStep('verification');
      } else {
        setError('社員番号またはメールアドレスが正しくありません');
      }
    } catch (err) {
      setError('認証コードの送信に失敗しました。しばらく後でお試しください。');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerification = async (form: VerificationForm) => {
    setIsLoading(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock validation
      if (form.code === '123456') {
        setCurrentStep('reset-password');
      } else {
        setError('認証コードが正しくありません');
      }
    } catch (err) {
      setError('認証に失敗しました。しばらく後でお試しください。');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async (form: ResetPasswordForm) => {
    setIsLoading(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock success
      alert('パスワードが正常に変更されました。新しいパスワードでログインしてください。');
      setCurrentStep('login');
    } catch (err) {
      setError('パスワードの変更に失敗しました。しばらく後でお試しください。');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    setIsResending(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Mock success - in real app, would send new code
    } catch (err) {
      setError('認証コードの再送信に失敗しました');
    } finally {
      setIsResending(false);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'login':
        return (
          <Login
            onLogin={handleLogin}
            onForgotPassword={() => setCurrentStep('forgot-password')}
            error={error}
            isLoading={isLoading}
          />
        );
      case 'forgot-password':
        return (
          <ForgotPassword
            onSubmit={handleForgotPassword}
            onBack={() => setCurrentStep('login')}
            error={error}
            isLoading={isLoading}
          />
        );
      case 'verification':
        return (
          <Verification
            onSubmit={handleVerification}
            onBack={() => setCurrentStep('forgot-password')}
            onResendCode={handleResendCode}
            email={userEmail}
            error={error}
            isLoading={isLoading}
            isResending={isResending}
          />
        );
      case 'reset-password':
        return (
          <ResetPassword
            onSubmit={handleResetPassword}
            error={error}
            isLoading={isLoading}
          />
        );
      default:
        return null;
    }
  };

  return <>{renderCurrentStep()}</>;
};

export default AuthContainer;