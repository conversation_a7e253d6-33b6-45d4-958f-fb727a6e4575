import React, { useEffect, useState } from 'react'
import { Card, Typography, Progress, Descriptions, List, Tag, Button } from 'antd'
import { CalendarOutlined, ClockCircleOutlined } from '@ant-design/icons'
import { useTranslation } from '@/hooks/useTranslation'
import { getApi, postApi } from '@/api/fetch-api'
import ApiUrlVars from '@/api/common/url-vars'
import { useApplicationDispatch } from '@/hook/hooks'

const { Title, Text } = Typography

interface PaidLeaveInfo {
  totalDays: number
  usedDays: number
  remainingDays: number
  expiredDays: number
  expirationDate: string
}

interface LeaveRecord {
  id: string
  type: string
  startDate: string
  endDate: string
  days: number
  status: string
  reason: string
}

const PaidLeavePage: React.FC = () => {
  const { t } = useTranslation()
  const dispatch = useApplicationDispatch()
  const [leaveInfo, setLeaveInfo] = useState<PaidLeaveInfo | null>(null)
  const [leaveRecords, setLeaveRecords] = useState<LeaveRecord[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchPaidLeaveInfo = async () => {
      try {
        setLoading(true)
        const [infoResponse, recordsResponse] = await Promise.all([
          postApi(ApiUrlVars.loginApi_domain + ApiUrlVars.summary_personal_info_get, {}, dispatch),
          getApi(ApiUrlVars.api_domain + ApiUrlVars.leaveList_get, {}, dispatch)
        ])

        if (infoResponse.data.status === 'OK') {
          setLeaveInfo(infoResponse.data)
        }

        if (recordsResponse.data.status === 'OK') {
          setLeaveRecords(recordsResponse.data)
        }
      } catch (error) {
        console.error('Error fetching paid leave info:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchPaidLeaveInfo()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'green'
      case 'pending': return 'orange'
      case 'rejected': return 'red'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return t('leave.approved', '已批准')
      case 'pending': return t('leave.pending', '待审批')
      case 'rejected': return t('leave.rejected', '已拒绝')
      default: return status
    }
  }

  if (loading) {
    return (
      <div style={{ padding: '24px' }}>
        <Card loading />
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>{t('profile.paidLeave.title', '有薪假期')}</Title>
      
      {leaveInfo && (
        <Card style={{ marginBottom: '24px' }}>
          <Title level={4}>
            <CalendarOutlined style={{ marginRight: '8px' }} />
            {t('paidLeave.overview', '假期概览')}
          </Title>
          
          <div style={{ marginBottom: '24px' }}>
            <Text strong>{t('paidLeave.usage', '假期使用情况')}</Text>
            <Progress
              percent={(leaveInfo.usedDays / leaveInfo.totalDays) * 100}
              format={() => `${leaveInfo.usedDays}/${leaveInfo.totalDays} 天`}
              style={{ marginTop: '8px' }}
            />
          </div>
          
          <Descriptions column={2} bordered>
            <Descriptions.Item label={t('paidLeave.totalDays', '总假期天数')}>
              {leaveInfo.totalDays} 天
            </Descriptions.Item>
            <Descriptions.Item label={t('paidLeave.usedDays', '已使用天数')}>
              {leaveInfo.usedDays} 天
            </Descriptions.Item>
            <Descriptions.Item label={t('paidLeave.remainingDays', '剩余天数')}>
              <Text type={leaveInfo.remainingDays > 5 ? 'success' : 'warning'}>
                {leaveInfo.remainingDays} 天
              </Text>
            </Descriptions.Item>
            <Descriptions.Item label={t('paidLeave.expiredDays', '过期天数')}>
              <Text type={leaveInfo.expiredDays > 0 ? 'danger' : 'secondary'}>
                {leaveInfo.expiredDays} 天
              </Text>
            </Descriptions.Item>
            <Descriptions.Item label={t('paidLeave.expirationDate', '到期日期')} span={2}>
              <Text>
                <ClockCircleOutlined style={{ marginRight: '4px' }} />
                {leaveInfo.expirationDate}
              </Text>
            </Descriptions.Item>
          </Descriptions>
          
          {leaveInfo.remainingDays <= 5 && leaveInfo.remainingDays > 0 && (
            <div style={{ 
              marginTop: '16px', 
              padding: '12px', 
              backgroundColor: '#fff7e6', 
              border: '1px solid #ffd591',
              borderRadius: '6px'
            }}>
              <Text type="warning">
                ⚠️ {t('paidLeave.expirationWarning', '您的假期即将到期，请及时使用')}
              </Text>
            </div>
          )}
        </Card>
      )}
      
      <Card>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          marginBottom: '16px' 
        }}>
          <Title level={4}>{t('paidLeave.records', '请假记录')}</Title>
          <Button type="primary">
            {t('paidLeave.applyLeave', '申请请假')}
          </Button>
        </div>
        
        <List
          dataSource={leaveRecords}
          renderItem={(record) => (
            <List.Item
              actions={[
                <Tag color={getStatusColor(record.status)}>
                  {getStatusText(record.status)}
                </Tag>
              ]}
            >
              <List.Item.Meta
                title={`${record.type} - ${record.days} 天`}
                description={
                  <div>
                    <div>{record.startDate} ~ {record.endDate}</div>
                    <div style={{ marginTop: '4px', color: '#666' }}>
                      {record.reason}
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Card>
    </div>
  )
}

export default PaidLeavePage
