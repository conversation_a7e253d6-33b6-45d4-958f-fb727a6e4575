import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';

export enum VisibilityFilters {
  SHOW_ALL = 'SHOW_ALL',
  SHOW_COMPLETED = 'SHOW_COMPLETED',
  SHOW_ACTIVE = 'SHOW_ACTIVE',
}

export const filtersSlice = createSlice({
  name: 'filters',
  initialState: VisibilityFilters.SHOW_ALL,
  reducers: {
    setVisibilityFilter(state: VisibilityFilters, action: PayloadAction<VisibilityFilters>) {
      return action.payload;
    },
  },
});

export const { setVisibilityFilter } = filtersSlice.actions;

export default filtersSlice.reducer;

//state 后面的为store中数据名称
export const filters = (state: RootState) => state.filters;
