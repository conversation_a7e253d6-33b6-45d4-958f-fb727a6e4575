{"name": "kaoqin_web_react", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "i18n:setup": "node scripts/setup-i18n.js", "i18n:scan": "node scripts/find-hardcoded-chinese.js", "i18n:check": "node scripts/check-translations.js", "i18n:fix": "node scripts/fix-i18n-errors.js"}, "dependencies": {"@ant-design/cssinjs": "1.17.0", "@reduxjs/toolkit": "1.9.7", "antd": "5.10.0", "axios": "1.5.1", "bootstrap": "5.3.2", "crypto-js": "4.2.0", "date-fns": "^2.30.0", "i18next": "^23.7.6", "js-export-excel": "1.1.4", "react": "^18.2.0", "react-cookies": "0.1.1", "react-csv": "2.2.2", "react-dom": "^18.2.0", "react-hot-toast": "2.4.1", "react-i18next": "^13.5.0", "react-router-dom": "^6.20.0", "redux-persist": "^6.0.0", "sharp": "^0.33.3", "xlsx": "0.18.5"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/react": "^18.2.37", "@types/react-cookies": "^0.1.3", "@types/react-dom": "^18.2.15", "@types/react-i18next": "^8.1.0", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0"}}