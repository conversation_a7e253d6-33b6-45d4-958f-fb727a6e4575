// 个人考勤相关类型定义

/**
 * 考勤状态枚举
 */
export enum AttendanceStatus {
  PRESENT = 'present',           // 正常出勤
  ABSENT = 'absent',             // 缺勤
  LATE = 'late',                 // 迟到
  EARLY_LEAVE = 'early_leave',   // 早退
  OVERTIME = 'overtime',         // 加班
  HOLIDAY = 'holiday',           // 假期
  WEEKEND = 'weekend'            // 周末
}

/**
 * 打卡类型枚举
 */
export enum ClockType {
  CLOCK_IN = 'clock_in',         // 上班打卡
  CLOCK_OUT = 'clock_out'        // 下班打卡
}

/**
 * 考勤记录
 */
export interface AttendanceRecord {
  id: string;
  user_id: number;
  date: string;                  // YYYY-MM-DD
  clock_in_time?: string;        // HH:mm 格式
  clock_out_time?: string;       // HH:mm 格式
  attendance_location?: string;  // 打卡地点
  status: AttendanceStatus;
  work_hours?: number;           // 工作时长（小时）
  overtime_hours?: number;       // 加班时长（小时）
  late_minutes?: number;         // 迟到分钟数
  early_leave_minutes?: number;  // 早退分钟数
  notes?: string;                // 备注
  created_at: string;
  updated_at: string;
}

/**
 * 打卡记录
 */
export interface ClockRecord {
  id: string;
  user_id: number;
  type: ClockType;
  timestamp: string;             // ISO 8601 格式
  location: string;              // 打卡地点
  latitude?: number;             // 纬度
  longitude?: number;            // 经度
  device_info?: string;          // 设备信息
  ip_address?: string;           // IP地址
  created_at: string;
}

/**
 * 考勤统计
 */
export interface AttendanceStatistics {
  total_work_days: number;       // 总工作日
  actual_work_days: number;      // 实际出勤天数
  absent_days: number;           // 缺勤天数
  late_count: number;            // 迟到次数
  early_leave_count: number;     // 早退次数
  total_work_hours: number;      // 总工作时长
  total_overtime_hours: number;  // 总加班时长
  attendance_rate: number;       // 出勤率
}

/**
 * 获取考勤记录请求参数
 */
export interface GetAttendanceRecordsRequest {
  date_from: string;             // YYYY-MM-DD
  date_to: string;               // YYYY-MM-DD
  user_id?: number;              // 可选，管理员查看其他用户
}

/**
 * 获取考勤记录响应
 */
export interface GetAttendanceRecordsResponse {
  status: 'OK' | 'ERROR';
  records: AttendanceRecord[];
  statistics: AttendanceStatistics;
  message?: string;
}

/**
 * 打卡请求参数
 */
export interface ClockInOutRequest {
  type: ClockType;
  location: string;
  latitude?: number;
  longitude?: number;
  notes?: string;
}

/**
 * 打卡响应
 */
export interface ClockInOutResponse {
  status: 'OK' | 'ERROR';
  message: string;
  clock_record?: ClockRecord;
  attendance_record?: AttendanceRecord;
}

/**
 * 考勤修正请求参数
 */
export interface AttendanceCorrectionRequest {
  date: string;                  // YYYY-MM-DD
  clock_in_time?: string;        // HH:mm
  clock_out_time?: string;       // HH:mm
  reason: string;                // 修正原因
  supporting_documents?: string[]; // 支持文档
}

/**
 * 考勤修正响应
 */
export interface AttendanceCorrectionResponse {
  status: 'OK' | 'ERROR';
  message: string;
  correction_id?: string;
}

/**
 * 月度考勤汇总
 */
export interface MonthlyAttendanceSummary {
  month: string;                 // YYYY-MM
  user_id: number;
  work_no: string;
  name: string;
  department: string;
  statistics: AttendanceStatistics;
  daily_records: AttendanceRecord[];
}

/**
 * 考勤地点
 */
export interface AttendanceLocation {
  id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  radius: number;                // 有效打卡半径（米）
  is_active: boolean;
  created_at: string;
}

/**
 * 工作时间配置
 */
export interface WorkTimeConfig {
  id: string;
  name: string;
  start_time: string;            // HH:mm
  end_time: string;              // HH:mm
  break_start_time?: string;     // HH:mm
  break_end_time?: string;       // HH:mm
  late_tolerance_minutes: number; // 迟到容忍分钟数
  is_default: boolean;
  applicable_departments: string[];
}
