import { createSlice, PayloadAction, } from "@reduxjs/toolkit";
import { RootState } from "../store";
import { approvalConfig } from "../utils/approvallist";
import { HYDRATE } from "next-redux-wrapper";
import ApiTasksTypes from "../api/common/task-types";
import ApiFetchVars from "../api/common/fetch-api-vars";
import ApiUrlVars from "../api/common/url-vars";
import dayjs from 'dayjs';

// 批量审批选择的最大值
const approvalChooseLimit = 100;

function getWeekday(date: Date): string {
    const weekdays = ['(日)', '(一)', '(二)', '(三)', '(四)', '(五)', '(六)'];
    return weekdays[date.getDay()];
}

// 定义要保存到Store的数据格式
export interface approvalState {
    status: string,
    title: string,
    updateTiaoxiuValue: string,
    updateShijiaValue: string,
    updateBingjiaValue: string,
    //判断当前修改请假时间是否显示
    show: boolean,
    //判断当前修改请假时间按钮是否显示
    showBtn: boolean,
    //确定当前要添加的行的位置
    selectedRowKey: number,
    //确定申请进度是否显示
    showInput: boolean,
    //确定当前点击删除的位置
    lSelectedRowKeys: number[],
    oSelectedRowKeys: number[],
    bSelectedRowKeys: number[],
    cSelectedRowKeys: number[],
    leaveApplication:
    {
        APPLY_CODE: any;
        USER_ID: any;
        AGENT_USER_ID: any;
        APPLY_TYPE_ID: any;
        APPLY_TYPE_NAME: any;
        START_TIME: any;
        END_TIME: any;
        NORMAL_HOURS: any;
        COMPENSATORY_HOURS: any;
        SICK_HOURS: any;
        OTHER_APPLY_TYPE: any;
        OTHER_DAYS: any;
        REASON: any;
        WORKFLOW_INDEX: any;
        WORKFLOW_RESULT: any;
        reject_reason: any;
        WORKFLOW_LIST: {
            workflow_index: any;
            workflow_role_name: any;
            workflow_result: any;
            handle_user_id: any;
            handle_user_name: any;
            update_time: any;
        }[];
    }[],
    overtimeApplication: {
        APPLY_CODE: any;
        USER_ID: any;
        AGENT_USER_ID: any;
        APPLY_TYPE_ID: any;
        APPLY_TYPE_NAME: any;
        START_TIME: any;
        END_TIME: any;
        HOURS: any;
        REASON: any;
        WORKFLOW_INDEX: any;
        WORKFLOW_RESULT: any;
        reject_reason: any;
        WORKFLOW_LIST: {
            workflow_index: any;
            workflow_role_name: any;
            workflow_result: any;
            handle_user_id: any;
            handle_user_name: any;
            update_time: any;
        }[];
    }[],
    confirmationApplication: {
        APPLY_CODE: any;
        USER_ID: any;
        AGENT_USER_ID: any;
        APPLY_TYPE_ID: any;
        APPLY_TYPE_NAME: any;
        DAY: any;
        WORK_START_TIME: any;
        WORK_END_TIME: any;
        REAL_START_TIME: any;
        REAL_END_TIME: any;
        REASON: any;
        WORKFLOW_INDEX: any;
        WORKFLOW_RESULT: any;
        reject_reason: any;
        WORKFLOW_LIST: {
            workflow_index: any;
            workflow_role_name: any;
            workflow_result: any;
            handle_user_id: any;
            handle_user_name: any;
            update_time: any;
        }[];
    }[],
    evectionApplication: {
        APPLY_CODE: any;
        USER_ID: any;
        AGENT_USER_ID: any;
        APPLY_TYPE_ID: any;
        APPLY_TYPE_NAME: any;
        START_TIME: any;
        END_TIME: any;
        LOCATION: any;
        T_DAYS: any;
        REASON: any;
        WORKFLOW_INDEX: any;
        WORKFLOW_RESULT: any;
        reject_reason: any;
        WORKFLOW_LIST: {
            workflow_index: any;
            workflow_role_name: any;
            workflow_result: any;
            handle_user_id: any;
            handle_user_name: any;
            update_time: any;
        }[];
    }[],
    updateData: {
        key: number;
        user: string;
        applicant: string;
        startDate: string;
        endDate: string;
        japan: string;
        china: string;
        other: string;
        reason: string;
        state: number;
    }[],
    //复选框
    leaveKeyList: any[],
    leaveChooseKey: any,
    overtimeKeyList: any[],
    overtimeChooseKey: any,
    confirmationKeyList: any[],
    confirmationChooseKey: any,
    evectionKeyList: any[],
    evectionChooseKey: any,
    keyword: string,
    confirmationTime: dayjs.Dayjs,
    startTime: dayjs.Dayjs,
    endTime: dayjs.Dayjs,
    updateEndTime: dayjs.Dayjs,
    showBatch: boolean,
    // 复选框
    checkboxes: any[],
    // 全选框
    allChecked: boolean
    notice: any,
    overtimeCode: any,
    targetDate: any,
    day_type: {
        date: any,
        type: any
    }[],
    rs_leave_list: any[],
    rs_overtime_list: any[],
    rs_overtime_current_all_list: any[],
    rs_overtime_current_list: any[],
    rs_overtime_current_ing_list: any[],
    rs_overtime_ing_current_list: any[],
    currentPage: number,
    pageSize: number,
    rs_overtime_all_staff_list: any[],
    rs_trip_list: any[],
    rs_edit_list: any[],
    rs_leave_list_ing: any[],
    rs_overtime_list_ing: any[],
    rs_trip_list_ing: any[],
    rs_edit_list_ing: any[],
    // 全选是否选中
    isLeaveChecked: boolean,
    isOvertimeChecked: boolean,
    isBusinessChecked: boolean,
    isEditChecked: boolean,
    approvalChooseLimit: any,
}

// 初始化数据
const initialState: approvalState = {
    status: ApiFetchVars.todo,
    title: approvalConfig.approvalNull.title,
    leaveApplication: [],
    overtimeApplication: [],
    confirmationApplication: [],
    evectionApplication: [],
    updateData: [],
    updateTiaoxiuValue: "0h",
    updateShijiaValue: "0h",
    updateBingjiaValue: "0h",
    show: false,
    showBtn: false,
    selectedRowKey: -1,
    showInput: false,
    lSelectedRowKeys: [],
    oSelectedRowKeys: [],
    bSelectedRowKeys: [],
    cSelectedRowKeys: [],
    leaveKeyList: [],
    leaveChooseKey: 0,
    overtimeKeyList: [],
    overtimeChooseKey: 0,
    confirmationKeyList: [],
    confirmationChooseKey: 0,
    evectionKeyList: [],
    evectionChooseKey: 0,
    keyword: '',
    confirmationTime: dayjs(),
    startTime: dayjs().subtract(32, 'day'),
    endTime: dayjs().add(2,'month'),
    updateEndTime: dayjs(),
    showBatch: false,
    checkboxes: Array().fill(false),
    allChecked: false,
    notice: '',
    overtimeCode: '',
    targetDate: '1999-01-01 00:00:00',
    day_type: [],
    rs_leave_list: [],
    rs_overtime_list: [],
    rs_overtime_current_all_list: [],
    rs_overtime_current_list: [],
    rs_overtime_current_ing_list: [],
    rs_overtime_ing_current_list: [],
    currentPage: 1,
    pageSize: 100,
    rs_overtime_all_staff_list: [],
    rs_trip_list: [],
    rs_edit_list: [],
    rs_leave_list_ing: [],
    rs_overtime_list_ing: [],
    rs_trip_list_ing: [],
    rs_edit_list_ing: [],
    // 全选是否选中
    isLeaveChecked: false,
    isOvertimeChecked: false,
    isBusinessChecked: false,
    isEditChecked: false,
    approvalChooseLimit: approvalChooseLimit,
};

//定义需要的变量
export const approvalSlice = createSlice({
    name: 'approval',
    initialState,
    // reducers 集合了reducer和action的功能
    // 每个case冒号之前的为action名
    // 每个case整体为reducer
    reducers: {
        onTabChange: (state: approvalState, action: PayloadAction<any>) => {
            state.status = ApiFetchVars.todo;
            state.title = action.payload;
        },
        //点击后显示当前申请进度
        handleRowDetailClick: (state: approvalState, action: PayloadAction<any>) => {
            state.status = ApiFetchVars.todo;
            if(action.payload.type == "evention"){
                if(state.bSelectedRowKeys.includes(action.payload.code)){
                    state.bSelectedRowKeys = state.bSelectedRowKeys.filter((item)=>item!==action.payload.code)
                }else{
                    state.bSelectedRowKeys.push(action.payload.code)
                    state.selectedRowKey = action.payload;
                }
            }
            if(action.payload.type == "leave"){
                if(state.lSelectedRowKeys.includes(action.payload.code)){
                    state.lSelectedRowKeys = state.lSelectedRowKeys.filter((item)=>item!==action.payload.code)
                }else{
                    state.lSelectedRowKeys.push(action.payload.code)
                    state.selectedRowKey = action.payload;
                }
            }
            if(action.payload.type == "overtime"){
                if(state.oSelectedRowKeys.includes(action.payload.code)){
                    state.oSelectedRowKeys = state.oSelectedRowKeys.filter((item)=>item!==action.payload.code)
                }else{
                    state.oSelectedRowKeys.push(action.payload.code)
                    state.selectedRowKey = action.payload;
                }
            }
            if(action.payload.type == "confirm"){
                if(state.cSelectedRowKeys.includes(action.payload.code)){
                    state.cSelectedRowKeys = state.cSelectedRowKeys.filter((item)=>item!==action.payload.code)
                }else{
                    state.cSelectedRowKeys.push(action.payload.code)
                    state.selectedRowKey = action.payload;
                }
            }
            // if (state.selectedRowKey === action.payload) {
            //     state.showInput = !state.showInput;
            // } else {
            //     state.selectedRowKey = action.payload;
            //     state.showInput = true;
            // }
        },
        setOvertimeDataWhenInit: (state: approvalState, action: PayloadAction<any>) => {
            if (action.payload.apply_list && Array.isArray(action.payload.apply_list) && action.payload.apply_list.length > 0) {
                const apply_list_1 = action.payload.apply_list.filter((item:any) => item.WORKFLOW_RESULT != null)
                let result = apply_list_1.reduce((accumulator:any, currentValue:any) => {
                    let index = accumulator.findIndex((item:any) => item.APPLY_CODE === currentValue.APPLY_CODE);
                    if(index > -1 && accumulator[index].WORKFLOW_INDEX < currentValue.WORKFLOW_INDEX) {
                      accumulator[index] = currentValue;
                    } else if (index === -1) {
                      accumulator.push(currentValue);
                    }
                    return accumulator;
                  }, []);
                state.overtimeApplication = result;
                state.overtimeApplication.forEach(apply => {
                    const applyCode = apply.APPLY_CODE;
                    if (action.payload.workflow_list && action.payload.workflow_list.hasOwnProperty(applyCode)) {
                        // 如果存在，将工作流信息添加到 apply 对象中
                        apply.WORKFLOW_LIST = action.payload.workflow_list[applyCode];
                    } else {
                        // 如果不存在，可以添加空数组或进行其他处理
                        apply.WORKFLOW_LIST = [];
                    }
                });
                state.targetDate = action.payload.target_date;
                state.startTime = dayjs(state.targetDate, 'YYYY-MM-DD HH:mm:ss');
                // state.day_type = action.payload.day_type;
            } else {
                state.overtimeApplication = [{
                    APPLY_CODE: '',
                    USER_ID: '',
                    AGENT_USER_ID: '',
                    APPLY_TYPE_ID: '',
                    APPLY_TYPE_NAME: '',
                    START_TIME: '',
                    END_TIME: '',
                    HOURS: '',
                    REASON: '',
                    WORKFLOW_INDEX: '',
                    WORKFLOW_RESULT: '',
                    reject_reason: '',
                    WORKFLOW_LIST: [{
                        workflow_index: '',
                        workflow_result: '',
                        workflow_role_name: '',
                        handle_user_id: '',
                        handle_user_name: '',
                        update_time: '',
                    }],
                }];
            }
            state.keyword = '';
        },
        setLeaveDataWhenInit: (state: approvalState, action: PayloadAction<any>) => {
            // 只保留当前最新的
            if (action.payload.apply_list && Array.isArray(action.payload.apply_list) && action.payload.apply_list.length > 0) {
                const apply_list_1 = action.payload.apply_list.filter((item:any) => item.WORKFLOW_RESULT != null)
                let result = apply_list_1.reduce((accumulator:any, currentValue:any) => {
                    let index = accumulator.findIndex((item:any) => item.APPLY_CODE === currentValue.APPLY_CODE);
                    if(index > -1 && accumulator[index].WORKFLOW_INDEX < currentValue.WORKFLOW_INDEX) {
                      accumulator[index] = currentValue;
                    } else if (index === -1) {
                      accumulator.push(currentValue);
                    }
                    return accumulator;
                  }, []);
                state.leaveApplication = result;
                // state.leaveApplication = action.payload.apply_list;
                state.leaveApplication.forEach(apply => {
                    const applyCode = apply.APPLY_CODE;
                    if (action.payload.workflow_list && action.payload.workflow_list.hasOwnProperty(applyCode)) {
                        // 如果存在，将工作流信息添加到 apply 对象中
                        apply.WORKFLOW_LIST = action.payload.workflow_list[applyCode];
                    } else {
                        // 如果不存在，可以添加空数组或进行其他处理
                        apply.WORKFLOW_LIST = [];
                    }
                });
                state.targetDate = action.payload.target_date;
                state.startTime = dayjs(state.targetDate, 'YYYY-MM-DD HH:mm:ss');
            } else {
                state.leaveApplication = [{
                    APPLY_CODE: '',
                    USER_ID: '',
                    AGENT_USER_ID: '',
                    APPLY_TYPE_ID: '',
                    APPLY_TYPE_NAME: '',
                    START_TIME: '',
                    END_TIME: '',
                    NORMAL_HOURS: '',
                    COMPENSATORY_HOURS: '',
                    SICK_HOURS: '',
                    OTHER_APPLY_TYPE: '',
                    OTHER_DAYS: '',
                    REASON: '',
                    WORKFLOW_INDEX: '',
                    WORKFLOW_RESULT: '',
                    reject_reason: '',
                    WORKFLOW_LIST: [{
                        workflow_index: '',
                        workflow_role_name: '',
                        workflow_result: '',
                        handle_user_id: '',
                        handle_user_name: '',
                        update_time: ''
                    }],
                }];
            }
            state.keyword = '';
        },
        setBusinessTripDataWhenInit: (state: approvalState, action: PayloadAction<any>) => {
            if (action.payload.apply_list && Array.isArray(action.payload.apply_list) && action.payload.apply_list.length > 0) {
                const apply_list_1 = action.payload.apply_list.filter((item:any) => item.WORKFLOW_RESULT != null)
                let result = apply_list_1.reduce((accumulator:any, currentValue:any) => {
                    let index = accumulator.findIndex((item:any) => item.APPLY_CODE === currentValue.APPLY_CODE);
                    if(index > -1 && accumulator[index].WORKFLOW_INDEX < currentValue.WORKFLOW_INDEX) {
                      accumulator[index] = currentValue;
                    } else if (index === -1) {
                      accumulator.push(currentValue);
                    }
                    return accumulator;
                  }, []);
                state.evectionApplication = result;
                state.evectionApplication.forEach(apply => {
                    const applyCode = apply.APPLY_CODE;
                    if (action.payload.workflow_list && action.payload.workflow_list.hasOwnProperty(applyCode)) {
                        // 如果存在，将工作流信息添加到 apply 对象中
                        apply.WORKFLOW_LIST = action.payload.workflow_list[applyCode];
                    } else {
                        // 如果不存在，可以添加空数组或进行其他处理
                        apply.WORKFLOW_LIST = [];
                    }
                });
                state.targetDate = action.payload.target_date;
                state.startTime = dayjs(state.targetDate, 'YYYY-MM-DD HH:mm:ss');
            } else {
                state.evectionApplication = [{
                    APPLY_CODE: '',
                    USER_ID: '',
                    AGENT_USER_ID: '',
                    APPLY_TYPE_ID: '',
                    APPLY_TYPE_NAME: '',
                    START_TIME: '',
                    END_TIME: '',
                    LOCATION: '',
                    T_DAYS: '',
                    REASON: '',
                    WORKFLOW_INDEX: '',
                    WORKFLOW_RESULT: '',
                    reject_reason: '',
                    WORKFLOW_LIST: [{
                        workflow_index: '',
                        workflow_role_name: '',
                        workflow_result: '',
                        handle_user_id: '',
                        handle_user_name: '',
                        update_time: ''
                    }],
                }];
            }
            state.keyword = '';
        },
        setEditDataWhenInit: (state: approvalState, action: PayloadAction<any>) => {
            if (action.payload.apply_list && Array.isArray(action.payload.apply_list) && action.payload.apply_list.length > 0) {
                const apply_list_1 = action.payload.apply_list.filter((item:any) => item.WORKFLOW_RESULT != null)
                let result = apply_list_1.reduce((accumulator:any, currentValue:any) => {
                    let index = accumulator.findIndex((item:any) => item.APPLY_CODE === currentValue.APPLY_CODE);
                    if(index > -1 && accumulator[index].WORKFLOW_INDEX < currentValue.WORKFLOW_INDEX) {
                        accumulator[index] = currentValue;
                    } else if (index === -1) {
                        accumulator.push(currentValue);
                    }
                    return accumulator;
                    }, []);
                state.confirmationApplication = result;
                state.confirmationApplication.forEach(apply => {
                    const applyCode = apply.APPLY_CODE;
                    if (action.payload.workflow_list && action.payload.workflow_list.hasOwnProperty(applyCode)) {
                        // 如果存在，将工作流信息添加到 apply 对象中
                        apply.WORKFLOW_LIST = action.payload.workflow_list[applyCode];
                    } else {
                        // 如果不存在，可以添加空数组或进行其他处理
                        apply.WORKFLOW_LIST = [];
                    }
                });
                state.targetDate = action.payload.target_date;
                state.startTime = dayjs(state.targetDate, 'YYYY-MM-DD HH:mm:ss');
            } else {
                state.confirmationApplication = [{
                    APPLY_CODE: '',
                    USER_ID: '',
                    AGENT_USER_ID: '',
                    APPLY_TYPE_ID: '',
                    APPLY_TYPE_NAME: '',
                    DAY: '',
                    WORK_START_TIME: '',
                    WORK_END_TIME: '',
                    REAL_START_TIME: '',
                    REAL_END_TIME: '',
                    REASON: '',
                    WORKFLOW_INDEX: '',
                    WORKFLOW_RESULT: '',
                    reject_reason: '',
                    WORKFLOW_LIST: [{
                        workflow_index: '',
                        workflow_role_name: '',
                        workflow_result: '',
                        handle_user_id: '',
                        handle_user_name: '',
                        update_time: ''
                    }],
                }];
            }
            state.keyword = '';
        },
        //同意当前申请
        handleLeaveAgree: (state: approvalState, action: PayloadAction<any>) => {
            const agreeData = state.leaveApplication.filter(item => item.APPLY_CODE === action.payload);
            const agreeFromApplicationState = agreeData.map((value, index) => {
                if (value.APPLY_CODE === action.payload) {
                    value.WORKFLOW_RESULT = 1;
                    value.WORKFLOW_LIST[value.WORKFLOW_INDEX].workflow_result = 1;
                    return { ...value }; // 将state更新为1  
                } else {
                    return { ...value }; // 保持其他元素不变  
                }
            });
            const otherData = state.leaveApplication.filter(item => item.APPLY_CODE !== action.payload);
            // 在设置state.evectionApplication之前，先对数组进行排序  
            const sortedData = [...otherData, ...agreeFromApplicationState];
            // let result = sortedData.reduce((accumulator:any, currentValue:any) => {
            //     let index = accumulator.findIndex((item:any) => item.APPLY_CODE === currentValue.APPLY_CODE);
            //     if(index > -1 && accumulator[index].WORKFLOW_INDEX < currentValue.WORKFLOW_INDEX) {
            //       accumulator[index] = currentValue;
            //     } else if (index === -1) {
            //       accumulator.push(currentValue);
            //     }
            //     return accumulator;
            //   }, []);
            const apply_list_1 = sortedData.filter((item:any) => item.WORKFLOW_RESULT != null)
            let result = apply_list_1.reduce((accumulator:any, currentValue:any) => {
                let index = accumulator.findIndex((item:any) => item.APPLY_CODE === currentValue.APPLY_CODE);
                if(index > -1 && accumulator[index].WORKFLOW_INDEX < currentValue.WORKFLOW_INDEX) {
                    accumulator[index] = currentValue;
                } else if (index === -1) {
                    accumulator.push(currentValue);
                }
                return accumulator;
                }, []);
            state.leaveApplication = result;
            // state.leaveApplication = sortedData;
            state.overtimeCode = action.payload;
        },
        handleEvectionAgree: (state: approvalState, action: PayloadAction<any>) => {
            state.status = ApiFetchVars.todo;
            const agreeData = state.evectionApplication.filter(item => item.APPLY_CODE === action.payload);
            const agreeFromApplicationState = agreeData.map((value, index) => {
                if (value.APPLY_CODE === action.payload) {
                    value.WORKFLOW_RESULT = 1;
                    value.WORKFLOW_LIST[value.WORKFLOW_INDEX].workflow_result = 1;
                    return { ...value }; // 将state更新为1  
                } else {
                    return { ...value }; // 保持其他元素不变  
                }
            });
            const otherData = state.evectionApplication.filter(item => item.APPLY_CODE !== action.payload);
            // 在设置state.evectionApplication之前，先对数组进行排序  
            const sortedData = [...otherData, ...agreeFromApplicationState];
            const apply_list_1 = sortedData.filter((item:any) => item.WORKFLOW_RESULT != null)
            let result = apply_list_1.reduce((accumulator:any, currentValue:any) => {
                let index = accumulator.findIndex((item:any) => item.APPLY_CODE === currentValue.APPLY_CODE);
                if(index > -1 && accumulator[index].WORKFLOW_INDEX < currentValue.WORKFLOW_INDEX) {
                    accumulator[index] = currentValue;
                } else if (index === -1) {
                    accumulator.push(currentValue);
                }
                return accumulator;
                }, []);
            state.evectionApplication = result;
            state.overtimeCode = action.payload;
        },
        handleOvertimeAgree: (state: approvalState, action: PayloadAction<any>) => {
            state.status = ApiFetchVars.todo;
            // 修改WORKFLOW_RESULT属性的值
            const agreeData = state.overtimeApplication.filter(item => item.APPLY_CODE === action.payload);
            const agreeFromApplicationState = agreeData.map((value, index) => {
                if (value.APPLY_CODE === action.payload) {
                    value.WORKFLOW_RESULT = 1;
                    value.WORKFLOW_LIST[value.WORKFLOW_INDEX].workflow_result = 1;
                    return { ...value }; // 将state更新为1  
                } else {
                    return { ...value }; // 保持其他元素不变  
                }
            });
            const otherData = state.overtimeApplication.filter(item => item.APPLY_CODE !== action.payload);
            // 在设置state.evectionApplication之前，先对数组进行排序 
            const sortedData = [...otherData, ...agreeFromApplicationState];
            const apply_list_1 = sortedData.filter((item:any) => item.WORKFLOW_RESULT != null)
                let result = apply_list_1.reduce((accumulator:any, currentValue:any) => {
                    let index = accumulator.findIndex((item:any) => item.APPLY_CODE === currentValue.APPLY_CODE);
                    if(index > -1 && accumulator[index].WORKFLOW_INDEX < currentValue.WORKFLOW_INDEX) {
                      accumulator[index] = currentValue;
                    } else if (index === -1) {
                      accumulator.push(currentValue);
                    }
                    return accumulator;
                  }, []);
            state.overtimeApplication = result;
            state.overtimeCode = action.payload;
        },
        handleConfirmationAgree: (state: approvalState, action: PayloadAction<any>) => {
            state.status = ApiFetchVars.todo;
            const agreeData = state.confirmationApplication.filter(item => item.APPLY_CODE === action.payload);
            const agreeFromApplicationState = agreeData.map((value, index) => {
                if (value.APPLY_CODE === action.payload) {
                    value.WORKFLOW_RESULT = 1;
                    value.WORKFLOW_LIST[value.WORKFLOW_INDEX].workflow_result = 1;
                    return { ...value }; // 将state更新为1  
                } else {
                    return { ...value }; // 保持其他元素不变  
                }
            });
            const otherData = state.confirmationApplication.filter(item => item.APPLY_CODE !== action.payload);
            // 在设置state.evectionApplication之前，先对数组进行排序  
            const sortedData = [...otherData, ...agreeFromApplicationState];
            const apply_list_1 = sortedData.filter((item:any) => item.WORKFLOW_RESULT != null)
                let result = apply_list_1.reduce((accumulator:any, currentValue:any) => {
                    let index = accumulator.findIndex((item:any) => item.APPLY_CODE === currentValue.APPLY_CODE);
                    if(index > -1 && accumulator[index].WORKFLOW_INDEX < currentValue.WORKFLOW_INDEX) {
                        accumulator[index] = currentValue;
                    } else if (index === -1) {
                        accumulator.push(currentValue);
                    }
                    return accumulator;
                    }, []);
            state.confirmationApplication = result;
            state.overtimeCode = action.payload;
        },
        //驳回当前申请
        handleLeaveDelete: (state: approvalState, action: PayloadAction<any>) => {
            state.status = ApiFetchVars.todo;
            const rejectData = state.leaveApplication.filter(item => item.APPLY_CODE === action.payload);
            const rejectFromAllApplicationState = rejectData.map((value, index) => {
                if (value.APPLY_CODE === action.payload) {
                    value.WORKFLOW_RESULT = 2;
                    value.WORKFLOW_LIST[value.WORKFLOW_INDEX].workflow_result = 2;
                    return { ...value }; // 将state更新为0  
                } else {
                    return { ...value }; // 保持其他元素不变  
                }
            });
            const otherData = state.leaveApplication.filter(item => item.APPLY_CODE !== action.payload);
            const sortedData = [...otherData, ...rejectFromAllApplicationState];
            state.leaveApplication = sortedData;
            state.overtimeCode = action.payload;
        },
        handleEvectionDelete: (state: approvalState, action: PayloadAction<any>) => {
            state.status = ApiFetchVars.todo;
            const rejectData = state.evectionApplication.filter(item => item.APPLY_CODE === action.payload);
            const rejectFromAllApplicationState = rejectData.map((value, index) => {
                if (value.APPLY_CODE === action.payload) {
                    value.WORKFLOW_RESULT = 2;
                    value.WORKFLOW_LIST[value.WORKFLOW_INDEX].workflow_result = 2;
                    return { ...value }; // 将state更新为0  
                } else {
                    return { ...value }; // 保持其他元素不变  
                }
            });
            const otherData = state.evectionApplication.filter(item => item.APPLY_CODE !== action.payload);
            // 在设置state.evectionApplication之前，先对数组进行排序  
            const sortedData = [...otherData, ...rejectFromAllApplicationState];
            state.evectionApplication = sortedData;
            state.overtimeCode = action.payload;
        },
        handleOvertimeDelete: (state: approvalState, action: PayloadAction<any>) => {
            state.status = ApiFetchVars.todo;
            // 修改WORKFLOW_RESULT属性的值
            const agreeData = state.overtimeApplication.filter(item => item.APPLY_CODE === action.payload);
            const agreeFromApplicationState = agreeData.map((value, index) => {
                if (value.APPLY_CODE === action.payload) {
                    value.WORKFLOW_RESULT = 2;
                    value.WORKFLOW_LIST[value.WORKFLOW_INDEX].workflow_result = 2;
                    return { ...value };
                } else {
                    return { ...value }; // 保持其他元素不变  
                }
            });
            const otherData = state.overtimeApplication.filter(item => item.APPLY_CODE !== action.payload);
            const sortedData = [...otherData, ...agreeFromApplicationState];
            state.overtimeApplication = sortedData;
            state.overtimeCode = action.payload;
        },
        handleConfirmationDelete: (state: approvalState, action: PayloadAction<any>) => {
            state.status = ApiFetchVars.todo;
            const rejectData = state.confirmationApplication.filter(item => item.APPLY_CODE === action.payload);
            const rejectFromAllApplicationState = rejectData.map((value, index) => {
                if (value.APPLY_CODE === action.payload) {
                    value.WORKFLOW_RESULT = 2;
                    value.WORKFLOW_LIST[value.WORKFLOW_INDEX].workflow_result = 2;
                    return { ...value }; // 将state更新为0  
                } else {
                    return { ...value }; // 保持其他元素不变  
                }
            });
            const otherData = state.confirmationApplication.filter(item => item.APPLY_CODE !== action.payload);
            const sortedData = [...otherData, ...rejectFromAllApplicationState];
            state.confirmationApplication = sortedData;
            state.overtimeCode = action.payload;
        },
        // 类型切换
        typeChange: (state: approvalState) => {
            state.isLeaveChecked = false;
            state.isOvertimeChecked = false;
            state.isBusinessChecked = false;
            state.isEditChecked = false;
            state.leaveKeyList = [];
            state.overtimeKeyList = [];
            state.evectionKeyList = [];
            state.confirmationKeyList = [];
        },
        // 关键字查询
        filterAgrees: (state: approvalState, action: PayloadAction<any>) => {
            state.status = ApiFetchVars.todo;
            // 获取关键词
            state.keyword = action.payload;
            state.isLeaveChecked = false;
            state.isOvertimeChecked = false;
            state.isBusinessChecked = false;
            state.isEditChecked = false;
            state.leaveKeyList = [];
            state.overtimeKeyList = [];
            state.evectionKeyList = [];
            state.confirmationKeyList = [];
        },
        // 开始日期选择器的状态更新  
        startTimeValues: (state: approvalState, action: PayloadAction<any>) => {
            // 获取开始日期
            state.startTime = action.payload;
            state.isLeaveChecked = false;
            state.isOvertimeChecked = false;
            state.isBusinessChecked = false;
            state.isEditChecked = false;
            state.leaveKeyList = [];
            state.overtimeKeyList = [];
            state.evectionKeyList = [];
            state.confirmationKeyList = [];
        },
        // 结束日期选择器的状态更新  
        endTimeValues: (state: approvalState, action: PayloadAction<any>) => {
            // 获取结束日期
            state.endTime = action.payload;
            state.isLeaveChecked = false;
            state.isOvertimeChecked = false;
            state.isBusinessChecked = false;
            state.isEditChecked = false;
            state.leaveKeyList = [];
            state.overtimeKeyList = [];
            state.evectionKeyList = [];
            state.confirmationKeyList = [];
        },
        // 确认单日期选择器的状态更新  
        confirmationTimeValues: (state: approvalState, action: PayloadAction<any>) => {
            state.confirmationTime = action.payload;
        },
        // 批量管理
        handleManage: (state: approvalState) => {
            state.showBatch = true;
        },
        // 返回申请一览
        handleReturn: (state: approvalState) => {
            state.showBatch = false;
        },
        //leave审批选择栏
        leaveCheckboxAll: (state: approvalState, action: PayloadAction<any>) => {
            const key_word = action.payload[0];
            const start_time = action.payload[1].format('YYYY-MM-DDT') + '09:00:00';
            const end_time = action.payload[2].format('YYYY-MM-DDT') + '18:00:00';
            const type = action.payload[3];
            let list:any = [];
            if (type == '全部' || type == '待审批'){
                list = state.leaveApplication.filter((item:any)=>(
                    item.WORKFLOW_RESULT == 0 && item.USER_ID.includes(key_word) && start_time <= item.START_TIME && end_time >= item.START_TIME));
            }else{
                list = [];
            }
            if (state.isLeaveChecked) {
                state.isLeaveChecked = false;
                state.leaveKeyList = [];
            }else{
                state.isLeaveChecked = true;
                let all: any[] = [];
                if (list.length > 0){
                    list.map((value: any, index: any) => {
                        if(index<state.approvalChooseLimit){
                            all.push(value.APPLY_CODE);
                        }
                    })
                }
                state.leaveKeyList = all;
            }
        },
        leaveCheckboxClick: (state: approvalState, action: PayloadAction<any>) => {
            if (state.leaveKeyList.includes(action.payload)) {
                const keyList = state.leaveKeyList.filter((item: any) => item !== action.payload)
                state.leaveKeyList = keyList;
            } else {
                state.leaveKeyList = [...state.leaveKeyList, action.payload]
            }
            let all: any[] = [];
            const list =  state.leaveApplication.filter((item: any) => 
            (item.WORKFLOW_RESULT == 0 && item.USER_ID.includes(state.keyword) && 
            state.startTime.format('YYYY-MM-DDT') + '09:00:00' <= item.START_TIME && 
            state.endTime.format('YYYY-MM-DDT') + '18:00:00' >= item.START_TIME))
            list.map((value: any, index: any) => {
                if (index < state.approvalChooseLimit){
                        all.push(value.APPLY_CODE);
                }
            })
            console.log(all)
            if (state.leaveKeyList.length == state.approvalChooseLimit){
                state.isLeaveChecked = true;
            }else{
                if (state.leaveKeyList.length != all.length){
                    state.isLeaveChecked = false;
                }else{
                    const set1:any = new Set(state.leaveKeyList);
                    const set2:any = new Set(all);
                    let isBoolean = true;
                    for (const item of set1) {
                        if (!set2.has(item)) {
                            isBoolean = false;
                        }
                    }
                    if(isBoolean){
                        state.isLeaveChecked = true;
                    }else{
                        state.isLeaveChecked = false;
                    }
                    
                }
            }
        },
        leaveSingleApproval: (state: approvalState, action: PayloadAction<any>) => {
            if (state.leaveKeyList.includes(action.payload)) {
                const keyList = state.leaveKeyList.filter((item: any) => item !== action.payload)
                state.leaveKeyList = keyList;
            }
            let all: any[] = [];
            const list =  state.leaveApplication.filter((item: any) => 
            (item.WORKFLOW_RESULT == 0 && item.USER_ID.includes(state.keyword) && 
            state.startTime.format('YYYY-MM-DDT') + '09:00:00' <= item.START_TIME && 
            state.endTime.format('YYYY-MM-DDT') + '18:00:00' >= item.START_TIME))
            list.map((value: any, index: any) => {
                if (index < state.approvalChooseLimit){
                        all.push(value.APPLY_CODE);
                }
            })
            console.log(all)
            if (state.leaveKeyList.length == state.approvalChooseLimit){
                state.isLeaveChecked = true;
            }else{
                if (state.leaveKeyList.length != all.length){
                    state.isLeaveChecked = false;
                }else{
                    const set1:any = new Set(state.leaveKeyList);
                    const set2:any = new Set(all);
                    let isBoolean = true;
                    for (const item of set1) {
                        if (!set2.has(item)) {
                            isBoolean = false;
                        }
                    }
                    if(isBoolean){
                        state.isLeaveChecked = true;
                    }else{
                        state.isLeaveChecked = false;
                    }
                    
                }
            }
        },
        leaveMultipleAgree: (state: approvalState) => {
            state.leaveKeyList = [];
            state.isLeaveChecked = false;
            // state.leaveApplication = state.leaveApplication.map(item => {
            //     if (state.leaveKeyList.includes(item.APPLY_CODE)) {
            //         return {
            //             ...item,
            //             WORKFLOW_RESULT: 1,
            //             WORKFLOW_LIST:
            //                 [{
            //                     workflow_index: item.WORKFLOW_INDEX,
            //                     workflow_role_name: "",
            //                     workflow_result: 1,
            //                     handle_user_id: "",
            //                     handle_user_name: "",
            //                     update_time: ""
            //                 }],
            //         };
            //     }
            //     return item;
            // });
        },
        leaveMultipleReject: (state: approvalState) => {
            // state.leaveApplication = state.leaveApplication.map(item => {
            //     if (state.leaveKeyList.includes(item.APPLY_CODE)) {
            //         return {
            //             ...item,
            //             WORKFLOW_RESULT: 2,
            //             WORKFLOW_LIST:
            //                 [{
            //                     workflow_index: item.WORKFLOW_INDEX,
            //                     workflow_result: 2,
            //                     handle_user_id: "",
            //                     update_time: "",
            //                     workflow_role_name: "",
            //                     handle_user_name: ""
            //                 }],
            //         };
            //     }
            //     return item;
            // });
            state.leaveKeyList = [];
            state.isLeaveChecked = false;
        },

        //overtime审批选择栏
        overtimeCheckboxAll: (state: approvalState, action: PayloadAction<any>) => {
            const key_word = action.payload[0];
            const start_time = action.payload[1].format('YYYY-MM-DD');
            const end_time = action.payload[2].format('YYYY-MM-DD');
            const type = action.payload[3];
            let list:any = [];
            if (type == '全部' || type == '待审批'){
                list = state.overtimeApplication.filter((item:any)=>(
                    item.WORKFLOW_RESULT == 0 && item.USER_ID.includes(key_word) && 
                    start_time <= dayjs(item.START_TIME).format('YYYY-MM-DD') && 
                    end_time >= dayjs(item.START_TIME).format('YYYY-MM-DD')));
            }else{
                list = [];
            }
            if (state.isOvertimeChecked) {
                state.isOvertimeChecked = false;
                state.overtimeKeyList = [];
            }else{
                state.isOvertimeChecked = true;
                let all: any[] = [];
                if (list.length > 0){
                    list.map((value: any, index: any) => {
                        if(index<state.approvalChooseLimit){
                            all.push(value.APPLY_CODE);
                        }
                    })
                }
                
                state.overtimeKeyList = all;
            }
        },
        overtimeCheckboxClick: (state: approvalState, action: PayloadAction<any>) => {
            if (state.overtimeKeyList.includes(action.payload)) {
                const keyList = state.overtimeKeyList.filter((item: any) => item !== action.payload)
                state.overtimeKeyList = keyList;
            } else {
                state.overtimeKeyList = [...state.overtimeKeyList, action.payload]
            }
            let all: any[] = [];
            const list = state.overtimeApplication.filter((item: any) => (item.WORKFLOW_RESULT == 0 && item.USER_ID.includes(state.keyword) && 
            (dayjs(item.START_TIME).format('YYYY-MM-DD') >= dayjs(state.startTime).format('YYYY-MM-DD')) && 
            (dayjs(item.START_TIME).format('YYYY-MM-DD') <= dayjs(state.endTime).format('YYYY-MM-DD'))))
            list.map((value: any, index: any) => {
                if (index < state.approvalChooseLimit){
                        all.push(value.APPLY_CODE);
                }
            })
            if (state.overtimeKeyList.length == state.approvalChooseLimit){
                state.isOvertimeChecked = true;
            }else{
                if (state.overtimeKeyList.length != all.length){
                    state.isOvertimeChecked = false;
                }else{
                    const set1:any = new Set(state.overtimeKeyList);
                    const set2:any = new Set(all);
                    let isBoolean = true;
                    for (const item of set1) {
                        if (!set2.has(item)) {
                            isBoolean = false;
                        }
                    }
                    if(isBoolean){
                        state.isOvertimeChecked = true;
                    }else{
                        state.isOvertimeChecked = false;
                    }
                }
            }
        },
        overtimeSingleApproval: (state: approvalState, action: PayloadAction<any>) => {
            if (state.overtimeKeyList.includes(action.payload)) {
                const keyList = state.overtimeKeyList.filter((item: any) => item !== action.payload)
                state.overtimeKeyList = keyList;
            }
            let all: any[] = [];
            const list = state.overtimeApplication.filter((item: any) => (item.WORKFLOW_RESULT == 0 && item.USER_ID.includes(state.keyword) && 
            (dayjs(item.START_TIME).format('YYYY-MM-DD') >= dayjs(state.startTime).format('YYYY-MM-DD')) && 
            (dayjs(item.START_TIME).format('YYYY-MM-DD') <= dayjs(state.endTime).format('YYYY-MM-DD'))))
            list.map((value: any, index: any) => {
                if (index < state.approvalChooseLimit){
                        all.push(value.APPLY_CODE);
                }
            })
            if (state.overtimeKeyList.length == state.approvalChooseLimit){
                state.isOvertimeChecked = true;
            }else{
                if (state.overtimeKeyList.length != all.length){
                    state.isOvertimeChecked = false;
                }else{
                    const set1:any = new Set(state.overtimeKeyList);
                    const set2:any = new Set(all);
                    let isBoolean = true;
                    for (const item of set1) {
                        if (!set2.has(item)) {
                            isBoolean = false;
                        }
                    }
                    if(isBoolean){
                        state.isOvertimeChecked = true;
                    }else{
                        state.isOvertimeChecked = false;
                    }
                }
            }
        },
        overtimeMultipleAgree: (state: approvalState) => {
            // state.overtimeApplication = state.overtimeApplication.map(item => {
            //     if (state.overtimeKeyList.includes(item.APPLY_CODE)) {
            //         return {
            //             ...item,
            //             WORKFLOW_RESULT: 1,
            //             WORKFLOW_LIST:
            //                 [{
            //                     workflow_index: item.WORKFLOW_INDEX,
            //                     workflow_result: 1,
            //                     workflow_role_name:"",
            //                     handle_user_id: "",
            //                     handle_user_name: "",
            //                     update_time: "",
            //                 }],
            //         };
            //     }
            //     return item;
            // });
            state.isOvertimeChecked = false;
            state.overtimeKeyList = [];
        },
        overtimeMultipleReject: (state: approvalState) => {
            // state.overtimeApplication = state.overtimeApplication.map(item => {
            //     if (state.overtimeKeyList.includes(item.APPLY_CODE)) {
            //         return {
            //             ...item,
            //             WORKFLOW_RESULT: 2,
            //             WORKFLOW_LIST:
            //                 [{
            //                     workflow_index: item.WORKFLOW_INDEX,
            //                     workflow_result: 2,
            //                     workflow_role_name:"",
            //                     handle_user_id: "",
            //                     handle_user_name: "",
            //                     update_time: "",
            //                 }],
            //         };
            //     }
            //     return item;
            // });
            state.isOvertimeChecked = false;
            state.overtimeKeyList = [];
        },

        //evection审批选择栏
        evectionCheckboxAll: (state: approvalState, action: PayloadAction<any>) => {
            const key_word = action.payload[0];
            const start_time = action.payload[1].format('YYYY-MM-DDT') + '09:00:00';
            let end_time = '';
            if(action.payload[2]){
                end_time = action.payload[2].format('YYYY-MM-DDT') + '18:00:00';
            }
            const type = action.payload[3];
            let list:any = [];
            if (type == '全部' || type == '待审批'){
                if (action.payload[2]){
                    list = state.evectionApplication.filter((item:any)=>(
                        item.WORKFLOW_RESULT == 0 && item.USER_ID.includes(key_word) && start_time <= item.START_TIME && end_time >= item.START_TIME));
                }else{
                    list = state.evectionApplication.filter((item:any)=>(
                        item.WORKFLOW_RESULT == 0 && item.USER_ID.includes(key_word) && start_time <= item.START_TIME));
                }
            }else{
                list = [];
            }
            if (state.isBusinessChecked) {
                state.isBusinessChecked = false;
                state.evectionKeyList = [];
            }else{
                state.isBusinessChecked = true;
                let all: any[] = [];
                if (list.length > 0){
                    list.map((value: any, index: any) => {
                        if(index<state.approvalChooseLimit){
                            all.push(value.APPLY_CODE);
                        }
                    })
                }
                state.evectionKeyList = all;
            }
        },
        evectionCheckboxClick: (state: approvalState, action: PayloadAction<any>) => {
            if (state.evectionKeyList.includes(action.payload[0])) {
                const keyList = state.evectionKeyList.filter((item: any) => item !== action.payload[0])
                state.evectionKeyList = keyList;
            } else {
                state.evectionKeyList = [...state.evectionKeyList, action.payload[0]]
            }
            let endEvectionTime = action.payload[1];
            let all: any[] = [];
            let list:any;
            if(endEvectionTime){
                list = state.evectionApplication.filter((item: any) => (item.WORKFLOW_RESULT == 0 && item.USER_ID.includes(state.keyword) && 
                (state.startTime.format('YYYY-MM-DDT') + '09:00:00' <= (item.START_TIME)) && 
                (endEvectionTime.format('YYYY-MM-DDT') + '18:00:00' >= (item.START_TIME))))
            }else{
                list = state.evectionApplication.filter((item: any) => (item.WORKFLOW_RESULT == 0 && item.USER_ID.includes(state.keyword) && 
                (state.startTime.format('YYYY-MM-DDT') + '09:00:00' <= (item.START_TIME))))
            }    
            list.map((value: any, index: any) => {
                if(index<state.approvalChooseLimit){
                    all.push(value.APPLY_CODE);
                }
                
            })
            if (state.evectionKeyList.length == state.approvalChooseLimit){
                state.isBusinessChecked = true;
            }else{
                if (state.evectionKeyList.length != all.length){
                    state.isBusinessChecked = false;
                }else{
                    const set1:any = new Set(state.evectionKeyList);
                    const set2:any = new Set(all);
                    let isBoolean = true;
                    for (const item of set1) {
                        if (!set2.has(item)) {
                            isBoolean = false;
                        }
                    }
                    if(isBoolean){
                        state.isBusinessChecked = true;
                    }else{
                        state.isBusinessChecked = false;
                    }
                }
            }
        },
        evectionSingleApproval: (state: approvalState, action: PayloadAction<any>) => {
            if (state.evectionKeyList.includes(action.payload[0])) {
                const keyList = state.evectionKeyList.filter((item: any) => item !== action.payload[0])
                state.evectionKeyList = keyList;
            }
            let endEvectionTime = action.payload[1];
            let all: any[] = [];
            let list:any;
            if(endEvectionTime){
                list = state.evectionApplication.filter((item: any) => (item.WORKFLOW_RESULT == 0 && item.USER_ID.includes(state.keyword) && 
                (state.startTime.format('YYYY-MM-DDT') + '09:00:00' <= (item.START_TIME)) && 
                (endEvectionTime.format('YYYY-MM-DDT') + '18:00:00' >= (item.START_TIME))))
            }else{
                list = state.evectionApplication.filter((item: any) => (item.WORKFLOW_RESULT == 0 && item.USER_ID.includes(state.keyword) && 
                (state.startTime.format('YYYY-MM-DDT') + '09:00:00' <= (item.START_TIME))))
            }    
            list.map((value: any, index: any) => {
                if(index<state.approvalChooseLimit){
                    all.push(value.APPLY_CODE);
                }
                
            })
            if (state.evectionKeyList.length == state.approvalChooseLimit){
                state.isBusinessChecked = true;
            }else{
                if (state.evectionKeyList.length != all.length){
                    state.isBusinessChecked = false;
                }else{
                    const set1:any = new Set(state.evectionKeyList);
                    const set2:any = new Set(all);
                    let isBoolean = true;
                    for (const item of set1) {
                        if (!set2.has(item)) {
                            isBoolean = false;
                        }
                    }
                    if(isBoolean){
                        state.isBusinessChecked = true;
                    }else{
                        state.isBusinessChecked = false;
                    }
                }
            }
        },
        evectionMultipleAgree: (state: approvalState) => {
            // state.evectionApplication = state.evectionApplication.map(item => {
            //     if (state.evectionKeyList.includes(item.APPLY_CODE)) {
            //         return {
            //             ...item,
            //             WORKFLOW_RESULT: 1,
            //             WORKFLOW_LIST:
            //                 [{
            //                     workflow_index: item.WORKFLOW_INDEX,
            //                     workflow_result: 1,
            //                     handle_user_id: "",
            //                     update_time: "",
            //                     workflow_role_name: "",
            //                     handle_user_name: ""
            //                 }],
            //         };
            //     }
            //     return item;
            // });
            state.isBusinessChecked = false;
            state.evectionKeyList = [];
        },
        evectionMultipleReject: (state: approvalState) => {
            // state.evectionApplication = state.evectionApplication.map(item => {
            //     if (state.evectionKeyList.includes(item.APPLY_CODE)) {
            //         return {
            //             ...item,
            //             WORKFLOW_RESULT: 2,
            //             WORKFLOW_LIST:
            //                 [{
            //                     workflow_index: item.WORKFLOW_INDEX,
            //                     workflow_result: 2,
            //                     handle_user_id: "",
            //                     update_time: "",
            //                     workflow_role_name: "",
            //                     handle_user_name: ""
            //                 }],
            //         };
            //     }
            //     return item;
            // });
            state.isBusinessChecked = false;
            state.evectionKeyList = [];
        },

        //confirmation审批选择栏
        confirmationCheckboxAll: (state: approvalState, action: PayloadAction<any>) => {
            const key_word = action.payload[0];
            const start_time = action.payload[1].format('YYYY-MM-DDT') + '00:00:00';
            const end_time = action.payload[2].format('YYYY-MM-DDT') + '23:59:59';
            const type = action.payload[3];
            let list:any = [];
            if (type == '全部' || type == '待审批'){
                list = state.confirmationApplication.filter((item:any)=>(
                    item.WORKFLOW_RESULT == 0 && item.USER_ID.includes(key_word) && start_time <= item.REAL_START_TIME && end_time >= item.REAL_START_TIME));
            }else{
                list = [];
            }
            if (state.isEditChecked) {
                state.isEditChecked = false;
                state.confirmationKeyList = [];
            }else{
                state.isEditChecked = true;
                let all: any[] = [];
                if (list.length > 0){
                    list.map((value: any, index: any) => {
                        if(index<state.approvalChooseLimit){
                            all.push(value.APPLY_CODE);
                        }
                    })
                }
                state.confirmationKeyList = all;
            }
        },
        confirmationCheckboxClick: (state: approvalState, action: PayloadAction<any>) => {
            if (state.confirmationKeyList.includes(action.payload)) {
                const keyList = state.confirmationKeyList.filter((item: any) => item !== action.payload)
                state.confirmationKeyList = keyList;
            } else {
                state.confirmationKeyList = [...state.confirmationKeyList, action.payload]
            }
            let all: any[] = [];
            const list =  state.confirmationApplication.filter((item: any) => (item.WORKFLOW_RESULT == 0 && item.USER_ID.includes(state.keyword) && 
            (state.startTime.format('YYYY-MM-DDT') + '00:00:00' <= (item.REAL_START_TIME)) && 
            (state.endTime.format('YYYY-MM-DDT') + '23:59:59' >= (item.REAL_START_TIME))))
            list.map((value: any, index: any) => {
                if(index<state.approvalChooseLimit){
                    all.push(value.APPLY_CODE);
                }
            })
            if (state.confirmationKeyList.length == state.approvalChooseLimit){
                state.isEditChecked = true;
            }else{
                if (state.confirmationKeyList.length != all.length){
                    state.isEditChecked = false;
                }else{
                    const set1:any = new Set(state.confirmationKeyList);
                    const set2:any = new Set(all);
                    let isBoolean = true;
                    for (const item of set1) {
                        if (!set2.has(item)) {
                            isBoolean = false;
                        }
                    }
                    if(isBoolean){
                        state.isEditChecked = true;
                    }else{
                        state.isEditChecked = false;
                    }
                }
            }
        },
        confirmationSingleApproval: (state: approvalState, action: PayloadAction<any>) => {
            if (state.confirmationKeyList.includes(action.payload)) {
                const keyList = state.confirmationKeyList.filter((item: any) => item !== action.payload)
                state.confirmationKeyList = keyList;
            }
            let all: any[] = [];
            const list =  state.confirmationApplication.filter((item: any) => (item.WORKFLOW_RESULT == 0 && item.USER_ID.includes(state.keyword) && 
            (state.startTime.format('YYYY-MM-DDT') + '00:00:00' <= (item.REAL_START_TIME)) && 
            (state.endTime.format('YYYY-MM-DDT') + '23:59:59' >= (item.REAL_START_TIME))))
            list.map((value: any, index: any) => {
                if(index<state.approvalChooseLimit){
                    all.push(value.APPLY_CODE);
                }
            })
            if (state.confirmationKeyList.length == state.approvalChooseLimit){
                state.isEditChecked = true;
            }else{
                if (state.confirmationKeyList.length != all.length){
                    state.isEditChecked = false;
                }else{
                    const set1:any = new Set(state.confirmationKeyList);
                    const set2:any = new Set(all);
                    let isBoolean = true;
                    for (const item of set1) {
                        if (!set2.has(item)) {
                            isBoolean = false;
                        }
                    }
                    if(isBoolean){
                        state.isEditChecked = true;
                    }else{
                        state.isEditChecked = false;
                    }
                }
            }
        },
        confirmationMultipleAgree: (state: approvalState) => {
            // state.confirmationApplication = state.confirmationApplication.map(item => {
            //     if (state.confirmationKeyList.includes(item.APPLY_CODE)) {
            //         return {
            //             ...item,
            //             WORKFLOW_RESULT: 1,
            //             WORKFLOW_LIST:
            //                 [{
            //                     workflow_index: item.WORKFLOW_INDEX,
            //                     workflow_result: 1,
            //                     handle_user_id: "",
            //                     update_time: "",
            //                     workflow_role_name: "",
            //                     handle_user_name: ""
            //                 }],
            //         };
            //     }
            //     return item;
            // });
            state.isEditChecked = false;
            state.confirmationKeyList = [];
        },
        confirmationMultipleReject: (state: approvalState) => {
            // state.confirmationApplication = state.confirmationApplication.map(item => {
            //     if (state.confirmationKeyList.includes(item.APPLY_CODE)) {
            //         return {
            //             ...item,
            //             WORKFLOW_RESULT: 2,
            //             WORKFLOW_LIST:
            //                 [{
            //                     workflow_index: item.WORKFLOW_INDEX,
            //                     workflow_result: 2,
            //                     handle_user_id: "",
            //                     update_time: "",
            //                     workflow_role_name: "",
            //                     handle_user_name: ""
            //                 }],
            //         };
            //     }
            //     return item;
            // });
            state.isEditChecked = false;
            state.confirmationKeyList = [];
        },
        handleLeaveApplicationAdd: (state: approvalState, action: PayloadAction<any>) => {
            const apply_list = action.payload?.apply_list;
            const code = apply_list.APPLY_CODE;
            const workflow_list = action.payload.workflow_list ? action.payload.workflow_list[code] : [];
            if(state.leaveApplication){
                state.leaveApplication.push({
                    APPLY_CODE: apply_list.APPLY_CODE,
                    USER_ID: apply_list.USER_ID,
                    AGENT_USER_ID: apply_list.AGENT_USER_ID,
                    APPLY_TYPE_ID: apply_list.APPLY_TYPE_ID,
                    APPLY_TYPE_NAME: apply_list.APPLY_TYPE_NAME,
                    START_TIME: apply_list.START_TIME,
                    END_TIME: apply_list.END_TIME,
                    NORMAL_HOURS: apply_list.NORMAL_HOURS,
                    COMPENSATORY_HOURS: apply_list.COMPENSATORY_HOURS,
                    SICK_HOURS: apply_list.SICK_HOURS,
                    OTHER_APPLY_TYPE: apply_list.OTHER_APPLY_TYPE,
                    OTHER_DAYS: apply_list.OTHER_DAYS,
                    REASON: apply_list.REASON,
                    WORKFLOW_INDEX: apply_list.WORKFLOW_INDEX,
                    WORKFLOW_RESULT: apply_list.WORKFLOW_RESULT,
                    reject_reason: apply_list.reject_reason,
                    WORKFLOW_LIST: workflow_list
                });
            }
            state.isLeaveChecked = false;
            state.leaveKeyList = [];
        },
        handleOvertimeApplicationAdd: (state: approvalState, action: PayloadAction<any>) => {
            const apply_list = action.payload?.apply_list;
            const code = apply_list.APPLY_CODE;
            const workflow_list = action.payload.workflow_list ? action.payload.workflow_list[code] : [];
            if(state.overtimeApplication){
                state.overtimeApplication.push({
                    APPLY_CODE: apply_list.APPLY_CODE,
                    USER_ID: apply_list.USER_ID,
                    AGENT_USER_ID: apply_list.AGENT_USER_ID,
                    APPLY_TYPE_ID: apply_list.APPLY_TYPE_ID,
                    APPLY_TYPE_NAME: apply_list.APPLY_TYPE_NAME,
                    START_TIME: apply_list.START_TIME,
                    END_TIME: apply_list.END_TIME,
                    HOURS: apply_list.HOURS,
                    REASON: apply_list.REASON,
                    WORKFLOW_INDEX: apply_list.WORKFLOW_INDEX,
                    WORKFLOW_RESULT: apply_list.WORKFLOW_RESULT,
                    reject_reason: apply_list.reject_reason,
                    WORKFLOW_LIST: workflow_list
                });
            }
            state.isOvertimeChecked = false;
            state.overtimeKeyList = [];
        },
        handleBusinessApplicationAdd: (state: approvalState, action: PayloadAction<any>) => {
            const apply_list = action.payload?.apply_list;
            const code = apply_list.APPLY_CODE;
            const workflow_list = action.payload.workflow_list ? action.payload.workflow_list[code] : [];
            if(state.evectionApplication){
                state.evectionApplication.push({
                    APPLY_CODE: apply_list.APPLY_CODE,
                    USER_ID: apply_list.USER_ID,
                    AGENT_USER_ID: apply_list.AGENT_USER_ID,
                    APPLY_TYPE_ID: apply_list.APPLY_TYPE_ID,
                    APPLY_TYPE_NAME: apply_list.APPLY_TYPE_NAME,
                    START_TIME: apply_list.START_TIME,
                    END_TIME: apply_list.END_TIME,
                    LOCATION: apply_list.LOCATION,
                    T_DAYS: apply_list.T_DAYS,
                    REASON: apply_list.REASON,
                    WORKFLOW_INDEX: apply_list.WORKFLOW_INDEX,
                    WORKFLOW_RESULT: apply_list.WORKFLOW_RESULT,
                    reject_reason: apply_list.reject_reason,
                    WORKFLOW_LIST: workflow_list
                });
            }
            state.isBusinessChecked = false;
            state.evectionKeyList = [];
        },
        handleConfirmApplicationAdd: (state: approvalState, action: PayloadAction<any>) => {
            const apply_list = action.payload?.apply_list;
            const code = apply_list.APPLY_CODE;
            const workflow_list = action.payload.workflow_list ? action.payload.workflow_list[code] : [];
            if(state.confirmationApplication){
                state.confirmationApplication.push({
                    APPLY_CODE: apply_list.APPLY_CODE,
                    USER_ID: apply_list.USER_ID,
                    AGENT_USER_ID: apply_list.AGENT_USER_ID,
                    APPLY_TYPE_ID: apply_list.APPLY_TYPE_ID,
                    APPLY_TYPE_NAME: apply_list.APPLY_TYPE_NAME,
                    DAY: apply_list.DAY,
                    WORK_START_TIME: apply_list.WORK_START_TIME,
                    WORK_END_TIME: apply_list.WORK_END_TIME,
                    REAL_START_TIME: apply_list.REAL_START_TIME,
                    REAL_END_TIME: apply_list.REAL_END_TIME,
                    REASON: apply_list.REASON,
                    WORKFLOW_INDEX: apply_list.WORKFLOW_INDEX,
                    WORKFLOW_RESULT: apply_list.WORKFLOW_RESULT,
                    reject_reason: apply_list.reject_reason,
                    WORKFLOW_LIST: workflow_list
                });
            }
            state.isEditChecked = false;
            state.confirmationKeyList = [];
        },
        handleLeaveApplicationDelete: (state: approvalState, action: PayloadAction<any>) => {
            const code = action.payload.apply_code;
            if(state.leaveApplication){
                state.leaveApplication = state.leaveApplication.filter((item:any)=>(item.APPLY_CODE !== code));
            }
            state.isLeaveChecked = false;
            state.leaveKeyList = [];
        },
        handleOvertimeApplicationDelete: (state: approvalState, action: PayloadAction<any>) => {
            const code = action.payload.apply_code;
            if(state.overtimeApplication){
                state.overtimeApplication = state.overtimeApplication.filter((item:any)=>(item.APPLY_CODE !== code));
            }
            state.isOvertimeChecked = false;
            state.overtimeKeyList = [];
        },
        handleBusinessDelete: (state: approvalState, action: PayloadAction<any>) => {
            const code = action.payload.apply_code;
            if(state.evectionApplication){
                state.evectionApplication = state.evectionApplication.filter((item:any)=>(item.APPLY_CODE !== code));
            }
            state.isBusinessChecked = false;
            state.evectionKeyList = [];
        },
        handleConfirmDelete: (state: approvalState, action: PayloadAction<any>) => {
            const code = action.payload.apply_code;
            if(state.confirmationApplication){
                state.confirmationApplication = state.confirmationApplication.filter((item:any)=>(item.APPLY_CODE !== code));
            }
            state.isEditChecked = false;
            state.confirmationKeyList = [];
        },
        //锁住申请
        handleLock: (state: approvalState, action: PayloadAction<any>) => {
            let isCode = false;
            const code = action.payload.APPLY_CODE;
            if(state.leaveApplication){
                state.leaveApplication.map((item:any)=>{
                    if(item.APPLY_CODE == code){
                        isCode = true
                    }
                })
                if(isCode){
                    state.leaveApplication = state.leaveApplication.filter(item=> item.APPLY_CODE != code);
                }
                isCode = false
            }
            if(state.overtimeApplication){
                state.overtimeApplication.map((item:any)=>{
                    if(item.APPLY_CODE == code){
                        isCode = true
                    }
                })
                if(isCode){
                    state.overtimeApplication = state.overtimeApplication.filter(item=> item.APPLY_CODE != code);
                }
                isCode = false
            }
            if(state.evectionApplication){
                state.evectionApplication.map((item:any)=>{
                    if(item.APPLY_CODE == code){
                        isCode = true
                    }
                })
                if(isCode){
                    state.evectionApplication = state.evectionApplication.filter(item=> item.APPLY_CODE != code);
                }
                isCode = false
            }
            if(state.confirmationApplication){
                state.confirmationApplication.map((item:any)=>{
                    if(item.APPLY_CODE == code){
                        isCode = true
                    }
                })
                if(isCode){
                    state.confirmationApplication = state.confirmationApplication.filter(item=> item.APPLY_CODE != code);
                }
                isCode = false
            }
        },
        //解锁
        handleLeaveUnlock: (state: approvalState, action: PayloadAction<any>) => {
            const apply_list = action.payload?.apply_list;
            const code = apply_list.APPLY_CODE;
            const workflow_list = action.payload.workflow_list ? action.payload.workflow_list[code] : [];
            if(state.leaveApplication){
                state.leaveApplication.push({
                    APPLY_CODE: apply_list.APPLY_CODE,
                    USER_ID: apply_list.USER_ID,
                    AGENT_USER_ID: apply_list.AGENT_USER_ID,
                    APPLY_TYPE_ID: apply_list.APPLY_TYPE_ID,
                    APPLY_TYPE_NAME: apply_list.APPLY_TYPE_NAME,
                    START_TIME: apply_list.START_TIME,
                    END_TIME: apply_list.END_TIME,
                    NORMAL_HOURS: apply_list.NORMAL_HOURS,
                    COMPENSATORY_HOURS: apply_list.COMPENSATORY_HOURS,
                    SICK_HOURS: apply_list.SICK_HOURS,
                    OTHER_APPLY_TYPE: apply_list.OTHER_APPLY_TYPE,
                    OTHER_DAYS: apply_list.OTHER_DAYS,
                    REASON: apply_list.REASON,
                    WORKFLOW_INDEX: apply_list.WORKFLOW_INDEX,
                    WORKFLOW_RESULT: apply_list.WORKFLOW_RESULT,
                    reject_reason: apply_list.reject_reason,
                    WORKFLOW_LIST: workflow_list
                });
            }
            const apply_list_1 = state.leaveApplication.filter((item:any) => item.WORKFLOW_RESULT != null)
                let result = apply_list_1.reduce((accumulator:any, currentValue:any) => {
                    let index = accumulator.findIndex((item:any) => item.APPLY_CODE === currentValue.APPLY_CODE);
                    if(index > -1 && accumulator[index].WORKFLOW_INDEX < currentValue.WORKFLOW_INDEX) {
                        accumulator[index] = currentValue;
                    } else if (index === -1) {
                        accumulator.push(currentValue);
                    }
                    return accumulator;
                    }, []);
            state.leaveApplication = result;
        },
        handleOvertimeUnlock: (state: approvalState, action: PayloadAction<any>) => {
            const apply_list = action.payload?.apply_list;
            const code = apply_list.APPLY_CODE;
            const workflow_list = action.payload.workflow_list ? action.payload.workflow_list[code] : [];
            if(state.overtimeApplication){
                state.overtimeApplication.push({
                    APPLY_CODE: apply_list.APPLY_CODE,
                    USER_ID: apply_list.USER_ID,
                    AGENT_USER_ID: apply_list.AGENT_USER_ID,
                    APPLY_TYPE_ID: apply_list.APPLY_TYPE_ID,
                    APPLY_TYPE_NAME: apply_list.APPLY_TYPE_NAME,
                    START_TIME: apply_list.START_TIME,
                    END_TIME: apply_list.END_TIME,
                    HOURS: apply_list.HOURS,
                    REASON: apply_list.REASON,
                    WORKFLOW_INDEX: apply_list.WORKFLOW_INDEX,
                    WORKFLOW_RESULT: apply_list.WORKFLOW_RESULT,
                    reject_reason: apply_list.reject_reason,
                    WORKFLOW_LIST: workflow_list
                });
            }
            const apply_list_1 = state.overtimeApplication.filter((item:any) => item.WORKFLOW_RESULT != null)
                let result = apply_list_1.reduce((accumulator:any, currentValue:any) => {
                    let index = accumulator.findIndex((item:any) => item.APPLY_CODE === currentValue.APPLY_CODE);
                    if(index > -1 && accumulator[index].WORKFLOW_INDEX < currentValue.WORKFLOW_INDEX) {
                        accumulator[index] = currentValue;
                    } else if (index === -1) {
                        accumulator.push(currentValue);
                    }
                    return accumulator;
                    }, []);
            state.overtimeApplication = result;
        },
        handleTripUnlock: (state: approvalState, action: PayloadAction<any>) => {
            const apply_list = action.payload?.apply_list;
            const code = apply_list.APPLY_CODE;
            const workflow_list = action.payload.workflow_list ? action.payload.workflow_list[code] : [];
            if(state.evectionApplication){
                state.evectionApplication.push({
                    APPLY_CODE: apply_list.APPLY_CODE,
                    USER_ID: apply_list.USER_ID,
                    AGENT_USER_ID: apply_list.AGENT_USER_ID,
                    APPLY_TYPE_ID: apply_list.APPLY_TYPE_ID,
                    APPLY_TYPE_NAME: apply_list.APPLY_TYPE_NAME,
                    START_TIME: apply_list.START_TIME,
                    END_TIME: apply_list.END_TIME,
                    LOCATION: apply_list.LOCATION,
                    T_DAYS: apply_list.T_DAYS,
                    REASON: apply_list.REASON,
                    WORKFLOW_INDEX: apply_list.WORKFLOW_INDEX,
                    WORKFLOW_RESULT: apply_list.WORKFLOW_RESULT,
                    reject_reason: apply_list.reject_reason,
                    WORKFLOW_LIST: workflow_list
                });
            }
            const apply_list_1 = state.evectionApplication.filter((item:any) => item.WORKFLOW_RESULT != null)
                let result = apply_list_1.reduce((accumulator:any, currentValue:any) => {
                    let index = accumulator.findIndex((item:any) => item.APPLY_CODE === currentValue.APPLY_CODE);
                    if(index > -1 && accumulator[index].WORKFLOW_INDEX < currentValue.WORKFLOW_INDEX) {
                        accumulator[index] = currentValue;
                    } else if (index === -1) {
                        accumulator.push(currentValue);
                    }
                    return accumulator;
                    }, []);
            state.evectionApplication = result;
        },
        handleEditUnlock: (state: approvalState, action: PayloadAction<any>) => {
            const apply_list = action.payload?.apply_list;
            const code = apply_list.APPLY_CODE;
            const workflow_list = action.payload.workflow_list ? action.payload.workflow_list[code] : [];
            if(state.confirmationApplication){
                state.confirmationApplication.push({
                    APPLY_CODE: apply_list.APPLY_CODE,
                    USER_ID: apply_list.USER_ID,
                    AGENT_USER_ID: apply_list.AGENT_USER_ID,
                    APPLY_TYPE_ID: apply_list.APPLY_TYPE_ID,
                    APPLY_TYPE_NAME: apply_list.APPLY_TYPE_NAME,
                    DAY: apply_list.DAY,
                    WORK_START_TIME: apply_list.WORK_START_TIME,
                    WORK_END_TIME: apply_list.WORK_END_TIME,
                    REAL_START_TIME: apply_list.REAL_START_TIME,
                    REAL_END_TIME: apply_list.REAL_END_TIME,
                    REASON: apply_list.REASON,
                    WORKFLOW_INDEX: apply_list.WORKFLOW_INDEX,
                    WORKFLOW_RESULT: apply_list.WORKFLOW_RESULT,
                    reject_reason: apply_list.reject_reason,
                    WORKFLOW_LIST: workflow_list
                });
            }
            const apply_list_1 = state.confirmationApplication.filter((item:any) => item.WORKFLOW_RESULT != null)
                let result = apply_list_1.reduce((accumulator:any, currentValue:any) => {
                    let index = accumulator.findIndex((item:any) => item.APPLY_CODE === currentValue.APPLY_CODE);
                    if(index > -1 && accumulator[index].WORKFLOW_INDEX < currentValue.WORKFLOW_INDEX) {
                        accumulator[index] = currentValue;
                    } else if (index === -1) {
                        accumulator.push(currentValue);
                    }
                    return accumulator;
                    }, []);
            state.confirmationApplication = result;
        },
        rsLeaveList: (state: approvalState, action: PayloadAction<any>) => {
            state.rs_leave_list = action.payload;
        },
        rsOvertimeAllList: (state: approvalState, action: PayloadAction<any>) => {
            state.rs_overtime_list = action.payload;
            state.rs_overtime_current_all_list = state.rs_overtime_list;
            const startIndex = (state.currentPage - 1) * state.pageSize;
            const endIndex = startIndex + state.pageSize;
            state.rs_overtime_current_list = state.rs_overtime_current_all_list.slice(startIndex, endIndex);
        },
        handleRsOvertimeAllPageChange: (state: approvalState, action: PayloadAction<any>) => {
            state.currentPage = action.payload;
            const startIndex = (state.currentPage - 1) * state.pageSize;
            const endIndex = startIndex + state.pageSize;
            state.rs_overtime_current_list = state.rs_overtime_current_all_list.slice(startIndex, endIndex);
        },
        handleRsOvertimeAllPageSearch: (state: approvalState, action: PayloadAction<any>) => {
            const keyword = action.payload;
            console.log('keyword', keyword);
            if(keyword != ''){
                state.rs_overtime_current_all_list = state.rs_overtime_list.filter((item:any)=>{
                    const dateStart = new Date(item.START_TIME);
                    const weekdayStart = getWeekday(dateStart);
                    const dateEnd = new Date(item.END_TIME);
                    const weekdayEnd = getWeekday(dateEnd);
                    const infKeyWord = keyword.toString().toLowerCase();
                    const start = dayjs(item.START_TIME).format('YYYY-MM-DD') + weekdayStart + dayjs(item.START_TIME).format('HH:mm');
                    const end = dayjs(item.END_TIME).format('YYYY-MM-DD') + weekdayEnd + dayjs(item.END_TIME).format('HH:mm');
                    return (item.USER_ID.toString().toLowerCase().includes(infKeyWord) || start.toString().toLowerCase().includes(infKeyWord) || 
                    item.depart_name.toString().toLowerCase().includes(infKeyWord))
                })
            }else{
                state.rs_overtime_current_all_list = state.rs_overtime_list;
            }
            state.currentPage = 1;
            const startIndex = (state.currentPage - 1) * state.pageSize;
            const endIndex = startIndex + state.pageSize;
            state.rs_overtime_current_list = state.rs_overtime_current_all_list.slice(startIndex, endIndex);
        },
        rsOvertimeIngList: (state: approvalState, action: PayloadAction<any>) => {
            state.rs_overtime_list_ing = action.payload;
            state.rs_overtime_current_ing_list = state.rs_overtime_list_ing;
            const startIndex = (state.currentPage - 1) * state.pageSize;
            const endIndex = startIndex + state.pageSize;
            state.rs_overtime_ing_current_list = state.rs_overtime_current_ing_list.slice(startIndex, endIndex);
        },
        handleRsOvertimeIngPageChange: (state: approvalState, action: PayloadAction<any>) => {
            state.currentPage = action.payload;
            const startIndex = (state.currentPage - 1) * state.pageSize;
            const endIndex = startIndex + state.pageSize;
            state.rs_overtime_ing_current_list = state.rs_overtime_current_ing_list.slice(startIndex, endIndex);
        },
        handleRsOvertimeIngPageSearch: (state: approvalState, action: PayloadAction<any>) => {
            const keyword = action.payload;
            if(keyword != ''){
                state.rs_overtime_current_ing_list = state.rs_overtime_list_ing.filter((item:any)=>{
                    const dateStart = new Date(item.START_TIME);
                    const weekdayStart = getWeekday(dateStart);
                    const dateEnd = new Date(item.END_TIME);
                    const weekdayEnd = getWeekday(dateEnd);
                    const infKeyWord = keyword.toString().toLowerCase();
                    const start = dayjs(item.START_TIME).format('YYYY-MM-DD') + weekdayStart + dayjs(item.START_TIME).format('HH:mm');
                    const end = dayjs(item.END_TIME).format('YYYY-MM-DD') + weekdayEnd + dayjs(item.END_TIME).format('HH:mm');
                    return (item.USER_ID.toString().toLowerCase().includes(infKeyWord) || start.toString().toLowerCase().includes(infKeyWord) || 
                    item.depart_name.toString().toLowerCase().includes(infKeyWord))
                })
            }else{
                state.rs_overtime_current_ing_list = state.rs_overtime_list_ing;
            }
            state.currentPage = 1;
            const startIndex = (state.currentPage - 1) * state.pageSize;
            const endIndex = startIndex + state.pageSize;
            state.rs_overtime_ing_current_list = state.rs_overtime_current_ing_list.slice(startIndex, endIndex);
        },
        rsOvertimeStaffList: (state: approvalState, action: PayloadAction<any>) => {
            state.rs_overtime_all_staff_list = action.payload;
        },
        rsTripList: (state: approvalState, action: PayloadAction<any>) => {
            state.rs_trip_list = action.payload;
        },
        rsEditList: (state: approvalState, action: PayloadAction<any>) => {
            state.rs_edit_list = action.payload;
        },
        rsLeaveListIng: (state: approvalState, action: PayloadAction<any>) => {
            state.rs_leave_list_ing = action.payload;
        },
        rsOvertimeListIng: (state: approvalState, action: PayloadAction<any>) => {
            state.rs_overtime_list_ing = action.payload;
            state.rs_overtime_current_ing_list = state.rs_overtime_list_ing;
            const startIndex = (state.currentPage - 1) * state.pageSize;
            const endIndex = startIndex + state.pageSize;
            state.rs_overtime_ing_current_list = state.rs_overtime_current_ing_list.slice(startIndex, endIndex);
        },
        rsTripListIng: (state: approvalState, action: PayloadAction<any>) => {
            state.rs_trip_list_ing = action.payload;
        },
        rsEditListIng: (state: approvalState, action: PayloadAction<any>) => {
            state.rs_edit_list_ing = action.payload;
        }
    }
});

//以下内容必须要有
export const { actions: approvalActions } = approvalSlice;

export default approvalSlice.reducer;

//state 后面的为store中数据名称
export const approvalData = (state: RootState) => state.approval;
