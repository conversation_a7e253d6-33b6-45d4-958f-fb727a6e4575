// 交通费申请相关类型定义

/**
 * 交通费申请记录
 */
export interface TransportationExpenseApplication {
  code: string;                    // 申请代码
  user_id: number;                 // 用户ID
  name: string;                    // 申请人姓名
  start_day: string;               // 开始日期 (YYYY-MM-DD)
  end_day: string;                 // 结束日期 (YYYY-MM-DD)
  route_from: string;              // 出发地
  route_to: string;                // 目的地
  fee_amount_single: number;       // 单次票价格
  fee_amount_monthly: number;      // 月票价格
  reason: string;                  // 申请理由
  status?: 'pending' | 'approved' | 'rejected'; // 申请状态
  created_at?: string;             // 创建时间
  updated_at?: string;             // 更新时间
}

/**
 * 新建申请请求参数
 */
export interface CreateApplicationRequest {
  start_day: string;
  end_day: string;
  route_from: string;
  route_to: string;
  fee_amount_single: string | number;
  fee_amount_monthly: number;
  reason: string;
}

/**
 * 更新申请请求参数
 */
export interface UpdateApplicationRequest {
  code: string;
  start_day: string;
  end_day: string;
  route_from: string;
  route_to: string;
  fee_amount_single: string | number;
  fee_amount_monthly: number;
  reason: string;
}

/**
 * 删除申请请求参数
 */
export interface DeleteApplicationRequest {
  code: string;
}

/**
 * 获取申请列表响应
 */
export interface GetApplicationListResponse {
  status: 'OK' | 'ERROR';
  apply_list: TransportationExpenseApplication[];
  message?: string;
}

/**
 * 创建申请响应
 */
export interface CreateApplicationResponse {
  status: 'OK' | 'ERROR';
  message: string;
  code?: string;
}

/**
 * 更新申请响应
 */
export interface UpdateApplicationResponse {
  status: 'OK' | 'ERROR';
  message: string;
}

/**
 * 删除申请响应
 */
export interface DeleteApplicationResponse {
  status: 'OK' | 'ERROR';
  message: string;
}

/**
 * 申请表单数据
 */
export interface ApplicationFormData {
  startDate: string;
  endDate: string;
  routeFrom: string;
  routeTo: string;
  singleTicketPrice: number;
  monthlyTicketPrice: number;
  reason: string;
}

/**
 * 申请状态枚举
 */
export enum ApplicationStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

/**
 * 路线信息
 */
export interface RouteInfo {
  from: string;
  to: string;
  distance?: number;
  estimatedTime?: number;
}

/**
 * 费用信息
 */
export interface FeeInfo {
  singleTicket: number;
  monthlyTicket: number;
  currency?: string;
}
