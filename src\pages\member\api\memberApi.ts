import { getApi, postApi, createAxios } from '@/api/fetch-api'
import ApiUrlVars from '@/api/common/url-vars'
import { statusActions } from '@/slice/statusSlice'

/**
 * 成员信息接口
 */
export interface MemberInfo {
  id: string
  name: string
  email: string
  department: string
  position: string
  role: string
  status: 'active' | 'inactive'
  joinDate: string
}

/**
 * 部门成员参数接口
 */
export interface DepartmentMemberParams {
  user_id: string
  department_id: string
  role?: string
  email?: string
  name?: string
}

/**
 * 组织成员参数接口
 */
export interface StaffMemberParams {
  user_id: string
  name: string
  email: string
  department: string
  position: string
  role: string
  password?: string
}

/**
 * 成员响应接口
 */
export interface MemberResponse {
  status: string
  message?: string
  data?: any
}

/**
 * 获取部门成员列表
 * @param departmentId 部门ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<MemberResponse>
 */
export async function getDepartmentMembers(departmentId: string, dispatch: any): Promise<MemberResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.departMember_get
    const params = { department_id: departmentId }
    const response = await getApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取部门成员失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get department members API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 添加部门成员
 * @param params 部门成员参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<MemberResponse>
 */
export async function addDepartmentMember(params: DepartmentMemberParams, dispatch: any): Promise<MemberResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.departMemberAdd_post
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '添加部门成员失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '添加成员成功'
    }
  } catch (error: any) {
    console.error('Add department member API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 删除部门成员
 * @param userId 用户ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<MemberResponse>
 */
export async function deleteDepartmentMember(userId: string, dispatch: any): Promise<MemberResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.departMemberDelete_post
    const params = { user_id: userId }
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '删除部门成员失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '删除成员成功'
    }
  } catch (error: any) {
    console.error('Delete department member API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 修改部门成员角色
 * @param params 成员角色参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<MemberResponse>
 */
export async function changeDepartmentMemberRole(params: DepartmentMemberParams, dispatch: any): Promise<MemberResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.departMemberChange_post
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '修改成员角色失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '修改角色成功'
    }
  } catch (error: any) {
    console.error('Change department member role API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 修改部门单个成员角色
 * @param params 成员角色参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<MemberResponse>
 */
export async function changeSingleDepartmentMemberRole(params: DepartmentMemberParams, dispatch: any): Promise<MemberResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.departMemberChangeSingle_post
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '修改成员角色失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '修改角色成功'
    }
  } catch (error: any) {
    console.error('Change single department member role API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取组织全部成员
 * @param dispatch Redux dispatch函数
 * @returns Promise<MemberResponse>
 */
export async function getStaffMembers(dispatch: any): Promise<MemberResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.staffMember_get
    const response = await getApi(url, {}, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取组织成员失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get staff members API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 添加组织成员
 * @param params 组织成员参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<MemberResponse>
 */
export async function addStaffMember(params: StaffMemberParams, dispatch: any): Promise<MemberResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.staffMemberAdd_post
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '添加组织成员失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '添加成员成功'
    }
  } catch (error: any) {
    console.error('Add staff member API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 删除组织成员
 * @param userId 用户ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<MemberResponse>
 */
export async function deleteStaffMember(userId: string, dispatch: any): Promise<MemberResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.staffMemberDelete_post
    const params = { user_id: userId }
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '删除组织成员失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '删除成员成功'
    }
  } catch (error: any) {
    console.error('Delete staff member API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 修改组织成员信息
 * @param params 组织成员参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<MemberResponse>
 */
export async function changeStaffMember(params: StaffMemberParams, dispatch: any): Promise<MemberResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.staffMemberChange_post
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '修改组织成员失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '修改成员成功'
    }
  } catch (error: any) {
    console.error('Change staff member API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 批量处理组织成员角色(发邮件)
 * @param members 成员列表
 * @param dispatch Redux dispatch函数
 * @returns Promise<MemberResponse>
 */
export async function batchProcessStaffMembers(members: StaffMemberParams[], dispatch: any): Promise<MemberResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.staffMemberBatch_post
    const params = { members }
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '批量处理成员失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '批量处理成功'
    }
  } catch (error: any) {
    console.error('Batch process staff members API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 下载组织成员数据导入模板
 * @param dispatch Redux dispatch函数
 * @returns Promise<void>
 */
export async function downloadStaffMemberTemplate(dispatch: any): Promise<void> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.staffMemberExcel_get
    const axios = createAxios()
    const response = await axios.get(url, {
      responseType: 'blob'
    })
    
    // 创建下载链接
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = downloadUrl
    a.download = '组织成员导入模板.xlsx'
    a.click()
    window.URL.revokeObjectURL(downloadUrl)
    
  } catch (error: any) {
    console.error('Download staff member template API error:', error)
    throw error
  }
}
