# API 提取完成报告

## 📋 概述

已成功将pages目录下每个子目录中使用到的API处理全部提取到各自的api目录中，实现了API调用的模块化和集中管理。

## 🗂️ 已完成的API提取

### 1. Login 目录 ✅
**文件**: `src/pages/login/api/loginApi.ts`

**包含的API功能**:
- `loginUser()` - 用户登录
- `resetPassword()` - 重置密码
- `checkLoginStatus()` - 检查登录状态
- `logoutUser()` - 用户登出

**接口定义**:
- `LoginRequest` - 登录请求参数
- `LoginResponse` - 登录响应数据

### 2. Attendance 目录 ✅
**文件**: `src/pages/attendance/api/attendanceApi.ts`

**包含的API功能**:
- `queryAttendanceRecords()` - 查询考勤记录
- `updateAttendanceRecord()` - 更新考勤记录
- `getMaxAttendanceDate()` - 获取考勤最大日期
- `importAttendanceRecords()` - 导入考勤记录
- `uploadHomeAttendance()` - 上传居家考勤
- `uploadBatchAttendance()` - 批量上传考勤
- `downloadAttendanceTemplate()` - 下载考勤模板
- `downloadErrorRecords()` - 下载错误记录
- `triggerDailyCheck()` - 触发每日检查
- `createAttendanceQueryParams()` - 创建查询参数

**接口定义**:
- `AttendanceQueryParams` - 考勤查询参数
- `AttendanceRecord` - 考勤记录
- `AttendanceResponse` - 考勤响应

### 3. Application 目录 ✅
**文件**: `src/pages/application/api/applicationApi.ts`

**包含的API功能**:
- `getLeaveApplicationList()` - 获取请假申请列表
- `submitLeaveApplication()` - 提交请假申请
- `cancelLeaveApplication()` - 撤回请假申请
- `submitTransportationExpense()` - 提交交通费申请
- `cancelTransportationExpense()` - 撤回交通费申请
- `getOvertimeApplicationList()` - 获取加班申请列表
- `submitOvertimeApplication()` - 提交加班申请
- `getApplicationPageSize()` - 获取申请页面配置

**接口定义**:
- `LeaveApplicationParams` - 请假申请参数
- `TransportationExpenseParams` - 交通费申请参数
- `OvertimeApplicationParams` - 加班申请参数
- `BusinessTripParams` - 出差申请参数
- `ApplicationResponse` - 申请响应
- `ApplicationType` - 申请类型枚举

### 4. Statistics 目录 ✅
**文件**: `src/pages/statistics/api/statisticsApi.ts`

**包含的API功能**:
- `getDepartmentStatistics()` - 获取部门数据汇总
- `downloadDepartmentReport()` - 下载部门统计报告
- `getDepartmentExpenseStatistics()` - 获取部门报销数据汇总
- `downloadDepartmentExpenseReport()` - 下载部门报销报告
- `getPersonalStatistics()` - 获取个人数据汇总
- `getCompanyStatistics()` - 获取组织数据汇总
- `getCompensatoryStatistics()` - 获取调休管理数据
- `triggerCompensatoryRecalc()` - 触发调休管理重新计算
- `getOvertimeApplicationStatistics()` - 获取加班申请统计

**接口定义**:
- `StatisticsQueryParams` - 统计查询参数
- `DepartmentStats` - 部门统计数据
- `StatsSummary` - 统计汇总数据
- `StatisticsResponse` - 统计响应

### 5. My 目录 ✅
**文件**: `src/pages/my/api/userApi.ts`

**包含的API功能**:
- `getUserInfo()` - 获取用户信息
- `updateUserInfo()` - 更新用户信息
- `changePassword()` - 修改密码
- `getPaidLeaveInfo()` - 获取有薪假期信息
- `getLeaveRecords()` - 获取请假记录
- `getPersonalTasks()` - 获取个人任务记录
- `getPersonalStatistics()` - 获取个人统计数据
- `uploadUserAvatar()` - 上传用户头像

**接口定义**:
- `UserInfo` - 用户信息
- `PaidLeaveInfo` - 有薪假期信息
- `LeaveRecord` - 请假记录
- `PasswordChangeParams` - 密码修改参数
- `UserResponse` - 用户响应

### 6. Approval 目录 ✅
**文件**: `src/pages/approval/api/approvalApi.ts`

**包含的API功能**:
- `getLeaveApprovalList()` - 获取请假申请审批列表
- `handleLeaveApproval()` - 处理请假申请审批
- `getOvertimeApprovalList()` - 获取加班申请审批列表
- `handleOvertimeApproval()` - 处理加班申请审批
- `getBusinessTripApprovalList()` - 获取出差申请审批列表
- `handleBusinessTripApproval()` - 处理出差申请审批
- `getTransportationExpenseApprovalList()` - 获取交通费申请审批列表
- `handleTransportationExpenseApproval()` - 处理交通费申请审批
- `handleBatchApproval()` - 批量处理审批

**接口定义**:
- `ApprovalParams` - 审批参数
- `ApprovalResponse` - 审批响应
- `ApprovalType` - 审批类型枚举
- `ApprovalAction` - 审批操作枚举

### 7. Member 目录 ✅
**文件**: `src/pages/member/api/memberApi.ts`

**包含的API功能**:
- `getDepartmentMembers()` - 获取部门成员列表
- `addDepartmentMember()` - 添加部门成员
- `deleteDepartmentMember()` - 删除部门成员
- `changeDepartmentMemberRole()` - 修改部门成员角色
- `changeSingleDepartmentMemberRole()` - 修改部门单个成员角色
- `getStaffMembers()` - 获取组织全部成员
- `addStaffMember()` - 添加组织成员
- `deleteStaffMember()` - 删除组织成员
- `changeStaffMember()` - 修改组织成员信息
- `batchProcessStaffMembers()` - 批量处理组织成员角色
- `downloadStaffMemberTemplate()` - 下载组织成员数据导入模板

**接口定义**:
- `MemberInfo` - 成员信息
- `DepartmentMemberParams` - 部门成员参数
- `StaffMemberParams` - 组织成员参数
- `MemberResponse` - 成员响应

### 8. Structure 目录 ✅
**文件**: `src/pages/structure/api/structureApi.ts`

**包含的API功能**:
- `getDepartmentStructure()` - 获取部门结构数据
- `addDepartment()` - 新增部门
- `deleteDepartment()` - 删除部门
- `changeDepartment()` - 修改部门数据
- `getDepartmentHierarchy()` - 获取部门层级结构
- `getDepartmentMemberStats()` - 获取部门成员统计
- `moveDepartment()` - 移动部门位置
- `batchDepartmentOperations()` - 批量操作部门

**接口定义**:
- `DepartmentInfo` - 部门信息
- `DepartmentParams` - 部门参数
- `StructureResponse` - 部门结构响应

**工具函数**:
- `buildDepartmentTree()` - 构建部门树形结构

### 9. Setting 目录 ✅
**文件**: `src/pages/setting/api/settingApi.ts`

**包含的API功能**:
- `getRolePermissions()` - 获取角色权限信息
- `updateRolePermissions()` - 修改角色权限信息
- `getHolidayList()` - 获取节假日信息
- `addHoliday()` - 新增节假日信息
- `deleteHoliday()` - 删除节假日信息
- `getSpecialArrangements()` - 获取特殊安排
- `addSpecialArrangement()` - 新增特殊安排
- `deleteSpecialArrangement()` - 删除特殊安排
- `getSearchDays()` - 获取考勤默认查询天数
- `setSearchDays()` - 设置考勤默认查询天数
- `getMailSettings()` - 获取邮箱设置
- `updateMailSettings()` - 修改邮箱设置

**接口定义**:
- `RolePermission` - 角色权限信息
- `HolidayInfo` - 节假日信息
- `SpecialArrangement` - 特殊安排
- `MailSettings` - 邮箱设置
- `SettingResponse` - 设置响应

## 🔧 API设计特点

### 1. 统一的错误处理
- 所有API函数都包含统一的错误处理逻辑
- 自动处理登录凭证失效的情况
- 返回标准化的响应格式

### 2. TypeScript类型支持
- 为每个API定义了完整的TypeScript接口
- 包含请求参数和响应数据的类型定义
- 提供了枚举类型用于常量值

### 3. Redux集成
- 所有API函数都接受dispatch参数
- 自动更新loading状态
- 集成了状态管理的错误处理

### 4. 模块化设计
- 按功能模块分离API调用
- 每个目录都有独立的API文件
- 便于维护和扩展

## 📝 使用示例

```typescript
// 导入API函数
import { loginUser } from '@/pages/login/api/loginApi'
import { queryAttendanceRecords } from '@/pages/attendance/api/attendanceApi'
import { submitLeaveApplication } from '@/pages/application/api/applicationApi'

// 在组件中使用
const dispatch = useApplicationDispatch()

// 登录
const loginResult = await loginUser({
  username: '<EMAIL>',
  password: 'password123',
  remember: true
}, dispatch)

// 查询考勤记录
const attendanceResult = await queryAttendanceRecords({
  keyword: 'user123',
  query_type: 1,
  start_dt: '2024-01-01',
  end_dt: '2024-01-31'
}, dispatch)

// 提交请假申请
const leaveResult = await submitLeaveApplication({
  use_type: 0,
  user_id: 'user123',
  agent_user_id: 0,
  leave_type: '有休',
  reason: '个人事务',
  start_time: '2024-01-15 09:00:00',
  end_time: '2024-01-15 18:00:00',
  workflow_list: 'approver123'
}, 'normal', dispatch)
```

## 🎯 后续优化建议

1. **API文档生成**: 可以基于TypeScript接口自动生成API文档
2. **单元测试**: 为每个API函数编写单元测试
3. **缓存机制**: 为频繁调用的API添加缓存机制
4. **请求拦截器**: 统一处理请求头和认证信息
5. **响应拦截器**: 统一处理响应数据格式化

## ✅ 总结

API提取工作已全部完成，共创建了9个API模块文件，包含了80+个API函数和30+个TypeScript接口定义。所有API调用都已从页面组件中提取出来，实现了：

- **代码复用**: API函数可以在多个组件中复用
- **类型安全**: 完整的TypeScript类型定义
- **错误处理**: 统一的错误处理机制
- **维护性**: 模块化的代码结构便于维护
- **扩展性**: 易于添加新的API功能

这种架构为后续的开发和维护提供了良好的基础。
