import { createSlice, PayloadAction, } from "@reduxjs/toolkit";
import { RootState } from "../store";
import dayjs from 'dayjs';

//approvalProcessSettings
// 定义要保存到Store的数据格式
export interface approvalProcessSettingsState {
    approvalType: string,
    detailType: string,
    approvalRole: string,
}
const approvalProcessSettingsState = {
    approvalType: '请假',
    detailType: '3日以内请假',
    approvalRole: 'D001',
};

//holidaySettings
const nowYear = dayjs().year();
const endYear = nowYear + 7;
const year = [];
for (let i = nowYear - 2; i <= endYear; i++) {
    year.push({ year: i });
};
//判断当前日期是星期几
function getWeekday(date: Date): string {
    const weekdays = ['(日)', '(一)', '(二)', '(三)', '(四)', '(五)', '(六)'];
    return weekdays[date.getDay()];
}
//判断当前日期是否是双休
function isWeekend(date: Date): boolean {
    const day = date.getDay();
    return day === 0 || day === 6;
}
const restDay = [
    '10/03', '10/01', '10/04', '10/05', '10/07', '10/08', '10/09', '10/10',
];
const restDayYear = [
    {
        year: '2023',
        value: restDay,
    },
    {
        year: '2024',
        value: restDay,
    }
]
const restWorkDay = [
    '10/03', '10/01', '10/04',
];
const restWorkDayYear = [
    {
        year: '2023',
        value: restWorkDay,
    },
    {
        year: '2024',
        value: restWorkDay,
    }
]
const restWeekends = [
    '10/05', '10/07', '10/08', '10/09', '10/10',
];
const restWeekendsYear = [
    {
        year: '2023',
        value: restWeekends,
    },
    {
        year: '2024',
        value: restWeekends,
    }
]
const specialDays = [
    {
        date: '02/02(一)',
        time: '09:00',
        limit: '全部',
        reason: '二月二'
    },
    {
        date: '03/08(一)',
        time: '14:00',
        limit: '女',
        reason: '妇女节'
    },
    {
        date: '06/01(一)',
        time: '09:00',
        limit: '全部',
        reason: '带孩子'
    },
];
const specialDaysYear = [
    {
        year: '2023',
        value: specialDays,
    },
    {
        year: '2024',
        value: specialDays,
    }
]
const holidayType = [
    {
        value: '法定假日',
        label: '法定假日',
    },
];

  //门禁列表颜色设置
  const doorColor = [
    {type:0,color:'#ff000085'},
    {type:1,color:'#f35c0085'},
    {type:2,color:'#0054f385'},
    {type:3,color:'#8c00f385'},
    {type:4,color:'#65495185'},
  ]

export interface holidaySettingsState {
    yearList: {}[];
    specialList: {
        user_type: any,
        date: string,
        clock_out_time: string,
        target: number,
        reason: string,
    }[];
    isYear: number;
    dayjs: dayjs.Dayjs;
    specialDaysYear: {
        year: string;
        value: {
            date: string;
            time: string;
            limit: string;
            reason: string;
        }[];
    }[];
    holidayType: {
        value: string;
        label: string;
    }[];
    addHoliday: string;
    // addHolidayType: string;
    //新增节假日的内容
    commitAdd: {
        user_type: any,
        date: any,
        type: number,
        update_name: any,
        update_time: any,
        update_page: any,
    }
    //新增特殊安排内容
    specialCommitAdd: {
        user_type: any,
        date: any,
        clock_out_time: any,
        target: any,
        reason: any,
    }
    addSpecialDayYear: dayjs.Dayjs;
    addSpecialDay: string;
    addHour: string;
    limit: any;
    reason: string;
};

// 初始化数据
const holidaySettingsState = {
    yearList: [],
    specialList: [{
        user_type: '',
        date: '',
        clock_out_time: '',
        target: -1,
        reason: '',
    }],
    isYear: dayjs().year(),
    dayjs: dayjs(),
    restDayYear: [],
    restWorkDayYear: [],
    restWeekendsYear: [],
    specialDaysYear: [],
    holidayType: holidayType,
    //新增单个假期
    addHoliday: '',
    // addHolidayType: '',
    commitAdd: {
        user_type: 1,
        date: '',
        type: 2,
        update_name: '',
        update_time: '',
        update_page: '',
    },
    //新增特定日期安排
    specialCommitAdd: {
        user_type: 1,
        date: '',
        clock_out_time: '14:00',
        target: 0,
        reason: '',
        update_name: '',
        update_time: '',
        update_page: '',
    },
    addSpecialDayYear: dayjs(),
    addSpecialDay: '',
    addHour: '14:00',
    limit: 0,
    reason: '',
};

//mailSettings
const mailType = [
    {
        value: '密码重置',
        label: '密码重置',
    },
    {
        value: '联系开发人员',
        label: '联系开发人员',
    },
];
const sendMail = '<EMAIL>';
const recipient = '自动判断';
const writer = '无';
const theme = '考勤系统密码重置';
const text = [
    {
        type: '联系开发人员',
        content: '%name%(%workno%)san、你好!、正在联系开发人员，请稍后，工作人员很快会与你联系，谢谢！、以上/考勤系统管理员',
    },
    {
        type: '密码重置',
        content: '%name%(%workno%)san、你好!、你的考勤系统密码已重置为:、%n_password%、旧密码已经作废!/请使用新密码登录考勤系统，谢谢！、以上/考勤系统管理员',
    },
];
const info = [
    {
        name: '链接',
        value: '%url%',
    },
    {
        name: '员工姓名',
        value: '%name%',
    },
    {
        name: '员工工号',
        value: '%workno%',
    },
    {
        name: '新密码',
        value: '%n_password%',
    },
];
// 定义要保存到Store的数据格式
export interface mailSettingsState {
    mail: {
        mailType: {
            value: string;
            label: string;
        }[],
        chooseType: string,
        recipient: string,
        writer: string,
        theme: string,
        text: {
            type: string;
            content: string;
        }[],
    }[],
    mailList: {
        mailType: {
            value: string;
            label: string;
        }[],
        chooseType: string,
        recipient: string,
        writer: string,
        theme: string,
        text: {
            type: string;
            content: string;
        }[],
    }[]
}

// 初始化数据
const mailSettingsState = {
    mail: [{
        mailType: [
            {
                value: '',
                label: '',
            }
        ],
        chooseType: '',
        recipient: '',
        writer: '',
        theme: '',
        text: [
            {
                type: '',
                content: '',
            }
        ],
    }],
    mailList: [{
        mailType: mailType,
        chooseType: '',
        sendMail: sendMail,
        recipient: recipient,
        writer: writer,
        theme: theme,
        text: text,
    }],
    info: info,
};

//otherSettings
// 定义要保存到Store的数据格式
export interface otherSettingsState {
    search_days: {
        user_type: any,
        default_value: string,
    },
    list: string[],
    start_dt: string
    commitAdd: {
        date: any,
        account: any,
        password: any
    },
    mailAccount: any,
    doorColor: any,
    doorDataList: any,
    door: any
}

// 初始化数据
const otherSettingsState = {
    search_days: {
        user_type: 1,
        default_value: "",
    },
    list: [],
    start_dt: "",
    commitAdd: {
        date: dayjs().format('YYYY-MM-DD'),
        account: '',
        password: ''
    },
    mailAccount: '',
    doorColor: doorColor,
    doorDataList: [],
    door: {
        name: '',
        import_flag: 0
    }
};
// 定义要保存到Store的数据格式
export interface roleLimitsSettingsState {
    base_role_list:{
        role_id: string,
        groups: string[][],
        permissions: {
            permissions_group_id: string,
            permissions_id: string,
            exist: boolean,
            permissions_name: string,
        }[]
    }[],
    change_role_list:{
        role_id: string,
        groups: string[][],
        permissions: {
            permissions_group_id: string,
            permissions_id: string,
            exist: boolean,
            permissions_name: string,
        }[]
    }[]
}

// 初始化数据
const roleLimitsSettingsState = {
    base_role_list:[{
        role_id: '',
        groups: [['','']],
        permissions: [{
            permissions_group_id: '',
            permissions_id: '',
            exist: false,
            permissions_name: '',
        }]
    }],
    change_role_list:[{
        role_id: '',
        groups: [['','']],
        permissions: [{
            permissions_group_id: '',
            permissions_id: '',
            exist: false,
            permissions_name: '',
        }]
    }],
};

export interface settingsState {
    approvalProcessSettings: approvalProcessSettingsState,
    holidaySettings: holidaySettingsState,
    mailSettings: mailSettingsState,
    otherSettings: otherSettingsState,
    roleLimitsSettings: roleLimitsSettingsState,
}
// 初始化数据
const initialState: settingsState = {
    approvalProcessSettings: approvalProcessSettingsState,
    holidaySettings: holidaySettingsState,
    mailSettings: mailSettingsState,
    otherSettings: otherSettingsState,
    roleLimitsSettings: roleLimitsSettingsState,
};

export const settingsSlice = createSlice({
    name: 'settings',
    initialState,
    // reducers 集合了reducer和action的功能
    // 每个case冒号之前的为action名
    // 每个case整体为reducer
    reducers: {
        // 使用PayloadAction来展示action.payload的详细内容
        //approvalProcessSettings
        getProcessWhenInit: (state: settingsState, action: PayloadAction<any>) => {
            state.approvalProcessSettings = action.payload;
            state.approvalProcessSettings.approvalType = '请假';
            state.approvalProcessSettings.detailType = '3日以内请假';
            state.approvalProcessSettings.approvalRole = 'D001';
        },
        approvalProcessSettingsRoleType: (state: settingsState, action: PayloadAction<any>) => {
            state.approvalProcessSettings.approvalRole = action.payload;
            console.log(state.approvalProcessSettings.approvalRole)
        },
        approvalProcessSettingsUpdateApprovalType: (state: settingsState, action: PayloadAction<any>) => {
            state.approvalProcessSettings.approvalType = action.payload;
            if(state.approvalProcessSettings.approvalType == '请假'){
                state.approvalProcessSettings.detailType = '3日以内请假';
            }else if(state.approvalProcessSettings.approvalType == '加班'){
                state.approvalProcessSettings.detailType = '平日加班';
            }else if(state.approvalProcessSettings.approvalType == '出差'){
                state.approvalProcessSettings.detailType = '出差';
            }
        },
        approvalProcessSettingsUpdateDetailType: (state: settingsState, action: PayloadAction<any>) => {
            state.approvalProcessSettings.detailType = action.payload;
        },

        //holidaySettings
        getHolidayListDataWhenInit: (state: settingsState, action: PayloadAction<any>) => {
            const arr = action.payload.year_list;
            const sortedArr = arr.slice().sort((a:any, b:any) => Number(a.year) - Number(b.year));
            state.holidaySettings.yearList = sortedArr;
        },
        holidaySettingsChooseYear: (state: settingsState, action: PayloadAction<any>) => {
            state.holidaySettings.isYear = action.payload;
        },
        holidaySettingsAddHolidayChange: (state: settingsState, action: PayloadAction<any>) => {
            //年份后面加api的时候用来判定放在哪个年份的位置
            state.holidaySettings.dayjs = action.payload;
            const year = action.payload.year();
            const month = action.payload.month() + 1;
            const date = action.payload.date();
            state.holidaySettings.addHoliday = year.toString() + '-' + month.toString().padStart(2, '0') + '-' + date.toString().padStart(2, '0');
            state.holidaySettings.commitAdd.date = state.holidaySettings.addHoliday;
        },
        holidaySettingsAddHolidayTypeChange: (state: settingsState, action: PayloadAction<any>) => {
            state.holidaySettings.commitAdd.type = action.payload;
        },
        //新增单个假期的提交
        holidaySettingsAddHoliday: (state: settingsState) => {
            if (state.holidaySettings.commitAdd.type == 3 && !isWeekend(state.holidaySettings.dayjs.toDate())) {
                (state.holidaySettings.yearList.map((item) => ({ ...item })).find((item: any) => item.year === state.holidaySettings.dayjs.year().toString()) as any)?.holiday_inodes.rest_on_holiday.push(state.holidaySettings.addHoliday);
            } else if (state.holidaySettings.commitAdd.type == 4 && isWeekend(state.holidaySettings.dayjs.toDate())) {
                (state.holidaySettings.yearList.map((item) => ({ ...item })).find((item: any) => item.year === state.holidaySettings.dayjs.year().toString()) as any)?.holiday_inodes.work_on_holiday.push(state.holidaySettings.addHoliday);
            } else if (state.holidaySettings.commitAdd.type == 2) {
                (state.holidaySettings.yearList.map((item) => ({ ...item })).find((item: any) => item.year === state.holidaySettings.dayjs.year().toString()) as any)?.holiday_inodes.legal_holiday.push(state.holidaySettings.addHoliday);
            }
        },
        getSpecialListDataWhenInit: (state: settingsState, action: PayloadAction<any>) => {
            state.holidaySettings.specialList = action.payload.special_list;
        },
        holidaySettingsAddSpecialDayChange: (state: settingsState, action: PayloadAction<any>) => {
            state.holidaySettings.addSpecialDayYear = action.payload;
            const year = action.payload.year();
            const month = action.payload.month() + 1;
            const date = action.payload.date();
            state.holidaySettings.addSpecialDay = year.toString() + '-' + month.toString().padStart(2, '0') + '-' + date.toString().padStart(2, '0');
            state.holidaySettings.specialCommitAdd.date = state.holidaySettings.addSpecialDay;
        },
        holidaySettingsAddHourChange: (state: settingsState, action: PayloadAction<any>) => {
            if(action.payload){
                const hour = action.payload?.hour() < 10 ? ('0' + action.payload.hour().toString()) : action.payload.hour().toString();
                const minute = action.payload?.minute() < 10 ? ('0' + action.payload.minute().toString()) : action.payload.minute().toString();
                state.holidaySettings.addHour = hour + ':' + minute;
                state.holidaySettings.specialCommitAdd.clock_out_time = state.holidaySettings.addHour;
            }else{
                state.holidaySettings.addHour = '00:00';
                state.holidaySettings.specialCommitAdd.clock_out_time = state.holidaySettings.addHour;
            }
        },
        holidaySettingsAddLimitChange: (state: settingsState, action: PayloadAction<any>) => {
            state.holidaySettings.limit = action.payload;
            if (state.holidaySettings.limit === "男") {
                state.holidaySettings.limit = 1
            }
            if (state.holidaySettings.limit === "女") {
                state.holidaySettings.limit = 2
            }
            if (state.holidaySettings.limit === "全部") {
                state.holidaySettings.limit = 0
            }
            state.holidaySettings.specialCommitAdd.target = state.holidaySettings.limit;
        },
        holidaySettingsAddReasonChange: (state: settingsState, action: PayloadAction<any>) => {
            state.holidaySettings.reason = action.payload.target.value;
            state.holidaySettings.specialCommitAdd.reason = state.holidaySettings.reason;
        },
        holidaySettingsAddSpecialDay: (state: settingsState) => {
            function dateToYear(dateString: any) {
                const dateObject = new Date(dateString);
                const year = dateObject.getFullYear();
                return year;
            }
            const index = state.holidaySettings.specialList?.findIndex(item => {
                return dateToYear(item.date).toString() === state.holidaySettings.addSpecialDayYear.year().toString();
            });

            // 如果找到了匹配的元素，使用 splice 方法插入新元素  
            if (index !== -1) {
                if(state.holidaySettings.specialList){
                    state.holidaySettings.specialList.splice(index, 0, {
                        date: state.holidaySettings.addSpecialDay,
                        clock_out_time: state.holidaySettings.addHour,
                        target: state.holidaySettings.limit,
                        reason: state.holidaySettings.reason,
                        user_type: 1
                    });
                }else{
                    state.holidaySettings.specialList = [{date: state.holidaySettings.addSpecialDay,
                        clock_out_time: state.holidaySettings.addHour,
                        target: state.holidaySettings.limit,
                        reason: state.holidaySettings.reason,
                        user_type: 1}]
                }
            } else {
                // 如果没有找到匹配的元素，你可以选择在数组的末尾添加新元素  
                state.holidaySettings.specialList.push({
                    date: state.holidaySettings.addSpecialDay,
                    clock_out_time: state.holidaySettings.addHour,
                    target: state.holidaySettings.limit,
                    reason: state.holidaySettings.reason,
                    user_type: 1
                });
            }
        },
        holidaySettingsMinusRestDay: (state: settingsState, action: PayloadAction<any>) => {
            state.holidaySettings.yearList.forEach((year: any) => {
                if (year.year == state.holidaySettings.isYear.toString()) {
                    year.holiday_inodes.legal_holiday = year.holiday_inodes.legal_holiday.filter((day: any) => day !== action.payload.day);
                }
            });
        },
        holidaySettingsMinusWeekends: (state: settingsState, action: PayloadAction<any>) => {
            state.holidaySettings.yearList.forEach((year: any) => {
                if (year.year == state.holidaySettings.isYear.toString()) {
                    year.holiday_inodes.work_on_holiday = year.holiday_inodes.work_on_holiday.filter((day: any) => day !== action.payload.day);
                }
            });
        },
        holidaySettingsMinusWorkDay: (state: settingsState, action: PayloadAction<any>) => {
            state.holidaySettings.yearList.forEach((year: any) => {
                if (year.year == state.holidaySettings.isYear.toString()) {
                    year.holiday_inodes.rest_on_holiday = year.holiday_inodes.rest_on_holiday.filter((day: any) => day !== action.payload.day);
                }
            });
        },
        holidaySettingsMinusSpecialDay: (state: settingsState, action: PayloadAction<any>) => {
            function dateToYear(dateString: any) {
                const dateObject = new Date(dateString);
                const year = dateObject.getFullYear();
                return year;
            }
            state.holidaySettings.specialList = state.holidaySettings.specialList.filter((item) => {
                // 比较年份是否匹配
                const itemYear = dateToYear(item.date).toString();
                const payloadYear = state.holidaySettings.isYear.toString();
                if (itemYear !== payloadYear) {
                    return true;
                }
                const itemDate = item.date;
                const payloadDate = action.payload.day;
                return itemDate !== payloadDate;
            });
        },
        //roleSettings
        setTotalPermissions: (state: settingsState, action: PayloadAction<any>) => {
            state.roleLimitsSettings.base_role_list = action.payload.role_list
            state.roleLimitsSettings.change_role_list = action.payload.role_list
        },
        changePermissionCheckedByIndex: (state: settingsState, action: PayloadAction<any>) => {
            const checked = state.roleLimitsSettings.change_role_list[action.payload.role_index].permissions[action.payload.permission_index].exist
            state.roleLimitsSettings.change_role_list[action.payload.role_index].permissions[action.payload.permission_index].exist = !checked
        },

        //mailSettings
        mailSettingsMailTypeSelect: (state: settingsState, action: PayloadAction<any>) => {
            state.mailSettings.mail = state.mailSettings.mailList.filter((item: any) => item.subject === action.payload);
        },
        getMailDataWhenInit: (state: settingsState, action: PayloadAction<any>) => {
            state.mailSettings.mailList = action.payload.mail_list;
            state.mailSettings.mail = state.mailSettings.mailList.filter((item: any) => item.type === 1);
        },
        //otherSettings
        otherSettingsUpdateSearchDayNum: (state: settingsState, action: PayloadAction<any>) => {
            state.otherSettings.search_days.default_value = action.payload;
        },

        //otherSettings
        getSearchDayNumDataWhenInit: (state: settingsState, action: PayloadAction<any>) => {
            if (action.payload.search_days != null) {
                state.otherSettings.search_days.default_value = action.payload.search_days;
                state.otherSettings.mailAccount = action.payload.Mail_Account;
                state.otherSettings.commitAdd.account = action.payload.Mail_Account;
            } else {
                state.otherSettings.search_days.default_value = '';
                state.otherSettings.mailAccount = '';
            }
        },

        //otherSettings
        getSearchAdminListWhenInit: (state: settingsState, action: PayloadAction<any>) => {
            if (action.payload.admin_list != null) {
                state.otherSettings.list = action.payload.admin_list;
            } else {
                state.otherSettings.list = [];
            }
        },

        //otherSettings
        deleteRecord: (state: settingsState, action: PayloadAction<any>) => {
            if (action.payload.start_dt != null) {
                state.otherSettings.start_dt = action.payload.start_dt;
            } else {
                state.otherSettings.start_dt = '';
            }
        },

        //otherSettings
        mailAccount: (state: settingsState, action: PayloadAction<any>) => {
            state.otherSettings.commitAdd.account = action.payload;
        },

        //otherSettings
        mailPassword: (state: settingsState, action: PayloadAction<any>) => {
            state.otherSettings.commitAdd.password = action.payload;
        },

        //roleLimitsSettings
        roleLimitsSettingsCommit: (state: settingsState) => {
            
        },
        roleLimitsSettingsCancel: (state: settingsState) => {
            state.roleLimitsSettings.change_role_list = state.roleLimitsSettings.base_role_list;
        },

        //otherSettings
        doorListGet: (state: settingsState, action: PayloadAction<any>) => {
            console.log(action.payload)
            state.otherSettings.doorDataList = action.payload;
        },
    },
    // extraReducers 集合了所有异步处理的内容
    extraReducers(builder) {
    },
});
//以下内容必须要有
export const { actions: settingsActions } = settingsSlice;

export default settingsSlice.reducer;

//state 后面的为store中数据名称
export const settingsData = (state: RootState) => state.settings;
