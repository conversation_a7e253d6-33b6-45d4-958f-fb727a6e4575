import React, { useState } from 'react';
import Sidebar from './Sidebar';
import Header from './Header';
import ClockIn from './ClockIn';
import Profile from './Profile';
import type { User } from '../types/auth';

export type Page = 'clock-in' | 'profile';

interface LayoutProps {
  user: User;
  onLogout: () => void;
}

const Layout: React.FC<LayoutProps> = ({ user, onLogout }) => {
  const [currentPage, setCurrentPage] = useState<Page>('clock-in');

  const renderContent = () => {
    switch (currentPage) {
      case 'clock-in':
        return <ClockIn />;
      case 'profile':
        return <Profile />;
      default:
        return <ClockIn />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 to-orange-50 flex">
      <Sidebar 
        currentPage={currentPage} 
        onPageChange={setCurrentPage}
        user={user}
        onLogout={onLogout}
      />
      <div className="flex-1 flex flex-col">
        <Header user={user} />
        <main className="flex-1 p-6">
          <div className="bg-white rounded-xl shadow-lg border border-amber-100 h-full">
            {renderContent()}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;