import { useEffect, useRef } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { getCookie, key_for_token, key_for_token_aes } from '@/utils/cookies'
import { is_login } from '@/utils/login'

// 认证守卫钩子 - 替代Next.js中间件的功能
export function useAuthGuard() {
  const location = useLocation()
  const navigate = useNavigate()
  const isCheckingRef = useRef(false)

  useEffect(() => {
    // 防止重复检查
    if (isCheckingRef.current) return

    const checkAuthAndRedirect = () => {
      isCheckingRef.current = true

      try {
        // 获取token
        const token = getCookie(key_for_token)
        const tokenAes = getCookie(key_for_token_aes)

        const isLoggedIn = is_login(token, tokenAes)
        const currentPath = location.pathname

        if (!isLoggedIn) {
          // 用户未登录或登录已失效
          // 清除可能残留的本地数据
          if (typeof window !== 'undefined') {
            localStorage.removeItem('login')
          }

          if (currentPath === '/reset') {
            // 允许访问重置密码页面
            return
          } else if (currentPath !== '/login') {
            // 重定向到登录页面
            navigate('/login', { replace: true })
            return
          }
          // 如果已经在登录页面，继续
        } else {
          // 用户已登录
          if (currentPath === '/' || currentPath === '/login') {
            // 重定向到用户信息页面
            navigate('/user/information', { replace: true })
            return
          }
          // 其他页面继续访问
        }
      } catch (error) {
        // 出错时重定向到登录页面
        navigate('/login', { replace: true })
      } finally {
        // 延迟重置检查标志，避免快速重复检查
        setTimeout(() => {
          isCheckingRef.current = false
        }, 100)
      }
    }

    checkAuthAndRedirect()
  }, [location.pathname, navigate])
}
