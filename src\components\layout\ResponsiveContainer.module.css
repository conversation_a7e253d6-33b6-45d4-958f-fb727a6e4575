/* 响应式容器样式 */
.container {
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 流体容器 - 不限制最大宽度 */
.fluid {
  max-width: none !important;
}

/* 最大宽度设置 */
.maxWidth_xs {
  max-width: var(--container-xs);
}

.maxWidth_sm {
  max-width: var(--container-sm);
}

.maxWidth_md {
  max-width: var(--container-md);
}

.maxWidth_lg {
  max-width: var(--container-lg);
}

.maxWidth_xl {
  max-width: var(--container-xl);
}

.maxWidth_xxl {
  max-width: var(--container-xxl);
}

.maxWidth_full {
  max-width: 100%;
}

/* 内边距设置 */
.padding_none {
  padding: 0;
}

.padding_sm {
  padding: var(--spacing-sm);
}

.padding_md {
  padding: var(--spacing-lg);
}

.padding_lg {
  padding: var(--spacing-xl);
}

/* 响应式内边距 */
@media (max-width: 480px) {
  .padding_sm {
    padding: var(--spacing-xs);
  }
  
  .padding_md {
    padding: var(--spacing-mobile);
  }
  
  .padding_lg {
    padding: var(--spacing-lg);
  }
}

@media (min-width: 1200px) {
  .padding_md {
    padding: var(--spacing-desktop);
  }
  
  .padding_lg {
    padding: var(--spacing-xxl);
  }
}
