/**
 * 登录页面类型定义
 * 重新导出认证模块的类型，并添加页面特定的类型
 */

// 重新导出认证模块的核心类型
export {
  LoginRequest,
  LoginResponse,
  TokenValidationResponse,
  UserInfo,
  ApiResponse,
  PasswordResetRequest,
  PasswordResetResponse,
  PasswordChangeRequest,
  PasswordChangeResponse
} from '@/api/auth/types';

/**
 * 登录表单数据
 */
export interface LoginFormData {
  username?: string;      // 兼容旧的字段名
  password?: string;      // 兼容旧的字段名
  user_account?: string;  // 新的字段名
  user_password?: string; // 新的字段名
  remember_me?: boolean;
  captcha?: string;
}

/**
 * 登录页面状态
 */
export interface LoginPageState {
  loading: boolean;
  error: string | null;
  showCaptcha: boolean;
  captchaUrl?: string;
  loginAttempts: number;
  isLocked: boolean;
  lockExpiry?: Date;
}

/**
 * 登录API响应（页面级封装）
 */
export interface LoginApiResponse {
  status: 'OK' | 'NG';
  data?: any;
  message?: string;
  code?: string | number;
}

/**
 * 密码重置表单数据
 */
export interface PasswordResetFormData {
  email?: string;
  work_no?: string;
  verification_code?: string;
  new_password?: string;
  confirm_password?: string;
}

/**
 * 登录配置
 */
export interface LoginConfig {
  maxAttempts: number;
  lockDuration: number; // 分钟
  enableCaptcha: boolean;
  enableRememberMe: boolean;
  sessionTimeout: number; // 分钟
}

/**
 * 登录历史记录
 */
export interface LoginHistory {
  timestamp: Date;
  ip_address: string;
  user_agent: string;
  success: boolean;
  failure_reason?: string;
}

/**
 * 登录安全设置
 */
export interface LoginSecurity {
  two_factor_enabled: boolean;
  last_password_change: Date;
  password_expires_in: number; // 天数
  failed_attempts: number;
  last_failed_attempt?: Date;
}
