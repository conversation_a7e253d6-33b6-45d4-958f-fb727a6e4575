
import { DatePicker, Space, Button, message, Spin, Checkbox, Select, Table } from 'antd';
import React, { useEffect, useState } from 'react';
import { useApplicationSelector, useApplicationDispatch } from "@/hook/hooks";
import { useTranslation } from '@/hooks/useTranslation';
import ApiUrlVars from '@/api/common/url-vars';
import { getTransportationExpenseApprovalList, handleTransportationExpenseApproval } from '@/pages/approval/api/approvalApi';

import styles from './tpc.module.css'
import { tabActions } from '@/slice/tabSlice';
import { menuConfig, menuTypes } from '@/utils/menulist';
import { statusData, statusActions } from '@/slice/statusSlice';
import ErrorPart from '@/pages/components/ui/ErrorPart';
import MobileTable from '@/components/ui/MobileTable';

import { ActionButton } from '@/pages/components/ui/Table';
import dayjs from 'dayjs';

let dispatch: any = null

const TransportationPersonalConfirmation: React.FC = () => {
    const { t } = useTranslation(['transportation', 'common']);
    const [messageApi, contextHolder] = message.useMessage();
    const infData = useApplicationSelector(statusData);
    
    // 状态管理
    const [loading, setLoading] = useState(false);
    const [dataSource, setDataSource] = useState<any[]>([]);
    const [originalDataSource, setOriginalDataSource] = useState<any[]>([]); // 保存原始数据
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize] = useState(50);
    const [selectedMonth, setSelectedMonth] = useState(dayjs());
    const [selectedRows, setSelectedRows] = useState<any[]>([]);
    const [expenseType, setExpenseType] = useState<'all' | 'commuter' | 'single'>('all');
    const [hasModifications, setHasModifications] = useState(false); // 跟踪是否有修改
    const [lastConfirmTime, setLastConfirmTime] = useState<string | null>(null); // 上次确认时间
    const [isFirstLoad, setIsFirstLoad] = useState(true); // 跟踪是否是第一次加载
    
    dispatch = useApplicationDispatch();

    // 初始化
    useEffect(() => {
        dispatch(tabActions.onTabChange(menuTypes.approvel));
        dispatch(tabActions.onSubChange('2')); // 对应审批菜单中的index
        fetchData();
    }, []);

    // 获取数据
    const fetchData = async () => {
        setLoading(true);
        try {
            const params = {
                month: selectedMonth.format('YYYY-MM'),
                type: expenseType
            };
            
            // 这里应该调用实际的API
            // const response = await getApi(ApiUrlVars.api_domain + '/transportation/personal-confirmation', params, dispatch);
            
            // 模拟数据
            const mockData = [
                {
                    key: '1',
                    employee_name: '田中太郎',
                    employee_no: 'EMP001',
                    attendance_date: '2024-01-15',
                    route: '新宿駅 → 渋谷駅',
                    ticket_type: 'commuter',
                    amount: 320
                },
                {
                    key: '2',
                    employee_name: '田中太郎',
                    employee_no: 'EMP001',
                    attendance_date: '2024-01-16',
                    route: '渋谷駅 → 品川駅',
                    ticket_type: 'single',
                    amount: 200
                },
                {
                    key: '3',
                    employee_name: '佐藤花子',
                    employee_no: 'EMP002',
                    attendance_date: '2024-01-17',
                    route: '新宿駅 → 渋谷駅',
                    ticket_type: 'commuter',
                    amount: 320
                },
                {
                    key: '4',
                    employee_name: '山田次郎',
                    employee_no: 'EMP003',
                    attendance_date: '2024-01-18',
                    route: '東京駅 → 新橋駅',
                    ticket_type: 'single',
                    amount: 160
                },
                {
                    key: '5',
                    employee_name: '鈴木一郎',
                    employee_no: 'EMP004',
                    attendance_date: '2024-01-19',
                    route: '池袋駅 → 新宿駅',
                    ticket_type: 'commuter',
                    amount: 200
                }
            ];
            
            // 保存原始数据
            setOriginalDataSource(mockData);

            // 根据类型过滤数据
            let filteredData = mockData;
            if (expenseType === 'commuter') {
                filteredData = mockData.filter(item => item.ticket_type === 'commuter');
            } else if (expenseType === 'single') {
                filteredData = mockData.filter(item => item.ticket_type === 'single');
            }

            setDataSource(filteredData);

            // 模拟获取上次确认时间
            setLastConfirmTime('2024-01-15 14:30:25');

            // 重置修改状态，但保持第一次加载状态
            setHasModifications(false);
        } catch (error) {
            messageApi.error('データの取得に失敗しました');
        } finally {
            setLoading(false);
        }
    };

    // 个人承认处理
    const handlePersonalConfirm = async (record: any) => {
        try {
            setLoading(true);
            
            // 这里应该调用实际的API
            // const response = await postApi(ApiUrlVars.api_domain + '/transportation/personal-confirm', { id: record.key }, dispatch);
            
            // 模拟API调用
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 更新数据状态
            setDataSource(prev => prev.map(item => 
                item.key === record.key 
                    ? { ...item, status: 'confirmed' }
                    : item
            ));
            
            messageApi.success(t('transportation:messages.confirmSuccess', '個人承認が完了しました'));
        } catch (error) {
            messageApi.error(t('transportation:messages.confirmError', '個人承認に失敗しました'));
        } finally {
            setLoading(false);
        }
    };

    // 批量承认处理
    const handleBatchConfirm = async () => {
        if (!hasModifications && !isFirstLoad) {
            messageApi.warning(t('transportation:ui.noModifications', '修正された項目がありません'));
            return;
        }

        try {
            setLoading(true);

            // 这里应该调用实际的API
            // const response = await postApi(ApiUrlVars.api_domain + '/transportation/batch-confirm', { data: dataSource }, dispatch);

            // 模拟API调用
            await new Promise(resolve => setTimeout(resolve, 1500));

            // 更新确认时间
            const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
            setLastConfirmTime(now);

            // 重置修改状态
            setHasModifications(false);
            setIsFirstLoad(false);
            setSelectedRows([]);

            messageApi.success(t('transportation:ui.confirmCompleted', 'データの確認が完了しました'));
        } catch (error) {
            messageApi.error(t('transportation:ui.confirmFailed', '確認に失敗しました'));
        } finally {
            setLoading(false);
        }
    };

    // 路线变更处理
    const handleRouteChange = (recordKey: string, newRoute: string) => {
        // 更新当前显示的数据
        setDataSource(prev => prev.map(item =>
            item.key === recordKey
                ? { ...item, route: newRoute }
                : item
        ));

        // 同时更新原始数据源，确保筛选时以修改后的数据为准
        setOriginalDataSource(prev => prev.map(item =>
            item.key === recordKey
                ? { ...item, route: newRoute }
                : item
        ));

        // 标记有修改
        setHasModifications(true);
        setIsFirstLoad(false);
    };

    // 票类型变更处理
    const handleTicketTypeChange = (recordKey: string, newTicketType: string) => {
        // 更新当前显示的数据
        setDataSource(prev => prev.map(item =>
            item.key === recordKey
                ? { ...item, ticket_type: newTicketType }
                : item
        ));

        // 同时更新原始数据源，确保筛选时以修改后的数据为准
        setOriginalDataSource(prev => prev.map(item =>
            item.key === recordKey
                ? { ...item, ticket_type: newTicketType }
                : item
        ));

        // 标记有修改
        setHasModifications(true);
        setIsFirstLoad(false);
    };

    // 表格列配置
    const getTableColumns = () => [
        {
            key: 'employee_info',
            title: t('common:labels.employeeInfo', '姓名/工号'),
            mobileLabel: t('common:labels.employeeInfo', '姓名/工号'),
            mobilePosition: 'title' as const,
            dataIndex: 'employee_info',
            width: '20%',
            render: (text: string, record: any) => (
                <div className={styles.employee_info}>
                    <div className={styles.employee_name}>{record.employee_name}</div>
                    <div className={styles.employee_no}>{record.employee_no}</div>
                </div>
            )
        },
        {
            key: 'attendance_date',
            title: t('common:labels.attendanceDate', '出勤日期'),
            mobileLabel: t('common:labels.attendanceDate', '出勤日期'),
            mobilePosition: 'subtitle' as const,
            dataIndex: 'attendance_date',
            width: '15%',
            render: (text: string) => (
                <span className={styles.date_text}>
                    {dayjs(text).format('YYYY/MM/DD')}
                </span>
            )
        },
        {
            key: 'route',
            title: t('transportation:labels.route', '路线'),
            mobileLabel: t('transportation:labels.route', '路线'),
            mobilePosition: 'content' as const,
            dataIndex: 'route',
            width: '30%',
            render: (text: string, record: any) => (
                <Select
                    value={text}
                    onChange={(value) => handleRouteChange(record.key, value)}
                    style={{ width: '100%' }}
                    options={[
                        { value: '新宿-渋谷', label: '新宿-渋谷' },
                        { value: '東京-品川', label: '東京-品川' },
                        { value: '池袋-新宿', label: '池袋-新宿' },
                        { value: '横浜-東京', label: '横浜-東京' },
                        { value: '大阪-梅田', label: '大阪-梅田' }
                    ]}
                />
            )
        },
        {
            key: 'ticket_type',
            title: t('transportation:labels.ticketType', '定期券/单次票'),
            mobileLabel: t('transportation:labels.ticketType', '定期券/单次票'),
            mobilePosition: 'content' as const,
            dataIndex: 'ticket_type',
            width: '20%',
            render: (text: string, record: any) => (
                <Select
                    value={text}
                    onChange={(value) => handleTicketTypeChange(record.key, value)}
                    style={{ width: '100%' }}
                    options={[
                        {
                            value: 'commuter',
                            label: (
                                <span style={{ color: '#1890ff', fontWeight: '600' }}>
                                    {t('transportation:ui.commuterPassLabel', '定期券')}
                                </span>
                            )
                        },
                        {
                            value: 'single',
                            label: (
                                <span style={{ color: '#f57c00', fontWeight: '600' }}>
                                    {t('transportation:ui.singleTicketLabel', '単次票')}
                                </span>
                            )
                        }
                    ]}
                />
            )
        },
        {
            key: 'amount',
            title: t('transportation:labels.amount', '金额'),
            mobileLabel: t('transportation:labels.amount', '金额'),
            mobilePosition: 'meta' as const,
            dataIndex: 'amount',
            width: '15%',
            align: 'right' as const,
            render: (amount: number) => (
                <span className={styles.amount_text}>¥{amount}</span>
            )
        }
    ];

    // 获取表格数据
    const getTableData = () => {
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        return dataSource.slice(startIndex, endIndex);
    };

    // 月份变更处理
    const handleMonthChange = (date: any) => {
        setSelectedMonth(date);
        setCurrentPage(1);
        fetchData();
    };

    // 费用类型变更处理
    const handleExpenseTypeChange = (value: 'all' | 'commuter' | 'single') => {
        setExpenseType(value);
        setCurrentPage(1);
        setSelectedRows([]);

        // 基于当前修改后的数据进行筛选，而不是重新获取原始数据
        applyExpenseTypeFilter(value);
    };

    // 应用费用类型筛选
    const applyExpenseTypeFilter = (filterType: 'all' | 'commuter' | 'single') => {
        // 基于已修改的原始数据源进行筛选
        let filteredData = originalDataSource;
        if (filterType === 'commuter') {
            filteredData = originalDataSource.filter(item => item.ticket_type === 'commuter');
        } else if (filterType === 'single') {
            filteredData = originalDataSource.filter(item => item.ticket_type === 'single');
        }

        setDataSource(filteredData);
    };



    return (
        <>
            {infData.status === 'OK' && (
                <div style={{padding: '24px', background: '#f5f5f5', minHeight: '100vh', width: '100%'}}>
                    {contextHolder}

                    {/* 页面标题 */}
                    <div style={{
                        marginBottom: '32px',
                        padding: '20px 0',
                        borderBottom: '3px solid #e2e8f0',
                        position: 'relative'
                    }}>
                        <div style={{
                            position: 'absolute',
                            bottom: '-3px',
                            left: '0',
                            width: '120px',
                            height: '3px',
                            background: 'linear-gradient(90deg, #1890ff 0%, #40a9ff 100%)',
                            borderRadius: '2px'
                        }}></div>
                        <h1 style={{
                            fontSize: '26px',
                            fontWeight: '600',
                            color: '#2d3748',
                            margin: '0',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '12px',
                            letterSpacing: '1px'
                        }}>
                            <span style={{
                                fontSize: '28px',
                                color: '#667eea',
                                filter: 'drop-shadow(0 2px 4px rgba(102, 126, 234, 0.2))'
                            }}>🚌</span>
                            {t('transportation:personalConfirmation.title', '交通費精算個人承認')}
                        </h1>
                    </div>

                    <div className={styles.container}>

                        {/* 检索区域 */}
                        <div className="search-area" style={{
                            background: '#ffffff',
                            borderRadius: '12px',
                            padding: '20px',
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
                            border: '1px solid #e2e8f0',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '24px'
                        }}>
                            <div style={{
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '8px'
                            }}>
                                <label style={{
                                    fontSize: '13px',
                                    fontWeight: '600',
                                    color: '#333',
                                    marginBottom: '4px'
                                }}>
                                    {t('common:labels.month', '月份')}
                                </label>
                                <DatePicker
                                    picker="month"
                                    value={selectedMonth}
                                    onChange={handleMonthChange}
                                    format="YYYY年MM月"
                                    placeholder={t('common:placeholders.selectMonth', '月を選択')}
                                    className="month-picker"
                                    style={{
                                        width: '180px',
                                        height: '36px'
                                    }}
                                />
                            </div>
                            <div style={{
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '8px'
                            }}>
                                <label style={{
                                    fontSize: '13px',
                                    fontWeight: '600',
                                    color: '#333',
                                    marginBottom: '4px'
                                }}>
                                    {t('transportation:labels.expenseType', '费用类型')}
                                </label>
                                <Select
                                    value={expenseType}
                                    onChange={handleExpenseTypeChange}
                                    className="expense-type-select"
                                    style={{
                                        width: '150px',
                                        height: '36px'
                                    }}
                                    options={[
                                        { value: 'all', label: t('transportation:types.all', '全部') },
                                        { value: 'commuter', label: t('transportation:types.commuter', '定期券') },
                                        { value: 'single', label: t('transportation:types.single', '単次票') },
                                    ]}
                                />
                            </div>
                        </div>

                        {/* 确认操作区域 */}
                        <div style={{
                            background: '#ffffff',
                            borderRadius: '12px',
                            padding: '20px',
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
                            border: '1px solid #e2e8f0'
                        }}>
                            <div className="confirmation-area" style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center'
                            }}>
                                <div style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    gap: '8px'
                                }}>
                                    <div style={{
                                        fontSize: '14px',
                                        color: '#666',
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '8px'
                                    }}>
                                        <span style={{ fontSize: '16px' }}>💡</span>
                                        <span>{t('transportation:ui.noModificationMessage', '如无修改，将会对下面列表中的情况进行承认')}</span>
                                    </div>
                                    {hasModifications && (
                                        <span style={{
                                            fontSize: '13px',
                                            color: '#ff9800',
                                            fontWeight: '600'
                                        }}>
                                            ⚠️ {t('transportation:ui.dataModified', 'データが修正されています')}
                                        </span>
                                    )}
                                    {isFirstLoad && !hasModifications && (
                                        <span style={{
                                            fontSize: '13px',
                                            color: '#52c41a',
                                            fontWeight: '600'
                                        }}>
                                            ✓ {t('transportation:ui.canConfirm', '確認可能です')}
                                        </span>
                                    )}
                                </div>
                                <div style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '16px'
                                }}>
                                    {/* 上次确认时间 */}
                                    <div style={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'flex-end',
                                        gap: '4px'
                                    }}>
                                        <span style={{
                                            fontSize: '12px',
                                            color: '#999',
                                            fontWeight: '500'
                                        }}>
                                            {t('transportation:ui.lastConfirmTime', '前回確認時間')}
                                        </span>
                                        <span style={{
                                            fontSize: '13px',
                                            color: '#666',
                                            fontWeight: '600',
                                            fontFamily: 'monospace'
                                        }}>
                                            {lastConfirmTime || '---'}
                                        </span>
                                    </div>
                                    <Button
                                        type="primary"
                                        onClick={handleBatchConfirm}
                                        disabled={!hasModifications && !isFirstLoad}
                                        loading={loading}
                                        style={{
                                            backgroundColor: (hasModifications || isFirstLoad) ? '#52c41a' : '#d9d9d9',
                                            borderColor: (hasModifications || isFirstLoad) ? '#52c41a' : '#d9d9d9',
                                            color: (hasModifications || isFirstLoad) ? '#ffffff' : '#999',
                                            fontWeight: '600',
                                            height: '40px',
                                            minWidth: '120px',
                                            borderRadius: '8px',
                                            cursor: (hasModifications || isFirstLoad) ? 'pointer' : 'not-allowed'
                                        }}
                                    >
                                        ✓ {t('transportation:buttons.confirmCorrect', '確認無誤')}
                                    </Button>
                                </div>
                            </div>
                        </div>

                        {/* 表格区域 */}
                        <div style={{
                            borderRadius: '12px',
                            boxShadow: '0 6px 20px rgba(0, 0, 0, 0.08)',
                            border: '1px solid #e2e8f0',
                            background: '#ffffff',
                            overflow: 'hidden',
                            display: 'flex',
                            flexDirection: 'column',
                            height: '644px'
                        }}>
                            <MobileTable
                                columns={getTableColumns()}
                                dataSource={getTableData()}
                                loading={loading}
                                scroll={{ y: 384 }}
                                style={{
                                    background: '#ffffff',
                                    border: 'none'
                                }}
                                locale={{
                                    emptyText: t('transportation:messages.noTransportationData', '交通費データがありません')
                                }}
                                pagination={{
                                    current: currentPage,
                                    pageSize: 8,
                                    total: dataSource.length,
                                    onChange: (page) => setCurrentPage(page),
                                    showSizeChanger: false,
                                    showQuickJumper: true,
                                    pageSizeOptions: ['8', '16', '32'],
                                    showTotal: (total, range) =>
                                        `${range[0]}-${range[1]} / ${total} 条记录`,
                                    size: "default"
                                }}
                                className="transportation-confirmation-table"
                            />
                        </div>

                        {/* 表格样式覆盖 */}
                        <style jsx global>{`
                            /* 交通费精算个人承认表格样式 */
                            .transportation-confirmation-table.ant-table {
                                background: #ffffff !important;
                                border: none !important;
                                margin: 0 !important;
                                padding: 0 !important;
                            }

                            .transportation-confirmation-table .ant-table-container {
                                border: none !important;
                                background: #ffffff !important;
                                margin: 0 !important;
                                padding: 0 !important;
                            }

                            /* 蓝色表头样式 - 与其他页面保持一致 */
                            .transportation-confirmation-table .ant-table-thead > tr > th {
                                background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
                                color: #ffffff !important;
                                font-weight: 600 !important;
                                font-size: 13px !important;
                                text-align: center !important;
                                border-bottom: 2px solid #1890ff !important;
                                border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
                                border-top: none !important;
                                border-left: none !important;
                                padding: 16px 12px !important;
                                height: 50px !important;
                                line-height: 1.4 !important;
                                letter-spacing: 0.5px !important;
                                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
                                position: relative !important;
                            }

                            .transportation-confirmation-table .ant-table-thead > tr > th:last-child {
                                border-right: none !important;
                            }

                            /* 修复滚动条对齐问题 */
                            .transportation-confirmation-table .ant-table-header {
                                overflow: hidden !important;
                                margin-bottom: 0 !important;
                                padding: 0 !important;
                            }

                            .transportation-confirmation-table .ant-table-body {
                                overflow-y: auto !important;
                                overflow-x: hidden !important;
                                scrollbar-gutter: stable !important;
                            }

                            .transportation-confirmation-table .ant-table-thead {
                                position: sticky !important;
                                top: 0 !important;
                                z-index: 10 !important;
                                scrollbar-gutter: stable !important;
                            }

                            /* 表格行样式 */
                            .transportation-confirmation-table .ant-table-tbody > tr > td {
                                padding: 12px !important;
                                border-bottom: 1px solid #f0f0f0 !important;
                                vertical-align: middle !important;
                            }

                            .transportation-confirmation-table .ant-table-tbody > tr:hover > td {
                                background: #f8f9fa !important;
                            }

                            /* 分页样式 */
                            .transportation-confirmation-table .ant-pagination {
                                padding: 16px 20px !important;
                                border-top: 1px solid #e2e8f0 !important;
                                background: #ffffff !important;
                                margin: 0 !important;
                                text-align: center !important;
                            }


                        `}</style>
                    </div>
                </div>
            )}
            {infData.status === 'NG' && (
                <ErrorPart type='NG' message={infData.text} />
            )}
        </>
    )
};

export default TransportationPersonalConfirmation
