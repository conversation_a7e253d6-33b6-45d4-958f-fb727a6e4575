// API客户端工具

import { API_CONFIG, ERROR_MESSAGES, ENV_CONFIG, HTTP_STATUS } from './config';

/**
 * API请求选项
 */
export interface ApiRequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retries?: number;
  token?: string;
}

/**
 * API响应类型
 */
export interface ApiResponse<T = any> {
  status: 'OK' | 'ERROR';
  data?: T;
  message?: string;
  code?: string | number;
}

/**
 * API错误类型
 */
export class ApiError extends Error {
  public status: number;
  public code?: string | number;
  public response?: any;

  constructor(message: string, status: number, code?: string | number, response?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.code = code;
    this.response = response;
  }
}

// HTTP_STATUS 从 config.ts 导入，避免重复定义

/**
 * 默认请求头
 */
const DEFAULT_REQUEST_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  'X-Requested-With': 'XMLHttpRequest'
};

/**
 * API客户端类
 */
export class ApiClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseUrl: string = API_CONFIG.BASE_URL) {
    this.baseUrl = baseUrl;
    this.defaultHeaders = { ...DEFAULT_REQUEST_HEADERS };
  }

  /**
   * 设置默认认证token
   */
  setAuthToken(token: string) {
    this.defaultHeaders['Authorization'] = token;
  }

  /**
   * 移除认证token
   */
  removeAuthToken() {
    delete this.defaultHeaders['Authorization'];
  }

  /**
   * 发送API请求
   */
  async request<T = any>(
    endpoint: string,
    options: ApiRequestOptions = {}
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = API_CONFIG.TIMEOUT,
      retries = 3,
      token
    } = options;

    // 构建完整URL
    const url = `${this.baseUrl}${endpoint}`;

    // 构建请求头
    const requestHeaders = {
      ...this.defaultHeaders,
      ...headers
    };

    // 如果提供了token，使用它覆盖默认token
    if (token) {
      requestHeaders['Authorization'] = token;
    }

    // 构建请求配置
    const requestConfig: RequestInit = {
      method,
      headers: requestHeaders,
      signal: AbortSignal.timeout(timeout)
    };

    // 添加请求体
    if (body && method !== 'GET') {
      if (body instanceof FormData) {
        // FormData不需要设置Content-Type
        delete requestHeaders['Content-Type'];
        requestConfig.body = body;
      } else {
        requestConfig.body = JSON.stringify(body);
      }
    }

    // 开发环境日志
    if (ENV_CONFIG.isDevelopment) {
      console.log(`[API] ${method} ${url}`, {
        headers: requestHeaders,
        body: body
      });
    }

    // 执行请求（带重试机制）
    return this.executeWithRetry(url, requestConfig, retries);
  }

  /**
   * 带重试机制的请求执行
   */
  private async executeWithRetry<T>(
    url: string,
    config: RequestInit,
    retries: number
  ): Promise<ApiResponse<T>> {
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const response = await fetch(url, config);
        return await this.handleResponse<T>(response);
      } catch (error) {
        // 如果是最后一次尝试，抛出错误
        if (attempt === retries) {
          throw this.handleError(error);
        }

        // 等待后重试
        await this.delay(1000 * (attempt + 1));
      }
    }

    throw new ApiError(ERROR_MESSAGES.DEFAULT, 0);
  }

  /**
   * 处理响应
   */
  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    const contentType = response.headers.get('content-type');
    const isJson = contentType?.includes('application/json');

    let data: any;
    try {
      data = isJson ? await response.json() : await response.text();
    } catch (error) {
      throw new ApiError('响应解析失败', response.status);
    }

    // 开发环境日志
    if (ENV_CONFIG.isDevelopment) {
      console.log(`[API Response] ${response.status}`, data);
    }

    // 检查HTTP状态码
    if (response.status !== HTTP_STATUS.OK) {
      const message = data?.message || this.getErrorMessage(response.status);
      throw new ApiError(message, response.status, data?.code, data);
    }

    // 检查业务状态码
    if (isJson && data?.status === 'ERROR') {
      throw new ApiError(data.message || ERROR_MESSAGES.DEFAULT, response.status, data.code, data);
    }

    return data;
  }

  /**
   * 处理错误
   */
  private handleError(error: any): ApiError {
    if (error instanceof ApiError) {
      return error;
    }

    if (error.name === 'AbortError') {
      return new ApiError('请求超时', 0);
    }

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return new ApiError('网络连接失败', 0);
    }

    return new ApiError(error.message || ERROR_MESSAGES.DEFAULT, 0);
  }

  /**
   * 根据HTTP状态码获取错误消息
   */
  private getErrorMessage(status: number): string {
    return ERROR_MESSAGES[status] || ERROR_MESSAGES.DEFAULT;
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * GET请求
   */
  async get<T = any>(endpoint: string, options?: Omit<ApiRequestOptions, 'method'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  /**
   * POST请求
   */
  async post<T = any>(endpoint: string, body?: any, options?: Omit<ApiRequestOptions, 'method' | 'body'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'POST', body });
  }

  /**
   * PUT请求
   */
  async put<T = any>(endpoint: string, body?: any, options?: Omit<ApiRequestOptions, 'method' | 'body'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'PUT', body });
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(endpoint: string, options?: Omit<ApiRequestOptions, 'method'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }
}

// 创建默认API客户端实例
export const apiClient = new ApiClient();

// 导出便捷方法
export const { get, post, put, delete: del } = apiClient;
