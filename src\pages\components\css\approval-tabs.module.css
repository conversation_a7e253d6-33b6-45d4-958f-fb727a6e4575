/* 审核页面Tab页签优化样式 */

/* 外层Tab样式 - 申请一览、全社一览、部门一览 */
.approval_main_tabs {
    margin-top: 20px !important;
    margin-bottom: 0 !important;
}

.approval_main_tabs .ant-tabs-nav {
    margin-bottom: 0 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px 12px 0 0;
    padding: 8px 16px 0;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

.approval_main_tabs .ant-tabs-nav-wrap {
    background: transparent;
}

.approval_main_tabs .ant-tabs-nav-list {
    background: transparent;
}

.approval_main_tabs .ant-tabs-tab {
    background: rgba(255, 255, 255, 0.1) !important;
    border: none !important;
    border-radius: 8px 8px 0 0 !important;
    margin: 0 4px !important;
    padding: 12px 24px !important;
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative;
    overflow: hidden;
}

.approval_main_tabs .ant-tabs-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.approval_main_tabs .ant-tabs-tab:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.approval_main_tabs .ant-tabs-tab:hover::before {
    opacity: 1;
}

.approval_main_tabs .ant-tabs-tab-active {
    background: white !important;
    color: #667eea !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.approval_main_tabs .ant-tabs-tab-active::before {
    opacity: 0;
}

.approval_main_tabs .ant-tabs-ink-bar {
    display: none !important;
}

.approval_main_tabs .ant-tabs-content-holder {
    background: white;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

/* 外层Tab容器滚动条优化 */
.approval_main_tabs {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 transparent;
}

.approval_main_tabs::-webkit-scrollbar {
    width: 8px;
}

.approval_main_tabs::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
}

.approval_main_tabs::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.approval_main_tabs::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

.approval_main_tabs::-webkit-scrollbar-corner {
    background: transparent;
}

.approval_main_tabs .ant-tabs-content {
    padding: 0 !important;
    margin: 0 !important;
}

.approval_main_tabs .ant-tabs-tabpane {
    padding: 0 !important;
    margin: 0 !important;
}

/* 内层Tab样式 - 全部、待审批、已同意、已驳回 */
.approval_sub_tabs {
    padding-top: 0px !important;
    padding-left: 20px !important;
    padding-right: 20px !important;
    padding-bottom: 0 !important;
    margin: 0 !important;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.approval_sub_tabs .ant-tabs-nav {
    margin-bottom: 16px !important;
    margin-top: 16px !important;
    background: white;
    border-radius: 8px;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.approval_sub_tabs .ant-tabs-nav-wrap {
    background: transparent;
}

.approval_sub_tabs .ant-tabs-nav-list {
    background: transparent;
    width: 100%;
    justify-content: space-around;
}

.approval_sub_tabs .ant-tabs-tab {
    background: transparent !important;
    border: none !important;
    border-radius: 6px !important;
    margin: 0 2px !important;
    padding: 8px 16px !important;
    color: #6c757d !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    flex: 1;
    text-align: center;
    position: relative;
}

.approval_sub_tabs .ant-tabs-tab:hover {
    background: #e9ecef !important;
    color: #495057 !important;
    transform: translateY(-1px);
}

.approval_sub_tabs .ant-tabs-tab-active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.approval_sub_tabs .ant-tabs-ink-bar {
    display: none !important;
}

.approval_sub_tabs .ant-tabs-content-holder {
    background: transparent;
    padding: 0;
}

/* 简化的状态样式 */
.status_indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status_pending {
    background: #ffc107;
}

.status_approved {
    background: #28a745;
}

.status_rejected {
    background: #dc3545;
}

.status_all {
    background: #6c757d;
}

/* 响应式设计 */
@media (max-width: 1366px) {
    .approval_main_tabs .ant-tabs-tab {
        padding: 10px 20px !important;
        font-size: 14px !important;
    }
    
    .approval_sub_tabs .ant-tabs-tab {
        padding: 6px 12px !important;
        font-size: 13px !important;
    }
}

@media (max-width: 1200px) {
    .approval_main_tabs .ant-tabs-tab {
        padding: 8px 16px !important;
        font-size: 13px !important;
    }
    
    .approval_sub_tabs .ant-tabs-tab {
        padding: 6px 10px !important;
        font-size: 12px !important;
    }
    
    .approval_sub_tabs {
        margin: 12px !important;
        padding: 12px 16px 0 !important;
    }
}

/* 动画效果 */
@keyframes tabSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes tabFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.approval_main_tabs .ant-tabs-content,
.approval_sub_tabs .ant-tabs-content {
    animation: tabSlideIn 0.3s ease-out;
}

.approval_main_tabs .ant-tabs-tabpane,
.approval_sub_tabs .ant-tabs-tabpane {
    animation: tabFadeIn 0.2s ease-out;
}

/* 焦点状态优化 */
.approval_main_tabs .ant-tabs-tab:focus-visible,
.approval_sub_tabs .ant-tabs-tab:focus-visible {
    outline: 2px solid #667eea;
    outline-offset: 2px;
    border-radius: 6px;
}

/* 禁用状态 */
.approval_main_tabs .ant-tabs-tab-disabled,
.approval_sub_tabs .ant-tabs-tab-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* 加载状态指示器 */
.tab_loading_indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 加载状态 */
.tab_loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #6c757d;
}

/* 空状态 */
.tab_empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 300px;
    color: #6c757d;
}

.tab_empty_icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.tab_empty_text {
    font-size: 16px;
    font-weight: 500;
}

/* 简化的Tab标签 */
.tab_label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}


