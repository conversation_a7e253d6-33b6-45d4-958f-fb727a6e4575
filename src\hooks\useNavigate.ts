import { useNavigate as useReactRouterNavigate } from 'react-router-dom'
import { shortUrlMapping } from '../config/routes'

// 自定义导航钩子 - 支持短链接映射
export function useNavigate() {
  const navigate = useReactRouterNavigate()

  const customNavigate = (to: string, options?: any) => {
    // 检查是否是短链接，如果是则转换为完整路径
    const fullPath = shortUrlMapping[to as keyof typeof shortUrlMapping] || to
    navigate(fullPath, options)
  }

  return customNavigate
}
