namespace ApiTasksTypes {
    //#region 任务类型详细
    // 获取任务一览
    export const tasks_list_get: string = "task-list-get"
    // EntityDemo
    export const demo_entity: string = "demo-entity"
    // 获取登录信息
    export const login_get: string = "login-get"
    // 用户密码重置
    export const reset_post: string = "reset-post"
    // 用户修改密码
    export const password_change: string = "password-change"
    // 获取任务一览
    export const tasks_list_get_filter: string = "task-list-get-filter"
    // 新增加班申请
    export const overtimeNewList_post: string = "overtimeNewList_post"
    // 撤回加班申请
    export const overtimeApplyCancel_post: string = "overtimeApplyCancel_post"
    // 指定申请审批通过
    export const handleApprovalAgree: string = "handleApprovalAgree"
    // 新增部门成员
    export const departMemberAdd_post: string = "departMemberAdd_post"
    //取得考勤默认查询天数
    export const setting_search_day_get: string = "settingSearchDay_get"
    //设置考勤默认查询天数
    export const setting_search_day_post: string = "settingSearchDay_post"
    //取得开发人员列表
    export const setting_admin_list_get: string = "settingAdminList_get"
    //取得节假日信息
    export const setting_holiday_list_get: string = "settingHolidayList_get"
    //新增单个节假日信息
    export const setting_holiday_add_post: string = "settingHolidayAdd_post"
    //删除单个节假日信息
    export const setting_holiday_delete_post: string = "settingHolidayDelete_post"
    //取得特殊安排信息
    export const setting_special_list_get: string = "settingSpecialList_get"
    //新增特殊安排
    export const setting_special_add_post: string = "settingSpecialAdd_post"
    //删除特殊安排
    export const setting_special_delete_post: string = "settingSpecialDelete_post"
    //取得预设邮件
    export const setting_mail_get: string = "settingMailList_get"
    //取得预设邮件
    export const setting_process_get: string = "settingProcess_get"
    //#endregion
}

export default ApiTasksTypes;