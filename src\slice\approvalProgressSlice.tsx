import { createSlice, PayloadAction, } from "@reduxjs/toolkit";
import { RootState } from "../store";
import { menuConfig } from "../utils/menulist";

// 定义要保存到Store的数据格式
export interface approvalProgressState {
    firstStatus: any,
    secondStatus: any,
    thirdStatus: any,
    fourthStatus: any,
    fifthStatus: any,
    step: number
}

// 初始化数据
const initialState: approvalProgressState = {
    firstStatus: 'Pending', // 初始状态为 'Pending'，可以根据实际需求设置其他初始状态  
    secondStatus: 'Approved', // 初始状态为 'Pending'，可以根据实际需求设置其他初始状态  
    thirdStatus: 'Approved', // 初始状态为 'Pending'，可以根据实际需求设置其他初始状态  
    fourthStatus: 'Pending', // 初始状态为 'Pending'，可以根据实际需求设置其他初始状态  
    fifthStatus: 'Approved', // 初始状态为 'Pending'，可以根据实际需求设置其他初始状态  
    step: 1, // 初始步骤为 1，可以根据实际需求设置其他初始步骤  
};

export const approvalProgressSlice = createSlice({
    name: 'approvalProgress',
    initialState,
    // reducers 集合了reducer和action的功能
    // 每个case冒号之前的为action名
    // 每个case整体为reducer
    reducers: {
        // 使用PayloadAction来展示action.payload的详细内容
        updateStatus: (state, action) => {
            state.firstStatus = action.payload; // 根据后台数据更新状态  
            state.secondStatus = action.payload; // 根据后台数据更新状态  
            state.thirdStatus = action.payload; // 根据后台数据更新状态  
            state.fourthStatus = action.payload; // 根据后台数据更新状态  
            state.fifthStatus = action.payload; // 根据后台数据更新状态  
        },
    },
});

//以下内容必须要有
export const { actions: approvalProgressActions } = approvalProgressSlice;

export default approvalProgressSlice.reducer;

//state 后面的为store中数据名称
export const approvalProgressData = (state: RootState) => state.approvalProgress;
