

import { useApplicationSelector, useApplicationDispatch } from "@/hook/hooks";
import { statisticsData, statisticsActions } from '@/slice/statisticsSlice';
import React, { useState, useEffect } from 'react';
import ApiUrlVars from '@/api/common/url-vars';
import styles from './department.module.css';


import { Button, Pagination, message, Row, Col, Card, Table, Tag, Space, Divider, Modal, Transfer, Descriptions, Select, Tooltip } from 'antd';
import { EditOutlined, SwapOutlined } from '@ant-design/icons';
import { menuConfig, menuTypes } from '@/utils/menulist';
import { tabActions } from '@/slice/tabSlice';
import dayjs from 'dayjs';

import { getDepartmentStatistics, downloadDepartmentReport } from '@/pages/statistics/api/statisticsApi';
import { fetchDataByPostOnInitSaveToState, createAxios } from '@/api/fetch-api';
import { statusData, statusActions } from '@/slice/statusSlice';
import ErrorPart from '@/pages/components/ui/ErrorPart';

import { useTranslation } from '@/hooks/useTranslation';
import { DownloadOutlined, CalendarOutlined } from '@ant-design/icons';
import TableStyleOverride from './TableStyleOverride';

const url = ApiUrlVars.loginApi_domain + ApiUrlVars.summary_depart_info_get

// 分页配置常量
const PAGINATION_CONFIG = {
    pageSize: 100,
    showSizeChanger: false,
    showQuickJumper: true,
    pageSizeOptions: ['50', '100', '200'] as string[],
};

// 新增接口定义
interface TransportationRecord {
    key: string
    date: string
    route: string
    expenseType: 'commuter' | 'single'
    amount: number
    departure?: string
    destination?: string
}

interface UserDetailInfo {
    id: string
    employeeName: string
    workNo: string
    department: string
    position: string
    email: string
    transportationRecords: TransportationRecord[]
}

const DepartmentStatistics: React.FC = () => {
    // 多语言支持
    const { t: tCommon } = useTranslation('common');
    const { t: tStatistics } = useTranslation('statistics');

    // 翻译后备映射
    const statisticsTranslations = {
        'departmentStatistics': '部门统计数据',
        'statisticsMonth': '统计月份',
        'statisticsData': '统计数据',
        'export': '导出数据',
        'workNoName': '工号/姓名',
        'department': '所属部门',
        'absence': '缺勤天数',
        'workDays': '出勤天数',
        'commuterPassTotal': '定期券费用总计',
        'singleTicketTotal': '单次票费用总计',
        'totalTransportationExpense': '总交通费用',
        'other': '其他信息',
        'detail': '查看详情',
        'records': '条记录'
    };

    // 创建带后备的翻译函数
    const t = (key: string) => {
        const result = tStatistics(key);
        if (result === key && statisticsTranslations[key as keyof typeof statisticsTranslations]) {
            return statisticsTranslations[key as keyof typeof statisticsTranslations];
        }
        return result;
    };
    // 列表每页显示列表项最大数目
    const [currentPage, changeCurrentPage] = useState(1)
    // 页面弹窗
    const [messageApi, contextHolder] = message.useMessage();

    // 新Dialog相关状态
    const [dialogVisible, setDialogVisible] = useState(false)
    const [selectedUser, setSelectedUser] = useState<UserDetailInfo | null>(null)
    const [dialogLoading, setDialogLoading] = useState(false)

    // 穿梭框相关状态 - 重新设计
    const [commuterPassKeys, setCommuterPassKeys] = useState<string[]>([]) // 左侧定期券
    const [singleTicketKeys, setSingleTicketKeys] = useState<string[]>([]) // 右侧单次票
    const [selectedRoute, setSelectedRoute] = useState<string>('') // 当前选择的路线
    const [availableRoutes, setAvailableRoutes] = useState<string[]>([]) // 可用路线列表
    const [hasDataChanged, setHasDataChanged] = useState(false) // 数据是否发生变化
    const [originalData, setOriginalData] = useState<{commuter: string[], single: string[]}>({commuter: [], single: []}) // 原始数据

    // 新增状态：支持路线编辑和类型转换
    const [editingRoutes, setEditingRoutes] = useState<{[key: string]: string}>({}) // 正在编辑的路线
    const [tempRouteValues, setTempRouteValues] = useState<{[key: string]: string}>({}) // 临时路线值

    // 常用路线选项
    const commonRoutes = [
        '新宿站 → 东京站',
        '涩谷站 → 品川站',
        '横滨站 → 新宿站',
        '池袋站 → 秋叶原站',
        '上野站 → 银座站',
        '东京站 → 大阪站',
        '新宿站 → 涩谷站',
        '品川站 → 东京站',
        '秋叶原站 → 上野站',
        '银座站 → 新宿站'
    ]

    // Mock数据：用户详细交通费记录
    const mockUserTransportationData: { [key: string]: UserDetailInfo } = {
        'EMP001': {
            id: 'EMP001',
            employeeName: '田中太郎',
            workNo: 'EMP001',
            department: '技术部',
            position: '高级工程师',
            email: '<EMAIL>',
            transportationRecords: [
                // 新宿站 → 东京站 路线
                {
                    key: '1-1',
                    date: '2024-01-15',
                    route: '新宿站 → 东京站',
                    expenseType: 'commuter',
                    amount: 15000,
                    departure: '新宿站',
                    destination: '东京站'
                },
                {
                    key: '1-2',
                    date: '2024-01-20',
                    route: '新宿站 → 东京站',
                    expenseType: 'single',
                    amount: 200,
                    departure: '新宿站',
                    destination: '东京站'
                },
                // 涩谷站 → 品川站 路线
                {
                    key: '1-3',
                    date: '2024-01-10',
                    route: '涩谷站 → 品川站',
                    expenseType: 'commuter',
                    amount: 12000,
                    departure: '涩谷站',
                    destination: '品川站'
                },
                {
                    key: '1-4',
                    date: '2024-01-25',
                    route: '涩谷站 → 品川站',
                    expenseType: 'single',
                    amount: 180,
                    departure: '涩谷站',
                    destination: '品川站'
                },
                // 横滨站 → 新宿站 路线
                {
                    key: '1-5',
                    date: '2024-01-12',
                    route: '横滨站 → 新宿站',
                    expenseType: 'commuter',
                    amount: 18000,
                    departure: '横滨站',
                    destination: '新宿站'
                },
                {
                    key: '1-6',
                    date: '2024-01-28',
                    route: '横滨站 → 新宿站',
                    expenseType: 'single',
                    amount: 320,
                    departure: '横滨站',
                    destination: '新宿站'
                }
            ]
        }
    }

    const loginDataFromStorage = localStorage.getItem('login')
    const localStorageData = loginDataFromStorage? JSON.parse(loginDataFromStorage) : {}
    const departmentName = localStorageData.depart_name;
    // 获取权限一览
    const permission_info = localStorageData.permission_info;
    let depart_expense = 0;
    let depart_report = 0;
    for(let i=0;i<permission_info.length;i++){
        // 判断当前用户是否有报销费用导出权限
        if(permission_info[i][0] == "E" && permission_info[i][1] == "008"){
            depart_expense = 1;
        }
        // 判断当前用户是否有部门报表导出权限
        if(permission_info[i][0] == "E" && permission_info[i][1] == "006"){
            depart_report = 1;
        }
    }

    const dispatch = useApplicationDispatch();
    const data = useApplicationSelector(statisticsData);
    const infData = useApplicationSelector(statusData);
    const dMonths = data.dMonthInfos ? Object.keys(data.dMonthInfos) : [];

    useEffect(() => {
        fetchDataByPostOnInitSaveToState(url, {dl:0, ym:'0'}, dispatch, statisticsActions.initialDepartMonthInfos);
        dispatch(tabActions.onTabChange(menuTypes.report))
        // dispatch(tabActions.onSubChange(menuConfig[4].list[2].index))
    }, [])

    function downloadReport(target_ym: string){
        const paras = {
            dl:1,
            ym: target_ym
        }
        const axios = createAxios()
        axios.post(url, paras,  {
            responseType: 'blob', // 切记类型 blob
        })
        .then((res) => {
            let blob = new Blob([res.data]);
            let url = window.URL.createObjectURL(blob); // 创建 url 并指向 blob
            let a = document.createElement('a');
            a.href = url;
            a.download = '部门报表_' + target_ym + '_' + dayjs() + '.xlsx';
            a.click();
            window.URL.revokeObjectURL(url); // 释放该 url
            //页面动作弹窗
            if (res?.data?.status == 'NG') {
                messageApi.open({
                  type: 'error',
                  content: res?.data?.message,
                });
                if(res?.data?.message == '登录凭证失效'){
                  dispatch(statusActions.onFalse(res?.data?.message))
                }
              }else if(res?.data?.status == 'OK') {
                messageApi.open({
                  type: 'success',
                  content: res?.data?.message,
                });
              }
        }).catch((err) => {
        });
    }

    const downloadDepartReport = (target_ym: string) => {
        const url = ApiUrlVars.api_domain + ApiUrlVars.summary_depart_info_expense_get;
        const paras = {
            ym: target_ym
        }
        const axios = createAxios()
        axios.post(url, paras,  {
            responseType: 'blob', // 切记类型 blob
        }).then((res) => {
            let blob = new Blob([res.data]);
            let url = window.URL.createObjectURL(blob); // 创建 url 并指向 blob
            let a = document.createElement('a');
            a.href = url;
            a.download = '报销费用详细表_' + dayjs().format('YYYYMMDD') + '.xlsx';
            a.click();
            window.URL.revokeObjectURL(url); // 释放该 url
            //页面动作弹窗
            if (res?.data?.status == 'NG') {
                messageApi.open({
                  type: 'error',
                  content: res?.data?.message,
                });
                if(res?.data?.message == '登录凭证失效'){
                  dispatch(statusActions.onFalse(res?.data?.message))
                }
              }else if(res?.data?.status == 'OK') {
                messageApi.open({
                  type: 'success',
                  content: res?.data?.message,
                });
              }
        }).catch((err) => {
        });
    }

    // 处理行点击事件 - 新Dialog功能
    const handleRowClick = async (record: any) => {
        setDialogLoading(true)
        setDialogVisible(true)

        try {
            // 模拟API调用延迟
            await new Promise(resolve => setTimeout(resolve, 500))

            // 使用Mock数据或生成数据
            let userData = mockUserTransportationData[record.workNo]

            if (!userData) {
                // 如果没有预设数据，生成Mock数据
                userData = {
                    id: record.workNo,
                    employeeName: record.userName,
                    workNo: record.workNo,
                    department: record.department,
                    position: '员工',
                    email: `${record.workNo.toLowerCase()}@company.com`,
                    transportationRecords: [
                        {
                            key: `${record.workNo}-1`,
                            date: '2024-01-15',
                            route: '新宿站 → 东京站',
                            expenseType: 'commuter',
                            amount: record.commuterPassAmount || 15000,
                            departure: '新宿站',
                            destination: '东京站'
                        },
                        {
                            key: `${record.workNo}-2`,
                            date: '2024-01-20',
                            route: '涩谷站 → 品川站',
                            expenseType: 'single',
                            amount: record.singleTicketAmount || 3200,
                            departure: '涩谷站',
                            destination: '品川站'
                        }
                    ]
                }
            }

            setSelectedUser(userData)

            // 初始化穿梭框数据
            const routes = [...new Set(userData.transportationRecords.map(record => record.route))]
            setAvailableRoutes(routes)

            // 初始化定期券和单次票数据（默认为空，用户需要手动分配）
            setCommuterPassKeys([])
            setSingleTicketKeys([])
            setOriginalData({commuter: [], single: []})
            setHasDataChanged(false)

            // 清空编辑状态
            setEditingRoutes({})
            setTempRouteValues({})

        } catch (error) {
            console.error('Error fetching user details:', error)
        } finally {
            setDialogLoading(false)
        }
    }

    // 处理Dialog关闭
    const handleDialogClose = () => {
        setDialogVisible(false)
        setSelectedUser(null)
        setCommuterPassKeys([])
        setSingleTicketKeys([])
        setSelectedRoute('')
        setAvailableRoutes([])
        setHasDataChanged(false)
        setOriginalData({commuter: [], single: []})
    }

    // 获取所有记录（不再按路线过滤）
    const getAllRecords = () => {
        if (!selectedUser) return []
        return selectedUser.transportationRecords
    }



    // 路线编辑相关函数
    const startEditRoute = (recordKey: string, currentRoute: string) => {
        setEditingRoutes(prev => ({ ...prev, [recordKey]: currentRoute }))
        setTempRouteValues(prev => ({ ...prev, [recordKey]: currentRoute }))
    }

    const saveRouteEdit = (recordKey: string) => {
        const newRoute = tempRouteValues[recordKey]
        if (newRoute && selectedUser) {
            // 更新记录的路线
            const updatedRecords = selectedUser.transportationRecords.map(record =>
                record.key === recordKey ? { ...record, route: newRoute } : record
            )

            setSelectedUser({
                ...selectedUser,
                transportationRecords: updatedRecords
            })

            // 更新可用路线列表
            const allRoutes = [...new Set(updatedRecords.map(r => r.route))]
            setAvailableRoutes(allRoutes)

            setHasDataChanged(true)
        }

        // 退出编辑模式
        setEditingRoutes(prev => {
            const newState = { ...prev }
            delete newState[recordKey]
            return newState
        })
        setTempRouteValues(prev => {
            const newState = { ...prev }
            delete newState[recordKey]
            return newState
        })
    }

    const cancelRouteEdit = (recordKey: string) => {
        setEditingRoutes(prev => {
            const newState = { ...prev }
            delete newState[recordKey]
            return newState
        })
        setTempRouteValues(prev => {
            const newState = { ...prev }
            delete newState[recordKey]
            return newState
        })
    }



    // 处理穿梭框变更 - 单个穿梭框，左边定期券，右边单次票
    const handleSingleTicketChange = (newTargetKeys: string[], direction: string, moveKeys: string[]) => {
        // 更新单次票列表
        setSingleTicketKeys(newTargetKeys)

        // 更新定期券列表（所有不在单次票中的记录都在定期券中）
        const allKeys = getAllRecords().map(record => record.key)
        const newCommuterPassKeys = allKeys.filter(key => !newTargetKeys.includes(key))
        setCommuterPassKeys(newCommuterPassKeys)

        setHasDataChanged(true)
    }



    // 保存数据
    const handleSaveData = async () => {
        try {
            // 模拟API调用
            await new Promise(resolve => setTimeout(resolve, 1000))

            // 这里应该调用实际的API保存数据
            console.log('保存数据:', {
                userId: selectedUser?.id,
                route: selectedRoute,
                commuterPass: commuterPassKeys,
                singleTicket: singleTicketKeys
            })

            messageApi.success('数据保存成功！')
            setHasDataChanged(false)
            setOriginalData({commuter: [...commuterPassKeys], single: [...singleTicketKeys]})

        } catch (error) {
            messageApi.error('数据保存失败！')
            console.error('Save error:', error)
        }
    }

    // 获取表格数据
    const getTableData = () => {
        if (!data.dMonthInfos || !data.targetMonth) return [];

        const monthInfos = (data.dMonthInfos as any)[data.targetMonth] || {};
        const userIds = Object.keys(monthInfos);

        return userIds.map((workNo, index) => {
            const userInfo = monthInfos[workNo];
            let otherInfos = '';

            // 处理出差信息
            if (userInfo["trip_list"]) {
                otherInfos += userInfo["trip_list"].map((item: any) =>
                    `${item.location}出差(${item.start_time.slice(5,10)}/${item.end_time.slice(5,10)})`
                ).join('；');
            }

            // 处理其他假期信息
            if (userInfo["other_list"]) {
                otherInfos += userInfo["other_list"].map((item: any) =>
                    `其他假(${item.start_time.slice(5,10)}/${item.end_time.slice(5,10)})`
                ).join('；');
            }

            // 处理部门变更信息
            if (userInfo["is_depart_change"] == 1) {
                userInfo["depart_change_area_list"].forEach((item: any) => {
                    if (item[1] == '3000-01-01 00:00:00') {
                        otherInfos += `${item[0].slice(0,10)}入部；`;
                    } else {
                        otherInfos += `${item[0].slice(0,10)}入部；${item[1].slice(0,10)}退部；`;
                    }
                });
            }

            // 生成交通费Mock数据
            const generateTransportationMockData = (workNo: string) => {
                const seed = workNo.charCodeAt(workNo.length - 1) || 65; // 使用工号最后一个字符作为种子
                const commuterPassAmount = Math.floor((seed % 5 + 1) * 15000 + Math.random() * 5000); // 15000-25000
                const commuterPassCount = Math.floor(seed % 20 + 15); // 15-35次
                const singleTicketAmount = Math.floor((seed % 3 + 1) * 3000 + Math.random() * 2000); // 3000-8000
                const singleTicketCount = Math.floor(seed % 15 + 5); // 5-20次

                return {
                    commuterPassAmount,
                    commuterPassCount,
                    singleTicketAmount,
                    singleTicketCount
                };
            };

            const transportationData = generateTransportationMockData(workNo);

            return {
                key: index,
                workNo: workNo,
                userName: userInfo["user_name"] || '',
                department: departmentName,
                absence: userInfo["workday_amount"]?.absence_time || '-',
                workDays: userInfo["workday_amount"]?.work_day || '-',
                // 添加交通费数据
                commuterPassAmount: transportationData.commuterPassAmount,
                commuterPassCount: transportationData.commuterPassCount,
                singleTicketAmount: transportationData.singleTicketAmount,
                singleTicketCount: transportationData.singleTicketCount,
                otherInfo: otherInfos,
                userDetail: userInfo
            };
        });
    };

    // 获取当前页的数据
    const getCurrentPageData = () => {
        const allData = getTableData();
        const startIndex = (currentPage - 1) * PAGINATION_CONFIG.pageSize;
        const endIndex = startIndex + PAGINATION_CONFIG.pageSize;
        return allData.slice(startIndex, endIndex);
    };

    // 表格列定义
    const columns = [
        {
            title: t('workNoName'),
            dataIndex: 'workNo',
            key: 'workNo',
            className: 'col-work-no',
            fixed: 'left' as const,
            render: (workNo: string, record: any) => (
                <div className={styles.employee_info}>
                    <span className={styles.employee_text}>
                        {workNo}{record.userName ? `-${record.userName}` : ''}
                    </span>
                </div>
            ),
        },
        {
            title: t('department'),
            dataIndex: 'department',
            key: 'department',
            className: 'col-department',
        },
        {
            title: t('absence'),
            dataIndex: 'absence',
            key: 'absence',
            className: 'col-absence',
            align: 'center' as const,
        },
        {
            title: t('workDays'),
            dataIndex: 'workDays',
            key: 'workDays',
            className: 'col-work-days',
            align: 'center' as const,
        },
        {
            title: t('commuterPassTotal'),
            dataIndex: 'commuterPassTotal',
            key: 'commuterPassTotal',
            className: 'col-commuter-pass-total',
            align: 'center' as const,
            render: (value: any, record: any) => (
                <div style={{ textAlign: 'center' }}>
                    <div style={{
                        fontSize: '14px',
                        fontWeight: '600',
                        color: '#1890ff',
                        marginBottom: '2px'
                    }}>
                        ¥{record.commuterPassAmount?.toLocaleString() || '0'}
                    </div>
                    <div style={{
                        fontSize: '12px',
                        color: '#666',
                        background: '#f0f8ff',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        display: 'inline-block'
                    }}>
                        {record.commuterPassCount || 0}日使用
                    </div>
                </div>
            ),
        },
        {
            title: t('singleTicketTotal'),
            dataIndex: 'singleTicketTotal',
            key: 'singleTicketTotal',
            className: 'col-single-ticket-total',
            align: 'center' as const,
            render: (value: any, record: any) => (
                <div style={{ textAlign: 'center' }}>
                    <div style={{
                        fontSize: '14px',
                        fontWeight: '600',
                        color: '#f57c00',
                        marginBottom: '2px'
                    }}>
                        ¥{record.singleTicketAmount?.toLocaleString() || '0'}
                    </div>
                    <div style={{
                        fontSize: '12px',
                        color: '#666',
                        background: '#fff8e1',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        display: 'inline-block'
                    }}>
                        {record.singleTicketCount || 0}日使用
                    </div>
                </div>
            ),
        },
        {
            title: '总交通费用',
            dataIndex: 'totalTransportationExpense',
            key: 'totalTransportationExpense',
            className: 'col-total-transportation-expense',
            align: 'center' as const,
            render: (_: any, record: any) => {
                const commuterAmount = record.commuterPassAmount || 0;
                const singleAmount = record.singleTicketAmount || 0;
                const totalAmount = commuterAmount + singleAmount;
                const totalCount = (record.commuterPassCount || 0) + (record.singleTicketCount || 0);

                return (
                    <div style={{ textAlign: 'center' }}>
                        <div style={{
                            fontSize: '14px',
                            fontWeight: '600',
                            color: '#52c41a',
                            marginBottom: '2px'
                        }}>
                            ¥{totalAmount.toLocaleString()}
                        </div>
                        <div style={{
                            fontSize: '12px',
                            color: '#666',
                            background: '#f6ffed',
                            padding: '2px 6px',
                            borderRadius: '4px',
                            display: 'inline-block'
                        }}>
                            {totalCount}日使用
                        </div>
                    </div>
                );
            },
        },
        {
            title: t('other'),
            dataIndex: 'otherInfo',
            key: 'otherInfo',
            className: 'col-other-info',
            ellipsis: true,
        },

    ];

    return (
        <>
        <TableStyleOverride />
        {infData.status=='OK' && (
            <div className={styles.container}>
                {contextHolder}
                <div className={data.open ? styles.hide : ''}>
                    {/* 页面标题 */}
                    <div className={styles.page_header}>
                        <h1 className={styles.page_title}>
                            <CalendarOutlined className={styles.title_icon} />
                            {t('departmentStatistics')}
                        </h1>
                    </div>

                    {/* 月份选择区域 */}
                    <Card className={styles.month_selector_card}>
                        <div className={styles.month_selector_header}>
                            <h3>{t('statisticsMonth')}</h3>
                        </div>
                        <div className={styles.month_list}>
                            {dMonths.map((monthKey: string) => (
                                <div
                                    key={monthKey}
                                    className={`${styles.month_item} ${
                                        data.targetMonth === monthKey ? styles.month_item_active : ''
                                    }`}
                                    onClick={() => dispatch(statisticsActions.onTabChange(monthKey))}
                                >
                                    <CalendarOutlined className={styles.month_icon} />
                                    <span className={styles.month_text}>{monthKey}</span>
                                </div>
                            ))}
                        </div>
                    </Card>

                    {/* 数据表格区域 */}
                    {data.targetMonth && (
                        <Card className={styles.table_card}>
                            {/* 表格标题区域 */}
                            <div className={styles.table_header}>
                                <div className={styles.table_title}>
                                    <h3>{data.targetMonth} {t('statisticsData')}</h3>
                                </div>
                                <div className={styles.table_actions}>
                                    <Button
                                        type="primary"
                                        icon={<DownloadOutlined />}
                                        onClick={() => {
                                            if (depart_report == 1) {
                                                downloadReport(data.targetMonth);
                                            }
                                            if (depart_expense == 1) {
                                                downloadDepartReport(data.targetMonth);
                                            }
                                        }}
                                        className={styles.export_btn}
                                    >
                                        {t('export')}
                                    </Button>
                                </div>
                            </div>

                            {/* 表格主体容器 */}
                            <div className={styles.table_main_container}>
                                {/* 固定表头容器 */}
                                <div className={styles.fixed_header_container}>
                                    <div className={styles.custom_table_header}>
                                        <div className={styles.custom_table_header_row}>
                                            <div className={`${styles.custom_table_header_cell} ${styles.header_work_no}`}>
                                                {t('workNoName')}
                                            </div>
                                            <div className={`${styles.custom_table_header_cell} ${styles.header_department}`}>
                                                {t('department')}
                                            </div>
                                            <div className={`${styles.custom_table_header_cell} ${styles.header_absence}`}>
                                                {t('absence')}
                                            </div>
                                            <div className={`${styles.custom_table_header_cell} ${styles.header_work_days}`}>
                                                {t('workDays')}
                                            </div>
                                            <div className={`${styles.custom_table_header_cell} ${styles.header_commuter_pass_total}`}>
                                                {t('commuterPassTotal')}
                                            </div>
                                            <div className={`${styles.custom_table_header_cell} ${styles.header_single_ticket_total}`}>
                                                {t('singleTicketTotal')}
                                            </div>
                                            <div className={`${styles.custom_table_header_cell} ${styles.header_total_transportation_expense}`}>
                                                {t('totalTransportationExpense')}
                                            </div>
                                            <div className={`${styles.custom_table_header_cell} ${styles.header_other}`}>
                                                {t('other')}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* 表格滚动区域 */}
                                <div className={styles.table_body_container}>
                                    <div className={styles.table_content_wrapper}>
                                        <Table
                                            columns={columns.map((col, index) => ({
                                                ...col,
                                                width: index === 0 ? '14%' :  // 工号/姓名
                                                       index === 1 ? '12%' :  // 所属部门
                                                       index === 2 ? '10%' :  // 缺勤天数
                                                       index === 3 ? '10%' :  // 出勤天数
                                                       index === 4 ? '14%' :  // 定期券费用总计
                                                       index === 5 ? '14%' :  // 单次票费用总计
                                                       index === 6 ? '14%' :  // 总交通费用
                                                       '12%'                   // 其他信息
                                            }))}
                                            dataSource={getCurrentPageData()}
                                            pagination={false}
                                            size="small"
                                            className={`${styles.data_table} ${styles.table_scroll_config} data_table department-statistics-table`}
                                            rowClassName={(_, index) =>
                                                index % 2 === 0 ? styles.table_row_even : styles.table_row_odd
                                            }
                                            bordered={false}
                                            showHeader={false}
                                            tableLayout="fixed"
                                            onRow={(record) => ({
                                                onClick: () => {
                                                    // 只使用新的Dialog功能
                                                    handleRowClick(record);
                                                },
                                                style: { cursor: 'pointer' }
                                            })}
                                            style={{
                                                background: '#ffffff',
                                                border: 'none',
                                                margin: 0,
                                                padding: 0,
                                                width: '100%'
                                            }}
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* 固定分页区域 */}
                            <div className={styles.pagination_container}>
                                <Pagination
                                    current={currentPage}
                                    pageSize={PAGINATION_CONFIG.pageSize}
                                    total={getTableData().length}
                                    onChange={(page) => changeCurrentPage(page)}
                                    showSizeChanger={PAGINATION_CONFIG.showSizeChanger}
                                    showQuickJumper={PAGINATION_CONFIG.showQuickJumper}
                                    pageSizeOptions={PAGINATION_CONFIG.pageSizeOptions}
                                    showTotal={(total, range) =>
                                        `${range[0]}-${range[1]} / ${total} ${t('records')}`
                                    }
                                    size="default"
                                />
                            </div>
                        </Card>
                    )}
                </div>

                {/* 用户详情Dialog */}
                <Modal
                    title={selectedUser ? `${selectedUser.employeeName} - ${tCommon('transportationExpenseDetails')}` : tCommon('userDetails')}
                    open={dialogVisible}
                    onCancel={handleDialogClose}
                    width={1200}
                    footer={[
                        <Button
                            key="save"
                            type="primary"
                            onClick={handleSaveData}
                            disabled={!hasDataChanged}
                            style={{ marginRight: '8px' }}
                        >
                            {tCommon('save')}
                        </Button>,
                        <Button key="close" onClick={handleDialogClose}>
                            {tCommon('close')}
                        </Button>
                    ]}
                >
                    {dialogLoading ? (
                        <div style={{ textAlign: 'center', padding: '40px' }}>
                            {tCommon('loading')}...
                        </div>
                    ) : selectedUser ? (
                        <div>
                            {/* 用户信息 */}
                            <Card
                                title={
                                    <span style={{ fontSize: '16px', fontWeight: '600' }}>
                                        👤 {tCommon('userInfo')}
                                    </span>
                                }
                                style={{ marginBottom: '24px' }}
                            >
                                <Descriptions column={2} bordered>
                                    <Descriptions.Item label={tCommon('workNoAndName')}>
                                        {selectedUser.workNo} - {selectedUser.employeeName}
                                    </Descriptions.Item>
                                    <Descriptions.Item label={t('department')}>
                                        {selectedUser.department}
                                    </Descriptions.Item>
                                    <Descriptions.Item label={tCommon('position')}>
                                        {selectedUser.position}
                                    </Descriptions.Item>
                                    <Descriptions.Item label={tCommon('email')}>
                                        {selectedUser.email}
                                    </Descriptions.Item>
                                </Descriptions>
                            </Card>

                            {/* 交通费管理 */}
                            <Card
                                title={
                                    <span style={{ fontSize: '16px', fontWeight: '600' }}>
                                        🚇 {tCommon('transportationExpenseManagement')} - {tCommon('commuterPassAndSingleTicketClassification')}
                                    </span>
                                }
                                style={{ marginBottom: '24px' }}
                            >
                                <div>
                                        {/* 单个穿梭框布局 - 左边定期券，右边单次票 */}
                                        <Transfer
                                            dataSource={getAllRecords().map(record => ({
                                                ...record,
                                                title: `${record.date}`,
                                                description: `¥${record.amount}`
                                            }))}
                                            targetKeys={singleTicketKeys}
                                            onChange={handleSingleTicketChange}
                                            titles={[
                                                <span key="left" style={{ color: '#1890ff', fontWeight: '600' }}>
                                                    🎫 {tCommon('commuterPass')}
                                                </span>,
                                                <span key="right" style={{ color: '#fa8c16', fontWeight: '600' }}>
                                                    🎟️ {tCommon('singleTicket')}
                                                </span>
                                            ]}
                                            render={(item) => (
                                                <div style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
                                                    <div style={{ fontWeight: '500', marginBottom: '6px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                        <span>{item.title} - {item.description}</span>
                                                        <Button
                                                            size="small"
                                                            type="link"
                                                            icon={<SwapOutlined />}
                                                            onClick={() => {
                                                                // 在定期券和单次票之间切换
                                                                if (singleTicketKeys.includes(item.key)) {
                                                                    // 当前在单次票中，移动到定期券
                                                                    setSingleTicketKeys(prev => prev.filter(key => key !== item.key))
                                                                    setCommuterPassKeys(prev => [...prev, item.key])
                                                                } else {
                                                                    // 当前在定期券中，移动到单次票
                                                                    setCommuterPassKeys(prev => prev.filter(key => key !== item.key))
                                                                    setSingleTicketKeys(prev => [...prev, item.key])
                                                                }
                                                                setHasDataChanged(true)
                                                            }}
                                                            style={{
                                                                padding: '0 4px',
                                                                fontSize: '12px',
                                                                color: singleTicketKeys.includes(item.key) ? '#1890ff' : '#fa8c16'
                                                            }}
                                                            title={singleTicketKeys.includes(item.key) ? tCommon('convertToCommuterPass') : tCommon('convertToSingleTicket')}
                                                        />
                                                    </div>
                                                    <div style={{ fontSize: '12px', color: '#666', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                                        <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                                                            {editingRoutes[item.key] !== undefined ? (
                                                                <div style={{ display: 'flex', alignItems: 'center', gap: '4px', flex: 1 }}>
                                                                    <Select
                                                                        size="small"
                                                                        value={tempRouteValues[item.key] || ''}
                                                                        onChange={(value) => setTempRouteValues(prev => ({ ...prev, [item.key]: value }))}
                                                                        style={{ fontSize: '11px', flex: 1, minWidth: '150px' }}
                                                                        placeholder={tCommon('selectRoute')}
                                                                        showSearch
                                                                        filterOption={(input, option) =>
                                                                            option?.value ? option.value.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0 : false
                                                                        }
                                                                        dropdownStyle={{ fontSize: '12px' }}
                                                                    >
                                                                        {/* 当前路线选项 */}
                                                                        {!commonRoutes.includes(item.route) && (
                                                                            <Select.Option key={item.route} value={item.route}>
                                                                                {item.route}
                                                                            </Select.Option>
                                                                        )}
                                                                        {/* 常用路线选项 */}
                                                                        {commonRoutes.map(route => (
                                                                            <Select.Option key={route} value={route}>
                                                                                {route}
                                                                            </Select.Option>
                                                                        ))}
                                                                        {/* 已有路线选项 */}
                                                                        {availableRoutes
                                                                            .filter(route => !commonRoutes.includes(route) && route !== item.route)
                                                                            .map(route => (
                                                                                <Select.Option key={route} value={route}>
                                                                                    {route}
                                                                                </Select.Option>
                                                                            ))
                                                                        }
                                                                    </Select>
                                                                    <Button size="small" type="link" onClick={() => saveRouteEdit(item.key)} style={{ padding: '0 2px', fontSize: '11px' }}>
                                                                        ✓
                                                                    </Button>
                                                                    <Button size="small" type="link" onClick={() => cancelRouteEdit(item.key)} style={{ padding: '0 2px', fontSize: '11px' }}>
                                                                        ✕
                                                                    </Button>
                                                                </div>
                                                            ) : (
                                                                <div style={{ display: 'flex', alignItems: 'center', gap: '4px', flex: 1 }}>
                                                                    <span style={{ flex: 1 }}>{item.route}</span>
                                                                    <Button
                                                                        size="small"
                                                                        type="link"
                                                                        icon={<EditOutlined />}
                                                                        onClick={() => startEditRoute(item.key, item.route)}
                                                                        style={{ padding: '0 2px', fontSize: '11px' }}
                                                                    />
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                            )}
                                            listStyle={{
                                                width: 450,
                                                height: 350,
                                            }}
                                            operations={[`${tCommon('moveToSingleTicket')} →`, `← ${tCommon('moveToCommuterPass')}`]}
                                            showSearch
                                            filterOption={(inputValue, option) =>
                                                option.title?.indexOf(inputValue) > -1 ||
                                                option.route?.indexOf(inputValue) > -1
                                            }
                                            style={{ marginBottom: '24px' }}
                                        />

                                        {/* 费用统计 - 根据穿梭框左右自动计算 */}
                                        <div style={{ marginTop: '24px', padding: '16px', backgroundColor: '#f6ffed', borderRadius: '6px' }}>
                                            <Row gutter={16}>
                                                <Col span={6}>
                                                    <div style={{ textAlign: 'center' }}>
                                                        <div style={{ fontSize: '20px', fontWeight: '600', color: '#1890ff' }}>
                                                            {getAllRecords().filter(record => !singleTicketKeys.includes(record.key)).length}
                                                        </div>
                                                        <div style={{ fontSize: '12px', color: '#666' }}>{tCommon('commuterPassRecordCount')}</div>
                                                    </div>
                                                </Col>
                                                <Col span={6}>
                                                    <div style={{ textAlign: 'center' }}>
                                                        <div style={{ fontSize: '20px', fontWeight: '600', color: '#1890ff' }}>
                                                            ¥{getAllRecords()
                                                                .filter(record => !singleTicketKeys.includes(record.key))
                                                                .reduce((sum, record) => sum + record.amount, 0)
                                                            }
                                                        </div>
                                                        <div style={{ fontSize: '12px', color: '#666' }}>{tCommon('commuterPassTotalCost')}</div>
                                                    </div>
                                                </Col>
                                                <Col span={6}>
                                                    <div style={{ textAlign: 'center' }}>
                                                        <div style={{ fontSize: '20px', fontWeight: '600', color: '#fa8c16' }}>
                                                            {singleTicketKeys.length}
                                                        </div>
                                                        <div style={{ fontSize: '12px', color: '#666' }}>{tCommon('singleTicketRecordCount')}</div>
                                                    </div>
                                                </Col>
                                                <Col span={6}>
                                                    <div style={{ textAlign: 'center' }}>
                                                        <div style={{ fontSize: '20px', fontWeight: '600', color: '#fa8c16' }}>
                                                            ¥{getAllRecords()
                                                                .filter(record => singleTicketKeys.includes(record.key))
                                                                .reduce((sum, record) => sum + record.amount, 0)
                                                            }
                                                        </div>
                                                        <div style={{ fontSize: '12px', color: '#666' }}>{tCommon('singleTicketTotalCost')}</div>
                                                    </div>
                                                </Col>
                                            </Row>

                                            {/* 总计信息 */}
                                            <div style={{ marginTop: '16px', paddingTop: '16px', borderTop: '1px solid #d9d9d9' }}>
                                                <Row gutter={16}>
                                                    <Col span={12}>
                                                        <div style={{ textAlign: 'center' }}>
                                                            <div style={{ fontSize: '18px', fontWeight: '600', color: '#52c41a' }}>
                                                                {getAllRecords().length}
                                                            </div>
                                                            <div style={{ fontSize: '12px', color: '#666' }}>{tCommon('totalRecordCount')}</div>
                                                        </div>
                                                    </Col>
                                                    <Col span={12}>
                                                        <div style={{ textAlign: 'center' }}>
                                                            <div style={{ fontSize: '18px', fontWeight: '600', color: '#52c41a' }}>
                                                                ¥{getAllRecords().reduce((sum, record) => sum + record.amount, 0)}
                                                            </div>
                                                            <div style={{ fontSize: '12px', color: '#666' }}>{tCommon('totalCost')}</div>
                                                        </div>
                                                    </Col>
                                                </Row>
                                            </div>
                                        </div>
                                </div>
                            </Card>
                        </div>
                    ) : null}
                </Modal>
            </div>
        )}
        {infData.status=='NG' && (
            <ErrorPart type='NG' message={infData.text} />
        )}
        </>
    );
};

export default DepartmentStatistics
