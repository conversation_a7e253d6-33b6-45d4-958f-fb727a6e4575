import React, { useState, useEffect, useRef } from 'react';
import { Badge } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import isBetween from 'dayjs/plugin/isBetween';
import minMax from 'dayjs/plugin/minMax';
import 'dayjs/locale/zh-cn';
import 'dayjs/locale/ja';

// 扩展 dayjs 插件
dayjs.extend(isSameOrBefore);
dayjs.extend(isBetween);
dayjs.extend(minMax);
import { useTranslation } from '@/hooks/useTranslation';
import styles from './AttendanceCalendar.module.css';

// 考勤记录接口定义
interface AttendanceRecord {
  day: string;
  start: string;
  end: string;
  day_type: number; // 1=周末, 2=祝日, 3=其他特殊日期
  err: number; // 1=异常, 2=确认
  work_no: string;
  name: string;
  update_name?: string;
  update_from?: string;
  [key: string]: any;
}

interface AttendanceCalendarProps {
  dataSource: AttendanceRecord[];
  loading?: boolean;
  onDateSelect?: (date: Dayjs, records: AttendanceRecord[]) => void;
  className?: string;
  style?: React.CSSProperties;
}

const AttendanceCalendar: React.FC<AttendanceCalendarProps> = ({
  dataSource = [],
  loading = false,
  onDateSelect,
  className = '',
  style = {}
}) => {
  const { t, currentLanguage } = useTranslation(['attendance', 'common']);
  const [selectedDate, setSelectedDate] = useState<Dayjs>(dayjs());
  const contentContainerRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);

  // 设置dayjs语言
  useEffect(() => {
    // 设置语言环境
    if (currentLanguage === 'ja') {
      dayjs.locale('ja');
    } else {
      dayjs.locale('zh-cn');
    }

    // 强制重新渲染以确保语言变更生效
    // 使用当前时间戳创建新的日期对象，确保组件重新渲染
    const now = dayjs();
    setSelectedDate(now);

    // 同时更新开始和结束日期以确保日历重新计算
    const newStartDate = now.subtract(1, 'month');
    const newEndDate = now;

    // 这里可以触发父组件的数据重新加载
    console.log('Language changed, calendar should refresh');
  }, [currentLanguage]);

  // 动态调整表头padding以匹配滚动条
  useEffect(() => {
    const adjustHeaderPadding = () => {
      if (contentContainerRef.current && headerRef.current) {
        const container = contentContainerRef.current;
        const header = headerRef.current;

        // 检测是否有滚动条
        const hasScrollbar = container.scrollHeight > container.clientHeight;

        if (hasScrollbar) {
          // 计算滚动条宽度
          const scrollbarWidth = container.offsetWidth - container.clientWidth;
          header.style.paddingRight = `${scrollbarWidth}px`;
        } else {
          header.style.paddingRight = '0px';
        }
      }
    };

    // 初始调整
    adjustHeaderPadding();

    // 监听窗口大小变化
    const handleResize = () => {
      setTimeout(adjustHeaderPadding, 100); // 延迟执行，确保DOM更新完成
    };

    window.addEventListener('resize', handleResize);

    // 监听数据变化
    const observer = new MutationObserver(adjustHeaderPadding);
    if (contentContainerRef.current) {
      observer.observe(contentContainerRef.current, {
        childList: true,
        subtree: true
      });
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      observer.disconnect();
    };
  }, [dataSource]);

  // 获取数据的日期范围 - 确保当前日期在最后一行
  const getDateRange = () => {
    if (dataSource.length === 0) {
      return { startDate: dayjs(), endDate: dayjs() };
    }

    const dates = dataSource.map(record => dayjs(record.day?.slice(0, 10)));
    const dataStartDate = dayjs.min(dates);
    const dataEndDate = dayjs.max(dates);
    const today = dayjs();

    // 确保结束日期至少是今天
    let endDate = dayjs.max([dataEndDate, today]);

    // 确保当前日期在最后一行：找到包含当前日期的周的周日
    while (endDate.day() !== 0) {
      endDate = endDate.add(1, 'day');
    }

    // 开始日期保持数据的最小日期，但确保是周一开始
    let startDate = dataStartDate;
    while (startDate.day() !== 1) {
      startDate = startDate.subtract(1, 'day');
    }

    return { startDate, endDate };
  };

  const { startDate, endDate } = getDateRange();

  // 根据日期获取考勤记录
  const getRecordsForDate = (date: Dayjs): AttendanceRecord[] => {
    const dateStr = date.format('YYYY-MM-DD');
    return dataSource.filter(record => {
      const recordDate = dayjs(record.day?.slice(0, 10)).format('YYYY-MM-DD');
      return recordDate === dateStr;
    });
  };

  // 判断日期是否为周末
  const isWeekend = (date: any) => {
    if (!date || typeof date.day !== 'function') return false;
    const dayOfWeek = date.day();
    return dayOfWeek === 0 || dayOfWeek === 6; // 0=周日, 6=周六
  };

  // 获取日期的状态类型
  const getDateStatus = (date: any, records: any[]) => {
    // 如果有考勤记录，优先根据记录判断
    if (records && records.length > 0) {
      const hasException = records.some((record: any) => record.err === 1);
      const hasConfirm = records.some((record: any) => record.err === 2);
      const isWeekendHoliday = records.some((record: any) =>
        record.day_type === 1 || record.day_type === 2 || record.day_type === 3
      );

      if (hasException) return 'exception';
      if (hasConfirm) return 'confirm';
      if (isWeekendHoliday) return 'weekend-holiday';
      return 'normal';
    }

    // 如果没有考勤记录，根据日期本身判断是否为周末
    if (isWeekend(date)) {
      return 'weekend-holiday';
    }

    // 工作日但没有考勤记录
    return 'workday-no-record';
  };

  // 获取状态对应的颜色
  const getStatusColor = (status: string | null) => {
    switch (status) {
      case 'exception': return '#ff4d4f';
      case 'confirm': return '#fa8c16';
      case 'weekend-holiday': return '#e6f7ff'; // 淡蓝色背景
      case 'normal': return '#f6ffed'; // 绿色背景
      case 'workday-no-record': return '#f6ffed'; // 工作日无记录也用绿色背景
      default: return '#f5f5f5';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string | null) => {
    switch (status) {
      case 'exception': return t('status.exception', '异常');
      case 'confirm': return t('status.confirm', '确认');
      case 'weekend-holiday': return t('status.weekendHoliday', '周末/祝日');
      case 'normal': return t('status.normal', '正常');
      case 'workday-no-record': return t('status.workdayNoRecord', '工作日');
      default: return '';
    }
  };

  // 渲染时间显示（类似表格视图）
  const renderTimeDisplay = (record: any, timeType: 'start' | 'end') => {
    const isStart = timeType === 'start';
    const currentTime = isStart ? record?.start : record?.end;
    const initTime = isStart ? record?.init_start : record?.init_end;
    const hasChanged = isStart ? record?.change_start : record?.change_end;

    if (record?.et_code === '999999') {
      if (initTime === currentTime) {
        return <span className={styles.time_normal}>{currentTime}</span>;
      } else {
        return (
          <span className={styles.time_changed}>
            <span className={styles.time_crossed}>{initTime}</span>
            <span className={styles.time_arrow}> ⇨ </span>
            <span>{currentTime}</span>
          </span>
        );
      }
    } else {
      if (hasChanged) {
        return (
          <span className={styles.time_changed}>
            <span className={styles.time_crossed}>{initTime}</span>
            <span className={styles.time_arrow}> ⇨ </span>
            <span>{currentTime}</span>
          </span>
        );
      } else {
        return <span className={styles.time_normal}>{currentTime}</span>;
      }
    }
  };

  // 日期单元格渲染
  const dateCellRender = (value: Dayjs) => {
    const records = getRecordsForDate(value);
    const status = getDateStatus(records);

    if (records.length === 0) return null;

    return (
      <div className={styles.attendance_info}>
        {records.map((record, index) => {
          // 检查是否有时间记录
          const hasTimeRecord = record.start || record.end;
          // 检查是否有休假记录
          const hasLeave = record.c_hours > 0 || record.t_hours > 0 || record.s_hours > 0 || record.o_hours > 0;

          return (
            <div key={index} className={`${styles.record_item} ${!hasTimeRecord ? styles.no_time_record : ''}`}>
              {/* 只有在有时间记录时才显示时间信息 */}
              {hasTimeRecord && (
                <>
                  {/* 时间和出勤地信息 - 放在一排，靠右显示 */}
                  <div className={styles.time_location_container}>
                    <div className={styles.time_section}>
                      {/* 上班时间 */}
                      {record.start && (
                        <div className={styles.time_row}>
                          <span className={styles.time_label}>{t('timeLabels.clockIn')}:</span>
                          {renderTimeDisplay(record, 'start')}
                        </div>
                      )}

                      {/* 下班时间 */}
                      {record.end && (
                        <div className={styles.time_row}>
                          <span className={styles.time_label}>{t('timeLabels.clockOut')}:</span>
                          {renderTimeDisplay(record, 'end')}
                        </div>
                      )}
                    </div>

                    {/* 出勤地信息 */}
                    <div className={styles.location_section}>
                      {record?.home == 1 ? (
                        <div className={styles.work_from_home_tag}>
                          <span className={styles.tag_text}>🏠 {t('common:workFromHome', '在宅勤務')}</span>
                        </div>
                      ) : record?.bt_code != 0 && record?.bt_hours > 0 ? (
                        <div className={styles.business_trip_tag}>
                          <span className={styles.tag_text}>✈️ {t('common:businessTrip', '出張')}</span>
                        </div>
                      ) : (
                        <div className={styles.office_tag}>
                          <span className={styles.tag_text}>🏢 {t('common:office', 'オフィス')}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}

              {/* 休假信息 */}
              {hasLeave && (
                <div className={styles.leave_overtime_info}>
                  {record.c_hours > 0 && (
                    <div className={styles.compensatory_tag}>
                      <span className={styles.tag_text}>{t('leaveTypes.compensatoryLeave')} {record.c_hours}h</span>
                    </div>
                  )}
                  {record.t_hours > 0 && (
                    <div className={styles.personal_leave_tag}>
                      <span className={styles.tag_text}>{t('leaveTypes.personalLeave')} {record.t_hours}h</span>
                    </div>
                  )}
                  {record.s_hours > 0 && (
                    <div className={styles.sick_leave_tag}>
                      <span className={styles.tag_text}>{t('leaveTypes.sickLeave')} {record.s_hours}h</span>
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  // 处理日期选择
  const handleDateSelect = (date: Dayjs) => {
    const records = getRecordsForDate(date);
    setSelectedDate(date);

    // 不再显示弹窗，直接调用回调
    onDateSelect?.(date, records);
  };

  // 生成日期范围内的所有日期
  const generateDateRange = () => {
    const dates = [];
    let current = startDate.clone();

    while (current.isSameOrBefore(endDate, 'day')) {
      dates.push(current.clone());
      current = current.add(1, 'day');
    }

    return dates;
  };

  // 渲染日期网格
  const renderDateGrid = () => {
    const dates = generateDateRange();
    if (dates.length === 0) return null;

    const weeks = [];
    let currentWeek = [];

    // 从第一个日期开始，按周分组
    const firstDate = dates[0];
    const lastDate = dates[dates.length - 1];

    // 获取第一个日期所在周的周一
    let startOfWeek = firstDate.clone();
    while (startOfWeek.day() !== 1) {
      startOfWeek = startOfWeek.subtract(1, 'day');
    }

    // 获取最后一个日期所在周的周日
    let endOfWeek = lastDate.clone();
    while (endOfWeek.day() !== 0) {
      endOfWeek = endOfWeek.add(1, 'day');
    }

    // 生成完整的日历网格（包括前后的空白日期）
    let current = startOfWeek;

    while (current.isSameOrBefore(endOfWeek, 'day')) {
      currentWeek.push(current.clone());

      // 如果是周日，结束当前周
      if (current.day() === 0) {
        weeks.push([...currentWeek]);
        currentWeek = [];
      }

      current = current.add(1, 'day');
    }

    // 如果最后一周没有完整，添加它
    if (currentWeek.length > 0) {
      weeks.push([...currentWeek]);
    }

    return (
      <div className={styles.date_grid}>
        {/* 表头 */}
        <div ref={headerRef} className={styles.week_header}>
          {(currentLanguage === 'ja' ?
            ['月', '火', '水', '木', '金', '土', '日'] :
            ['一', '二', '三', '四', '五', '六', '日']
          ).map((day, index) => (
            <div key={`${currentLanguage}-${index}`} className={styles.day_header}>
              {day}
            </div>
          ))}
        </div>

        {/* 可滚动的日期内容区域 */}
        <div ref={contentContainerRef} className={styles.date_content_container}>
          {/* 日期网格 */}
          {weeks.map((week, weekIndex) => (
          <div key={weekIndex} className={styles.week_row}>
            {/* 渲染日期 */}
            {week.map((date) => {
              const records = getRecordsForDate(date);
              const status = getDateStatus(date, records);
              const hasData = records.length > 0;
              const isInRange = date.isBetween(startDate, endDate, 'day', '[]');
              const isToday = date.isSame(dayjs(), 'day');

              return (
                <div
                  key={date.format('YYYY-MM-DD')}
                  className={`${styles.date_cell} ${
                    !isInRange ? styles.out_of_range :
                    status === 'exception' ? styles.exception :
                    status === 'confirm' ? styles.confirm :
                    status === 'weekend-holiday' ? styles.weekend_holiday :
                    status === 'normal' ? styles.normal :
                    status === 'workday-no-record' ? styles.workday_no_record : ''
                  } ${isToday ? styles.today : ''}
                  }`}
                  onClick={() => isInRange && handleDateSelect(date)}
                >
                  <div className={`${styles.date_number} ${!isInRange ? styles.disabled : ''} ${isToday ? styles.today_number : ''}`}>
                    {date.format('D')}
                    {isToday && <span className={styles.today_indicator}>今</span>}
                  </div>
                  {hasData && isInRange && (
                    <div className={styles.date_content}>
                      {dateCellRender(date)}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ))}
        </div>
      </div>
    );
  };

  return (
    <div
      key={`calendar-${currentLanguage}`} // 添加语言作为key，强制重新渲染
      className={`${styles.attendance_calendar} ${className}`}
      style={style}
    >
      {/* 日期范围标题 */}
      <div className={styles.calendar_header}>
        <div className={styles.date_range_title}>
          {currentLanguage === 'ja' ?
            `${startDate.format('YYYY年MM月DD日')} - ${endDate.format('YYYY年MM月DD日')}` :
            `${startDate.format('YYYY年MM月DD日')} - ${endDate.format('YYYY年MM月DD日')}`
          }
        </div>
      </div>

      {/* 自定义日期网格 */}
      {renderDateGrid()}
    </div>
  );
};

export default AttendanceCalendar;
