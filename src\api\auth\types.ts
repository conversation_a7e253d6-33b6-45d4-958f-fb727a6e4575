/**
 * 认证模块类型定义
 */

/**
 * 用户信息接口
 */
export interface UserInfo {
  user_id: number;
  work_no: string;
  name: string;
  department: string;
  position: string;
  email: string;
  phone?: string;
  avatar?: string;
  role?: string;
  permissions?: string[];
}

/**
 * 登录请求参数
 */
export interface LoginRequest {
  user_account: string;
  user_password: string;
  remember_me?: boolean;
}

/**
 * 登录响应数据
 */
export interface LoginResponse {
  status: 'OK' | 'ERROR';
  token?: string;
  message?: string;
  user_info?: UserInfo;
  expires_in?: number;
  refresh_token?: string;
}

/**
 * Token验证响应
 */
export interface TokenValidationResponse {
  status: 'OK' | 'ERROR';
  valid: boolean;
  message?: string;
  user_info?: UserInfo;
  expires_in?: number;
}

/**
 * 通用API响应接口
 */
export interface ApiResponse {
  status: 'OK' | 'ERROR';
  message?: string;
  code?: string | number;
  data?: any;
}

/**
 * 密码重置请求
 */
export interface PasswordResetRequest {
  email?: string;
  work_no?: string;
  verification_code?: string;
  new_password?: string;
}

/**
 * 密码重置响应
 */
export interface PasswordResetResponse {
  status: 'OK' | 'ERROR';
  message?: string;
  verification_required?: boolean;
}

/**
 * 密码修改请求
 */
export interface PasswordChangeRequest {
  old_password: string;
  new_password: string;
  confirm_password: string;
}

/**
 * 密码修改响应
 */
export interface PasswordChangeResponse {
  status: 'OK' | 'ERROR';
  message?: string;
}

/**
 * 登出请求
 */
export interface LogoutRequest {
  token: string;
  all_devices?: boolean;
}

/**
 * 登出响应
 */
export interface LogoutResponse {
  status: 'OK' | 'ERROR';
  message?: string;
}

/**
 * 刷新Token请求
 */
export interface RefreshTokenRequest {
  refresh_token: string;
}

/**
 * 刷新Token响应
 */
export interface RefreshTokenResponse {
  status: 'OK' | 'ERROR';
  token?: string;
  refresh_token?: string;
  expires_in?: number;
  message?: string;
}

/**
 * 认证状态
 */
export interface AuthState {
  isAuthenticated: boolean;
  user: UserInfo | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

/**
 * 认证上下文
 */
export interface AuthContext {
  state: AuthState;
  login: (params: LoginRequest) => Promise<LoginResponse>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  checkAuth: () => Promise<boolean>;
}
