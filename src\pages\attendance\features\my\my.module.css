/* 现代化表格样式 */
.record_box {
    background: #fff;
    width: 95%;
    height: 100%;
    min-height: 600px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-radius: 8px;
    margin: 20px auto;
    padding: 24px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .record_box {
        width: 98%;
        padding: 16px;
    }
}

@media (max-width: 768px) {
    .record_box {
        width: 100%;
        margin: 10px auto;
        padding: 12px;
        border-radius: 4px;
    }
}

.record_all {
    width: 98%;
    overflow-x: hidden;
    overflow-y: auto;
    flex: 1;
    margin-left: 2%;
}

.list_header {
    width: 96%;
    overflow: hidden;
    margin-left: 2%;
    height: 50px;
    color: white;
    font-size: 14px;
    font-weight: bold;
}
.list_header_row {
    width: 100%;
    border-radius: 5px;
    mask-type: flex;
    justify-content: center;
    align-items: middle;
    margin-bottom: 5px;
    height: 32px;
    margin-top: 15px;
    background-color:#4f81ee;
}
.list_header_row_day {
    float: left;
    width: 12%;
    text-align: center;
    border-right: 1px solid white;
}
.list_item_day {
    float: left;
    width: 12%;
    text-align: center;
}
.list_header_row_time {
    float: left;
    width: 16%;
    text-align: center;
    border-right: 1px solid white;
}
.list_item_time {
    float: left;
    width: 16%;
    text-align: center;
}
.list_header_row_leave_overtime {
    float: left;
    width: 13%;
    text-align: center;
    border-right: 1px solid white;
}
.list_item_leave_overtime {
    float: left;
    width: 13%;
    text-align: center;
    padding-left: 15px;
}
.list_header_row_comment {
    float: left;
    width: 30%;
    text-align: center;
}
.list_item_comment {
    float: left;
    width: 30%;
    padding-left: 15px;
    display: flex;
    align-items: flex-start;
}

.record_li {
    width: 99%;
    border-radius: 5px;
    border: 1px solid rgb(171, 170, 170);
    margin-bottom: 3px;
    height: 24px;
    font-size: 12px;
    display: flex;
    align-items: center;
}

.record_li div {
    float: left;
}

.li_exception {
    color: red;
}

.li_green {
    margin-left: 20px;    
}
.comment_ok {
    margin-left: 20px;    
    color: green;
}

.comment_edit_apply {
    margin-left: 20px;    
}
.comment_edit_apply a {
    color: blue;   
    text-decoration: underline;
}

.li_confirm {
    color: orange;
}

.li_gray {
    background-color: #8080802b;
}

.btn {
    margin-left: 25px;
    display: inline-block;
    width: 30%;
}

.btn:first-child{
    margin-left: 5px;
}

.btn div {
    line-height: 20px;
}

.pagination_div {
    width: 100%;
    bottom: 15px;
}

.pagination {
    text-align: center;
    width: 100%;
    margin-top: 5px;
}

/* 表格容器样式 - 参考部门统计页面的固定表头方案 */
.table_container {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%; /* 使用100%高度适应父容器 */
    position: relative;
    flex: 1;
}

/* 表格主体容器 - 固定表头和滚动内容分离 */
.table_main_container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #ffffff;
    position: relative;
    min-height: 0; /* 重要：允许flex子元素收缩 */
}

/* 固定表头容器 - 为滚动条预留空间 */
.fixed_header_container {
    height: 60px;
    background: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    z-index: 15;
    overflow: hidden;
    border-radius: 12px 12px 0 0;
    position: absolute;
    top: 0;
    left: 0;
    right: 17px; /* 为滚动条预留17px空间 */
}

/* 表格内容滚动区域 */
.table_body_container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    background: #ffffff;
    position: relative;
    min-height: 0; /* 重要：允许flex子元素收缩 */
}

/* 滚动条样式优化 */
.table_body_container::-webkit-scrollbar {
    width: 17px;
}

.table_body_container::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 0;
    border-left: 1px solid #e2e8f0;
}

.table_body_container::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 8px;
    border: 3px solid #f8f9fa;
}

.table_body_container::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* 表格内容包装器 - 与表头对齐 */
.table_content_wrapper {
    background: #ffffff;
    width: 100%;
    padding-top: 60px; /* 为固定表头预留空间 */
}

/* 分页区域 - 固定底部 */
.pagination_container {
    height: 60px;
    padding: 16px 20px;
    border-top: 1px solid #e2e8f0;
    background: #ffffff;
    border-radius: 0 0 12px 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
    flex-shrink: 0; /* 防止分页区域被压缩 */
}

/* 自定义表头样式 - 精确对齐 */
.custom_table_header {
    display: table;
    width: 100%;
    table-layout: fixed;
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    color: #ffffff;
    font-weight: 600;
    font-size: 13px;
    text-align: center;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    border-bottom: 2px solid #1890ff;
    min-width: 1200px;
}

.custom_table_header_row {
    display: table-row;
}

.custom_table_header_cell {
    display: table-cell;
    padding: 16px 12px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    vertical-align: middle;
    text-align: center;
}

.custom_table_header_cell:last-child {
    border-right: none;
}

/* 强制隐藏Ant Design Table的默认表头 */
.table_content_wrapper :global(.ant-table-thead) {
    display: none !important;
}

/* 隐藏Ant Design的测量行 */
.table_content_wrapper :global(.ant-table-measure-row) {
    display: none !important;
    height: 0 !important;
    visibility: hidden !important;
}

.table_content_wrapper :global(.ant-table-measure-row > td) {
    display: none !important;
    height: 0 !important;
    padding: 0 !important;
    border: none !important;
}

/* 确保表格内容样式正确 */
.table_content_wrapper :global(.ant-table) {
    border: none !important;
    background: #ffffff !important;
    width: 100% !important;
    margin: 0 !important;
    table-layout: fixed !important;
}

.table_content_wrapper :global(.ant-table-container) {
    border: none !important;
    background: #ffffff !important;
    width: 100% !important;
    overflow: visible !important;
}

.table_content_wrapper :global(.ant-table-content) {
    background: #ffffff !important;
    width: 100% !important;
    overflow: visible !important;
}

.table_content_wrapper :global(.ant-table-body) {
    overflow: visible !important;
}

/* 表格样式重写 - 参考部门统计页面样式 */
.table_container :global(.ant-table) {
    border: none !important;
    border-radius: 12px !important;
    background: #ffffff !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08) !important;
    overflow: hidden !important;
}

.table_container :global(.ant-table-container) {
    border: 1px solid #e2e8f0 !important;
    border-radius: 12px !important;
    background: #ffffff !important;
}

/* 表格头部样式 - 参考部门统计页面的蓝白渐变 */
.table_container :global(.ant-table-thead > tr > th) {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    text-align: center !important;
    border-bottom: 2px solid #1890ff !important;
    border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
    padding: 16px 12px !important;
    height: 60px !important;
    line-height: 1.2 !important;
    vertical-align: middle !important;
    letter-spacing: 0.5px !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.table_container :global(.ant-table-thead > tr > th:last-child) {
    border-right: none !important;
}

/* 表格行样式 - 参考部门统计页面的48px行高 */
.table_container :global(.ant-table-tbody > tr > td) {
    border-bottom: 1px solid #f0f0f0 !important;
    border-right: 1px solid #f5f5f5 !important;
    padding: 14px 12px !important;
    background-color: #ffffff !important;
    font-size: 13px !important;
    line-height: 1.2 !important;
    height: 48px !important;
    max-height: 48px !important;
    min-height: 48px !important;
    vertical-align: middle !important;
    text-align: center !important;
    color: #2d3748 !important;
    transition: all 0.2s ease !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    box-sizing: border-box !important;
}

.table_container :global(.ant-table-tbody > tr > td:last-child) {
    border-right: none !important;
}

/* 确保表格行高度 */
.table_container :global(.ant-table-tbody > tr) {
    height: 48px !important;
    max-height: 48px !important;
    min-height: 48px !important;
}

/* 日系风格表格行样式 - 参考部门统计页面 */
.table_container :global(.ant-table-tbody > tr.table_row_even > td) {
    background: linear-gradient(180deg, #fafbfc 0%, #f8f9fa 100%) !important;
    transition: all 0.3s ease !important;
}

.table_container :global(.ant-table-tbody > tr.table_row_odd > td) {
    background: #ffffff !important;
    transition: all 0.3s ease !important;
}

/* 周末/假日行样式 - 保持灰色背景但调整为日系风格 */
.table_container :global(.ant-table-tbody > tr.weekend-holiday > td) {
    background: linear-gradient(180deg, #f5f5f5 0%, #eeeeee 100%) !important;
    border-bottom: 1px solid #e2e8f0 !important;
    transition: all 0.3s ease !important;
}

/* 异常行样式 - 调整为更柔和的红色 */
.table_container :global(.ant-table-tbody > tr.exception > td) {
    background-color: #fff2f0 !important;
    border-bottom: 1px solid #ffccc7 !important;
    color: #ff4d4f !important;
}

/* 确认行样式 - 调整为更柔和的橙色 */
.table_container :global(.ant-table-tbody > tr.confirm > td) {
    background-color: #fff7e6 !important;
    border-bottom: 1px solid #ffd591 !important;
    color: #fa8c16 !important;
}

/* 日系悬停效果 - 参考部门统计页面的蓝色渐变 */
.table_container :global(.ant-table-tbody > tr:hover > td) {
    background: linear-gradient(135deg, #e8f2ff 0%, #f0f4ff 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1) !important;
    border-color: #d6e3ff !important;
    transition: all 0.3s ease !important;
}

/* 表格边框样式 - 参考部门统计页面 */
.table_container :global(.ant-table-tbody) {
    border: none !important;
    background: #ffffff !important;
}

/* 最后一行特殊处理 */
.table_container :global(.ant-table-tbody > tr:last-child > td) {
    border-bottom: 2px solid #e2e8f0 !important;
}

/* 行分割线增强 - 每5行加强分割线 */
.table_container :global(.ant-table-tbody > tr:nth-child(5n)) {
    border-bottom: 2px solid #e2e8f0 !important;
}

/* 周末/祝日行的特殊处理 - 保持灰色但使用日系风格 */
.table_container :global(.ant-table-tbody > tr.weekend-holiday:nth-child(even) > td) {
    background: linear-gradient(180deg, #f5f5f5 0%, #eeeeee 100%) !important;
}

.table_container :global(.ant-table-tbody > tr.weekend-holiday:nth-child(odd) > td) {
    background: linear-gradient(180deg, #f8f8f8 0%, #f0f0f0 100%) !important;
}

/* 清爽的行间距 */
.table_container :global(.ant-table-tbody > tr) {
    border-bottom: 1px solid #e2e8f0 !important;
}

.table_container :global(.ant-table-tbody > tr:last-child) {
    border-bottom: 2px solid #e2e8f0 !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .btn {
        width: 100% !important;
        margin-left: 0 !important;
    }
}