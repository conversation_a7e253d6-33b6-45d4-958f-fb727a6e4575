/* 现代化响应式表格样式 - 使用CSS变量 */
.record_box {
    background: var(--bg-primary);
    width: 100%;
    height: 100%;
    min-height: 600px;
    box-shadow: var(--shadow-md);
    border-radius: var(--border-radius-lg);
    margin: var(--spacing-lg) auto;
    padding: var(--spacing-xl);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid var(--border-color);
    max-width: none; /* 移除最大宽度限制，允许使用全部可用宽度 */
}

/* 响应式设计 - 优化移动端体验 */
@media (max-width: 1200px) {
    .record_box {
        padding: var(--spacing-lg);
        margin: var(--spacing-md) auto;
    }
}

@media (max-width: 768px) {
    .record_box {
        margin: var(--spacing-sm) auto;
        padding: var(--spacing-md);
        border-radius: var(--border-radius-md);
        min-height: 500px;
    }

    /* 移动端隐藏视图选择区域 */
    .view_selector_area {
        display: none !important;
    }

    /* 移动端隐藏表头 */
    .table_header_area {
        display: none !important;
    }

    /* 移动端隐藏分页 */
    .pc_only_pagination {
        display: none !important;
    }
}

@media (max-width: 480px) {
    .record_box {
        margin: var(--spacing-xs) auto;
        padding: var(--spacing-sm);
        border-radius: var(--border-radius-sm);
        min-height: 400px;
    }
}

/* 表格容器 - 确保列表项完全显示 */
.record_all {
    width: 100%;
    overflow-x: auto; /* 允许横向滚动以确保列表项完全显示 */
    overflow-y: auto;
    flex: 1;
    margin: 0;
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

/* 移动端表格容器优化 */
@media (max-width: 768px) {
    .record_all {
        /* 移动端确保横向滚动可见 */
        overflow-x: scroll;
        scrollbar-width: thin;
        scrollbar-color: var(--color-primary-light) var(--bg-secondary);
    }

    .record_all::-webkit-scrollbar {
        height: 8px;
    }

    .record_all::-webkit-scrollbar-track {
        background: var(--bg-secondary);
        border-radius: 4px;
    }

    .record_all::-webkit-scrollbar-thumb {
        background: var(--color-primary-light);
        border-radius: 4px;
    }

    .record_all::-webkit-scrollbar-thumb:hover {
        background: var(--color-primary);
    }
}

/* 表格头部 - 使用现代CSS Grid布局 */
.list_header {
    width: 100%;
    overflow: hidden;
    margin: 0;
    height: 60px;
    color: var(--text-white);
    font-size: var(--font-size-md);
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    background: var(--bg-gradient-primary);
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
}

.list_header_row {
    width: 100%;
    height: 60px;
    display: grid;
    grid-template-columns: 1fr 1.5fr 1fr 1fr 2fr 1fr; /* 响应式网格列 */
    align-items: center;
    background: var(--bg-gradient-primary);
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
    box-shadow: var(--shadow-sm);
    min-width: 800px; /* 确保在小屏幕上可以横向滚动 */
}

/* 表格头部单元格 */
.list_header_row_day,
.list_header_row_time,
.list_header_row_leave_overtime,
.list_header_row_comment,
.list_header_row_location,
.list_header_row_status {
    text-align: center;
    padding: var(--spacing-sm);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.list_header_row_comment:last-child {
    border-right: none;
}

/* 表格数据行 - 使用相同的网格布局 */
.list_item_row {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1.5fr 1fr 1fr 2fr 1fr; /* 与头部相同的列布局 */
    align-items: center;
    min-height: 48px;
    border-bottom: 1px solid var(--border-color-light);
    background: var(--bg-primary);
    transition: var(--transition-normal);
    min-width: 800px; /* 确保在小屏幕上可以横向滚动 */
}

.list_item_row:hover {
    background: linear-gradient(135deg, #e8f2ff 0%, #f0f4ff 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* 表格数据单元格 */
.list_item_day,
.list_item_time,
.list_item_leave_overtime,
.list_item_comment,
.list_item_location,
.list_item_status {
    text-align: center;
    padding: var(--spacing-sm);
    border-right: 1px solid var(--border-color-light);
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    word-break: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
}

.list_item_comment:last-child {
    border-right: none;
}
/* 移动端响应式表格 - 卡片视图 */
@media (max-width: 768px) {
    .list_header {
        display: none; /* 移动端隐藏表格头部 */
    }

    .list_item_row {
        display: block; /* 移动端使用块级布局 */
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-md);
        margin-bottom: var(--spacing-md);
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-sm);
        min-width: auto;
    }

    .list_item_row:hover {
        transform: none;
        box-shadow: var(--shadow-md);
    }

    /* 移动端卡片式布局 */
    .list_item_day,
    .list_item_time,
    .list_item_leave_overtime,
    .list_item_comment,
    .list_item_location,
    .list_item_status {
        display: block;
        text-align: left;
        padding: var(--spacing-xs) 0;
        border-right: none;
        border-bottom: 1px solid var(--border-color-light);
        position: relative;
        padding-left: 120px; /* 为标签预留空间 */
    }

    .list_item_day:last-child,
    .list_item_time:last-child,
    .list_item_leave_overtime:last-child,
    .list_item_comment:last-child,
    .list_item_location:last-child,
    .list_item_status:last-child {
        border-bottom: none;
    }

    /* 移动端标签 */
    .list_item_day::before,
    .list_item_time::before,
    .list_item_leave_overtime::before,
    .list_item_comment::before,
    .list_item_location::before,
    .list_item_status::before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        width: 110px;
        font-weight: 600;
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }
}

/* 状态样式 */
.li_exception {
    color: var(--color-error);
    font-weight: 600;
}

.li_green {
    color: var(--color-success);
    font-weight: 500;
}

.comment_ok {
    color: var(--color-success);
    font-weight: 500;
}

.comment_edit_apply a {
    color: var(--color-primary);
    text-decoration: underline;
    transition: var(--transition-fast);
}

.comment_edit_apply a:hover {
    color: var(--color-primary-dark);
}

.li_confirm {
    color: var(--color-warning);
    font-weight: 500;
}

.li_gray {
    background-color: var(--bg-secondary);
    opacity: 0.7;
}

/* 分页容器 */
.pagination_div {
    width: 100%;
    padding: var(--spacing-lg) 0;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    position: sticky;
    bottom: 0;
    z-index: var(--z-sticky);
}

.pagination {
    text-align: center;
    width: 100%;
}

/* 现代化表格容器样式 - 使用CSS变量和响应式设计 */
.table_container {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    flex: 1;
    max-width: none; /* 移除最大宽度限制 */
}

/* 表格主体容器 - 优化滚动和布局 */
.table_main_container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: var(--bg-primary);
    position: relative;
    min-height: 0; /* 重要：允许flex子元素收缩 */
}

/* 固定表头容器 - 响应式设计 */
.fixed_header_container {
    height: 60px;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    z-index: var(--z-sticky);
    overflow: hidden;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
}

/* 表格内容滚动区域 - 确保列表项完全显示 */
.table_body_container {
    flex: 1;
    overflow-y: auto;
    overflow-x: auto; /* 允许横向滚动确保列表项完全显示 */
    background: var(--bg-primary);
    position: relative;
    min-height: 0;
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

/* 滚动条样式优化 - 使用CSS变量 */
.table_body_container::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

.table_body_container::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-sm);
    border-left: 1px solid var(--border-color);
}

.table_body_container::-webkit-scrollbar-thumb {
    background: var(--color-primary-light);
    border-radius: var(--border-radius-sm);
    border: 2px solid var(--bg-secondary);
    transition: var(--transition-normal);
}

.table_body_container::-webkit-scrollbar-thumb:hover {
    background: var(--color-primary);
}

.table_body_container::-webkit-scrollbar-corner {
    background: var(--bg-secondary);
}

/* 移动端滚动条优化 */
@media (max-width: 768px) {
    .table_body_container::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    .table_body_container::-webkit-scrollbar-thumb {
        border: 1px solid var(--bg-secondary);
    }
}

/* 表格内容包装器 - 响应式设计 */
.table_content_wrapper {
    background: var(--bg-primary);
    width: 100%;
    min-width: 800px; /* 确保在小屏幕上可以横向滚动 */
}

/* 分页区域 - 固定底部，响应式设计 */
.pagination_container {
    height: 60px;
    padding: var(--spacing-lg) var(--spacing-xl);
    border-top: 1px solid var(--border-color);
    background: var(--bg-primary);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: var(--z-sticky);
    flex-shrink: 0; /* 防止分页区域被压缩 */
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

/* 移动端分页容器优化 */
@media (max-width: 768px) {
    .pagination_container {
        height: auto;
        padding: var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}

/* 自定义表头样式 - 使用CSS变量和响应式设计 */
.custom_table_header {
    display: table;
    width: 100%;
    table-layout: fixed;
    background: var(--bg-gradient-primary);
    color: var(--text-white);
    font-weight: 600;
    font-size: var(--font-size-sm);
    text-align: center;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    border-bottom: 2px solid var(--color-primary);
    min-width: 800px; /* 确保在小屏幕上可以横向滚动 */
}

.custom_table_header_row {
    display: table-row;
}

.custom_table_header_cell {
    display: table-cell;
    padding: var(--spacing-lg) var(--spacing-md);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    vertical-align: middle;
    text-align: center;
    height: 60px;
    line-height: var(--line-height-tight);
}

.custom_table_header_cell:last-child {
    border-right: none;
}

/* 移动端表头优化 */
@media (max-width: 768px) {
    .custom_table_header {
        font-size: var(--font-size-xs);
        min-width: 600px;
    }

    .custom_table_header_cell {
        padding: var(--spacing-md) var(--spacing-sm);
        height: 50px;
    }
}

/* 强制隐藏Ant Design Table的默认表头 */
.table_content_wrapper :global(.ant-table-thead) {
    display: none !important;
}

/* 隐藏Ant Design的测量行 */
.table_content_wrapper :global(.ant-table-measure-row) {
    display: none !important;
    height: 0 !important;
    visibility: hidden !important;
}

.table_content_wrapper :global(.ant-table-measure-row > td) {
    display: none !important;
    height: 0 !important;
    padding: 0 !important;
    border: none !important;
}

/* 确保表格内容样式正确 */
.table_content_wrapper :global(.ant-table) {
    border: none !important;
    background: #ffffff !important;
    width: 100% !important;
    margin: 0 !important;
    table-layout: fixed !important;
}

.table_content_wrapper :global(.ant-table-container) {
    border: none !important;
    background: #ffffff !important;
    width: 100% !important;
    overflow: visible !important;
}

.table_content_wrapper :global(.ant-table-content) {
    background: #ffffff !important;
    width: 100% !important;
    overflow: visible !important;
}

.table_content_wrapper :global(.ant-table-body) {
    overflow: visible !important;
}

/* 表格样式重写 - 使用CSS变量和响应式设计 */
.table_container :global(.ant-table) {
    border: none !important;
    border-radius: var(--border-radius-lg) !important;
    background: var(--bg-primary) !important;
    box-shadow: var(--shadow-lg) !important;
    overflow: hidden !important;
}

.table_container :global(.ant-table-container) {
    border: 1px solid var(--border-color) !important;
    border-radius: var(--border-radius-lg) !important;
    background: var(--bg-primary) !important;
}

/* 表格头部样式 - 使用CSS变量的蓝白渐变 */
.table_container :global(.ant-table-thead > tr > th) {
    background: var(--bg-gradient-primary) !important;
    color: var(--text-white) !important;
    font-weight: 600 !important;
    font-size: var(--font-size-sm) !important;
    text-align: center !important;
    border-bottom: 2px solid var(--color-primary) !important;
    border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
    padding: var(--spacing-lg) var(--spacing-md) !important;
    height: 60px !important;
    line-height: var(--line-height-tight) !important;
    vertical-align: middle !important;
    letter-spacing: 0.5px !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.table_container :global(.ant-table-thead > tr > th:last-child) {
    border-right: none !important;
}

/* 表格行样式 - 使用CSS变量，确保列表项完全显示 */
.table_container :global(.ant-table-tbody > tr > td) {
    border-bottom: 1px solid var(--border-color-light) !important;
    border-right: 1px solid var(--border-color-light) !important;
    padding: var(--spacing-md) var(--spacing-sm) !important;
    background-color: var(--bg-primary) !important;
    font-size: var(--font-size-sm) !important;
    line-height: var(--line-height-tight) !important;
    height: 48px !important;
    max-height: 48px !important;
    min-height: 48px !important;
    vertical-align: middle !important;
    text-align: center !important;
    color: var(--text-primary) !important;
    transition: var(--transition-normal) !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    box-sizing: border-box !important;
}

.table_container :global(.ant-table-tbody > tr > td:last-child) {
    border-right: none !important;
}

/* 确保表格行高度 */
.table_container :global(.ant-table-tbody > tr) {
    height: 48px !important;
    max-height: 48px !important;
    min-height: 48px !important;
}

/* 移动端表格行优化 */
@media (max-width: 768px) {
    .table_container :global(.ant-table-tbody > tr > td) {
        padding: var(--spacing-sm) var(--spacing-xs) !important;
        font-size: var(--font-size-xs) !important;
        height: 44px !important;
        max-height: 44px !important;
        min-height: 44px !important;
    }

    .table_container :global(.ant-table-tbody > tr) {
        height: 44px !important;
        max-height: 44px !important;
        min-height: 44px !important;
    }
}

/* 日系风格表格行样式 - 使用CSS变量 */
.table_container :global(.ant-table-tbody > tr.table_row_even > td) {
    background: linear-gradient(180deg, var(--bg-secondary) 0%, #f8f9fa 100%) !important;
    transition: var(--transition-normal) !important;
}

.table_container :global(.ant-table-tbody > tr.table_row_odd > td) {
    background: var(--bg-primary) !important;
    transition: var(--transition-normal) !important;
}

/* 周末/假日行样式 - 使用CSS变量 */
.table_container :global(.ant-table-tbody > tr.weekend-holiday > td) {
    background: linear-gradient(180deg, #f5f5f5 0%, #eeeeee 100%) !important;
    border-bottom: 1px solid var(--border-color) !important;
    transition: var(--transition-normal) !important;
}

/* 异常行样式 - 使用CSS变量 */
.table_container :global(.ant-table-tbody > tr.exception > td) {
    background-color: #fff2f0 !important;
    border-bottom: 1px solid #ffccc7 !important;
    color: var(--color-error) !important;
}

/* 确认行样式 - 使用CSS变量 */
.table_container :global(.ant-table-tbody > tr.confirm > td) {
    background-color: #fff7e6 !important;
    border-bottom: 1px solid #ffd591 !important;
    color: var(--color-warning) !important;
}

/* 日系悬停效果 - 使用CSS变量的蓝色渐变 */
.table_container :global(.ant-table-tbody > tr:hover > td) {
    background: linear-gradient(135deg, #e8f2ff 0%, #f0f4ff 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: var(--shadow-md) !important;
    border-color: #d6e3ff !important;
    transition: var(--transition-normal) !important;
}

/* 表格边框样式 */
.table_container :global(.ant-table-tbody) {
    border: none !important;
    background: var(--bg-primary) !important;
}

/* 最后一行特殊处理 */
.table_container :global(.ant-table-tbody > tr:last-child > td) {
    border-bottom: 2px solid var(--border-color) !important;
}

/* 行分割线增强 - 每5行加强分割线 */
.table_container :global(.ant-table-tbody > tr:nth-child(5n)) {
    border-bottom: 2px solid var(--border-color) !important;
}

/* 周末/祝日行的特殊处理 */
.table_container :global(.ant-table-tbody > tr.weekend-holiday:nth-child(even) > td) {
    background: linear-gradient(180deg, #f5f5f5 0%, #eeeeee 100%) !important;
}

.table_container :global(.ant-table-tbody > tr.weekend-holiday:nth-child(odd) > td) {
    background: linear-gradient(180deg, #f8f8f8 0%, #f0f0f0 100%) !important;
}

/* 清爽的行间距 */
.table_container :global(.ant-table-tbody > tr) {
    border-bottom: 1px solid var(--border-color) !important;
}

.table_container :global(.ant-table-tbody > tr:last-child) {
    border-bottom: 2px solid var(--border-color) !important;
}

/* 移动端适配优化 */
@media (max-width: 768px) {
    .table_container {
        border-radius: var(--border-radius-md);
        margin: var(--spacing-sm);
    }

    .table_container :global(.ant-table-thead > tr > th) {
        padding: var(--spacing-md) var(--spacing-xs) !important;
        font-size: var(--font-size-xs) !important;
        height: 50px !important;
    }

    .pagination_container {
        padding: var(--spacing-md) !important;
    }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
    .table_container {
        margin: var(--spacing-xs);
        border-radius: var(--border-radius-sm);
    }

    .table_container :global(.ant-table-thead > tr > th) {
        padding: var(--spacing-sm) var(--spacing-xs) !important;
        font-size: 11px !important;
        height: 44px !important;
    }

    .table_container :global(.ant-table-tbody > tr > td) {
        padding: var(--spacing-xs) !important;
        font-size: 11px !important;
        height: 40px !important;
        max-height: 40px !important;
        min-height: 40px !important;
    }

    .table_container :global(.ant-table-tbody > tr) {
        height: 40px !important;
        max-height: 40px !important;
        min-height: 40px !important;
    }
}