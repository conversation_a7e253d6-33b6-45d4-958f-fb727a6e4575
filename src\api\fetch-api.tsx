import axios, { AxiosError, AxiosResponse } from 'axios';
import { createAsyncThunk } from "@reduxjs/toolkit";
import ApiFetchVars from './common/fetch-api-vars';
import { getCookie, key_for_token } from '../utils/cookies';
import { statusActions } from '@/slice/statusSlice';
import { setUserActions } from '@/slice/authSlice';

// 定义 API 响应类型
interface ApiResponse<T = any> {
  status: 'OK' | 'NG';
  message?: string;
  data?: T;
}

// 定义错误类型
interface ApiError {
  response?: {
    data?: {
      detail?: {
        message?: string;
      };
    };
  };
  message?: string;
}

/**
 * 统一的错误处理函数
 */
function handleApiError(error: ApiError): string {
  const errorMessage = error.response?.data?.detail?.message;

  if (errorMessage === '无效token') {
    return '登录凭证失效';
  }

  return errorMessage || '请求失败，请稍后重试';
}

/**
 * 统一的数据获取和状态更新函数
 */
async function fetchDataAndUpdateState(
  apiCall: () => Promise<AxiosResponse<any>>,
  dispatch: Function,
  action: Function
): Promise<void> {
  try {
    const result = await apiCall();
    dispatch(action(result.data));
    dispatch(statusActions.onTure());
  } catch (error: any) {
    const errorMessage = handleApiError(error);
    dispatch(statusActions.onFalse(errorMessage));
    throw error; // 重新抛出错误，让调用者可以处理
  }
}

/**
 * 页面加载时获取数据并保存到 State (GET 请求)
 */
export async function fetchDataOnInitSaveToState(
  url: string,
  params: any,
  dispatch: Function,
  action: Function
): Promise<void> {
  return fetchDataAndUpdateState(
    () => getApi(url, params, dispatch),
    dispatch,
    action
  );
}

/**
 * 页面加载时获取数据并保存到 State (POST 请求)
 * 合并了重复的函数
 */
export async function fetchDataOnInitSaveToStatePost(
  url: string,
  params: any,
  dispatch: Function,
  action: Function
): Promise<void> {
  return fetchDataAndUpdateState(
    () => postApi(url, params, dispatch),
    dispatch,
    action
  );
}

// 为了向后兼容，保留旧的函数名
export const fetchDataByPostOnInitSaveToState = fetchDataOnInitSaveToStatePost;

/**
 * 发送 GET 请求
 */
export async function getApi(
  url: string,
  params: any,
  dispatch: Function
): Promise<AxiosResponse<any>> {
  dispatch(setUserActions.setLoading(true));

  try {
    const axios = createAxios();
    const response = await axios.get(url, { params });

    return response;
  } catch (error: any) {
    const errorMessage = handleApiError(error);

    // 返回统一的错误响应格式
    const errorResponse: AxiosResponse<ApiResponse> = {
      data: {
        status: 'NG',
        message: errorMessage
      },
      status: error.response?.status || 500,
      statusText: error.response?.statusText || 'Internal Server Error',
      headers: error.response?.headers || {},
      config: error.config || {}
    };

    return errorResponse;
  } finally {
    // 使用 finally 确保 loading 状态总是被重置
    dispatch(setUserActions.setLoading(false));
  }
}

/**
 * 发送 POST 请求
 */
export async function postApi(
  url: string,
  data: any,
  dispatch: Function
): Promise<AxiosResponse<any>> {
  dispatch(setUserActions.setLoading(true));

  try {
    const axios = createAxios();
    const response = await axios.post(url, data);

    return response;
  } catch (error: any) {
    const errorMessage = handleApiError(error);

    // 返回统一的错误响应格式
    const errorResponse: AxiosResponse<ApiResponse> = {
      data: {
        status: 'NG',
        message: errorMessage
      },
      status: error.response?.status || 500,
      statusText: error.response?.statusText || 'Internal Server Error',
      headers: error.response?.headers || {},
      config: error.config || {}
    };

    return errorResponse;
  } finally {
    // 使用 finally 确保 loading 状态总是被重置
    dispatch(setUserActions.setLoading(false));
  }
}

/**
 * 创建异步 Thunk 的工厂函数
 */
export function createApiAsyncThunk(
  taskType: string,
  url: string,
  params: any,
  method: 'GET' | 'POST' = 'GET'
) {
  return createAsyncThunk(taskType, async (_, { dispatch, rejectWithValue }) => {
    try {
      let response: AxiosResponse<any>;

      if (method === 'GET') {
        response = await getApi(url, params, dispatch);
      } else {
        response = await postApi(url, params, dispatch);
      }

      // 检查响应状态
      if (response.data.status === 'NG') {
        return rejectWithValue(response.data.message || '请求失败');
      }

      return response.data;
    } catch (error: any) {
      const errorMessage = handleApiError(error);
      return rejectWithValue(errorMessage);
    }
  });
}

/**
 * 简化的 API 调用函数，不需要 dispatch
 */
export async function simpleGetApi(url: string, params?: any): Promise<any> {
  const axios = createAxios();
  try {
    const response = await axios.get(url, { params });
    return response.data;
  } catch (error: any) {
    throw new Error(handleApiError(error));
  }
}

/**
 * 简化的 POST API 调用函数，不需要 dispatch
 */
export async function simplePostApi(url: string, data?: any): Promise<any> {
  const axios = createAxios();
  try {
    const response = await axios.post(url, data);
    return response.data;
  } catch (error: any) {
    throw new Error(handleApiError(error));
  }
}

/**
 * 创建配置好的 Axios 实例
 */
export function createAxios() {
  const token = getCookie(key_for_token);

  const config = {
    timeout: 10000, // 10秒超时
    headers: {
      'Content-Type': 'application/json',
    } as any
  };

  // 如果有 token，添加到请求头
  if (token && token.length > 0) {
    config.headers.token = token;
  }

  const instance = axios.create(config);

  // 添加请求拦截器
  instance.interceptors.request.use(
    (config) => {
      // 可以在这里添加请求日志
      if (process.env.NODE_ENV === 'development') {
        console.log('API Request:', config.method?.toUpperCase(), config.url);
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // 添加响应拦截器
  instance.interceptors.response.use(
    (response) => {
      // 可以在这里添加响应日志
      if (process.env.NODE_ENV === 'development') {
        console.log('API Response:', response.status, response.config.url);
      }
      return response;
    },
    (error) => {
      // 统一处理网络错误
      if (!error.response) {
        error.message = '网络连接失败，请检查网络设置';
      } else if (error.response.status === 401) {
        error.message = '登录凭证失效，请重新登录';
      } else if (error.response.status >= 500) {
        error.message = '服务器错误，请稍后重试';
      }

      return Promise.reject(error);
    }
  );

  return instance;
}