import React, { useEffect, useState } from 'react';
import { Card, Row, Col, Progress, Table, Tag, Statistic, Divider, Alert, Spin } from 'antd';
import { CalendarOutlined, ClockCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useTranslation } from '@/hooks/useTranslation';

import dayjs from 'dayjs';
import styles from './paidLeave.module.css';

// 模拟数据接口
interface PaidLeaveData {
  totalDays: number;
  usedDays: number;
  remainingDays: number;
  requiredForLegal: number;
  currentAvailable: PaidLeaveRecord[];
  usageHistory: PaidLeaveUsage[];
}

interface PaidLeaveRecord {
  id: string;
  grantDate: string;
  availableFromDate: string;
  expiryDate: string;
  grantedDays: number;
  invalidDays: number;
  usedDays: number;
  remainingDays: number;
  status: 'active' | 'expired' | 'expiring';
}

interface PaidLeaveUsage {
  id: string;
  date: string;
  days: number;
  reason: string;
  status: 'approved' | 'pending' | 'rejected';
  approver?: string;
}

const PaidLeavePage: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<PaidLeaveData | null>(null);

  // 模拟数据获取
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockData: PaidLeaveData = {
        totalDays: 20,
        usedDays: 8,
        remainingDays: 12,
        requiredForLegal: 5,
        currentAvailable: [
          {
            id: '1',
            grantDate: '2024-04-01',
            availableFromDate: '2024-10-01',
            expiryDate: '2026-03-31',
            grantedDays: 20,
            invalidDays: 0,
            usedDays: 8,
            remainingDays: 12,
            status: 'active'
          },
          {
            id: '2',
            grantDate: '2023-04-01',
            availableFromDate: '2023-10-01',
            expiryDate: '2025-03-31',
            grantedDays: 22,
            invalidDays: 2,
            usedDays: 15,
            remainingDays: 5,
            status: 'expiring'
          }
        ],
        usageHistory: [
          {
            id: '1',
            date: '2024-12-25',
            days: 1,
            reason: '年末休暇',
            status: 'approved',
            approver: '田中部長'
          },
          {
            id: '2',
            date: '2024-11-15',
            days: 2,
            reason: '私用',
            status: 'approved',
            approver: '佐藤課長'
          },
          {
            id: '3',
            date: '2024-10-10',
            days: 3,
            reason: '家族旅行',
            status: 'approved',
            approver: '田中部長'
          },
          {
            id: '4',
            date: '2024-09-05',
            days: 2,
            reason: '体調不良',
            status: 'approved',
            approver: '佐藤課長'
          }
        ]
      };
      
      setData(mockData);
      setLoading(false);
    };

    fetchData();
  }, []);

  // 表格列定义
  const availableColumns = [
    {
      title: t('付与日', '付与日期'),
      dataIndex: 'grantDate',
      key: 'grantDate',
      width: '13%',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: t('利用可能開始日', '利用可能开始日期'),
      dataIndex: 'availableFromDate',
      key: 'availableFromDate',
      width: '16%',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: t('有効期限', '有效期限'),
      dataIndex: 'expiryDate',
      key: 'expiryDate',
      width: '13%',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: t('付与有休日数', '赋予有休日数'),
      dataIndex: 'grantedDays',
      key: 'grantedDays',
      width: '12%',
      render: (days: number) => `${days}${t('日', '天')}`,
    },
    {
      title: t('無効日数', '无效日数'),
      dataIndex: 'invalidDays',
      key: 'invalidDays',
      width: '10%',
      render: (days: number) => (
        <span style={{ color: days > 0 ? '#ff4d4f' : '#666' }}>
          {days}{t('日', '天')}
        </span>
      ),
    },
    {
      title: t('使用済日数', '已使用天数'),
      dataIndex: 'usedDays',
      key: 'usedDays',
      width: '12%',
      render: (days: number) => `${days}${t('日', '天')}`,
    },
    {
      title: t('残日数', '剩余天数'),
      dataIndex: 'remainingDays',
      key: 'remainingDays',
      width: '12%',
      render: (days: number) => `${days}${t('日', '天')}`,
    },
    {
      title: t('ステータス', '状态'),
      dataIndex: 'status',
      key: 'status',
      width: '12%',
      render: (status: string) => {
        const statusConfig = {
          active: { color: 'green', text: t('有効', '有效') },
          expiring: { color: 'orange', text: t('期限間近', '即将到期') },
          expired: { color: 'red', text: t('期限切れ', '已过期') }
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
  ];

  const historyColumns = [
    {
      title: t('使用日', '使用日期'),
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: t('使用日数', '使用天数'),
      dataIndex: 'days',
      key: 'days',
      render: (days: number) => `${days}${t('日', '天')}`,
    },
    {
      title: t('使用理由', '使用理由'),
      dataIndex: 'reason',
      key: 'reason',
    },
    {
      title: t('承認者', '审批人'),
      dataIndex: 'approver',
      key: 'approver',
    },
    {
      title: t('ステータス', '状态'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          approved: { color: 'green', icon: <CheckCircleOutlined />, text: t('承認済み', '已批准') },
          pending: { color: 'orange', icon: <ClockCircleOutlined />, text: t('承認待ち', '待审批') },
          rejected: { color: 'red', icon: <ExclamationCircleOutlined />, text: t('却下', '已驳回') }
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      },
    },
  ];

  if (loading) {
    return (
      <div className={styles.loading_container}>
        <Spin size="large" />
        <p>{t('読み込み中...', '加载中...')}</p>
      </div>
    );
  }

  if (!data) {
    return (
      <div className={styles.error_container}>
        <Alert
          message={t('エラー', '错误')}
          description={t('データの読み込みに失敗しました', '数据加载失败，请稍后重试')}
          type="error"
          showIcon
        />
      </div>
    );
  }

  const usageRate = (data.usedDays / data.totalDays) * 100;
  const legalRequirementMet = data.usedDays >= data.requiredForLegal;

  return (
    <>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>
            <CalendarOutlined className={styles.title_icon} />
            {t('マイ有休', '我的有休')}
          </h1>
        </div>

        {/* 概要统计 */}
        <Row gutter={[16, 16]} className={styles.summary_section}>
          <Col xs={24} sm={12} md={6}>
            <Card className={styles.stat_card}>
              <Statistic
                title={t('総有休日数', '总有休天数')}
                value={data.totalDays}
                suffix={t('日', '天')}
                valueStyle={{ color: '#1890ff' }}
              />
              <div className={styles.card_tip}>
                {t('最大20日、超出の場合は消滅', '最大20天，超出的场合会消灭')}
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card className={styles.stat_card}>
              <Statistic
                title={t('使用済日数', '已使用天数')}
                value={data.usedDays}
                suffix={t('日', '天')}
                valueStyle={{ color: '#52c41a' }}
              />
              <div className={styles.card_placeholder}></div>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card className={styles.stat_card}>
              <Statistic
                title={t('残日数', '剩余天数')}
                value={data.remainingDays}
                suffix={t('日', '天')}
                valueStyle={{ color: '#fa8c16' }}
              />
              <div className={styles.card_placeholder}></div>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card className={styles.stat_card}>
              <Statistic
                title={t('法定5日まで', '距法定5天')}
                value={data.requiredForLegal - data.usedDays}
                suffix={t('日不足', '天不足')}
                valueStyle={{ color: legalRequirementMet ? '#52c41a' : '#ff4d4f' }}
              />
              <div className={styles.card_placeholder}></div>
            </Card>
          </Col>
        </Row>

        {/* 使用率进度条 */}
        <Card className={styles.progress_card}>
          <h3>{t('有休使用進捗', '有休使用进度')}</h3>
          <div className={styles.progress_wrapper}>
            <Progress
              percent={Math.round(usageRate)}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
              format={(percent) => `${percent}%`}
            />
            <div className={styles.progress_detail}>
              {data.usedDays}/{data.totalDays}{t('日', '天')} ({Math.round(usageRate)}%)
            </div>
          </div>

          {/* 法定要求提醒 */}
          <div className={styles.legal_reminder}>
            {legalRequirementMet ? (
              <Alert
                message={t('✅ 法定年次有給休暇5日の要件を満たしています', '✅ 已满足法定年休假5天要求')}
                type="success"
                showIcon
                icon={<CheckCircleOutlined />}
              />
            ) : (
              <Alert
                message={`⚠️ ${t('法定年次有給休暇5日の要件まであと', '距离法定年休假5天要求还需要')} ${data.requiredForLegal - data.usedDays} ${t('日必要です', '天')}`}
                type="warning"
                showIcon
                icon={<ExclamationCircleOutlined />}
              />
            )}
          </div>
        </Card>

        <Divider />

        {/* 当前可用有休一览 */}
        <Card className={styles.table_card}>
          <h3>{t('現在利用可能な有休一覧', '当前可用有休一览')}</h3>
          <Table
            columns={availableColumns}
            dataSource={data.currentAvailable}
            rowKey="id"
            pagination={false}
            size="middle"
            className={styles.available_table}
          />
        </Card>

        <Divider />

        {/* 有休使用履历 */}
        <Card className={styles.table_card}>
          <h3>{t('有休使用履歴', '有休使用履历')}</h3>
          <Table
            columns={historyColumns}
            dataSource={data.usageHistory}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: false,
              showQuickJumper: true,
            }}
            size="middle"
            className={styles.history_table}
          />
        </Card>
      </div>
    </>
  );
};

export default PaidLeavePage;
