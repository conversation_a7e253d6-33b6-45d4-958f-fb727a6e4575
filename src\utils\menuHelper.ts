import { tabActions } from '../slice/tabSlice';
import { getTabMenuByType } from './menulist';
import { getCurrentLanguage } from './languageManager';

/**
 * 安全地切换菜单标签，支持多语言
 * 这个函数应该被业务页面使用，而不是直接调用 tabActions.onTabChange
 */
export const switchMenuTab = (dispatch: any, tabType: string, subIndex?: string) => {
  try {
    console.log('menuHelper: Switching to tab:', tabType, 'subIndex:', subIndex);
    
    // 获取当前语言
    const currentLang = getCurrentLanguage();
    console.log('menuHelper: Current language:', currentLang);
    
    // 先更新 tab 类型
    dispatch(tabActions.onTabChange(tabType));
    
    // 如果提供了子索引，也更新它
    if (subIndex) {
      dispatch(tabActions.onSubChange(subIndex));
    }
    
    // 延迟更新菜单，确保语言翻译函数可用
    setTimeout(() => {
      try {
        // 动态导入翻译函数
        import('../hooks/useTranslation').then(({ useTranslation }) => {
          // 注意：这里不能直接使用 useTranslation，因为它是 hook
          // 我们需要另一种方式来获取翻译函数
          console.log('menuHelper: Translation module loaded');
        });
        
        // 暂时使用默认的菜单配置，让 left-cornor 组件来处理多语言更新
        console.log('menuHelper: Tab switch completed, left-cornor will handle menu translation');
      } catch (error) {
        console.error('menuHelper: Error updating menu:', error);
      }
    }, 50);
    
  } catch (error) {
    console.error('menuHelper: Error in switchMenuTab:', error);
    // 如果出错，至少更新基本的 tab 状态
    dispatch(tabActions.onTabChange(tabType));
    if (subIndex) {
      dispatch(tabActions.onSubChange(subIndex));
    }
  }
};

/**
 * 获取当前应该使用的菜单配置
 * 这个函数可以被业务页面用来获取正确的菜单索引
 */
export const getCurrentMenuConfig = () => {
  try {
    const currentLang = getCurrentLanguage();
    console.log('menuHelper: Getting menu config for language:', currentLang);
    
    // 返回一个 Promise，因为我们可能需要异步加载翻译
    return new Promise((resolve) => {
      // 暂时返回默认配置，实际的多语言菜单由 left-cornor 组件管理
      import('./menulist').then(({ menuConfig }) => {
        resolve(menuConfig);
      });
    });
  } catch (error) {
    console.error('menuHelper: Error getting menu config:', error);
    // 返回默认配置
    import('./menulist').then(({ menuConfig }) => {
      return menuConfig;
    });
  }
};

/**
 * 为业务页面提供的便捷函数
 * 用于替换直接使用 menuConfig[x].list[y].index 的地方
 */
export const getMenuIndex = (menuIndex: number, listIndex: number): string => {
  try {
    // 这里我们返回一个计算出的索引，避免直接依赖 menuConfig
    // 因为 menuConfig 可能会因为语言变化而不同
    return `${listIndex + 1}`; // 简单的索引计算
  } catch (error) {
    console.error('menuHelper: Error getting menu index:', error);
    return "1"; // 默认返回第一个索引
  }
};
