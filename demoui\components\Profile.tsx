import React, { useState } from 'react';
import { User, Globe, Lock, Eye, EyeOff, Check, Shield, RefreshCw, Mail } from 'lucide-react';

const Profile: React.FC = () => {
  const [selectedLanguage, setSelectedLanguage] = useState('ja');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordForm, setPasswordForm] = useState({
    verificationCode: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [passwordChangeSuccess, setPasswordChangeSuccess] = useState(false);
  const [isLoadingVerification, setIsLoadingVerification] = useState(false);
  const [isResendingCode, setIsResendingCode] = useState(false);
  const [isSendingCode, setIsSendingCode] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [codeSent, setCodeSent] = useState(false);

  const userInfo = {
    name: '田中 太郎',
    nameKana: 'たなか たろう',
    department: '開発部門',
    position: 'シニアエンジニア',
    employeeId: 'EMP-2024-001',
    email: '<EMAIL>',
  };

  const languages = [
    { code: 'ja', name: '日本語', flag: '🇯🇵' },
    { code: 'zh', name: '中文', flag: '🇨🇳' },
    { code: 'en', name: 'English', flag: '🇺🇸' },
  ];

  React.useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const handleLanguageChange = (languageCode: string) => {
    setSelectedLanguage(languageCode);
    console.log('Language changed to:', languageCode);
  };

  const handlePasswordChange = (field: string, value: string) => {
    setPasswordForm(prev => ({
      ...prev,
      [field]: value,
    }));
    setPasswordChangeSuccess(false);
  };

  const handleSendVerificationCode = async () => {
    setIsSendingCode(true);
    
    try {
      // Simulate sending verification code
      await new Promise(resolve => setTimeout(resolve, 1500));
      setCodeSent(true);
      setCountdown(60);
    } catch (error) {
      alert('認証コードの送信に失敗しました');
    } finally {
      setIsSendingCode(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!codeSent || passwordForm.verificationCode.length !== 6) {
      alert('6桁の認証コードを入力してください');
      return;
    }

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      alert('新しいパスワードが一致しません');
      return;
    }

    if (passwordForm.newPassword.length < 8) {
      alert('パスワードは8文字以上で入力してください');
      return;
    }

    setIsLoadingVerification(true);
    
    try {
      // Simulate password change
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock validation
      if (passwordForm.verificationCode === '123456') {
        setPasswordChangeSuccess(true);
        setPasswordForm({
          verificationCode: '',
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        });
        setCodeSent(false);
        setCountdown(0);
        
        setTimeout(() => {
          setPasswordChangeSuccess(false);
        }, 3000);
      } else {
        alert('認証コードが正しくありません');
      }
    } catch (error) {
      alert('パスワードの変更に失敗しました');
    } finally {
      setIsLoadingVerification(false);
    }
  };

  const handleResendCode = async () => {
    setIsResendingCode(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setCountdown(60);
    } catch (error) {
      alert('認証コードの再送信に失敗しました');
    } finally {
      setIsResendingCode(false);
    }
  };

  const handleCodeChange = (value: string) => {
    const numericValue = value.replace(/\D/g, '').slice(0, 6);
    handlePasswordChange('verificationCode', numericValue);
  };

  const maskedEmail = userInfo.email.replace(/(.{2})(.*)(@.*)/, '$1***$3');
  const passwordsMatch = passwordForm.newPassword && passwordForm.confirmPassword && passwordForm.newPassword === passwordForm.confirmPassword;
  const passwordLengthValid = passwordForm.newPassword.length >= 8;

  return (
    <div className="p-8 h-full overflow-y-auto">
      {/* Profile Header */}
      <div className="bg-gradient-to-r from-slate-50 to-gray-50 rounded-2xl p-8 mb-8 border border-gray-200">
        <div className="flex items-center gap-8">
          <div className="w-24 h-24 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
            <span className="text-4xl font-bold text-white">田</span>
          </div>
          
          <div className="flex-1">
            <h2 className="text-3xl font-bold text-slate-800 mb-2">
              {userInfo.name}
            </h2>
            <p className="text-lg text-slate-600 mb-1">{userInfo.nameKana}</p>
            <p className="text-slate-700 font-medium">{userInfo.position}</p>
            <p className="text-slate-600">{userInfo.department}</p>
          </div>
          
          <div className="text-right">
            <p className="text-sm text-slate-500 mb-1">社員番号</p>
            <p className="text-xl font-bold text-slate-800 font-mono">
              {userInfo.employeeId}
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Language Settings */}
        <div className="space-y-6">
          <h3 className="text-xl font-bold text-slate-800 flex items-center gap-3">
            <Globe className="w-6 h-6 text-blue-600" />
            言語設定
          </h3>
          
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <p className="text-sm text-slate-600 mb-4">
              システムの表示言語を選択してください
            </p>
            
            <div className="space-y-3">
              {languages.map((language) => (
                <label
                  key={language.code}
                  className={`flex items-center gap-4 p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                    selectedLanguage === language.code
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <input
                    type="radio"
                    name="language"
                    value={language.code}
                    checked={selectedLanguage === language.code}
                    onChange={() => handleLanguageChange(language.code)}
                    className="sr-only"
                  />
                  <span className="text-2xl">{language.flag}</span>
                  <span className="font-medium text-slate-800">{language.name}</span>
                  {selectedLanguage === language.code && (
                    <Check className="w-5 h-5 text-blue-600 ml-auto" />
                  )}
                </label>
              ))}
            </div>
          </div>
        </div>

        {/* Password Change */}
        <div className="space-y-6">
          <h3 className="text-xl font-bold text-slate-800 flex items-center gap-3">
            <Lock className="w-6 h-6 text-red-600" />
            パスワード変更
          </h3>
          
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            {passwordChangeSuccess && (
              <div className="bg-green-50 border border-green-200 rounded-xl p-4 mb-6 flex items-center gap-3">
                <Check className="w-5 h-5 text-green-600" />
                <p className="text-green-800 font-medium">
                  パスワードが正常に変更されました
                </p>
              </div>
            )}

            <form onSubmit={handlePasswordSubmit} className="space-y-4">
              {/* Verification Code Section */}
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
                <div className="flex items-center gap-3 mb-4">
                  <Mail className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="text-blue-800 font-medium">メール認証</p>
                    <p className="text-blue-700 text-sm">
                      パスワード変更には認証コードが必要です ({maskedEmail})
                    </p>
                  </div>
                </div>

                <div className="flex gap-3">
                  <div className="flex-1">
                    <input
                      type="text"
                      value={passwordForm.verificationCode}
                      onChange={(e) => handleCodeChange(e.target.value)}
                      placeholder="123456"
                      className="w-full px-4 py-3 text-center text-lg font-mono border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 tracking-widest"
                      maxLength={6}
                      disabled={!codeSent}
                    />
                  </div>
                  <div className="flex flex-col gap-2">
                    {!codeSent ? (
                      <button
                        type="button"
                        onClick={handleSendVerificationCode}
                        disabled={isSendingCode}
                        className="px-6 py-3 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white rounded-xl font-medium transition-all duration-200 whitespace-nowrap"
                      >
                        {isSendingCode ? '送信中...' : 'コード送信'}
                      </button>
                    ) : countdown > 0 ? (
                      <div className="px-6 py-3 bg-gray-100 text-gray-500 rounded-xl text-center text-sm">
                        再送信まで<br />{countdown}秒
                      </div>
                    ) : (
                      <button
                        type="button"
                        onClick={handleResendCode}
                        disabled={isResendingCode}
                        className="px-6 py-3 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white rounded-xl font-medium transition-all duration-200 flex items-center gap-2 whitespace-nowrap"
                      >
                        <RefreshCw className={`w-4 h-4 ${isResendingCode ? 'animate-spin' : ''}`} />
                        {isResendingCode ? '送信中...' : '再送信'}
                      </button>
                    )}
                  </div>
                </div>

                {codeSent && (
                  <p className="text-xs text-blue-600 mt-2 text-center">
                    6桁の認証コードを入力してください
                  </p>
                )}
              </div>

              {/* Current Password */}
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  現在のパスワード
                </label>
                <div className="relative">
                  <input
                    type={showCurrentPassword ? 'text' : 'password'}
                    value={passwordForm.currentPassword}
                    onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}
                    className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  >
                    {showCurrentPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              {/* New Password */}
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  新しいパスワード
                </label>
                <div className="relative">
                  <input
                    type={showNewPassword ? 'text' : 'password'}
                    value={passwordForm.newPassword}
                    onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
                    className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200"
                    minLength={8}
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  >
                    {showNewPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
                <div className="mt-2 flex items-center gap-2">
                  {passwordLengthValid ? (
                    <Check className="w-4 h-4 text-green-600" />
                  ) : (
                    <div className="w-4 h-4 rounded-full border-2 border-gray-300" />
                  )}
                  <span className={`text-xs ${passwordLengthValid ? 'text-green-600' : 'text-gray-500'}`}>
                    8文字以上
                  </span>
                </div>
              </div>

              {/* Confirm Password */}
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  新しいパスワード（確認）
                </label>
                <div className="relative">
                  <input
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={passwordForm.confirmPassword}
                    onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}
                    className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  >
                    {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
                {passwordForm.confirmPassword && (
                  <div className="mt-2 flex items-center gap-2">
                    {passwordsMatch ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : (
                      <div className="w-4 h-4 rounded-full border-2 border-red-300" />
                    )}
                    <span className={`text-xs ${passwordsMatch ? 'text-green-600' : 'text-red-600'}`}>
                      {passwordsMatch ? 'パスワードが一致しています' : 'パスワードが一致しません'}
                    </span>
                  </div>
                )}
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                disabled={
                  isLoadingVerification || 
                  !codeSent || 
                  passwordForm.verificationCode.length !== 6 ||
                  !passwordsMatch || 
                  !passwordLengthValid
                }
                className="w-full bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed text-white py-3 px-6 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
              >
                {isLoadingVerification ? 'パスワード変更中...' : 'パスワードを変更'}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;