import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// 动态导入翻译资源
const loadResources = async (lng: string) => {
  try {
    const modules = await Promise.all([
      import(`../../public/locales/${lng}/common.json`),
      import(`../../public/locales/${lng}/menu.json`),
      import(`../../public/locales/${lng}/login.json`),
      import(`../../public/locales/${lng}/errors.json`),
      import(`../../public/locales/${lng}/attendance.json`),
      import(`../../public/locales/${lng}/application.json`),
      import(`../../public/locales/${lng}/approval.json`),
      import(`../../public/locales/${lng}/user.json`),
      import(`../../public/locales/${lng}/transportation.json`),
      import(`../../public/locales/${lng}/profile.json`),
      import(`../../public/locales/${lng}/statistics.json`),
      // 可以根据需要添加更多模块
    ]);

    return {
      common: modules[0].default,
      menu: modules[1].default,
      login: modules[2].default,
      errors: modules[3].default,
      attendance: modules[4].default,
      application: modules[5].default,
      approval: modules[6].default,
      user: modules[7].default,
      transportation: modules[8].default,
      profile: modules[9].default,
      statistics: modules[10].default,
    };
  } catch (error) {
    console.error(`Failed to load resources for language: ${lng}`, error);
    // 返回空对象作为后备
    return {
      common: {},
      menu: {},
      login: {},
      errors: {},
      attendance: {},
      application: {},
      approval: {},
      user: {},
      transportation: {},
      profile: {},
      statistics: {},
    };
  }
};

// 安全的 i18n 初始化
let isInitialized = false;

const initializeI18n = () => {
  if (isInitialized) return;

  try {
    // 获取用户的语言偏好作为初始语言
    let initialLanguage = 'zh';
    if (typeof window !== 'undefined') {
      initialLanguage = localStorage.getItem('preferred-language') || 'zh';
      console.log('i18n: Initializing with language:', initialLanguage);
    }

    i18n
      .use(initReactI18next)
      .init({
        lng: initialLanguage, // 使用用户偏好的语言
        fallbackLng: 'zh',
        debug: import.meta.env.MODE === 'development',

        interpolation: {
          escapeValue: false, // React 已经处理了 XSS
        },

        // 命名空间
        ns: ['common', 'menu', 'login', 'errors', 'attendance', 'application', 'approval', 'user', 'transportation', 'profile', 'statistics'],
        defaultNS: 'common',

        // 资源将通过动态导入加载
        resources: {},

        // 添加错误处理
        initImmediate: false, // 不立即初始化
        react: {
          useSuspense: false, // 禁用 Suspense
        }
      });

    isInitialized = true;
    console.log('i18n: Initialization completed with language:', initialLanguage);
  } catch (error) {
    console.error('Failed to initialize i18n:', error);
  }
};

// 立即初始化
initializeI18n();

// 动态加载语言资源
export const loadLanguageResources = async (language: string) => {
  try {
    // 检查 i18n 是否可用
    if (!i18n || typeof i18n.hasResourceBundle !== 'function') {
      console.warn('i18n not properly initialized, skipping resource loading');
      return;
    }

    // 验证语言参数
    if (!language || typeof language !== 'string') {
      console.warn('Invalid language parameter:', language);
      return;
    }

    // 检查资源是否已加载
    if (!i18n.hasResourceBundle(language, 'common')) {
      const resources = await loadResources(language);

      // 安全地添加资源
      if (resources && typeof resources === 'object') {
        Object.keys(resources).forEach(ns => {
          try {
            const resourceKey = ns as keyof typeof resources;
            if (resources[resourceKey] &&
                typeof resources[resourceKey] === 'object' &&
                Object.keys(resources[resourceKey]).length > 0) {

              // 检查 addResourceBundle 方法是否可用
              if (typeof i18n.addResourceBundle === 'function') {
                i18n.addResourceBundle(language, ns, resources[resourceKey]);
              }
            }
          } catch (bundleError) {
            console.error(`Failed to add resource bundle for ${ns}:`, bundleError);
          }
        });
      }
    }
  } catch (error) {
    console.error(`Failed to load language resources for: ${language}`, error);
    // 不要抛出错误，让应用继续运行
  }
};

export default i18n;
