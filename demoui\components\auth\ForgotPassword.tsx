import React, { useState } from 'react';
import { User, Mail, ArrowLeft, AlertCircle } from 'lucide-react';
import type { ForgotPasswordForm } from '../../types/auth';

interface ForgotPasswordProps {
  onSubmit: (form: ForgotPasswordForm) => void;
  onBack: () => void;
  error?: string;
  isLoading?: boolean;
}

const ForgotPassword: React.FC<ForgotPasswordProps> = ({ onSubmit, onBack, error, isLoading }) => {
  const [form, setForm] = useState<ForgotPasswordForm>({
    employeeId: '',
    email: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(form);
  };

  const handleInputChange = (field: keyof ForgotPasswordForm, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 to-orange-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
            <Mail className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-slate-800 mb-2">
            パスワードリセット
          </h1>
          <p className="text-slate-600">
            社員番号とメールアドレスを入力してください
          </p>
        </div>

        {/* Form */}
        <div className="bg-white rounded-2xl shadow-xl border border-amber-100 p-8">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6 flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-red-800 font-medium mb-1">エラー</p>
                <p className="text-red-700 text-sm">{error}</p>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Employee ID */}
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                社員番号
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  value={form.employeeId}
                  onChange={(e) => handleInputChange('employeeId', e.target.value)}
                  placeholder="EMP-2024-001"
                  className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  required
                />
              </div>
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                メールアドレス
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="email"
                  value={form.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  required
                />
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed text-white py-4 px-6 rounded-xl font-bold text-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
            >
              {isLoading ? '送信中...' : '認証コードを送信'}
            </button>

            {/* Back Button */}
            <button
              type="button"
              onClick={onBack}
              className="w-full flex items-center justify-center gap-2 text-slate-600 hover:text-slate-800 py-3 px-6 rounded-xl border border-gray-300 hover:border-gray-400 transition-all duration-200"
            >
              <ArrowLeft className="w-4 h-4" />
              ログイン画面に戻る
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;