import type { ReactElement, ReactNode} from 'react';
import type { NextPage } from 'next';
import { ConfigProvider } from "antd";
import type { AppProps } from "next/app";
import ReduxProvider from "../src/components/ReduxProvider";
import ErrorBoundary from "../src/components/ErrorBoundary";
import I18nProvider from "../src/components/I18nProvider";
import '../styles/globals.css';
import '../styles/layout-fixes.css';
// 移除了所有可能导致问题的导入和逻辑

// 程序入口，不要修改
export type NextPageWithLayout<P = {}, IP = P> = NextPage<P, IP> & {
  getLayout?: (page: ReactElement) => ReactNode
}

type AppPropsWithLayout = AppProps & {
  Component: NextPageWithLayout
}

function App({ Component, pageProps }: AppPropsWithLayout) {
  const getLayout = Component.getLayout ?? ((page) => page)
  const layout = getLayout(<Component {...pageProps} />)

  return (
    <ErrorBoundary>
      <I18nProvider>
        <ReduxProvider>
          <ConfigProvider>
            {layout}
          </ConfigProvider>
        </ReduxProvider>
      </I18nProvider>
    </ErrorBoundary>
  )
}

export default App
