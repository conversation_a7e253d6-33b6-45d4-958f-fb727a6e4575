# 考勤页面移动端卡片布局实现总结

## 用户需求
1. **PC端需要保留分页和滚动**
2. **移动端的卡片宽度100%自适应**
3. **移动端卡片布局**：
   - 左上：日期
   - 右上：办公地点
   - 左下：上班打卡，下方显示打卡时间
   - 右下：下班打卡，下方显示打卡时间

## 实现方案

### 1. 恢复PC端分页功能

#### 恢复分页相关导入和状态
```tsx
// 添加Pagination导入
import { Pagination, message, Spin, Tooltip, Tag, Button, Space, Table } from 'antd';

// 恢复分页状态变量
const [currentPage, setCurrentPage] = useState(1);
const [pageSize, setPageSize] = useState(50);

// 恢复分页计算变量
const startIndex = (currentPage -1) * pageSize;
const endIndex = currentPage * pageSize;
```

#### 修改数据获取逻辑
```tsx
// PC端分页，移动端全部数据
const getTableData = () => {
    const dataList = (queryResult as any).data_list || [];
    const filteredData = dataList
        .filter((item: any) => dayjs(item?.day?.slice(0,10)) <= dayjs((queryResult as any).max_day));
    
    // 检测是否为移动端
    const isMobile = typeof window !== 'undefined' && window.innerWidth <= 768;
    
    // 移动端显示全部数据，PC端使用分页
    const displayData = isMobile ? filteredData : filteredData.slice(startIndex, endIndex);
    
    return displayData.map((item: any, index: number) => {
        const actualIndex = isMobile ? index : startIndex + index;
        return {
            key: `table_${actualIndex}`,
            day: item?.day,
            start_time: item?.start,
            end_time: item?.end,
            leave: '',
            comments: '',
            ...item
        };
    });
};
```

#### 恢复分页组件（仅PC端显示）
```tsx
{/* PC端分页区域 */}
<div className={`${styles.pagination_container} ${styles.pc_only_pagination}`}>
    <Pagination
        current={currentPage}
        pageSize={pageSize}
        total={(queryResult as any).data_list?.length || 0}
        onChange={(page) => setCurrentPage(page)}
        showSizeChanger={false}
        showQuickJumper={true}
        pageSizeOptions={['50', '100', '200']}
        showTotal={(total, range) =>
            `${range[0]}-${range[1]} / ${total} ${tCommon('records', '条记录')}`
        }
        size="default"
    />
</div>
```

#### 恢复PC端overflow设置
```tsx
// 主容器恢复overflow: 'hidden'
<div style={{
    borderRadius: '12px',
    boxShadow: '0 6px 20px rgba(0, 0, 0, 0.08)',
    background: '#fff',
    overflow: 'hidden',  // 恢复
    display: 'flex',
    flexDirection: 'column',
    height: 'calc(100vh - 200px)',
    minHeight: '500px'
}}>

// 内容区域恢复overflow: 'hidden'
<div style={{
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',  // 恢复
    padding: '16px 20px 20px 20px'
}}>
```

### 2. 移动端卡片100%自适应

#### 修改MobileTable卡片样式
```css
.mobile_card {
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  width: 100%;           /* 100%自适应 */
  min-height: 120px;     /* 最小高度 */
  box-sizing: border-box; /* 包含padding和border */
}
```

#### 移动端隐藏分页
```css
@media (max-width: 768px) {
    /* 移动端隐藏分页 */
    .pc_only_pagination {
        display: none !important;
    }
}
```

### 3. 移动端四角卡片布局

#### 修改列配置
```tsx
// 日期列 - 左上角
{
    key: 'day',
    title: tCommon('date', '日期'),
    mobilePosition: 'title' as const,
    mobileRender: (value: any, record: any) => {
        let textStyle = { 
            fontWeight: '600',
            fontSize: '16px',
            color: record?.err == 1 ? '#ff4d4f' : 
                   record?.err == 2 ? '#fa8c16' : '#1890ff'
        };
        return <span style={textStyle}>{value}</span>;
    }
}

// 办公地点列 - 右上角
{
    key: 'work_location',
    title: tCommon('workLocation', '出勤地'),
    mobilePosition: 'subtitle' as const,
    // 保持原有render逻辑
}

// 上班打卡列 - 左下角
{
    key: 'start_time',
    title: t('workTime.clockIn'),
    mobilePosition: 'content' as const,
    mobileRender: (value: any, record: any) => {
        const timeDisplay = renderTimeDisplay(record, 'start');
        return (
            <div style={{ textAlign: 'left' }}>
                <div style={{ 
                    fontSize: '12px', 
                    color: '#666', 
                    marginBottom: '4px',
                    fontWeight: '500'
                }}>
                    上班打卡
                </div>
                <div style={{ 
                    fontSize: '14px', 
                    fontWeight: '600',
                    color: '#1890ff'
                }}>
                    {timeDisplay}
                </div>
            </div>
        );
    }
}

// 下班打卡列 - 右下角
{
    key: 'end_time',
    title: t('workTime.clockOut'),
    mobilePosition: 'meta' as const,
    mobileRender: (value: any, record: any) => {
        const timeDisplay = renderTimeDisplay(record, 'end');
        return (
            <div style={{ textAlign: 'right' }}>
                <div style={{ 
                    fontSize: '12px', 
                    color: '#666', 
                    marginBottom: '4px',
                    fontWeight: '500'
                }}>
                    下班打卡
                </div>
                <div style={{ 
                    fontSize: '14px', 
                    fontWeight: '600',
                    color: '#52c41a'
                }}>
                    {timeDisplay}
                </div>
            </div>
        );
    }
}
```

#### 修改MobileTable组件支持四角布局
```tsx
// 检查是否为考勤页面的特殊布局
const isAttendanceLayout = titleColumns.length > 0 && subtitleColumns.length > 0 && 
                          contentColumns.length > 0 && metaColumns.length > 0;

if (isAttendanceLayout) {
  // 考勤页面专用四角布局
  return (
    <div className={styles.mobile_card}>
      <div className={styles.attendance_card_layout}>
        {/* 上方区域 */}
        <div className={styles.attendance_top_row}>
          {/* 左上：日期 */}
          <div className={styles.attendance_top_left}>
            {titleColumns.map((col, idx) => (
              <div key={idx}>{getColumnValue(col)}</div>
            ))}
          </div>
          {/* 右上：办公地点 */}
          <div className={styles.attendance_top_right}>
            {subtitleColumns.map((col, idx) => (
              <div key={idx}>{getColumnValue(col)}</div>
            ))}
          </div>
        </div>
        
        {/* 下方区域 */}
        <div className={styles.attendance_bottom_row}>
          {/* 左下：上班打卡 */}
          <div className={styles.attendance_bottom_left}>
            {contentColumns.map((col, idx) => (
              <div key={idx}>{getColumnValue(col)}</div>
            ))}
          </div>
          {/* 右下：下班打卡 */}
          <div className={styles.attendance_bottom_right}>
            {metaColumns.map((col, idx) => (
              <div key={idx}>{getColumnValue(col)}</div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
```

#### 添加四角布局CSS样式
```css
/* 考勤页面专用四角布局 */
.attendance_card_layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.attendance_top_row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.attendance_bottom_row {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  margin-top: auto;
}

.attendance_top_left {
  flex: 1;
  text-align: left;
}

.attendance_top_right {
  flex: 1;
  text-align: right;
}

.attendance_bottom_left {
  flex: 1;
  text-align: left;
}

.attendance_bottom_right {
  flex: 1;
  text-align: right;
}
```

## 实现效果

### PC端体验
- ✅ **保留分页功能**：显示分页控件，支持分页浏览
- ✅ **保留滚动功能**：表格内部滚动机制正常
- ✅ **原有功能完整**：所有交互和样式保持不变

### 移动端体验
- ✅ **卡片100%自适应**：卡片宽度完全适应屏幕，无横向滚动
- ✅ **四角布局**：
  - 左上角：日期（带状态颜色）
  - 右上角：办公地点（带标签样式）
  - 左下角：上班打卡（标题+时间）
  - 右下角：下班打卡（标题+时间）
- ✅ **隐藏分页**：移动端不显示分页控件
- ✅ **显示全部数据**：一次性加载所有考勤记录
- ✅ **列表滚动**：支持流畅的垂直滚动

## 修改文件清单

### 主要文件
1. **src/pages/attendance/features/my/index.tsx**
   - 恢复分页相关导入和状态变量
   - 修改getTableData函数支持PC/移动端不同逻辑
   - 恢复分页组件（仅PC端显示）
   - 恢复PC端overflow设置
   - 修改列配置支持移动端四角布局

2. **src/components/ui/MobileTable.tsx**
   - 添加考勤页面四角布局检测逻辑
   - 实现专用四角布局渲染

3. **src/components/ui/MobileTable.module.css**
   - 添加考勤页面四角布局样式
   - 修改mobile_card为100%自适应

4. **src/pages/attendance/features/my/my.module.css**
   - 已有移动端隐藏分页的样式规则

## 技术特点

### 响应式设计
- **768px断点**：PC端和移动端的分界线
- **动态检测**：运行时检测屏幕宽度决定显示模式
- **条件渲染**：PC端显示分页，移动端隐藏分页

### 布局优化
- **Flexbox布局**：四角布局使用flex实现精确定位
- **自适应宽度**：移动端卡片100%宽度，无最小宽度限制
- **合理间距**：16px间距确保内容不拥挤

### 用户体验
- **视觉层次**：不同颜色和字体大小区分信息重要性
- **触摸友好**：120px最小高度确保触摸目标足够大
- **流畅滚动**：iOS风格的平滑滚动体验

## 总结

✅ **完成状态**：考勤页面移动端卡片布局优化全部完成

✅ **核心改进**：
- PC端保留完整的分页和滚动功能
- 移动端实现100%自适应的四角卡片布局
- 专门的考勤信息展示方式，信息层次清晰

✅ **技术方案**：响应式设计 + 条件渲染 + Flexbox四角布局

✅ **用户体验**：PC端功能完整，移动端布局清晰直观，信息一目了然

现在考勤页面在PC端保持原有的分页和滚动功能，在移动端提供专门优化的四角卡片布局，每个卡片100%自适应屏幕宽度，信息布局清晰合理。
