import React from 'react';
import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import { StatusTag, ActionButton } from '@/pages/components/ui/Table';

// 格式化日期时间
export const formatDateTime = (dateTime: string | Date, format: string = 'YYYY-MM-DD HH:mm') => {
  if (!dateTime) return '-';
  return dayjs(dateTime).format(format);
};

// 格式化日期
export const formatDate = (date: string | Date) => {
  return formatDateTime(date, 'YYYY-MM-DD');
};

// 格式化时间
export const formatTime = (time: string | Date) => {
  return formatDateTime(time, 'HH:mm');
};

// 获取星期几
export const getWeekday = (date: Date | string) => {
  const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
  const day = new Date(date).getDay();
  return `(${weekdays[day]})`;
};

// 渲染状态列
export const renderStatus = (status: number | string) => {
  const statusMap: Record<string | number, { text: string; type: 'pending' | 'approved' | 'rejected' | 'processing' }> = {
    0: { text: '等待审批', type: 'pending' },
    1: { text: '已通过', type: 'approved' },
    2: { text: '已驳回', type: 'rejected' },
    3: { text: '已失效', type: 'rejected' },
    'pending': { text: '等待审批', type: 'pending' },
    'approved': { text: '已通过', type: 'approved' },
    'rejected': { text: '已驳回', type: 'rejected' },
    'processing': { text: '处理中', type: 'processing' }
  };

  const statusInfo = statusMap[status] || { text: '未知状态', type: 'pending' as const };
  
  return (
    <StatusTag status={statusInfo.type}>
      {statusInfo.text}
    </StatusTag>
  );
};

// 渲染操作列
export const renderActions = (actions: Array<{
  text: string;
  type?: 'primary' | 'secondary' | 'danger' | 'warning';
  onClick: () => void;
  disabled?: boolean;
  tooltip?: string;
  icon?: string;
}>) => {
  return (
    <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
      {actions.map((action, index) => {
        const button = (
          <ActionButton
            key={index}
            type={action.type}
            onClick={action.onClick}
            disabled={action.disabled}
          >
            {action.icon && (
              <img
                src={action.icon}
                alt={action.text}
                width={16}
                height={16}
                style={{ marginRight: action.text ? '4px' : '0' }}
              />
            )}
            {action.text}
          </ActionButton>
        );

        return action.tooltip ? (
          <Tooltip key={index} title={action.tooltip} placement="top">
            {button}
          </Tooltip>
        ) : button;
      })}
    </div>
  );
};

// 渲染用户信息列
export const renderUserInfo = (name: string, id?: string) => {
  if (!name) return '-';
  
  // 处理格式如 "JS0001-张三" 的用户名
  if (name.includes('-')) {
    const parts = name.split('-');
    const userId = parts[0];
    const userName = parts[1];
    return (
      <div style={{ textAlign: 'center' }}>
        <div style={{ fontWeight: 'bold', color: '#2A82E4' }}>{userName}</div>
        <div style={{ fontSize: '12px', color: '#666' }}>{userId}</div>
      </div>
    );
  }
  
  return (
    <div style={{ textAlign: 'center' }}>
      <div style={{ fontWeight: 'bold', color: '#2A82E4' }}>{name}</div>
      {id && <div style={{ fontSize: '12px', color: '#666' }}>{id}</div>}
    </div>
  );
};

// 渲染时间范围列
export const renderTimeRange = (startTime: string, endTime: string) => {
  if (!startTime || !endTime) return '-';
  
  const start = dayjs(startTime);
  const end = dayjs(endTime);
  const startWeekday = getWeekday(start.toDate());
  const endWeekday = getWeekday(end.toDate());
  
  return (
    <div style={{ textAlign: 'center', fontSize: '12px' }}>
      <div>{start.format('YYYY-MM-DD')} {startWeekday}</div>
      <div style={{ color: '#2A82E4', fontWeight: 'bold' }}>{start.format('HH:mm')}</div>
      <div style={{ margin: '4px 0', color: '#666' }}>至</div>
      <div>{end.format('YYYY-MM-DD')} {endWeekday}</div>
      <div style={{ color: '#2A82E4', fontWeight: 'bold' }}>{end.format('HH:mm')}</div>
    </div>
  );
};

// 渲染假期类型标签
export const renderLeaveTypes = (record: any) => {
  const types = [];
  
  if (record.compensatory_hours > 0) {
    types.push({ type: '调休', time: `${record.compensatory_hours}h`, color: '#63B8FF' });
  }
  if (record.normal_hours > 0) {
    types.push({ type: '事假', time: `${record.normal_hours}h`, color: '#FF3030' });
  }
  if (record.sick_hours > 0) {
    types.push({ type: '病假', time: `${record.sick_hours}h`, color: '#FFA500' });
  }
  if (record.other_days > 0) {
    types.push({ type: '其他', time: `${record.other_days}d`, color: '#0000FF' });
  }
  
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', alignItems: 'center' }}>
      {types.map((item, index) => (
        <div
          key={index}
          style={{
            background: item.color,
            color: 'white',
            padding: '2px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            fontWeight: 'bold',
            minWidth: '50px',
            textAlign: 'center'
          }}
        >
          {item.type} {item.time}
        </div>
      ))}
    </div>
  );
};

// 渲染文本省略
export const renderEllipsisText = (text: string, maxLength: number = 20) => {
  if (!text) return '-';
  
  if (text.length <= maxLength) {
    return text;
  }
  
  return (
    <Tooltip title={text} placement="top">
      <span>{text.substring(0, maxLength)}...</span>
    </Tooltip>
  );
};

// 渲染数值
export const renderNumber = (value: number | string, suffix?: string) => {
  if (value === null || value === undefined || value === '') return '-';
  return `${value}${suffix || ''}`;
};

// 渲染百分比
export const renderPercentage = (value: number, decimals: number = 1) => {
  if (value === null || value === undefined) return '-';
  return `${(value * 100).toFixed(decimals)}%`;
};

// 通用表格列配置生成器
export const createTableColumns = (config: Array<{
  key: string;
  title: string;
  dataIndex: string;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  type?: 'text' | 'date' | 'datetime' | 'time' | 'status' | 'user' | 'actions' | 'timeRange' | 'leaveTypes' | 'ellipsis' | 'number';
  render?: (value: any, record: any, index: number) => React.ReactNode;
  ellipsis?: boolean;
  maxLength?: number;
  suffix?: string;
  mobileLabel?: string; // 移动端显示标签
  mobilePosition?: 'title' | 'subtitle' | 'content' | 'meta' | 'action'; // 移动端位置
  actions?: Array<{
    text: string;
    type?: 'primary' | 'secondary' | 'danger' | 'warning';
    onClick: (record: any) => void;
    disabled?: (record: any) => boolean;
    tooltip?: string;
    icon?: string;
  }>;
}>) => {
  return config.map(column => ({
    key: column.key,
    title: column.title,
    dataIndex: column.dataIndex,
    width: column.width,
    align: column.align || 'center',
    ellipsis: column.ellipsis,
    mobileLabel: column.mobileLabel,
    mobilePosition: column.mobilePosition,
    render: column.render || ((value: any, record: any, index: number) => {
      switch (column.type) {
        case 'date':
          return formatDate(value);
        case 'datetime':
          return formatDateTime(value);
        case 'time':
          return formatTime(value);
        case 'status':
          return renderStatus(value);
        case 'user':
          return renderUserInfo(value);
        case 'timeRange':
          return renderTimeRange(record.start_time, record.end_time);
        case 'leaveTypes':
          return renderLeaveTypes(record);
        case 'ellipsis':
          return renderEllipsisText(value, column.maxLength);
        case 'number':
          return renderNumber(value, column.suffix);
        case 'actions':
          return renderActions(
            (column.actions || []).map(action => ({
              ...action,
              onClick: () => action.onClick(record),
              disabled: action.disabled ? action.disabled(record) : false
            }))
          );
        default:
          return value || '-';
      }
    })
  }));
};
