// 定义权限对应关系

import { tr } from "date-fns/locale";
import { getApi } from '@/api/fetch-api';
import { setUserActions, loginData } from "@/slice/authSlice";
import ApiUrlVars from '@/api/common/url-vars';
import { useApplicationSelector } from "@/hook/hooks";
import { routerConfig } from "./routeconfig";

// 与数据库保持一致
export const permissionsGroups = {
    GROUP_CARD_RECORDS: {
        code: "A",
        title: "考勤管理权限组",
        persmissions: {
            1: { code: "001", title: "浏览本人" },
            2: { code: "002", title: "浏览他人" },
            3: { code: "003", title: "编辑考勤" },
            4: { code: "004", title: "导入考勤" },
        }
    },
    GROUP_APPLYS: {
        code: "B",
        title: "申请管理权限组",
        persmissions: {
            1: { code: "001", title: "浏览/提出/撤回" },
            2: { code: "002", title: "代人申请" },
        }
    },
    GROUP_APPROVAL: {
        code: "C",
        title: "审批管理权限组",
        persmissions: {
            1: { code: "001", title: "批准/驳回(一般)" },
            2: { code: "002", title: "批准/驳回(特定)" },
            3: { code: "003", title: "批准/驳回(出差)" },
            4: { code: "004", title: "批准/驳回(上下班确认单)" },
        }
    },
    GROUP_MEMBERS: {
        code: "D",
        title: "成员管理权限组",
        persmissions: {
            1: { code: "001", title: "浏览成员(部门级)" },
            2: { code: "002", title: "编辑成员(部门级)" },
            3: { code: "003", title: "角色授予(部门级)" },
            4: { code: "004", title: "浏览成员(组织级)" },
            5: { code: "005", title: "编辑成员(组织级)" },
            6: { code: "006", title: "角色授予(组织级)" },
        }
    },
    GROUP_REPORTS: {
        code: "E",
        title: "数据统计管理权限组",
        persmissions: {
            1: { code: "001", title: "假日加班统计" },
            2: { code: "002", title: "报表浏览(个人级)" },
            3: { code: "003", title: "报表浏览(部门级)" },
            4: { code: "004", title: "报表浏览(组织级)" },
            5: { code: "005", title: "报表导出(个人级)" },
            6: { code: "006", title: "报表导出(部门级)" },
            7: { code: "007", title: "报表导出(组织级)" },
        }
    },
    GROUP_DEPARTS: {
        code: "F",
        title: "组织构成管理权限组",
        persmissions: {
            1: { code: "001", title: "信息浏览" },
            2: { code: "002", title: "信息编辑" },
        }
    },
    GROUP_SYS_SETTINGS: {
        code: "G",
        title: "系统设定管理权限组",
        persmissions: {
            1: { code: "001", title: "节假日信息浏览" },
            2: { code: "002", title: "节假日信息编辑" },
            3: { code: "003", title: "邮件预设信息浏览" },
            4: { code: "004", title: "角色权限信息浏览" },
            5: { code: "005", title: "角色权限信息编辑" },
            6: { code: "006", title: "编辑/其他设定编辑/浏览" },
            7: { code: "007", title: "审批流程浏览" },
        }
    },
    GROUP_LOGS: {
        code: "H",
        title: "操作管理权限组",
        persmissions: {
            1: { code: "001", title: "个人记录浏览" },
            2: { code: "002", title: "他人记录浏览" },
        }
    },
}

export const allPermissionUrls = [
    "/user/information2",
    "/user/information",
    "/approval/leaveApproval",
    "/approval/overtimeApproval",
    "/approval/evectionApproval",
    "/approval/confirmationApproval",
    "/application/leaveApplication",
    "/application/overtimeApplication",
    "/application/confirmationApplication",
    "/application/confirmationApplication",
    "/statistics/personalStatistics",
    "/statistics/applicationStatistics",
    "/statistics/departmentStatistics",
    "/statistics/tissueStatistics",
    "/statistics/paidLeaveStatistics",
    "/records/attendanceimport",
    "/records/attendanceimport1",
    "/records/attendanceimport2",
    "/records/attendanceimport3",
]

export function permissionGroupIsGranted(group: string) {
    let isGranted = false
    if (typeof window === 'undefined') {
        return true; // 服务端渲染时默认授权，避免错误
    }

    try {
        const loginData = localStorage.getItem('login');
        if (!loginData) {
            return false; // 没有登录数据，直接返回 false
        }

        const userPermissions = JSON.parse(loginData)?.permissions;
        if (!userPermissions || !Array.isArray(userPermissions)) {
            return false; // 权限数据无效，返回 false
        }

        for (let i = 0; i < userPermissions.length; i++) {
            if (userPermissions[i]?.PERMISSIONS_GROUP_ID == group) {
                isGranted = true
                break
            }
        }
    } catch (error) {
        console.error('Permission check error:', error);
        return false;
    }

    return isGranted
}


export function subPermissionIsGranted(permission: string) {
    let isGranted = false;
    if (typeof window === 'undefined') {
        return true; // 服务端渲染时默认授权，避免错误
    }

    try {
        const loginData = localStorage.getItem('login');
        if (!loginData) {
            return false; // 没有登录数据，直接返回 false
        }

        const userPermissions = JSON.parse(loginData)?.permissions;
        if (!userPermissions || !Array.isArray(userPermissions)) {
            return false; // 权限数据无效，返回 false
        }

        for (let i = 0; i < userPermissions.length; i++) {
            const pIds = userPermissions[i]?.PERMISSIONS_ID;
            if (!pIds || !Array.isArray(pIds)) {
                continue; // 跳过无效的权限项
            }

            for (let j = 0; j < pIds.length; j++) {
                if (userPermissions[i]?.PERMISSIONS_GROUP_ID + pIds[j] == permission) {
                    isGranted = true;
                    break;
                }
            }
            if (isGranted) break;
        }
    } catch (error) {
        console.error('Sub permission check error:', error);
        return false;
    }

    return isGranted;
}

export function subPermissionIsGrantedByPath(tmpPath: string): boolean | 'not_logged_in' {
    if (typeof window === 'undefined') {
        return true; // 服务端渲染时默认授权，避免错误
    }

    try {
        // 首先检查登录状态
        const { getCookie, key_for_token, key_for_token_aes } = require('./cookies');
        const { is_login } = require('./login');

        const token = getCookie(key_for_token);
        const token_aes = getCookie(key_for_token_aes);
        const isLoggedIn = is_login(token, token_aes);

        if (!isLoggedIn) {
            return 'not_logged_in'; // 返回特殊值表示未登录
        }

        // 检查是否有登录数据
        const loginData = localStorage.getItem('login');
        if (!loginData) {
            return 'not_logged_in'; // 没有登录数据，返回未登录状态
        }

        // 特殊处理：/user/information 页面直接授权
        if (tmpPath === '/user/information') {
            return true;
        }

        let isGranted = false
        const routeKeys = routerConfig ? Object.keys(routerConfig) : [];
        let path = tmpPath
        if(routeKeys.indexOf(tmpPath) >= 0){
            path = (routerConfig as any)[path]
        }

        // 无权限限制，默认授权
        if (allPermissionUrls.indexOf(path) >= 0) {
            isGranted = true
        } else {
            // 临时解决方案：对于迁移期间，默认授权所有路径
            // TODO: 重新实现权限检查逻辑，避免循环依赖
            isGranted = true

            // 可以在这里添加特定路径的权限检查
            // 例如：
            // if (path === '/admin/settings') {
            //     isGranted = subPermissionIsGranted('admin_permission_code')
            // }
        }
        return isGranted
    } catch (error) {
        console.error('Path permission check error:', error);
        return 'not_logged_in'; // 出错时返回未登录状态，触发跳转
    }
}