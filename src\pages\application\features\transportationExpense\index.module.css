.record_box {
    background: #fff;
    width: 96%;
    min-width: 1158px;
    height: 96%;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    line-height: 35px;
    margin-top: 20px; /* 减少上边距 */
    overflow: hidden;
}
.record_small {
    background: #fff;
    width: 96%;
    min-width: 1158px;
    height: 58%;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    line-height: 35px;
    margin-top: 20px; /* 减少上边距 */
    overflow: hidden;
}
.record_title {
    width: auto;
    min-width: 120px;
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    border-radius: 8px;
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0 18px;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    position: relative;
    transition: all 0.3s ease;
}

.record_title::after {
    content: '';
    position: absolute;
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid #1890ff;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
}

.record_title:hover {
    background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.record_title_font {
    color: white;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.5px;
}
.record_title_btn {
    margin-top: 10px;
    margin-left: auto;
    width: 150px;
    height: 36px;
    border-radius: var(--radius-medium);
    font-size: 13px;
    font-weight: 500;
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;

    /* 使用CSS变量的渐变背景 */
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-light) 100%);
    box-shadow: var(--shadow-light);
}

.record_title_btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    filter: brightness(1.1);
}

.record_title_btn:active {
    transform: translateY(0);
    filter: brightness(0.9);
}
.record_all {
    width: 100%;
    padding-left: 40px;
    overflow-y: auto;
    overflow-x: hidden;
    margin-top: 10px;
}
.record_row {
    width: 96%;
    border: 1px solid rgb(187, 186, 186);
    border-radius: 10px;
    mask-type: flex;
    justify-content: center;
    text-align: middle;
    font-size: 12px;
    margin-bottom: 5px;
    height: auto;
}
.record_value {
    float:left;
    margin-top: -25px;
    margin-left: 5px;
    font-size: 12px;
    font-weight: bold;
}
.record_user{
    margin-top: -25px;
    margin-left: -10px;
    font-size: 12px;
    font-weight: bold;
}

.value_color {
    color: rgb(163, 162, 162);
    margin-top: -25px;
    font-size: 12px;
}
.record_data {
    padding-top: 24px;
    height: auto;
}

.form_title {
    width: 150px;
    border-radius: 10px;
    height: 35px;
    padding-top: 0px;
    padding-left: 40px;
    background-color: #fff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}
.form_title_font {
    color: rgb(79, 129, 238);
    padding-left: 5px;
    padding-top: 5px;
    position: absolute;
    font-size: 15px;
}

.form_content {
    float: left;
    max-width: 1000px;
    min-width:800px;
    width:800px;
    background-color: white;
    margin-top: -7px;
    height: 290px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    border-top-right-radius: 10px;
}
.form_text {
    width: 500px;
    height: 290px;
    background-color: white;
    float: left;
    margin-left: 50px;
    margin-top: -7px;
    border-radius: 10px;
}
.form_text_title {
    height: 30px;
    width: 100%;
    float: left;
    margin-top: 10px;
    margin-left: 10px;
}
.form_text_content {
    max-height: 420px;
    width: 100%;
    float: left;
}
.form_content_people {
    margin-top: 15px;
    width: 100%;
    float: left;
}
.form_content_people_undo {
    float: left;
    margin-left: 10px;
    margin-top: -3px;
    cursor: pointer; 
}
.form_content_people_title {
    min-width: 70px;
    float: left;
    margin-left: 25px;
}
.form_content_people_default {
    height: 25px;
    float: left;
    width: 150px;
    top: -3px;
}
.form_content_people_buttonSwitch {
    width: 25px;
    height: 25px;
    float: left;
    background-color: orange;
    color: white;
    line-height: 25px;
    margin-left: 10px;
    margin-top: -3px;
    border-radius: 5px;
    text-align: center;
}

.form_block {
    margin-top: 5px;
    width: 100%;
    float: left;
}

.form_block_content {
    width: 7%;
    min-width: 70px;
    float: left;
    margin-left: 25px;
}
.form_block_content_Space {
    width: 80%;
    min-width: 110px;
    float: left;
}
.form_block_content_Input {
    width: 548px;
    float: left;
    height: 25px;
    top: -3px;
}
.calendar_area {
    width: 10%;
    float: left;
    min-width: 120px;
}
.calendar_datePicker {
    height: 25px;
    width: 130px;
    top: -3px;
}
.hour_area {
    width: 10%;
    float: left;
    margin-left: 5px;
}
.hour_select {
    height: 25px;
    min-width: 80px;
    top: -3px;
}
.form_endTime {
    width: 70px;
    float: left;
    margin-left: 68px;
}

.form_vacationTime_area {
    margin-top: 5px;
    width: 100%;
    float: left;
    margin-left: 25px;
}
.form_vacationTime_title {
    float: left;
    width: 70px;
    margin-left: 0px;
}
.form_vacationTime_icon {
    float: left;
    margin-left: -25px;
}
.form_vacationTime_data {
    float: left;
    width: 12px;
    margin-left: 5px;
    padding-top: 1px;
}
.form_relax_button {
    float: left;
    background-color: #63B8FF;
    margin-left: 27px;
    padding-top:1px;
    margin-top: 0px;
    width: 45px;
    height: 22px;
    color: white;
    text-align: center;
    text-decoration: none;
    font-size: 12px;
    line-height: 20px;
    text-indent: 0px;
    border-radius: 5px;
    font-weight: bold;
}
.form_work_button {
    float: left;
    margin-left: 27px;
    padding-top:1px;
    margin-top: 0px;
    width: 45px;
    height: 22px;
    color: white;
    text-align: center;
    text-decoration: none;
    font-size: 12px;
    line-height: 20px;
    text-indent: 0px;
    border-radius: 5px;
    font-weight: bold;
    background-color: #FF3030;
}
.form_illness_button {
    float: left;
    background-color: orange;
    margin-left: 27px;
    padding-top:1px;
    margin-top: 0px;
    width: 45px;
    height: 22px;
    color: white;
    text-align: center;
    text-decoration: none;
    font-size: 12px;
    line-height: 20px;
    text-indent: 0px;
    border-radius: 5px;
    font-weight: bold;
}
.form_pregnant_button {
    float: left;
    background-color: blue;
    margin-left: 27px;
    padding-top:1px;
    margin-top: 0px;
    width: 45px;
    height: 22px;
    color: white;
    text-align: center;
    text-decoration: none;
    font-size: 12px;
    line-height: 20px;
    text-indent: 0px;
    border-radius: 5px;
    font-weight: bold;
}

.form_project_area {
    margin-top: 10px;
    width: 100%;
    float: left;
    margin-left: 25px;
}
.form_project_title {
    float: left;
    width: 70px;
    margin-left: 0px;
}
.form_project_content {
    float: left;
    width: 93%;
    margin-left: -55px;
}
.form_project_content_space {
    float: left;
    width: 600px;
    height: 25px;
    margin-left: 55px;
    font-size: 12px;
}
.form_project_content_cascader {
    width: 600px;
    top: -3px;
    float: left;
    height: 32px;
    font-size: 12px;
}
.form_project_minus {
    display: flex;
    float: left;
    width: 25px;
    margin-left: 2px;
    height: 25px;
    background-color: grey;
    color: white;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    position: relative;
    top: -3px;
}
.form_project_plus {
    display: flex;
    float: left;
    width: 25px;
    height: 25px;
    background-color: purple;
    color: white;
    justify-content: center;
    align-items: center;
    margin-left: 2px;
    border-radius: 6px;
    position: relative;
    top: -3px;
}

.form_department {
    margin-top: 10px;
    width: 100%;
    float: left;
    margin-left: 25px;
}
.form_department_title {
    float: left;
    width: 70px;
}
.form_department_content {
    float: left;
    width: 80%;
}
.form_department_tag {
    float: left;
    height: 25px;
    line-height: 25px;
    margin-left: 2px;
    top: -2px;
    margin-top:2px;
}

.form_commit_area {
    margin-top: 6px;
    width: 100%;
    float: left;
}
.form_commit_button {
    margin-left: 95px;
    width: 200px;
    float: left;
}
.form_cancel_button {
    margin-left: 145px;
    width: 200px;
    float: left;
}
.deleteWarn {
    width: 400px;
    height: 200px;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    background-color: #fff;
    border-width: 1px;
    border-color: #2B99FF;
    border-radius: 10px;
}
.changeForm_title {
    color: rgb(79, 129, 238);
    font-size: 15px;
    float: left;
    padding-top: 15px;
    margin-left: 30px;
}
.changeForm_tab {
    width: 500px;
    border-color:#63B8FF;
    border-width: 2px;
    border-bottom-width: 1px;
    border-top-width: 1px;
    border-bottom-color: #63B8FF;
    height: 30px;
    float: left;
}
.deleteSure {
    float: left;
    width: 130px;
    height: 25px;
    margin-top: 12px;
    margin-left: 20px;
    text-align: center;
    background: -webkit-gradient(linear, 100% 0%, 0% 0%, from(rgb(246,107,166)), to(rgb(246,87,119)));
    font-weight: bolder;
    color: white;
    background-color: #1E90FF;
    padding-top: -5px;
}
.deleteCancel {
    float: left;
    width: 130px;
    height: 25px;
    margin-top: 12px;
    margin-left: 50px;
    text-align: center;
    background: -webkit-gradient(linear, 100% 0%, 0% 0%, from(rgb(157,169,207)), to(rgb(224,236,232)));
    font-weight: bolder;
    color: white;
    background-color: grey;
}
.deleteWarnArea {
    background-color: #fff;
    margin-top: 40px;
    margin-left: 20px;
    width: 350px;
    height: 150px;
}
.confirmText {
    float: left;
    width: 100px;
    margin-left: 125px;
    text-align: center;
}
.autoInput {
    float: left;
    width: 100px;
    margin-left: 10px;
    margin-top: -2px;
    color: #1E90FF;
}

.row {
    width: 15%;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    mask-type: flex;
    justify-content: center;
    align-items: middle;
    margin-bottom: 5px;
    height: 40px;
    background-color:#4f81ee;
    color: white;
    font-size: 14px;
    font-weight: bold;
}
.headText {
    margin-left: 35%;
}

.changeForm_select {
    height: 20px;
    margin-left: -33px;
}

.depart_leader {
    width: 100px;
    float: left;
    margin-left: 0px;
}

.pagination_div {
    width: 100%;
    bottom: 15px;
}

.pagination {
    text-align: center;
    width: 100%;
    margin-top: 10px;
}
