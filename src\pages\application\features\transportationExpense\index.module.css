/* 响应式记录容器 - 移除最大宽度限制，确保列表项完全显示 */
.record_box {
    background: var(--bg-primary);
    width: 100%;
    min-width: auto; /* 移除固定最小宽度限制 */
    height: 96%;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    line-height: 35px;
    margin-top: var(--spacing-lg);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.record_small {
    background: var(--bg-primary);
    width: 100%;
    min-width: auto; /* 移除固定最小宽度限制 */
    height: 58%;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    line-height: 35px;
    margin-top: var(--spacing-lg);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 移动端容器优化 */
@media (max-width: 768px) {
    .record_box,
    .record_small {
        width: 100%;
        margin-top: var(--spacing-md);
        border-radius: var(--border-radius-md);
        height: auto;
        min-height: 400px;
    }
}

@media (max-width: 480px) {
    .record_box,
    .record_small {
        margin-top: var(--spacing-sm);
        border-radius: var(--border-radius-sm);
        min-height: 350px;
    }
}
/* 响应式标题样式 - 使用CSS变量 */
.record_title {
    width: auto;
    min-width: 120px;
    background: var(--bg-gradient-primary);
    border-radius: var(--border-radius-md);
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0 var(--spacing-lg);
    box-shadow: var(--shadow-md);
    position: relative;
    transition: var(--transition-normal);
    margin: var(--spacing-lg);
    flex-shrink: 0;
}

.record_title::after {
    content: '';
    position: absolute;
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid var(--color-primary);
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
}

.record_title:hover {
    background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.record_title_font {
    color: var(--text-white);
    font-size: var(--font-size-md);
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* 移动端标题优化 */
@media (max-width: 768px) {
    .record_title {
        margin: var(--spacing-md);
        padding: 0 var(--spacing-md);
        height: 36px;
    }

    .record_title_font {
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 480px) {
    .record_title {
        margin: var(--spacing-sm);
        padding: 0 var(--spacing-sm);
        height: 32px;
    }

    .record_title::after {
        border-left-width: 8px;
        border-top-width: 8px;
        border-bottom-width: 8px;
        right: -8px;
    }
}
/* 响应式按钮样式 */
.record_title_btn {
    margin-top: var(--spacing-sm);
    margin-left: auto;
    width: 150px;
    height: 36px;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-white);
    border: none;
    cursor: pointer;
    transition: var(--transition-normal);
    background: var(--bg-gradient-primary);
    box-shadow: var(--shadow-sm);
}

.record_title_btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    filter: brightness(1.1);
}

.record_title_btn:active {
    transform: translateY(0);
    filter: brightness(0.9);
}

/* 响应式记录区域 - 确保列表项完全显示 */
.record_all {
    width: 100%;
    padding: 0 var(--spacing-xl);
    overflow-y: auto;
    overflow-x: auto; /* 允许横向滚动确保列表项完全显示 */
    flex: 1;
    margin-top: var(--spacing-sm);
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

/* 移动端记录区域优化 */
@media (max-width: 768px) {
    .record_title_btn {
        width: 120px;
        height: 32px;
        font-size: var(--font-size-xs);
    }

    .record_all {
        padding: 0 var(--spacing-md);
        /* 移动端确保横向滚动可见 */
        overflow-x: scroll;
        scrollbar-width: thin;
        scrollbar-color: var(--color-primary-light) var(--bg-secondary);
    }

    .record_all::-webkit-scrollbar {
        height: 8px;
    }

    .record_all::-webkit-scrollbar-track {
        background: var(--bg-secondary);
        border-radius: 4px;
    }

    .record_all::-webkit-scrollbar-thumb {
        background: var(--color-primary-light);
        border-radius: 4px;
    }

    .record_all::-webkit-scrollbar-thumb:hover {
        background: var(--color-primary);
    }
}

@media (max-width: 480px) {
    .record_title_btn {
        width: 100px;
        height: 28px;
        margin-top: var(--spacing-xs);
    }

    .record_all {
        padding: 0 var(--spacing-sm);
    }
}
/* 响应式记录行样式 - 使用现代布局 */
.record_row {
    width: 100%;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    display: flex;
    flex-direction: column;
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--bg-primary);
    transition: var(--transition-normal);
    min-width: 0; /* 确保可以收缩 */
}

.record_row:hover {
    background: linear-gradient(135deg, #e8f2ff 0%, #f0f4ff 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.record_value {
    display: flex;
    align-items: center;
    margin: var(--spacing-xs) 0;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.record_user {
    display: flex;
    align-items: center;
    margin: var(--spacing-xs) 0;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.value_color {
    color: var(--text-secondary);
    margin: var(--spacing-xs) 0;
    font-size: var(--font-size-sm);
}

.record_data {
    padding: var(--spacing-lg) 0;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

/* 移动端记录行优化 */
@media (max-width: 768px) {
    .record_row {
        margin-bottom: var(--spacing-xs);
        padding: var(--spacing-sm);
        border-radius: var(--border-radius-sm);
    }

    .record_value,
    .record_user,
    .value_color {
        font-size: var(--font-size-xs);
        margin: var(--spacing-xs) 0;
    }

    .record_data {
        padding: var(--spacing-md) 0;
        gap: var(--spacing-xs);
    }
}

/* 响应式表单标题 */
.form_title {
    width: auto;
    min-width: 150px;
    border-radius: var(--border-radius-md);
    height: 35px;
    padding: var(--spacing-sm) var(--spacing-xl);
    background-color: var(--bg-primary);
    border-top-left-radius: var(--border-radius-md);
    border-top-right-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    box-shadow: var(--shadow-sm);
}

.form_title_font {
    color: var(--color-primary);
    font-size: var(--font-size-md);
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* 响应式表单内容 - 移除固定宽度限制 */
.form_content {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: none; /* 移除最大宽度限制 */
    min-width: auto; /* 移除最小宽度限制 */
    background-color: var(--bg-primary);
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.form_text {
    width: 100%;
    background-color: var(--bg-primary);
    margin: var(--spacing-md) 0;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

/* 移动端表单优化 */
@media (max-width: 768px) {
    .form_title {
        padding: var(--spacing-sm) var(--spacing-md);
        height: 32px;
    }

    .form_title_font {
        font-size: var(--font-size-sm);
    }

    .form_content,
    .form_text {
        padding: var(--spacing-md);
        border-radius: var(--border-radius-sm);
    }
}

@media (max-width: 480px) {
    .form_title {
        padding: var(--spacing-xs) var(--spacing-sm);
        height: 28px;
    }

    .form_content,
    .form_text {
        padding: var(--spacing-sm);
        margin: var(--spacing-sm) 0;
    }
}
/* 响应式表单文本区域 */
.form_text_title {
    height: 30px;
    width: 100%;
    display: flex;
    align-items: center;
    margin: var(--spacing-sm) 0;
    padding: 0 var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.form_text_content {
    max-height: 420px;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* 响应式表单人员区域 */
.form_content_people {
    margin: var(--spacing-md) 0;
    width: 100%;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.form_content_people_undo {
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.form_content_people_undo:hover {
    background: var(--bg-secondary);
}

.form_content_people_title {
    min-width: 70px;
    font-weight: 500;
    color: var(--text-primary);
    flex-shrink: 0;
}

.form_content_people_default {
    height: 32px;
    width: 150px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 0 var(--spacing-sm);
    background: var(--bg-primary);
}

.form_content_people_buttonSwitch {
    width: 32px;
    height: 32px;
    background-color: var(--color-warning);
    color: var(--text-white);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-normal);
    border: none;
    font-weight: 600;
}

.form_content_people_buttonSwitch:hover {
    background-color: var(--color-warning-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* 移动端表单人员区域优化 */
@media (max-width: 768px) {
    .form_content_people {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }

    .form_content_people_default {
        width: 100%;
        height: 36px;
    }

    .form_content_people_buttonSwitch {
        width: 36px;
        height: 36px;
    }
}

/* 响应式表单块布局 */
.form_block {
    margin: var(--spacing-sm) 0;
    width: 100%;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.form_block_content {
    min-width: 70px;
    font-weight: 500;
    color: var(--text-primary);
    flex-shrink: 0;
}

.form_block_content_Space {
    flex: 1;
    min-width: 110px;
}

.form_block_content_Input {
    width: 100%;
    max-width: 548px;
    height: 32px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 0 var(--spacing-sm);
    background: var(--bg-primary);
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
}

.form_block_content_Input:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    outline: none;
}

/* 响应式日历和时间选择区域 */
.calendar_area {
    min-width: 120px;
    flex-shrink: 0;
}

.calendar_datePicker {
    height: 32px;
    width: 130px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 0 var(--spacing-sm);
    background: var(--bg-primary);
}

.hour_area {
    min-width: 80px;
    flex-shrink: 0;
}

.hour_select {
    height: 32px;
    min-width: 80px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 0 var(--spacing-sm);
    background: var(--bg-primary);
}

.form_endTime {
    min-width: 70px;
    font-weight: 500;
    color: var(--text-primary);
    flex-shrink: 0;
}

/* 移动端表单块优化 */
@media (max-width: 768px) {
    .form_block {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .form_block_content_Input,
    .calendar_datePicker,
    .hour_select {
        width: 100%;
        height: 36px;
    }

    .calendar_area,
    .hour_area {
        width: 100%;
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .form_block_content_Input,
    .calendar_datePicker,
    .hour_select {
        height: 40px; /* 移动端更大的触摸目标 */
    }
}

/* 响应式假期时间区域 */
.form_vacationTime_area {
    margin: var(--spacing-sm) 0;
    width: 100%;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
    padding: 0 var(--spacing-lg);
}

.form_vacationTime_title {
    min-width: 70px;
    font-weight: 500;
    color: var(--text-primary);
    flex-shrink: 0;
}

.form_vacationTime_icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.form_vacationTime_data {
    min-width: 12px;
    display: flex;
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

/* 响应式按钮组 */
.form_relax_button,
.form_work_button,
.form_illness_button,
.form_pregnant_button {
    min-width: 60px;
    height: 32px;
    color: var(--text-white);
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    padding: 0 var(--spacing-sm);
}

.form_relax_button {
    background-color: #63B8FF;
}

.form_work_button {
    background-color: #FF3030;
}

.form_illness_button {
    background-color: var(--color-warning);
}

.form_pregnant_button {
    background-color: var(--color-primary);
}

.form_relax_button:hover,
.form_work_button:hover,
.form_illness_button:hover,
.form_pregnant_button:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
    filter: brightness(1.1);
}

/* 移动端按钮优化 */
@media (max-width: 768px) {
    .form_vacationTime_area {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
        padding: 0 var(--spacing-md);
    }

    .form_relax_button,
    .form_work_button,
    .form_illness_button,
    .form_pregnant_button {
        width: 100%;
        height: 36px;
        margin: var(--spacing-xs) 0;
    }
}

/* 响应式项目区域 */
.form_project_area {
    margin: var(--spacing-sm) 0;
    width: 100%;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
    padding: 0 var(--spacing-lg);
}

.form_project_title {
    min-width: 70px;
    font-weight: 500;
    color: var(--text-primary);
    flex-shrink: 0;
}

.form_project_content {
    flex: 1;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 0;
}

.form_project_content_space {
    flex: 1;
    min-width: 200px;
    height: 32px;
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 0 var(--spacing-sm);
    background: var(--bg-primary);
}

.form_project_content_cascader {
    flex: 1;
    min-width: 200px;
    height: 32px;
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background: var(--bg-primary);
}

.form_project_minus,
.form_project_plus {
    display: flex;
    width: 32px;
    height: 32px;
    color: var(--text-white);
    justify-content: center;
    align-items: center;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition-normal);
    border: none;
    font-weight: 600;
}

.form_project_minus {
    background-color: var(--color-secondary);
}

.form_project_plus {
    background-color: var(--color-primary);
}

.form_project_minus:hover,
.form_project_plus:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
    filter: brightness(1.1);
}

/* 响应式部门区域 */
.form_department {
    margin: var(--spacing-sm) 0;
    width: 100%;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
    padding: 0 var(--spacing-lg);
}

.form_department_title {
    min-width: 70px;
    font-weight: 500;
    color: var(--text-primary);
    flex-shrink: 0;
}

.form_department_content {
    flex: 1;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.form_department_tag {
    height: 32px;
    line-height: 32px;
    padding: 0 var(--spacing-sm);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

/* 移动端项目和部门区域优化 */
@media (max-width: 768px) {
    .form_project_area,
    .form_department {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
        padding: 0 var(--spacing-md);
    }

    .form_project_content,
    .form_department_content {
        width: 100%;
        flex-direction: column;
        align-items: stretch;
    }

    .form_project_content_space,
    .form_project_content_cascader {
        width: 100%;
        min-width: auto;
    }

    .form_project_minus,
    .form_project_plus {
        width: 36px;
        height: 36px;
    }

    .form_department_tag {
        height: 36px;
        line-height: 36px;
    }
}

/* 响应式提交按钮区域 */
.form_commit_area {
    margin: var(--spacing-lg) 0;
    width: 100%;
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

.form_commit_button,
.form_cancel_button {
    width: 200px;
    height: 40px;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.form_commit_button {
    background: var(--bg-gradient-primary);
    color: var(--text-white);
    box-shadow: var(--shadow-md);
}

.form_cancel_button {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.form_commit_button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    filter: brightness(1.1);
}

.form_cancel_button:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-color-dark);
}

/* 移动端按钮优化 */
@media (max-width: 768px) {
    .form_commit_area {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }

    .form_commit_button,
    .form_cancel_button {
        width: 100%;
        height: 44px;
    }
}

@media (max-width: 480px) {
    .form_commit_button,
    .form_cancel_button {
        height: 48px; /* 移动端更大的触摸目标 */
    }
}
/* 响应式删除确认对话框 */
.deleteWarn {
    width: 90%;
    max-width: 400px;
    min-height: 200px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--bg-primary);
    border: 1px solid var(--color-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: var(--z-modal);
    padding: var(--spacing-lg);
}

.changeForm_title {
    color: var(--color-primary);
    font-size: var(--font-size-md);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.changeForm_tab {
    width: 100%;
    max-width: 500px;
    border: 2px solid #63B8FF;
    border-radius: var(--border-radius-sm);
    height: 32px;
    background: var(--bg-primary);
    margin: var(--spacing-md) 0;
}

.deleteSure,
.deleteCancel {
    width: 130px;
    height: 36px;
    margin: var(--spacing-md) var(--spacing-sm);
    text-align: center;
    font-weight: 600;
    color: var(--text-white);
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.deleteSure {
    background: var(--bg-gradient-primary);
}

.deleteCancel {
    background: var(--color-secondary);
}

.deleteSure:hover,
.deleteCancel:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
    filter: brightness(1.1);
}

.deleteWarnArea {
    background-color: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-md);
    text-align: center;
}

.confirmText {
    width: auto;
    text-align: center;
    margin: var(--spacing-md) 0;
    font-size: var(--font-size-md);
    color: var(--text-primary);
}

.autoInput {
    width: auto;
    min-width: 100px;
    margin: var(--spacing-sm);
    color: var(--color-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--bg-primary);
}

.changeForm_select {
    height: 32px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background: var(--bg-primary);
    padding: 0 var(--spacing-sm);
}

.depart_leader {
    min-width: 100px;
    font-weight: 500;
    color: var(--text-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
}

/* 移动端对话框优化 */
@media (max-width: 768px) {
    .deleteWarn {
        width: 95%;
        padding: var(--spacing-md);
    }

    .deleteSure,
    .deleteCancel {
        width: 100%;
        margin: var(--spacing-sm) 0;
        height: 40px;
    }

    .changeForm_tab {
        height: 36px;
    }

    .changeForm_select {
        height: 36px;
    }
}

@media (max-width: 480px) {
    .deleteSure,
    .deleteCancel {
        height: 44px; /* 移动端更大的触摸目标 */
    }

    .changeForm_tab,
    .changeForm_select {
        height: 40px;
    }
}

/* 响应式分页样式 */
.pagination_div {
    width: 100%;
    padding: var(--spacing-lg) 0;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    position: sticky;
    bottom: 0;
    z-index: var(--z-sticky);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.pagination {
    text-align: center;
    width: 100%;
    margin: 0;
}

/* 移动端分页优化 */
@media (max-width: 768px) {
    .pagination_div {
        padding: var(--spacing-md) 0;
    }
}

/* ========================================
   响应式工具类 - 确保列表项完全显示
   ======================================== */

/* 横向滚动容器 - 确保列表项完全显示 */
.horizontal_scroll_container {
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: var(--color-primary-light) var(--bg-secondary);
}

.horizontal_scroll_container::-webkit-scrollbar {
    height: 8px;
}

.horizontal_scroll_container::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

.horizontal_scroll_container::-webkit-scrollbar-thumb {
    background: var(--color-primary-light);
    border-radius: 4px;
}

.horizontal_scroll_container::-webkit-scrollbar-thumb:hover {
    background: var(--color-primary);
}

/* 响应式表格容器 */
.responsive_table_container {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

/* 移动端卡片视图 */
.mobile_card_view {
    display: none;
}

@media (max-width: 768px) {
    .desktop_table_view {
        display: none;
    }

    .mobile_card_view {
        display: block;
    }

    .mobile_card_item {
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-md);
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-sm);
        box-shadow: var(--shadow-sm);
    }

    .mobile_card_item:hover {
        background: linear-gradient(135deg, #e8f2ff 0%, #f0f4ff 100%);
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }
}

/* 响应式网格布局 */
.responsive_grid {
    display: grid;
    gap: var(--spacing-md);
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (max-width: 768px) {
    .responsive_grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
}

/* 响应式间距工具类 */
.spacing_responsive {
    padding: var(--spacing-lg);
}

@media (max-width: 768px) {
    .spacing_responsive {
        padding: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .spacing_responsive {
        padding: var(--spacing-sm);
    }
}

/* 响应式文字大小 */
.text_responsive {
    font-size: var(--font-size-md);
}

@media (max-width: 768px) {
    .text_responsive {
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 480px) {
    .text_responsive {
        font-size: var(--font-size-xs);
    }
}

/* 响应式表格行样式 */
.row {
    width: auto;
    min-width: 120px;
    border-radius: var(--border-radius-sm) 0 0 var(--border-radius-sm);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    height: 40px;
    background: var(--bg-gradient-primary);
    color: var(--text-white);
    font-size: var(--font-size-md);
    font-weight: 600;
    padding: 0 var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.headText {
    text-align: center;
    flex: 1;
}

/* 移动端行样式优化 */
@media (max-width: 768px) {
    .row {
        width: 100%;
        height: 36px;
        font-size: var(--font-size-sm);
        padding: 0 var(--spacing-md);
        border-radius: var(--border-radius-sm);
    }
}
