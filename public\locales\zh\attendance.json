{"myAttendance": {"title": "我的考勤", "normal": "正常", "abnormal": "异常", "absent": "缺勤", "absentHours": "缺勤{{hours}}小时", "insufficientLeave": "※请假时长不足", "noLeave": "※未请假", "pendingApproval": "※申请审批中"}, "query": {"title": "考勤查询", "directedQuery": "考勤|指定查询", "queryButton": "查询", "defaultDays": "考勤默认查询天数", "saveChanges": "保存修改"}, "edit": {"title": "考勤编辑", "processComplete": "处理完成"}, "import": {"title": "考勤导入", "dataImport": "考勤|数据导入", "selectFile": "选择文件", "startProcessing": "开始处理", "attention": "注意", "attentionContent1": "1.考勤文件中最大日期不能超过", "attentionContent2": "2.请下载模板文件后按照规则输入考勤数据，否则批量处理将会出错", "downloadTemplate": "下载模板", "processingProgress": "处理进度", "processingContent": "正在处理，请稍后......", "inputRules": "输入规则", "accessControlData": "门禁数据", "homeOfficeData": "居家办公数据", "paperDocumentData": "纸质单据数据", "latestDataTime": "当前数据已更新至", "importNewData": "导入新数据"}, "workTime": {"clockIn": "上班打卡", "clockOut": "下班打卡", "clockInCorrection": "上班修正", "clockOutCorrection": "下班修正", "noClockRecord": "当日无打卡记录"}, "leaveTypes": {"compensatoryLeave": "调休", "personalLeave": "事假", "sickLeave": "病假", "otherLeave": "其他", "businessTrip": "出差"}, "overtimeTypes": {"weekday": "平时", "weekend": "周末", "holiday": "假日"}, "timeLabels": {"clockIn": "上班", "clockOut": "下班"}, "rules": {"rule1": "1）工号、姓名、考勤日期均需要填写", "rule2": "2）允许填写的考勤日期如 2024/03/01 或 2024-03-01", "rule3": "3）上下班时间和请假时间至少填写一个", "rule4": "4）上下班时间需要同时为空或同时存在内容", "rule5": "5）上班时间允许填写的格式如 17:00", "rule6": "6）下班时间允许填写的格式如 17:00 或 2024/03/01 17:00(日期格式需符合第2条)", "rule7": "7）下班时间可以跨1天，但禁止超过第二天9时", "rule8": "8）调休可不填写。若填写，仅允许[0,4,8]范围内的值", "rule9": "9）病假/事假/出差可不填写。若填写，仅允许[0～8]范围内的值", "rule10": "10）请假总时长不可超过8小时", "rule11": "11）出错的数据将以文件形式返回。请注意下载的文件"}, "viewMode": {"table": "表格视图", "calendar": "日历视图"}, "status": {"exception": "异常", "confirm": "确认", "weekendHoliday": "周末/祝日", "normal": "正常"}, "calendar": {"monthView": "月视图", "yearView": "年视图", "attendanceDetails": "考勤详情", "noTime": "无时间", "noRecord": "无记录", "modifiedBy": "修改者"}, "homeOfficeRules": {"rule1": "1）所有项目均需要填写", "rule2": "2）允许填写的日期如 2024/03/01 或 2024-03-01", "rule3": "3）允许填写的日期禁止超过已导入考勤的最大日期", "rule4": "4）允许填写的时间如 17:00 或 2024/03/01 17:00(日期格式需符合第2条)", "rule5": "5）下班时间可以跨1天，但禁止超过第二天9时", "rule6": "6）居家办公数据重复的只保留第一条数据", "rule7": "7）上级确认项目内容将不做检查，请确保内容真实", "rule8": "8）出错的数据将以文件形式返回。请注意下载的文件"}, "importPrompts": {"prompt1": "■导入新数据前请先提取门禁数据", "prompt1_1": "1.进门禁系统，选取所有门，并\"提取数据\"", "prompt1_2": "2.确保所有门禁数据提取成功", "prompt1_3": "3.进本考勤系统，点\"导入新数据\"", "prompt1_4": "※新旧门禁系统的数据都需要提取。如果只提取一份数据将无法导入。", "prompt2": "■工号变更", "prompt2_1": "1.确保每位员工只有一个门禁卡有工号信息", "prompt2_2": "2.如果员工更换了门禁卡，请及时更新新旧门禁系统中对应门禁卡的工号信息"}}