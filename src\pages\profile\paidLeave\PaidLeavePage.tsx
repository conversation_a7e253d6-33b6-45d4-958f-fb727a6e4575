import React from 'react'
import { Card, Typography } from 'antd'
import { useTranslation } from '../../../hooks/useTranslation'

const { Title } = Typography

const PaidLeavePage: React.FC = () => {
  const { t } = useTranslation()

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Title level={2}>{t('profile.paidLeave.title', '有薪假期')}</Title>
        <p>这里是有薪假期页面的内容</p>
        <p>路径: /profile/paidLeave</p>
        {/* TODO: 从原 pages/profile/paidLeave 迁移具体内容 */}
      </Card>
    </div>
  )
}

export default PaidLeavePage
