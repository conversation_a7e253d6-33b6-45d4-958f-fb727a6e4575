import React from 'react'
import { Card, Typography } from 'antd'
import { useTranslation } from '../../../hooks/useTranslation'

const { Title } = Typography

const AttendanceImport1Page: React.FC = () => {
  const { t } = useTranslation()

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Title level={2}>{t('attendance.myRecord.title', '我的考勤记录')}</Title>
        <p>这里是考勤记录页面的内容</p>
        <p>路径: /attendance/attendanceimport1</p>
        <p>短链接: /record/my</p>
        {/* TODO: 从原 pages/attendance/attendanceimport1 迁移具体内容 */}
      </Card>
    </div>
  )
}

export default AttendanceImport1Page
