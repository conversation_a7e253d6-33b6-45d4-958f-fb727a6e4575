import React from 'react';
import { Clock, User, LogOut } from 'lucide-react';
import type { Page } from './Layout';
import type { User as UserType } from '../types/auth';

interface SidebarProps {
  currentPage: Page;
  onPageChange: (page: Page) => void;
  user: UserType;
  onLogout: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ currentPage, onPageChange, user, onLogout }) => {
  const menuItems = [
    {
      id: 'clock-in' as Page,
      label: '打卡',
      icon: Clock,
    },
    {
      id: 'profile' as Page,
      label: '我的',
      icon: User,
    },
  ];

  const handleLogout = () => {
    if (window.confirm('确定要退出系统吗？')) {
      onLogout();
    }
  };

  return (
    <div className="w-80 bg-gradient-to-b from-slate-800 to-slate-900 text-white shadow-2xl flex flex-col">
      {/* Header */}
      <div className="p-8 border-b border-slate-700">
        <div className="mb-8">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-pink-300 to-rose-300 bg-clip-text text-transparent">
            打卡系统
          </h1>
        </div>
        
        {/* User Info */}
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
            <span className="text-2xl font-bold text-white">
              {user.name.charAt(0)}
            </span>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">{user.name}</h3>
            <p className="text-slate-300 text-sm">{user.department}</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="p-6 flex-1">
        <div className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = currentPage === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => onPageChange(item.id)}
                className={`w-full flex items-center gap-4 px-6 py-4 rounded-xl transition-all duration-200 ${
                  isActive
                    ? 'bg-gradient-to-r from-pink-500 to-rose-500 text-white shadow-lg transform scale-105'
                    : 'text-slate-300 hover:bg-slate-700/50 hover:text-white'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium">{item.label}</span>
              </button>
            );
          })}
        </div>
      </nav>

      {/* Logout Button */}
      <div className="p-6 border-t border-slate-700">
        <button
          onClick={handleLogout}
          className="w-full flex items-center gap-4 px-6 py-4 rounded-xl text-slate-300 hover:bg-red-600/20 hover:text-red-300 transition-all duration-200 group"
        >
          <LogOut className="w-5 h-5 group-hover:rotate-12 transition-transform duration-200" />
          <span className="font-medium">退出登录</span>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;