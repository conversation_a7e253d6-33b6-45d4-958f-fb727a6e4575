{"myAttendance": {"title": "私の勤怠", "normal": "正常", "abnormal": "異常", "absent": "欠勤", "absentHours": "欠勤{{hours}}時間", "insufficientLeave": "※休暇時間不足", "noLeave": "※未申請", "pendingApproval": "※申請承認中"}, "query": {"title": "勤怠照会", "directedQuery": "勤怠|指定照会", "queryButton": "検索", "defaultDays": "勤怠デフォルト照会日数", "saveChanges": "変更を保存"}, "edit": {"title": "勤怠編集", "processComplete": "処理完了"}, "import": {"title": "勤怠インポート", "dataImport": "勤怠|データインポート", "selectFile": "ファイル選択", "startProcessing": "処理開始", "attention": "注意", "attentionContent1": "1.勤怠ファイル内の最大日付は以下を超えてはいけません", "attentionContent2": "2.テンプレートファイルをダウンロードしてルールに従って勤怠データを入力してください。そうでなければ一括処理でエラーが発生します", "downloadTemplate": "テンプレートダウンロード", "processingProgress": "処理進捗", "processingContent": "処理中です。しばらくお待ちください......", "inputRules": "入力ルール", "accessControlData": "入退室データ", "homeOfficeData": "在宅勤務データ", "paperDocumentData": "紙文書データ", "latestDataTime": "現在のデータは以下まで更新されています", "importNewData": "新しいデータをインポート"}, "workTime": {"clockIn": "出勤打刻", "clockOut": "退勤打刻", "clockInCorrection": "出勤修正", "clockOutCorrection": "退勤修正", "noClockRecord": "当日打刻記録なし"}, "leaveTypes": {"compensatoryLeave": "振替休日", "personalLeave": "私用休暇", "sickLeave": "病気休暇", "otherLeave": "その他", "businessTrip": "出張"}, "overtimeTypes": {"weekday": "平日", "weekend": "週末", "holiday": "祝日"}, "timeLabels": {"clockIn": "出勤", "clockOut": "退勤"}, "rules": {"rule1": "1）社員番号、氏名、勤怠日付はすべて入力が必要です", "rule2": "2）入力可能な勤怠日付の形式：2024/03/01 または 2024-03-01", "rule3": "3）出退勤時間と休暇時間のうち少なくとも一つは入力が必要です", "rule4": "4）出退勤時間は同時に空白か同時に内容が存在する必要があります", "rule5": "5）出勤時間の入力可能な形式：17:00", "rule6": "6）退勤時間の入力可能な形式：17:00 または 2024/03/01 17:00（日付形式は第2条に準拠）", "rule7": "7）退勤時間は1日をまたぐことができますが、翌日9時を超えることは禁止されています", "rule8": "8）振替休日は入力不要です。入力する場合は[0,4,8]の範囲内の値のみ許可されます", "rule9": "9）病気休暇/事務休暇/出張は入力不要です。入力する場合は[0～8]の範囲内の値のみ許可されます", "rule10": "10）休暇の総時間は8時間を超えることはできません", "rule11": "11）エラーデータはファイル形式で返されます。ダウンロードファイルにご注意ください"}, "homeOfficeRules": {"rule1": "1）すべての項目の入力が必要です", "rule2": "2）日付は 2024/03/01 または 2024-03-01 の形式で入力してください", "rule3": "3）入力可能な日付は、既にインポートされた勤怠の最大日付を超えてはいけません", "rule4": "4）時間は 17:00 または 2024/03/01 17:00 の形式で入力してください（日付形式は第2条に準拠）", "rule5": "5）退勤時間は1日をまたぐことができますが、翌日9時を超えてはいけません", "rule6": "6）在宅勤務データが重複している場合は、最初のデータのみ保持されます", "rule7": "7）上司確認項目の内容はチェックされません。内容が正確であることを確認してください", "rule8": "8）エラーデータはファイル形式で返されます。ダウンロードファイルにご注意ください"}, "importPrompts": {"prompt1": "■新しいデータをインポートする前に入退室データを抽出してください", "prompt1_1": "1.入退室システムに入り、すべてのドアを選択して\"データ抽出\"を行う", "prompt1_2": "2.すべての入退室データの抽出が成功していることを確認する", "prompt1_3": "3.本勤怠システムに入り、\"新しいデータをインポート\"をクリックする", "prompt1_4": "※新旧入退室システムのデータを両方とも抽出する必要があります。一つのデータのみ抽出した場合はインポートできません。", "prompt2": "■社員番号変更", "prompt2_1": "1.各従業員が社員番号情報を持つ入退室カードを一つだけ持っていることを確認する", "prompt2_2": "2.従業員が入退室カードを交換した場合は、新旧入退室システムの対応する入退室カードの社員番号情報を速やかに更新する"}, "viewMode": {"table": "テーブルビュー", "calendar": "カレンダービュー"}, "status": {"exception": "異常", "confirm": "確認", "weekendHoliday": "週末/祝日", "normal": "正常"}, "calendar": {"monthView": "月表示", "yearView": "年表示", "attendanceDetails": "勤怠詳細", "noTime": "時間なし", "noRecord": "記録なし", "modifiedBy": "修正者"}}