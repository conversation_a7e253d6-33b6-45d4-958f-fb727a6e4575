/* MobileTable 组件样式 */
.mobile_table_wrapper {
  width: 100%;
  overflow-x: auto;
}

.mobile_table {
  width: 100%;
  min-width: 600px;
}

/* 移动端自定义卡片布局 */
.mobile_list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 0;
}

.mobile_card {
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.mobile_card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
  border-color: #1890ff;
}

.mobile_card:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 卡片标题区域 */
.card_title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.title_text {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  line-height: 1.4;
}

/* 卡片副标题区域 */
.card_subtitle {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.subtitle_text {
  font-size: 14px;
  color: #595959;
  line-height: 1.4;
}

/* 卡片内容区域 */
.card_content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 16px;
  margin-bottom: 12px;
}

.content_item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.content_label {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.content_value {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
  line-height: 1.4;
}

/* 卡片元信息区域 */
.card_meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.meta_item {
  font-size: 12px;
  color: #8c8c8c;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 卡片操作区域 */
.card_actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.action_item {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40px 20px;
  color: #8c8c8c;
  font-size: 14px;
}

/* 空状态 */
.empty_state {
  text-align: center;
  padding: 60px 20px;
  color: #bfbfbf;
  font-size: 14px;
}

/* 移动端分页 */
.mobile_pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

/* 移动端表头隐藏 */
@media (max-width: 768px) {
  /* 确保Ant Design表头在移动端隐藏 */
  .mobile_table_wrapper :global(.ant-table-thead) {
    display: none !important;
  }

  .mobile_table_wrapper :global(.ant-table-header) {
    display: none !important;
  }
}

/* 响应式调整 */
@media (max-width: 480px) {
  .mobile_card {
    padding: 12px;
    border-radius: 8px;
  }

  .card_content {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .title_text {
    font-size: 15px;
  }

  .subtitle_text {
    font-size: 13px;
  }

  .content_value {
    font-size: 13px;
  }
}
