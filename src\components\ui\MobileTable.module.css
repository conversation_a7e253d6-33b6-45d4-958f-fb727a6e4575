/* 移动端表格组件样式 - iOS风格 */
.mobile_table_wrapper {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.mobile_table {
  width: 100%;
  min-width: 800px;
}

/* 移动端优化 - iOS风格列表 */
@media (max-width: 768px) {
  .mobile_table_wrapper {
    overflow-x: visible;
  }
  
  .mobile_table {
    min-width: 100%;
  }
  
  /* 隐藏表头 */
  .mobile_table :global(.ant-table-thead) {
    display: none !important;
  }
  
  /* 表格容器 */
  .mobile_table :global(.ant-table) {
    border: none;
  }
  
  /* 表格行转换为iOS风格卡片 */
  .mobile_table :global(.ant-table-tbody > tr) {
    display: block !important;
    background: var(--bg-primary) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: var(--border-radius-md) !important;
    margin-bottom: var(--spacing-md) !important;
    padding: var(--spacing-md) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.2s ease !important;
  }
  
  .mobile_table :global(.ant-table-tbody > tr:hover) {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-1px) !important;
    border-color: var(--color-primary-light) !important;
  }
  
  /* 表格单元格转换为块级元素 */
  .mobile_table :global(.ant-table-tbody > tr > td) {
    display: block !important;
    border: none !important;
    padding: var(--spacing-xs) 0 !important;
    white-space: normal !important;
    max-width: none !important;
    overflow: visible !important;
    text-overflow: unset !important;
    position: relative !important;
    padding-left: 120px !important;
    min-height: 28px !important;
    line-height: 1.6 !important;
    font-size: var(--font-size-sm) !important;
  }
  
  /* 为每个单元格添加标签 - iOS风格 */
  .mobile_table :global(.ant-table-tbody > tr > td::before) {
    content: attr(data-label) !important;
    position: absolute !important;
    left: 0 !important;
    top: var(--spacing-xs) !important;
    width: 110px !important;
    font-weight: 600 !important;
    color: var(--text-secondary) !important;
    font-size: var(--font-size-xs) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    line-height: 1.6 !important;
  }
  
  /* 隐藏空单元格 */
  .mobile_table :global(.ant-table-tbody > tr > td:empty) {
    display: none !important;
  }
  
  /* 第一个单元格特殊样式 */
  .mobile_table :global(.ant-table-tbody > tr > td:first-child) {
    padding-top: var(--spacing-sm) !important;
    font-weight: 600 !important;
    color: var(--text-primary) !important;
  }
  
  .mobile_table :global(.ant-table-tbody > tr > td:first-child::before) {
    font-weight: 700 !important;
    color: var(--color-primary) !important;
  }
  
  /* 最后一个单元格特殊样式 */
  .mobile_table :global(.ant-table-tbody > tr > td:last-child) {
    padding-bottom: var(--spacing-sm) !important;
    border-bottom: none !important;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .mobile_table :global(.ant-table-tbody > tr) {
    margin-bottom: var(--spacing-sm) !important;
    padding: var(--spacing-sm) !important;
  }
  
  .mobile_table :global(.ant-table-tbody > tr > td) {
    padding-left: 100px !important;
    min-height: 24px !important;
    font-size: var(--font-size-xs) !important;
  }
  
  .mobile_table :global(.ant-table-tbody > tr > td::before) {
    width: 90px !important;
    font-size: 10px !important;
  }
}
