import { getApi, postApi } from '@/api/fetch-api'
import ApiUrlVars from '@/api/common/url-vars'
import { statusActions } from '@/slice/statusSlice'

/**
 * 审批类型枚举
 */
export enum ApprovalType {
  LEAVE = 'leave',
  OVERTIME = 'overtime',
  BUSINESS_TRIP = 'business_trip',
  TRANSPORTATION_EXPENSE = 'transportation_expense',
  EDIT_ST_ET = 'edit_st_et'
}

/**
 * 审批操作枚举
 */
export enum ApprovalAction {
  AGREE = 1,
  REJECT = 2,
  CANCEL = 3
}

/**
 * 审批参数接口
 */
export interface ApprovalParams {
  apply_code: string
  workflow_result: ApprovalAction
  comment?: string
  user_id?: string
}

/**
 * 审批响应接口
 */
export interface ApprovalResponse {
  status: string
  message?: string
  data?: any
}

/**
 * 获取请假申请审批列表
 * @param userId 用户ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApprovalResponse>
 */
export async function getLeaveApprovalList(userId: string, dispatch: any): Promise<ApprovalResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.workflowLeaveApprove_get
    const params = { user_id: userId }
    const response = await getApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取请假审批列表失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get leave approval list API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 处理请假申请审批
 * @param params 审批参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApprovalResponse>
 */
export async function handleLeaveApproval(params: ApprovalParams, dispatch: any): Promise<ApprovalResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.workflowApprove_post
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '处理请假审批失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '审批处理成功'
    }
  } catch (error: any) {
    console.error('Handle leave approval API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取加班申请审批列表
 * @param userId 用户ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApprovalResponse>
 */
export async function getOvertimeApprovalList(userId: string, dispatch: any): Promise<ApprovalResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.workflowOvertimeApprove_get
    const params = { user_id: userId }
    const response = await getApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取加班审批列表失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get overtime approval list API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 处理加班申请审批
 * @param params 审批参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApprovalResponse>
 */
export async function handleOvertimeApproval(params: ApprovalParams, dispatch: any): Promise<ApprovalResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.workflowApprove_post
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '处理加班审批失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '审批处理成功'
    }
  } catch (error: any) {
    console.error('Handle overtime approval API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取出差申请审批列表
 * @param userId 用户ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApprovalResponse>
 */
export async function getBusinessTripApprovalList(userId: string, dispatch: any): Promise<ApprovalResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.workflowBusinessTripApprove_get
    const params = { user_id: userId }
    const response = await getApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取出差审批列表失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get business trip approval list API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 处理出差申请审批
 * @param params 审批参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApprovalResponse>
 */
export async function handleBusinessTripApproval(params: ApprovalParams, dispatch: any): Promise<ApprovalResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.workflowApprove_post
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '处理出差审批失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '审批处理成功'
    }
  } catch (error: any) {
    console.error('Handle business trip approval API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取交通费申请审批列表
 * @param userId 用户ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApprovalResponse>
 */
export async function getTransportationExpenseApprovalList(userId: string, dispatch: any): Promise<ApprovalResponse> {
  try {
    // TODO: 替换为实际的交通费审批列表API
    const url = ApiUrlVars.api_domain + ApiUrlVars.workflowLeaveApprove_get // 暂时使用leave的API
    const params = { user_id: userId }
    const response = await getApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取交通费审批列表失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get transportation expense approval list API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 处理交通费申请审批
 * @param params 审批参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApprovalResponse>
 */
export async function handleTransportationExpenseApproval(params: ApprovalParams, dispatch: any): Promise<ApprovalResponse> {
  try {
    // TODO: 替换为实际的交通费审批API
    const url = ApiUrlVars.api_domain + ApiUrlVars.workflowApprove_post // 暂时使用leave的API
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '处理交通费审批失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '审批处理成功'
    }
  } catch (error: any) {
    console.error('Handle transportation expense approval API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 批量处理审批
 * @param approvals 批量审批参数数组
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApprovalResponse>
 */
export async function handleBatchApproval(approvals: ApprovalParams[], dispatch: any): Promise<ApprovalResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.workflowApproveBatch_post
    const params = { approvals }
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '批量审批处理失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '批量审批处理成功'
    }
  } catch (error: any) {
    console.error('Handle batch approval API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}
