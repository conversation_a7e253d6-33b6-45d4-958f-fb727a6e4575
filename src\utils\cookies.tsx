import dayjs from 'dayjs'
import cookie from 'react-cookies'

export const key_for_token = "auth_token"
export const key_for_token_aes = "res_token"

// 获取当前用户cookie
export const getCookie = (key: string) => {
  return cookie.load(key)
}

// 用户登录，保存cookie
export const onLogin = (auth_token: string, res_token: string, remember?: boolean) => {
  const expireTime = remember ? 7 * 24 * 60 : 30 // 记住我：7天，否则30分钟
  setCookie(key_for_token, auth_token, expireTime)
  setCookie(key_for_token_aes, res_token, expireTime)
}

// 用户登出，删除cookie
export const onLogout = () => {
  setCookie(key_for_token, "", -30)
  setCookie(key_for_token_aes, "", -30)
}

const setCookie = (key: string, value: string, diff: number) => {
  const exp_time = dayjs().add(diff, 'minutes').toDate();
  cookie.save(key, value, { path: '/', expires: exp_time });
}

export default class Cookies {
  /**
   * 获取cookie
   * @param key cookie名称/键名
   */
  static get(key: string): string | number | undefined {
    const name = key + "=";
    const ca = document.cookie.split(";");
    for (let i = 0; i < ca.length; i++) {
      const c = ca[i].trim();
      if (c.indexOf(name) == 0) {
        return c.substring(name.length, c.length);
      }
    }
    return undefined;
  }

  /**
   * 设置cookie
   * @param key cookie名称/键名
   * @param value cookie值
   * @param exp cookie到期时间戳
   */
  static set(key: string, value: string | number, exp: string) {
    const expires = "expires=" + new Date(exp).toUTCString();
    return (document.cookie = key + "=" + value + "; " + expires);
  }

  /**
   * 删除cookie
   * @param key cookie名称/键名
   */
  static delete(key: string) {
    return (document.cookie = `${key}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`);
  }
}

