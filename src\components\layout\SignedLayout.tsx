import React, { useState, useEffect } from 'react'
import { Outlet } from 'react-router-dom'
import { Button } from 'antd'
import LeftCornor from '@/components/ui/left-cornor'
import styles from './signed-layout.module.css'

// 签名布局组件 - 替代Next.js的getLayout模式
const SignedLayout: React.FC = () => {
  const [isCollapsed, setIsCollapsed] = useState<boolean>(false)
  const [isMobile, setIsMobile] = useState<boolean>(false)

  // 响应式检测 - 在768px以下启用收起功能
  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth
      const mobile = width <= 768 // 扩大到768px以下都视为移动端
      setIsMobile(mobile)
      if (mobile) {
        setIsCollapsed(true) // 移动端默认收起
      } else {
        setIsCollapsed(false) // 桌面端始终展开
      }
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // 切换侧边栏收起/展开
  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed)
  }

  return (
    <div className={styles.main_layout}>
      <aside className={`${styles.sidebar} ${isMobile && isCollapsed ? styles.collapsed : ''}`}>
        <LeftCornor />
      </aside>

      {/* 手机端遮罩层 */}
      {isMobile && !isCollapsed && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 260,
            right: 0,
            bottom: 0,
            background: 'rgba(0,0,0,0.4)',
            zIndex: 999,
            backdropFilter: 'blur(3px)'
          }}
          onClick={toggleSidebar}
        />
      )}

      <main className={styles.main_content}>
        {/* 页面头部区域 - 包含收起按钮 */}
        {isMobile && (
          <div style={{
            position: 'sticky',
            top: 0,
            zIndex: 998,
            background: '#fff',
            borderBottom: '1px solid #e8e8e8',
            padding: '12px 20px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end'
          }}>
            <Button
              type="text"
              onClick={toggleSidebar}
              style={{
                backgroundColor: 'rgba(102, 126, 234, 0.95)',
                color: '#fff',
                border: '1px solid rgba(255,255,255,0.3)',
                borderRadius: '6px',
                width: '40px',
                height: '40px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 2px 12px rgba(102, 126, 234, 0.3)',
                fontSize: '16px',
                fontWeight: 'bold'
              }}
            >
              ☰
            </Button>
          </div>
        )}

        {/* 页面内容区域 */}
        <div style={{
          flex: 1,
          overflow: 'auto',
          height: isMobile ? 'calc(100vh - 64px)' : '100vh'
        }}>
          <Outlet />
        </div>
      </main>
    </div>
  )
}

export default SignedLayout
