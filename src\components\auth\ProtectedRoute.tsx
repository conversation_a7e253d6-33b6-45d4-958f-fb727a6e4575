import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { getCookie, key_for_token, key_for_token_aes } from '@/utils/cookies'
import { is_login } from '@/utils/login'

interface ProtectedRouteProps {
  children: React.ReactNode
}

// 受保护路由组件 - 替代Next.js中间件的路由保护功能
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const location = useLocation()

  const checkAuth = () => {
    try {
      const token = getCookie(key_for_token)
      const tokenAes = getCookie(key_for_token_aes)
      return is_login(token, tokenAes)
    } catch (error) {
      console.error('Auth check failed:', error)
      return false
    }
  }

  const isAuthenticated = checkAuth()

  if (!isAuthenticated) {
    // 保存当前路径，登录后可以重定向回来
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  return <>{children}</>
}

export default ProtectedRoute
