import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { recentHistorySlice } from '../slice/manager/recentHistorySlice'
import { informationSlice } from '../slice/user/informationSlice';
import { applicationSlice } from '../slice/applicationSlice';
import { filtersSlice } from '../slice/filterSlice';
import { authSlice } from '../slice/authSlice'
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import createWebStorage from 'redux-persist/lib/storage/createWebStorage';
import { tabSlice } from '../slice/tabSlice';
import { approvalProgressSlice } from '../slice/approvalProgressSlice';
import { approvalSlice } from '../slice/approvalSlice';
import { statisticsSlice } from '../slice/statisticsSlice';
import { recordsSlice } from '../slice/recordsSlice';
import { settingsSlice } from '../slice/settingsSlice';
import { structureSlice } from '../slice/structureSlice';
import { memberSlice } from '../slice/memberSlice';
import { attendanceImportSlice } from '../slice/attendanceImportSlice';
import { statusSlice } from '../slice/statusSlice';

// 合并所有的reducer
// 一定要添加name
const combinedReducers = combineReducers({
  [filtersSlice.name]: filtersSlice.reducer,
  [recentHistorySlice.name]: recentHistorySlice.reducer,
  [authSlice.name]: authSlice.reducer,
  [tabSlice.name]: tabSlice.reducer,
  [informationSlice.name]: informationSlice.reducer,
  [applicationSlice.name]: applicationSlice.reducer,
  [approvalProgressSlice.name]: approvalProgressSlice.reducer,
  [approvalSlice.name]: approvalSlice.reducer,
  [statisticsSlice.name]: statisticsSlice.reducer,
  [recordsSlice.name]: recordsSlice.reducer,
  [settingsSlice.name]: settingsSlice.reducer,
  [structureSlice.name]: structureSlice.reducer,
  [memberSlice.name]: memberSlice.reducer,
  [attendanceImportSlice.name]: attendanceImportSlice.reducer,
  [statusSlice.name]: statusSlice.reducer,
});

// 创建一个在服务端安全的 storage
const createNoopStorage = () => {
  return {
    getItem(_key: string) {
      return Promise.resolve(null);
    },
    setItem(_key: string, value: any) {
      return Promise.resolve(value);
    },
    removeItem(_key: string) {
      return Promise.resolve();
    },
  };
};

const storageEngine = typeof window !== 'undefined' ? storage : createNoopStorage();

// 配置需要持久化的状态
const persistConfig = {
  key: 'kaoqin_root',
  storage: storageEngine,
  // 只持久化用户认证信息和标签状态
  whitelist: ['login', 'tab'],
  // 排除不需要持久化的状态
  blacklist: ['status'] // status 是临时状态，不需要持久化
}

//将合并的reducer持久化
const persistedReducer = persistReducer(persistConfig, combinedReducers)

// 创建Store
const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // 忽略 redux-persist 的 action types
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        // 忽略 redux-persist 在 state 中添加的字段
        ignoredActionsPaths: ['meta.arg', 'payload.timestamp'],
        ignoredPaths: [
          '_persist',
          // 忽略包含 Day.js 对象的路径
          'application.leaveApplication.listValues.startDate',
          'application.leaveApplication.listValues.endDate',
          'application.leaveApplication.startDay',
          'application.leaveApplication.endDay',
          'application.leaveApplication.endDayView',
          'application.businessTripApplication.startDay',
          'application.businessTripApplication.endDay',
          'application.overtimeApplication.startDay',
          'application.overtimeApplication.endDay',
          'application.overtimeApplication.endDayView',
          'application.confirmationApplication.startDay',
          'application.confirmationApplication.endDay',
          'application.confirmationApplication.endDayView',
          // 忽略审批相关的日期对象
          'approval.startTime',
          'approval.endTime',
          'approval.confirmationTime',
          'approval.updateEndTime',
          // 忽略设置相关的日期对象
          'settings.holidaySettings.dayjs',
          'settings.holidaySettings.addSpecialDayYear',
        ],
      },
    }),
});

const persistor = persistStore(store)

// 类型定义
export type RootState = ReturnType<typeof store.getState>;
export type ApplicationDispatch = typeof store.dispatch;

export { store, persistor };