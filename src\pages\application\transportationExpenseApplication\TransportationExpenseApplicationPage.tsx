import React, { useState } from 'react'
import { Card, Typography, Form, Input, DatePicker, Select, Button, Space, InputNumber } from 'antd'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons'
import { useTranslation } from '@/hooks/useTranslation'

const { Title } = Typography
const { Option } = Select
const { TextArea } = Input

interface TransportationExpense {
  id: string
  date: string
  route: string
  amount: number
  type: 'commuter' | 'single'
  description: string
}

const TransportationExpenseApplicationPage: React.FC = () => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const [expenses, setExpenses] = useState<TransportationExpense[]>([])
  const [loading, setLoading] = useState(false)

  const addExpense = () => {
    const newExpense: TransportationExpense = {
      id: Date.now().toString(),
      date: '',
      route: '',
      amount: 0,
      type: 'single',
      description: ''
    }
    setExpenses([...expenses, newExpense])
  }

  const removeExpense = (id: string) => {
    setExpenses(expenses.filter(expense => expense.id !== id))
  }

  const updateExpense = (id: string, field: keyof TransportationExpense, value: any) => {
    setExpenses(expenses.map(expense => 
      expense.id === id ? { ...expense, [field]: value } : expense
    ))
  }

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true)
      // TODO: 实现提交逻辑
      console.log('Submit transportation expense:', { ...values, expenses })
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 重置表单
      form.resetFields()
      setExpenses([])
      
    } catch (error) {
      console.error('Submit error:', error)
    } finally {
      setLoading(false)
    }
  }

  const totalAmount = expenses.reduce((sum, expense) => sum + expense.amount, 0)

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Title level={2}>{t('application.transportationExpense.title', '交通费申请')}</Title>
        
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="applicant"
            label={t('application.applicant', '申请人')}
            rules={[{ required: true, message: t('application.applicantRequired', '请输入申请人') }]}
          >
            <Input placeholder={t('application.applicantPlaceholder', '请输入申请人姓名')} />
          </Form.Item>

          <Form.Item
            name="department"
            label={t('application.department', '部门')}
            rules={[{ required: true, message: t('application.departmentRequired', '请选择部门') }]}
          >
            <Select placeholder={t('application.departmentPlaceholder', '请选择部门')}>
              <Option value="tech">{t('department.tech', '技术部')}</Option>
              <Option value="sales">{t('department.sales', '销售部')}</Option>
              <Option value="hr">{t('department.hr', '人事部')}</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="period"
            label={t('application.period', '申请期间')}
            rules={[{ required: true, message: t('application.periodRequired', '请选择申请期间') }]}
          >
            <DatePicker.RangePicker style={{ width: '100%' }} />
          </Form.Item>

          <div style={{ marginBottom: '24px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center', 
              marginBottom: '16px' 
            }}>
              <Title level={4}>{t('application.expenseDetails', '费用明细')}</Title>
              <Button type="dashed" icon={<PlusOutlined />} onClick={addExpense}>
                {t('application.addExpense', '添加费用')}
              </Button>
            </div>

            {expenses.map((expense, index) => (
              <Card 
                key={expense.id} 
                size="small" 
                style={{ marginBottom: '16px' }}
                title={`${t('application.expense', '费用')} ${index + 1}`}
                extra={
                  <Button 
                    type="text" 
                    danger 
                    icon={<DeleteOutlined />} 
                    onClick={() => removeExpense(expense.id)}
                  />
                }
              >
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr 2fr', gap: '16px' }}>
                  <div>
                    <label>{t('application.date', '日期')}</label>
                    <DatePicker 
                      style={{ width: '100%' }}
                      onChange={(date) => updateExpense(expense.id, 'date', date?.format('YYYY-MM-DD'))}
                    />
                  </div>
                  <div>
                    <label>{t('application.type', '类型')}</label>
                    <Select 
                      style={{ width: '100%' }}
                      value={expense.type}
                      onChange={(value) => updateExpense(expense.id, 'type', value)}
                    >
                      <Option value="commuter">{t('application.commuterPass', '通勤券')}</Option>
                      <Option value="single">{t('application.singleTicket', '单程票')}</Option>
                    </Select>
                  </div>
                  <div>
                    <label>{t('application.amount', '金额')}</label>
                    <InputNumber 
                      style={{ width: '100%' }}
                      min={0}
                      precision={0}
                      value={expense.amount}
                      onChange={(value) => updateExpense(expense.id, 'amount', value || 0)}
                      addonAfter="円"
                    />
                  </div>
                  <div>
                    <label>{t('application.route', '路线')}</label>
                    <Input 
                      value={expense.route}
                      onChange={(e) => updateExpense(expense.id, 'route', e.target.value)}
                      placeholder={t('application.routePlaceholder', '请输入路线')}
                    />
                  </div>
                </div>
              </Card>
            ))}

            {expenses.length > 0 && (
              <div style={{ 
                textAlign: 'right', 
                fontSize: '16px', 
                fontWeight: 'bold',
                marginTop: '16px',
                padding: '12px',
                backgroundColor: '#f5f5f5',
                borderRadius: '6px'
              }}>
                {t('application.totalAmount', '总金额')}: ¥{totalAmount}
              </div>
            )}
          </div>

          <Form.Item
            name="remarks"
            label={t('application.remarks', '备注')}
          >
            <TextArea 
              rows={4} 
              placeholder={t('application.remarksPlaceholder', '请输入备注信息')} 
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                {t('application.submit', '提交申请')}
              </Button>
              <Button onClick={() => form.resetFields()}>
                {t('application.reset', '重置')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
        
        <div style={{ marginTop: '16px', color: '#666' }}>
          <p>路径: /application/transportationExpenseApplication</p>
          <p>短链接: /apply/tf</p>
        </div>
      </Card>
    </div>
  )
}

export default TransportationExpenseApplicationPage
