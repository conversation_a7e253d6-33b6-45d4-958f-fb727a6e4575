/* 响应式表单样式 */
.responsiveForm {
  width: 100%;
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
}

/* 布局样式 */
.layout_horizontal {
  /* 水平布局样式 */
}

.layout_vertical {
  /* 垂直布局样式 */
}

.layout_inline {
  /* 内联布局样式 */
}

/* 移动端样式 */
.mobile {
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
}

.mobile :global(.ant-form-item) {
  margin-bottom: var(--spacing-lg);
}

.mobile :global(.ant-form-item-label) {
  padding-bottom: var(--spacing-xs);
}

.mobile :global(.ant-form-item-label > label) {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
}

/* 桌面端样式 */
.desktop :global(.ant-form-item) {
  margin-bottom: var(--spacing-xl);
}

.desktop :global(.ant-form-item-label > label) {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-secondary);
}

/* 表单控件样式 */
.responsiveForm :global(.ant-input),
.responsiveForm :global(.ant-input-number),
.responsiveForm :global(.ant-select-selector),
.responsiveForm :global(.ant-picker) {
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
  transition: var(--transition-normal);
  font-size: var(--font-size-md);
  height: 40px;
}

.responsiveForm :global(.ant-input:focus),
.responsiveForm :global(.ant-input-number:focus),
.responsiveForm :global(.ant-select-focused .ant-select-selector),
.responsiveForm :global(.ant-picker:focus) {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.responsiveForm :global(.ant-input:hover),
.responsiveForm :global(.ant-input-number:hover),
.responsiveForm :global(.ant-select:hover .ant-select-selector),
.responsiveForm :global(.ant-picker:hover) {
  border-color: var(--color-primary-light);
}

/* 按钮样式 */
.responsiveForm :global(.ant-btn) {
  border-radius: var(--border-radius-md);
  height: 40px;
  font-weight: 500;
  transition: var(--transition-normal);
}

.responsiveForm :global(.ant-btn-primary) {
  background: var(--bg-gradient-primary);
  border: none;
  box-shadow: var(--shadow-sm);
}

.responsiveForm :global(.ant-btn-primary:hover) {
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 移动端按钮样式 */
.mobile :global(.ant-btn) {
  width: 100%;
  height: 44px;
  font-size: var(--font-size-lg);
}

/* 表单项错误状态 */
.responsiveForm :global(.ant-form-item-has-error .ant-input),
.responsiveForm :global(.ant-form-item-has-error .ant-input-number),
.responsiveForm :global(.ant-form-item-has-error .ant-select-selector),
.responsiveForm :global(.ant-form-item-has-error .ant-picker) {
  border-color: var(--color-error);
}

.responsiveForm :global(.ant-form-item-explain-error) {
  color: var(--color-error);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
}

/* 表单项成功状态 */
.responsiveForm :global(.ant-form-item-has-success .ant-input),
.responsiveForm :global(.ant-form-item-has-success .ant-input-number),
.responsiveForm :global(.ant-form-item-has-success .ant-select-selector),
.responsiveForm :global(.ant-form-item-has-success .ant-picker) {
  border-color: var(--color-success);
}

/* 响应式断点 */
@media (max-width: 480px) {
  .responsiveForm {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
  }
  
  .mobile :global(.ant-form-item) {
    margin-bottom: var(--spacing-md);
  }
  
  .mobile :global(.ant-btn) {
    height: 48px;
    font-size: var(--font-size-lg);
  }
}
