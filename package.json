{"name": "kaoqin_web", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "i18n:setup": "node scripts/setup-i18n.js", "i18n:scan": "node scripts/find-hardcoded-chinese.js", "i18n:check": "node scripts/check-translations.js", "i18n:fix": "node scripts/fix-i18n-errors.js"}, "dependencies": {"@ant-design/cssinjs": "1.17.0", "@reduxjs/toolkit": "1.9.7", "@types/node": "20.5.6", "@types/react": "18.2.21", "@types/react-dom": "18.2.7", "antd": "5.10.0", "autoprefixer": "10.4.15", "axios": "1.5.1", "babel-plugin-import": "1.13.8", "bootstrap": "5.3.2", "crypto-js": "4.2.0", "date-fns": "^2.30.0", "eslint": "8.48.0", "i18next": "^23.7.6", "js-export-excel": "1.1.4", "postcss": "8.4.28", "react": "18.2.0", "react-cookies": "0.1.1", "react-csv": "2.2.2", "react-dom": "18.2.0", "react-hot-toast": "2.4.1", "react-i18next": "^13.5.0", "redux-persist": "^6.0.0", "tailwindcss": "3.3.3", "typescript": "5.2.2", "xlsx": "0.18.5"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/react-cookies": "^0.1.3", "@types/react-i18next": "^8.1.0"}}