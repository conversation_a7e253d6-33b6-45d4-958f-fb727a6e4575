/* 统一表格组件样式 */

/* 表格容器 - 参考部门统计页面样式 */
.table_container {
  width: 100%;
  height: 100%;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
  display: flex;
  flex-direction: column;
}

/* 表格头部 - 使用蓝白渐变 */
.table_header {
  width: 100%;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: #ffffff;
  font-size: 13px;
  font-weight: 600;
  min-height: 60px;
  display: flex;
  align-items: center;
  border-radius: 12px 12px 0 0;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  border-bottom: 2px solid #1890ff;
}

.table_header_row {
  width: 100%;
  display: flex;
  align-items: center;
  min-height: 60px;
  padding: 0 12px;
}

.table_header_cell {
  flex: 1;
  padding: 16px 12px;
  text-align: center;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;
  vertical-align: middle;
}

.table_header_cell:last-child {
  border-right: none;
}

.table_header_text {
  font-weight: 600;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 13px;
  letter-spacing: 0.5px;
}

/* 表格内容区域 - 参考部门统计页面的固定表头方案 */
.table_content {
  width: 100%;
  flex: 1;
  overflow: visible;
  min-height: 0;
  position: relative;
  display: block;
}

/* 表格主体容器 - 固定表头和滚动内容分离 */
.table_main_container {
  height: 524px; /* 调整高度：644px - 60px(分页) */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #ffffff;
  position: relative;
}

/* 固定表头容器 - 为滚动条预留空间 */
.fixed_header_container {
  height: 60px;
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0;
  z-index: 15;
  overflow: hidden;
  border-radius: 12px 12px 0 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 17px; /* 为滚动条预留17px空间 */
}

/* 表格内容滚动区域 */
.table_body_container {
  height: 464px; /* 524px - 60px(表头) = 464px，可显示8个48px高的项目 */
  overflow-y: auto;
  overflow-x: hidden;
  background: #ffffff;
  position: relative;
}

/* 滚动条样式优化 */
.table_body_container::-webkit-scrollbar {
  width: 17px;
}

.table_body_container::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 0;
  border-left: 1px solid #e2e8f0;
}

.table_body_container::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 8px;
  border: 3px solid #f8f9fa;
}

.table_body_container::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 表格内容包装器 - 与表头对齐 */
.table_content_wrapper {
  background: #ffffff;
  width: 100%;
  padding-top: 60px; /* 为固定表头预留空间 */
}

/* 当Table组件内部使用时，不需要padding-top */
.table_content_wrapper .table_content {
  padding-top: 0;
}

/* 表格行 - 参考部门统计页面的48px行高 */
.table_row {
  width: 100%;
  display: flex;
  align-items: center;
  height: 48px;
  max-height: 48px;
  min-height: 48px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  background: #ffffff;
}

.table_row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: transparent;
  transition: all 0.2s ease;
}

/* 日系风格奇偶行 */
.table_row:nth-child(even),
.table_row:global(.table_row_even) {
  background: linear-gradient(180deg, #fafbfc 0%, #f8f9fa 100%);
}

.table_row:nth-child(odd),
.table_row:global(.table_row_odd) {
  background: #ffffff;
}

/* 日系悬停效果 */
.table_row:hover {
  background: linear-gradient(135deg, #e8f2ff 0%, #f0f4ff 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
  border-color: #d6e3ff;
}

.table_row:hover::before {
  background: #1890ff;
}

.table_row:active {
  transform: translateY(0);
  background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
}

.table_row:last-child {
  border-bottom: 2px solid #e2e8f0;
}

/* 展开行样式 */
.table_expanded_row {
  width: 100%;
  background-color: #f8f9fa;
  border-bottom: 1px solid var(--table-border-color);
  padding: var(--spacing-md);
  animation: expandRow 0.3s ease-out;
  border-left: 3px solid var(--primary-color);
}

/* 展开状态的行样式 */
.table_row_expanded {
  background-color: #e3f2fd;
  border-left: 3px solid var(--primary-color);
}

.table_row_expanded::before {
  background: var(--primary-color);
}

/* 周末和节假日行样式 */
.table_content .table_row:global(.weekend-holiday) {
  background-color: #8080802b !important;
}

.table_content .table_row:global(.weekend-holiday):hover {
  background-color: #80808040 !important;
}

.table_content .table_row:global(.weekend-holiday):nth-child(even) {
  background-color: #8080802b !important;
}

/* 异常状态行样式 */
.table_row:global(.exception) {
  color: #ff4d4f !important;
}

/* 确认状态行样式 */
.table_row:global(.confirm) {
  color: #fa8c16 !important;
}

@keyframes expandRow {
  from {
    opacity: 0;
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
  to {
    opacity: 1;
    max-height: 500px;
    padding-top: var(--spacing-md);
    padding-bottom: var(--spacing-md);
  }
}

/* 表格单元格 - 参考部门统计页面样式 */
.table_cell {
  flex: 1;
  padding: 14px 12px;
  font-size: 13px;
  color: #2d3748;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48px;
  max-height: 48px;
  min-height: 48px;
  border-right: 1px solid #f5f5f5;
  vertical-align: middle;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  box-sizing: border-box;
  transition: all 0.2s ease;
}

.table_cell:last-child {
  border-right: none;
}

.table_cell_left {
  justify-content: flex-start;
  text-align: left;
}

.table_cell_right {
  justify-content: flex-end;
  text-align: right;
}

.table_cell_center {
  justify-content: center;
  text-align: center;
}

/* 表格单元格内容样式 */
.table_cell_content {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.table_cell_multiline {
  white-space: normal;
  line-height: 1.4;
}

/* 操作按钮列 */
.table_actions {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
}

.table_action_btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: none;
  border-radius: var(--radius-small);
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  height: 28px;
}

.table_action_btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

.table_action_btn_primary {
  background: var(--primary-color);
  color: white;
}

.table_action_btn_secondary {
  background: var(--secondary-color);
  color: white;
}

.table_action_btn_danger {
  background: #ff4757;
  color: white;
}

.table_action_btn_warning {
  background: #ffa502;
  color: white;
}

/* 状态标签 */
.table_status_tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-small);
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

.table_status_pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.table_status_approved {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.table_status_rejected {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.table_status_processing {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* 分页样式 - 参考部门统计页面 */
.table_pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 20px;
  background: #ffffff;
  border-top: 1px solid #e2e8f0;
  border-radius: 0 0 12px 12px;
  height: 60px;
  position: relative;
  z-index: 100;
}

/* 空状态 */
.table_empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  color: var(--text-muted);
  font-size: var(--table-font-size);
  min-height: 200px;
}

.table_empty_icon {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.table_empty_text {
  font-size: 16px;
  margin-bottom: var(--spacing-sm);
}

.table_empty_description {
  font-size: 14px;
  color: var(--text-muted);
}

/* 加载状态 */
.table_loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .table_cell {
    padding: var(--spacing-sm);
    font-size: 13px;
  }
  
  .table_header_cell {
    padding: var(--spacing-sm);
    font-size: 13px;
  }
  
  .table_actions {
    gap: var(--spacing-xs);
  }
  
  .table_action_btn {
    min-width: 50px;
    height: 24px;
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .table_container {
    margin: 0 -var(--spacing-md);
    border-radius: 0;
  }
  
  .table_cell {
    padding: var(--spacing-xs);
    font-size: 12px;
  }
  
  .table_header_cell {
    padding: var(--spacing-xs);
    font-size: 12px;
  }
}
