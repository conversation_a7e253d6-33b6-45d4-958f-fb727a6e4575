import React, { useEffect, useState } from "react";
import { useApplicationSelector, useApplicationDispatch } from "@/hook/hooks";
import { statisticsData, statisticsActions } from "@/slice/statisticsSlice";
import styles from '../css/statisticsList.module.css';
import { menuConfig } from '@/utils/menulist';
import Image from 'next/image';
import dayjs from 'dayjs';

interface StatisticsFormProps {
  title: string;
  target: number; //1: 个人(默认) 2：部门 3：组织
}
const StatisticsList = ({ title, target }: StatisticsFormProps) => {
  const statistics = useApplicationSelector(statisticsData)
  let pMonths = statistics.pMonthInfos ? Object.keys(statistics.pMonthInfos) : [];
  if(target == 2){
    pMonths = statistics.dMonthInfos ? Object.keys(statistics.dMonthInfos) : [];
  }

  const dispatch = useApplicationDispatch();
  const handleTabClick = (key: string) => {
    dispatch(statisticsActions.onTabChange(key));
  };

  return (
    <div className={styles.record_progress}>
      <div className={styles.progress_title}>
        <span className={styles.progress_title_font}>{title}</span>
      </div>
      <div>
        <ul className={styles.user_menu_tab_ul}>
          {
            pMonths.map((month: any, index: number)=>{
              return(
              <li onClick={() => handleTabClick(month)} key={index}>
                <Image src={statistics.targetMonth === month ? "/image/icon/all-application-blue.png" : "/image/icon/all-application.png"} alt={month} width={19} height={19} priority></Image>
                {
                  statistics.targetMonth === month ? <div style={{ color: '#2A82E4' }}>{month}</div> : <div>{month}</div>
                }
              </li>
            );
            })
          }
        </ul>
      </div>
    </div>

  );
};

export default StatisticsList;