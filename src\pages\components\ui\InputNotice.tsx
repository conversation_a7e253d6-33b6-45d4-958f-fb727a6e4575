import { Popover } from "antd";
import type { ActionType } from '@rc-component/trigger/lib/interface';
import Image from 'next/image';
import undo from '@/public/image/icon/help.png';

interface InputNoticeProps {
    typeStr: string
}

const InputNotice  = ({ typeStr }: InputNoticeProps) => {
    return (
        <Popover trigger='hover' content={(messages  as any)[typeStr]} placement="rightTop"
            style={{fontSize: '10px'}} arrowPointAtCenter={true}>
            <Image width={20} height={35} src={undo} alt="" priority />
        </Popover>
    )
}
export default InputNotice


const messages = {
    // 调休/事假 说明文
    '1-1':(
        <div>
            <p><b>1.</b>&nbsp;调休可选时间段：[9时～14时]\[14时～18时]\[9时～18时]</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;时长分别计为：4小时\4小时\8小时</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;超出时间段的部分全部计：<b>事假</b></p>
            <br/>
            <p><b>2.</b>&nbsp;请假申请范围为<b style={{color:'red'}}>当前考勤月的21号</b>到当前日的<b style={{color:'red'}}>后21天</b></p>
            <br/>
            <p><b>3.</b>&nbsp;<b style={{color:'red'}}>3日及以上</b>的请假由总经理审批</p>
            <br/>
            <p><b>4.</b>&nbsp;从<b style={{color:'red'}}>结算日之后第2个工作日0时</b>开始将无法提出上个月的申请！！！</p>
            <p>&nbsp;&nbsp;&nbsp;例：2024/05/22之后无法提出2024/05/20之前的申请</p>
            <br/>
            <p><b>5.</b>&nbsp;当想要延长申请调休时间时，请撤回之前申请然后重新申请调休</p>
            <br/>
            <p><b>6.</b>&nbsp;调休不足时直接计：<b>事假</b></p>
            <br/>
            <p><b style={{color:'red'}}>!!!</b>您应及时提交申请，避免因提交不及时而影响结算<b style={{color:'red'}}>!!!</b></p>
        </div>
    ),
    // 病假 说明文
    '1-2':(
        <div>
            <p><b>1.</b>&nbsp;请及时提交相关证明材料</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;证明材料需由<b style={{color:'red'}}>三甲医院</b>开具，就诊时请注意医院资质</p>
            <br/>
            <p><b>2.</b>&nbsp;证明材料由<b>人事部门/管理部</b>审核通过后方能生效</p>
            <br/>
            <p><b>3.</b>&nbsp;若申请日期区间存在重叠，以最后一次审核通过的申请为准，其他将忽略</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;请尽量避免日期上存在重叠，以免造成不必要的麻烦</p>
            <br/>
            <p><b style={{color:'red'}}>!!!</b>您应及时提交申请，避免因提交不及时而影响结算<b style={{color:'red'}}>!!!</b></p>
        </div>
    ),
    // 其他假 说明文
    '1-3':(
        <div>
            <p><b>1.</b>&nbsp;请及时提交相关证明材料</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;证明材料要求请联系<b style={{color:'red'}}>人事部门/管理部</b>获取</p>
            <br/>
            <p><b>2.</b>&nbsp;证明材料由<b>人事部门/管理部</b>审核通过后方能生效</p>
            <br/>
            <p><b style={{color:'red'}}>!!!</b>您应及时提交申请，避免因提交不及时而影响结算<b style={{color:'red'}}>!!!</b></p>
        </div>
    ),
    // 加班 说明文
    '2-1':(
        <div>
            <p><b>1.&nbsp;平日</b></p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;申请于当日<b style={{color:'red'}}>19：30</b>截止</p>
            <br/>
            <p><b>2.&nbsp;周末/节假日</b></p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;申请于<b style={{color:'red'}}>前一工作日下午13：30</b>截止</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;<b style={{color:'red'}}>周日加班</b>请线下联系<b style={{color:'red'}}>管理部</b>处理</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;<b style={{color:'red'}}>!!!当日及以后无法补充对应申请!!!</b></p>
            <br/>
            <p><b>3.&nbsp;跨天申请</b>(适用场景：<b style={{color:'red'}}>深夜作业</b>等)</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;需分<b style={{color:'red'}}>2</b>次提出</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;第1次：结束日期不得超过<b style={{color:'red'}}>次日0时</b></p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;第2次：开始日期为<b style={{color:'red'}}>次日0时</b>；结束日期不得超过<b style={{color:'red'}}>次日9时</b></p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b style={{color:'red'}}>!!!</b>次日若为周末/节假日，则按周末/节假日加班计算；若为平时，则按平时计算</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;申请于当日<b style={{color:'red'}}>19：30</b>截止</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;<b style={{color:'red'}}>!!!当日及以后无法补充对应申请!!!</b></p>
            <br/>
            <p><b style={{color:'red'}}>!!!</b><b style={{color:'red'}}>加班时间的计算是综合了加班申请时间和实际打卡时间的，所以请不要随意填写加班时间</b><b style={{color:'red'}}>!!!</b></p>
            <p><b style={{color:'red'}}>!!!加班申请结束时间超过晚上十点并且申请通过才可以填写车费报销数据!!!</b></p>
            <p><b style={{color:'red'}}>!!!</b>您应及时提交申请，避免因提交不及时而影响结算<b style={{color:'red'}}>!!!</b></p>
        </div>
    ),
    // 上下班确认单 说明文
    '3-1':(
        <div>
            <p><b>1.</b>&nbsp;每个考勤周期内仅有<b style={{color:'red'}}>2</b>次提出机会，请慎用</p>
            <p><b>2.</b>&nbsp;仅限<b style={{color:'red'}}>工作日</b>才能申请上下班确认单申请</p>
            <p><b>3.</b>&nbsp;当进行深夜作业后需要填写上下班确认单时应按照上下班打卡记录处理：</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(1)例如打卡记录只有当天上班打卡一条时，那么只需要申请上班时间到自己的下班时间一条申请即可</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(2)如果打卡记录有两条时，需要分两种情况来申请，</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;第一种情况是如果下班时间在第二天的五点到九点之间，那么只需要申请</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;当天上班时间到第二天的下班时间一条申请即可，</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;第二种情况是如果下班时间在第二天的九点之后，那么需要申请</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;当天上班时间到第二天的九点以及第二天的九点到第二天的下班时间两条申请。</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;若第二天是双休或者节假日，无法提交申请，请联系管理员修改数据</p>
            <p><b>4.</b>&nbsp;深夜作业前请记得填写加班申请，否则上下班确认单中的深夜作业时间是无效的</p>
            <br/>
            <p><b style={{color:'red'}}>!!!</b>您应及时提交申请，避免因提交不及时而影响结算<b style={{color:'red'}}>!!!</b></p>
        </div>
    ),
    // 上下班确认单审批页面 说明文
    '4-1':(
        <div>
            <p>&nbsp;深夜作业的审批请仔细检查，避免深夜作业的第二日数据出现问题</p>
        </div>
    ),
    // 设定 删除所选日期后的所有记录 说明文
    '99-1':(
        <div>
            <p>&nbsp;删除时间最早不能超过上个考勤月的21号</p>
        </div>
    ),
    // 设定 考勤邮箱 说明文
    '99-2':(
        <div>
            <p>&nbsp;收到提醒邮件后请及时更新<b style={{color:'red'}}>邮箱密码</b></p>
            <p>&nbsp;如不更新将会导致系统邮件<b style={{color:'red'}}>无法发送</b></p>
        </div>
    )
}