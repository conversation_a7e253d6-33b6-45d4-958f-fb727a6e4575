import { useState, useEffect } from 'react'

// 客户端数据获取钩子 - 替代Next.js的getServerSideProps和getStaticProps
export function useClientSideData<T>(
  fetchFunction: () => Promise<T>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    let isMounted = true

    const fetchData = async () => {
      try {
        setLoading(true)
        setError(null)
        const result = await fetchFunction()
        
        if (isMounted) {
          setData(result)
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error('Unknown error'))
        }
      } finally {
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    fetchData()

    return () => {
      isMounted = false
    }
  }, dependencies)

  return { data, loading, error, refetch: () => fetchData() }
}

// 预加载数据钩子
export function usePreloadData<T>(
  fetchFunction: () => Promise<T>,
  condition: boolean = true
) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (!condition) return

    let isMounted = true

    const preloadData = async () => {
      try {
        setLoading(true)
        const result = await fetchFunction()
        
        if (isMounted) {
          setData(result)
        }
      } catch (error) {
        console.error('Preload error:', error)
      } finally {
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    preloadData()

    return () => {
      isMounted = false
    }
  }, [condition])

  return { data, loading }
}
