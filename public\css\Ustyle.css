.user {
    margin-left:50px;
    width:16%;
    float: left;
}

.user_message{
    background-color:#fff;
    height : 80px;
    width : 100%;
    margin-top: 50px;
    border-radius: 10px;
}
.user_name_workNumber{
    padding-top: 12px;
    margin-left: 10px;
    width: 150px;
    height: 10px;
    font-size: 20px;
}
.user_mailAddress{
    width: 150px;
    height: 10px;
    margin-top: 20px;
    margin-left: 10px;
    color: dimgray;
}
.jump_pic{
    margin-top: -22px;
    float: right;
}

.kaoqin_user_page{
    background-color: #fff;
    width : 100%;
    height: 110px;
    margin-top: 30px;
    border-radius: 10px;
}
.kaoqin_user_page_btn{
    width: 80%;
    height: 48px;
    margin-left: 23px;
    margin-top: 31px;
    background: -webkit-gradient(linear, 100% 0%, 0% 0%, from(#75bcff), to(#b3e0fa));
}

.kaoqin_reminder{
    background-color: #fff;
    width : 100%;
    height: 160px;
    margin-top: 30px;
    border-radius: 10px;
}
.kaoqin_reminder_title{
    margin-left: 20px;
    margin-bottom: 10px;
    width: 100%;
    height: 30px;
}
.pic_lighting{
    width: 20px;
    height: 20px;
    margin-left: 10px;
    margin-top: 10px;
}
.kaoqin_reminder_state{
    margin-left: 20px;
    padding-top: 10px;
}
.pic_round_green{
    margin-left: 10px;
    width: 22px;
    height: 22px;
}
.reminder_text{
    font-size: 22px;
}
.pic_round_red{
    margin-left: 10px;
    width: 22px;
    height: 22px;
}

.logoff{
    background-color: #fff;
    height: 50px;
    width:16%;
    bottom: 50px;
    border-radius: 10px;
    position:absolute;
}
.logoff_area{
    width: 100%;
    height: 100%;
}
.logoff_text{
    font-size: 25px;
    color: #fff;
}