# Next.js 到 React.js 迁移指南

## 概述

本指南详细说明了如何将考勤管理系统从 Next.js 框架迁移到纯 React.js + Vite 的架构。

## 迁移步骤

### 1. 项目结构变更

#### 原 Next.js 结构
```
pages/
├── _app.tsx
├── _document.tsx
├── index.tsx
├── login/
├── user/
└── ...
```

#### 新 React 结构
```
src/
├── main.tsx          # 应用入口
├── App.tsx           # 主应用组件
├── pages/            # 页面组件
├── components/       # 通用组件
├── hooks/           # 自定义钩子
├── utils/           # 工具函数
├── config/          # 配置文件
└── styles/          # 样式文件
```

### 2. 路由系统迁移

#### 从文件系统路由到 React Router

**原 Next.js 方式:**
- 文件系统自动路由
- `pages/user/information.tsx` → `/user/information`

**新 React Router 方式:**
- 手动配置路由
- 在 `App.tsx` 中定义 `<Routes>` 和 `<Route>`

#### 路由重写处理

**原 Next.js (next.config.js):**
```javascript
async rewrites() {
  return [
    { source: '/record/my', destination: '/attendance/attendanceimport1' }
  ]
}
```

**新 React Router:**
```tsx
<Route path="/record/my" element={<Navigate to="/attendance/attendanceimport1" replace />} />
```

### 3. 中间件迁移

#### 认证中间件

**原 Next.js (middleware.ts):**
```typescript
export function middleware(request: NextRequest) {
  // 认证逻辑
}
```

**新 React 方式:**
- `useAuthGuard` 钩子：全局认证检查
- `ProtectedRoute` 组件：路由级别保护

### 4. 数据获取迁移

#### 从 SSR 到 CSR

**原 Next.js:**
```typescript
export async function getServerSideProps() {
  const data = await fetchData()
  return { props: { data } }
}
```

**新 React:**
```typescript
const { data, loading, error } = useClientSideData(fetchData)
```

### 5. 页面组件迁移

#### 移除 Next.js 特定 API

**需要移除的导入:**
```typescript
// 移除这些
import { NextPage } from 'next'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Image from 'next/image'
```

**替换为 React Router:**
```typescript
// 替换为这些
import { useNavigate, useLocation } from 'react-router-dom'
```

### 6. 布局系统迁移

**原 Next.js:**
```typescript
Component.getLayout = (page) => <Layout>{page}</Layout>
```

**新 React Router:**
```tsx
<Route path="/" element={<Layout />}>
  <Route path="dashboard" element={<Dashboard />} />
</Route>
```

## 迁移检查清单

### ✅ 已完成的迁移项目

- [x] 项目结构重组
- [x] 依赖项更新
- [x] 路由系统重构
- [x] 认证中间件迁移
- [x] 基础页面组件迁移
- [x] 数据获取策略调整
- [x] 构建配置更新

### 🔄 需要手动迁移的项目

- [ ] 所有页面组件的完整迁移
- [ ] API 调用的客户端适配
- [ ] 国际化配置调整
- [ ] 样式文件的路径更新
- [ ] 静态资源引用更新

## 运行新项目

### 开发环境

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 生产构建

```bash
# 构建项目
npm run build

# 预览构建结果
npm run preview
```

### Docker 部署

```bash
# 构建镜像
docker build -f Dockerfile-react -t kaoqin-web-react .

# 运行容器
docker run -p 80:80 kaoqin-web-react
```

## 注意事项

### 1. 环境变量

- Next.js: `NEXT_PUBLIC_*`
- Vite: `VITE_*`

### 2. 静态资源

- Next.js: `/public/image/icon.png`
- Vite: `/image/icon.png` (public 目录自动映射到根路径)

### 3. CSS 模块

- 保持相同的命名约定
- 确保 Vite 配置支持 CSS 模块

### 4. TypeScript 配置

- 更新 `tsconfig.json` 中的路径映射
- 确保类型定义正确

## 性能优化建议

1. **代码分割**: 使用 `React.lazy()` 和 `Suspense`
2. **缓存策略**: 实现适当的数据缓存
3. **Bundle 优化**: 配置 Vite 的 rollup 选项
4. **静态资源优化**: 使用 CDN 和压缩

## 故障排除

### 常见问题

1. **路由不工作**: 检查 nginx 配置的 `try_files` 设置
2. **API 调用失败**: 确认环境变量和代理配置
3. **样式丢失**: 检查 CSS 文件的导入路径
4. **构建失败**: 检查 TypeScript 类型错误

### 调试技巧

1. 使用浏览器开发者工具检查网络请求
2. 检查控制台错误信息
3. 使用 React Developer Tools
4. 启用 Vite 的详细日志
