# 交通费申请页面开发文档

## 📋 概述

基于现有的请假申请页面 (`pages/application/leaveApplication/`) 创建了交通费申请页面，保持了相同的页面结构和功能模式。

## 📁 文件结构

```
pages/application/transportationExpenseApplication/
├── index.tsx                                    # 主页面组件
└── transportationExpenseApplication.module.css  # 样式文件
```

## 🔧 主要功能

### 1. 申请表单
- **交通方式选择**: 电车、巴士、出租车、其他
- **日期选择**: 支持过去30天到未来7天的日期范围
- **金额输入**: 支持0-50,000日元的金额范围
- **路线信息**: 出发地和目的地输入
- **申请原因**: 必填的文本区域，支持5-200字符
- **审批人选择**: 项目审批人和部门审批人级联选择

### 2. 申请列表
- **用户信息显示**: 姓名-工号格式
- **申请日期**: 带星期显示
- **交通方式**: 彩色标签显示
- **路线信息**: 出发地→目的地格式
- **金额显示**: 红色高亮显示
- **状态管理**: 审批状态显示
- **操作按钮**: 撤回和详情查看

### 3. 表格功能
- **分页**: 每页50条记录
- **展开详情**: 点击行查看审批流程
- **撤回功能**: 带确认对话框的撤回操作
- **空状态**: 无数据时的友好提示

## 🎨 UI 设计特点

### 表单设计
- **现代化弹窗**: 居中显示，700px宽度
- **响应式布局**: 使用 Ant Design 的 Grid 系统
- **图标装饰**: 🚌 交通工具图标
- **颜色编码**: 不同交通方式使用不同颜色

### 表格设计
- **紧凑布局**: 优化的列宽分配
- **视觉层次**: 清晰的信息层次结构
- **交互反馈**: 悬停和点击效果

## 🌐 多语言支持

### 中文 (zh)
```json
{
  "transportationExpense": {
    "title": "交通费申请",
    "transportationType": "交通方式",
    "departure": "出发地",
    "destination": "目的地",
    "amount": "金额"
  }
}
```

### 日语 (ja)
```json
{
  "transportationExpense": {
    "title": "交通費申請",
    "transportationType": "交通手段",
    "departure": "出発地",
    "destination": "目的地",
    "amount": "金額"
  }
}
```

## 🔗 路由配置

### 菜单配置 (`src/utils/menulist.tsx`)
```typescript
{
  index: "2", 
  text: t('menu:application.transportationFeeApplication'), 
  linkTo: "/apply/tf", 
  pCode: permissionsGroups.GROUP_APPLYS.code + permissionsGroups.GROUP_APPLYS.persmissions[1].code
}
```

### 路由映射 (`src/utils/routeconfig.tsx`)
```typescript
'/application/transportationExpenseApplication': '/apply/tf'
```

## ⚠️ 注意事项

### 当前使用的临时方案
1. **数据状态管理**: 暂时复用了 `leaveApplication` 的 Redux state
2. **API 接口**: 暂时使用了请假申请的 API 端点
3. **Action 方法**: 暂时使用了请假申请的 action 方法

### 需要后续完善的部分
1. **创建专门的 Redux slice**: `transportationExpenseSlice`
2. **添加 API 端点**: 
   - `transportationExpenseList_get`
   - `transportationExpenseAdd_post`
   - `transportationExpenseCancel_post`
3. **创建专门的 action 方法**
4. **添加数据验证逻辑**
5. **完善错误处理**

## 🚀 下一步开发计划

1. **后端 API 开发**
   - 创建交通费申请相关的数据库表
   - 实现 CRUD 操作的 API 端点
   - 添加审批流程支持

2. **前端状态管理**
   - 创建独立的 Redux slice
   - 实现专门的 action 和 reducer
   - 添加数据缓存和同步逻辑

3. **功能增强**
   - 添加文件上传功能（收据图片）
   - 实现批量操作
   - 添加导出功能

4. **测试和优化**
   - 单元测试
   - 集成测试
   - 性能优化

## 📝 开发规范

### 代码风格
- 使用 TypeScript 严格模式
- 遵循 React Hooks 最佳实践
- 使用 Ant Design 组件库
- CSS Modules 模块化样式

### 命名规范
- 组件名: PascalCase
- 文件名: camelCase
- CSS 类名: kebab-case
- 变量名: camelCase

### 提交规范
- feat: 新功能
- fix: 修复问题
- docs: 文档更新
- style: 样式调整
- refactor: 代码重构
