/**
 * 交通费申请模块类型定义
 */

// 交通费申请基础信息
export interface TransportationApplication {
  code: string;                    // 申请代码
  user_id: number;                 // 用户ID
  name: string;                    // 用户姓名
  start_day: string;               // 开始日期 (YYYY-MM-DD)
  end_day: string;                 // 结束日期 (YYYY-MM-DD)
  route_from: string;              // 出发地
  route_to: string;                // 目的地
  fee_amount_single: number;       // 单次票价格
  fee_amount_monthly: number;      // 月票价格
  reason: string;                  // 申请理由
  status?: string;                 // 申请状态
  created_at?: string;             // 创建时间
  updated_at?: string;             // 更新时间
}

// 获取申请列表响应
export interface GetApplicationListResponse {
  status: string;
  apply_list: TransportationApplication[];
  message?: string;
}

// 新建申请请求参数
export interface CreateApplicationRequest {
  start_day: string;               // 开始日期 (YYYY-MM-DD)
  end_day: string;                 // 结束日期 (YYYY-MM-DD)
  route_from: string;              // 出发地
  route_to: string;                // 目的地
  fee_amount_single: string | number; // 单次票价格
  fee_amount_monthly: number;      // 月票价格
  reason: string;                  // 申请理由
}

// 新建申请响应
export interface CreateApplicationResponse {
  status: string;
  message: string;
  code: string;                    // 生成的申请代码
}

// 更新申请请求参数
export interface UpdateApplicationRequest {
  code: string;                    // 申请代码
  start_day: string;               // 开始日期 (YYYY-MM-DD)
  end_day: string;                 // 结束日期 (YYYY-MM-DD)
  route_from: string;              // 出发地
  route_to: string;                // 目的地
  fee_amount_single: string | number; // 单次票价格
  fee_amount_monthly: number;      // 月票价格
  reason: string;                  // 申请理由
}

// 更新申请响应
export interface UpdateApplicationResponse {
  status: string;
  message: string;
}

// 删除申请请求参数
export interface DeleteApplicationRequest {
  code: string;                    // 申请代码
}

// 删除申请响应
export interface DeleteApplicationResponse {
  status: string;
  message: string;
}

// 申请状态枚举
export enum ApplicationStatus {
  PENDING = 'pending',             // 待审批
  APPROVED = 'approved',           // 已批准
  REJECTED = 'rejected',           // 已拒绝
  CANCELLED = 'cancelled',         // 已取消
}

// 申请列表查询参数
export interface ApplicationListQuery {
  page?: number;                   // 页码
  limit?: number;                  // 每页数量
  status?: ApplicationStatus;      // 状态筛选
  start_date?: string;             // 开始日期筛选
  end_date?: string;               // 结束日期筛选
  user_id?: number;                // 用户ID筛选
}

// 分页信息
export interface PaginationInfo {
  current_page: number;
  total_pages: number;
  total_count: number;
  page_size: number;
}

// 带分页的申请列表响应
export interface PaginatedApplicationListResponse {
  status: string;
  apply_list: TransportationApplication[];
  pagination: PaginationInfo;
  message?: string;
}

/**
 * 交通费记录模块类型定义
 */

// 报销方式
export interface ReimbursementMethod {
  monthly?: number;                // 月票价格
  single?: number;                 // 单次票价格
}

// 个人交通费记录
export interface PersonalTransportationRecord {
  date: string;                    // 日期 (YYYY-MM-DD)
  clock_in_time: string;           // 打卡时间
  clock_out_time: string;          // 下班时间
  attendance_location: string;     // 打卡地点
  applied_from: string;            // 申请住宿点
  applied_to: string;              // 申请出勤点
  reimbursement_method: ReimbursementMethod; // 报销方式
}

// 个人交通费集计请求参数
export interface PersonalRecordRequest {
  date: string;                    // 查询月份 (YYYY-MM)
}

// 个人交通费集计响应
export interface PersonalRecordResponse {
  status: string;
  records: PersonalTransportationRecord[];
  message?: string;
}

// 报销天数详情
export interface ReimbursementDay {
  type: 'monthly' | 'single';      // 报销类型
  amount: number;                  // 金额
  route_from: string;              // 申请住宿点
  route_to: string;                // 申请出勤点
}

// 总交通费记录
export interface CollectiveTransportationRecord {
  work_no: string;                 // 工号
  name: string;                    // 姓名
  actual_work_days: number;        // 实际出勤天数
  absent_days: number;             // 缺勤天数
  attendance_locations: string[];  // 实际打卡的所有地点
  reimbursement_method: ReimbursementMethod | null; // 报销方式
  reimbursement_days: Record<string, ReimbursementDay>; // 报销天数详情
}

// 总交通费集计请求参数
export interface CollectiveRecordRequest {
  date: string;                    // 查询月份 (YYYY-MM)
}

// 总交通费集计响应
export interface CollectiveRecordResponse {
  status: string;
  records: CollectiveTransportationRecord[];
  query_period: string;            // 查询期间
  message?: string;
}

// 修改个人数据请求参数
export interface OverrideRecordRequest {
  date: string;                    // 日期 (YYYY-MM-DD)
  method_type: 'monthly' | 'single'; // 报销类型
  amount: number;                  // 价格
  route_from?: string;             // 可选 - 出发地
  route_to?: string;               // 可选 - 目的地
  user_id?: number;                // 可选 - 用户ID（管理员使用）
}

// 修改个人数据响应
export interface OverrideRecordResponse {
  status: string;
  message?: string;
}
