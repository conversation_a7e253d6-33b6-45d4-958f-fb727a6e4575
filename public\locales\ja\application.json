{"title": "申請", "leaveApplication": "休暇申請", "overtimeApplication": "残業申請", "businessTripApplication": "出張申請", "confirmationApplication": "確認書申請", "common": {"applicationOverview": "申請一覧", "newApplication": "新規申請", "submit": "提出", "cancel": "キャンセル", "save": "保存", "delete": "削除", "modify": "修正", "withdraw": "申請取消", "confirm": "確認", "close": "閉じる", "selectApprover": "承認者を選択してください", "pleaseSelect": "選択してください", "required": "必須", "optional": "任意"}, "form": {"date": "日付", "startDate": "開始日付", "endDate": "終了日付", "startTime": "開始時間", "endTime": "終了時間", "reason": "理由", "reasonPlaceholder": "申請理由を詳しく記入してください", "timeSelection": "時刻選択", "location": "場所", "applicant": "申請者", "approver": "承認者", "projectApprover": "プロジェクト承認", "departmentApprover": "部門承認", "approverSelection": "承認者選択", "selectProjectFirst": "まずプロジェクトを選択してください", "status": "ステータス", "operation": "操作", "workNumber": "社員番号", "name": "氏名", "workNumberName": "社員番号/氏名"}, "leaveTypes": {"compensatoryLeave": "振替休日", "personalLeave": "私用休暇", "sickLeave": "病気休暇", "other": "その他"}, "overtimeTypes": {"weekday": "平日", "weekend": "週末", "holiday": "祝日"}, "status": {"pending": "承認待ち", "approved": "承認済み", "rejected": "却下", "withdrawn": "取消済み", "processing": "承認中", "submitted": "提出", "approval": "承認", "waiting": "待機", "abnormal": "状態異常", "hrCopy": "人事写し"}, "messages": {"submitSuccess": "提出成功", "submitFailed": "提出失敗", "deleteSuccess": "削除成功", "deleteFailed": "削除失敗", "selectApproverFirst": "承認者を選択してください", "confirmDelete": "削除確認", "inputConfirmText": "'我確認'を入力して削除を確認してください", "attention": "注意", "loginExpired": "ログイン認証が無効です", "modifyRequest": "申請修正", "originalTime": "元の時間", "modifyTime": "修正時間", "modifyNote": "注意：申請修正後は再承認が必要です", "confirmModify": "修正確認"}, "leave": {"title": "休暇申請", "leaveType": "休暇種別", "duration": "休暇期間", "halfDay": "半日", "fullDay": "全日", "hours": "時間", "days": "日", "availableLeave": "利用可能休暇", "compensatoryHours": "振替休日時間", "personalLeaveHours": "私用休暇時間", "sickLeaveHours": "病気休暇時間", "otherLeaveHours": "その他休暇", "reminders": {"title": "リマインダー：", "compensatoryLeave": "1.【振替休日】は当日10:00までに申請が必要で、半日が最小単位です。（申請時間が近い場合は、上司に口頭で報告が必要）", "sickAndOtherLeave": "2.【病気休暇】【その他休暇】申請提出後、書面が必要（申請理由に記載）、病気休暇は三甲病院の診断証明書（病院印章付き）が必要。【その他休暇】の長期間は管理部の審査が必要。", "deadline": "3.申請提出の締切日は当月決算日（20日）後の第2営業日の正午まで。", "example": "例：02/21-03/20期間の休暇申請提出は、03/22正午12時まで。", "contact": "その他不明な点は、管理部にお問い合わせください。"}}, "overtime": {"title": "残業申請", "overtimeType": "残業種別", "overtimeHours": "残業時間", "project": "プロジェクト", "workContent": "作業内容", "expenseReimbursement": "交通費精算", "transportationFee": "交通費", "predictedTime": "予定残業時間", "weekdayOvertime": "平日残業", "weekendOvertime": "土曜残業", "otherTime": "その他時間", "reminders": {"title": "申請リマインダー：", "weekdayRule": "1, 平日残業：当日19:30まで", "weekendRule": "2, 土曜残業：前日19:30まで", "otherRule": "3, その他時間：管理部に別途申請してください"}}, "businessTrip": {"title": "出張申請", "destination": "出張先", "purpose": "出張目的", "transportation": "交通手段", "accommodation": "宿泊手配", "area": "出張地域", "locations": {"japan": "日本", "domestic": "国内", "other": "その他"}, "reminders": {"title": "申請リマインダー", "advance": "出張申請：事前に申請し、行程を適切に手配してください。", "location": "地域選択：実際の出張先に応じて対応地域を選択してください。", "approval": "承認プロセス：正しい承認者を選択してください。"}}, "transportationExpense": {"title": "交通費路線", "departure": "出発地", "destination": "目的地", "regularPassAmount": "定期券費用 (円)", "singleTripAmount": "単発費用 (円)", "startDate": "交通開始日", "endDate": "交通終了日", "route": "ルート", "autoApproval": "自動承認", "autoApprovalNote": "交通費申請は提出後自動的に有効になり、承認プロセスは不要です。", "submitApplication": "申請提出", "startDateTooltip": "この申請が有効になると、前回の申請の終了時間が更新されます", "startDateRequired": "交通開始日を選択してください", "startDatePlaceholder": "交通開始日を選択してください", "endDatePlaceholder": "交通終了日を選択してください（任意）", "startTimeRequired": "交通開始時間を選択してください", "startTimePlaceholder": "交通開始時間を選択してください", "endTimePlaceholder": "交通終了時間を選択してください（任意）", "regularPassAmountError": "定期券費用は0-100000の間で入力してください", "regularPassAmountPlaceholder": "定期券費用を入力してください", "singleTripAmountError": "単発費用は0-50000の間で入力してください", "singleTripAmountPlaceholder": "単発費用を入力してください", "departureRequired": "出発地を入力してください", "departurePlaceholder": "出発地を入力してください", "destinationRequired": "目的地を入力してください", "destinationPlaceholder": "目的地を入力してください", "reasonRequired": "申請理由を入力してください", "reasonMinLength": "申請理由は最低5文字必要です", "reasonMaxLength": "申請理由は200文字以内で入力してください", "reasonPlaceholder": "交通費申請の理由を詳しく説明してください", "reminders": {"title": "交通費申請リマインダー", "accuracy": "正確な出発地と目的地の情報を記入してください", "startDate": "交通開始時間：交通手段変更の開始時間を選択し、レポート生成に使用します", "regularPass": "定期券費用：定期券の費用を記入し、最大100,000円を超えないでください", "singleTrip": "単発費用：単発交通の費用を記入し、最大50,000円を超えないでください", "receipt": "関連する交通費の領収書を保管し、確認に備えてください", "contact": "ご質問がある場合は、管理部門にお問い合わせください"}}, "confirmation": {"title": "出退勤確認書", "clockInTime": "出勤打刻", "clockOutTime": "退勤打刻", "clockInCorrection": "出勤修正", "clockOutCorrection": "退勤修正", "hasCardRecord": "打刻記録あり", "noCardRecord": "打刻記録なし", "confirmationDate": "確認日付", "confirmationTime": "確認時間", "cardTime": "打刻時間", "realTime": "実際時間", "originalCardRecord": "元の打刻記録", "correctedTime": "修正時間", "reminders": {"title": "申請リマインダー", "dateRange": "確認日付：過去15日以内の日付のみ選択可能です。", "timeAccuracy": "時間正確性：記入する時間が正確であることを確認してください。", "reasonDetail": "理由詳細：確認が必要な理由を詳しく説明してください。", "accessControl": "入退室システム故障：入退室システムの故障により打刻記録が漏れた場合、時間修正を申請できます。", "otherSituation": "その他の状況：管理部に別途修正を依頼してください。"}}}