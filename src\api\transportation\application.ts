/**
 * 交通费申请模块API
 */

import {
  GetApplicationListResponse,
  CreateApplicationRequest,
  CreateApplicationResponse,
  UpdateApplicationRequest,
  UpdateApplicationResponse,
  DeleteApplicationRequest,
  DeleteApplicationResponse,
  ApplicationListQuery,
  PaginatedApplicationListResponse,
} from './types';
import { API_CONFIG, TRANSPORTATION_ENDPOINTS } from '../config';

// API基础URL - 从配置文件获取
const API_BASE_URL = API_CONFIG.BASE_URL;

/**
 * 通用API请求函数
 */
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;

  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  };

  const response = await fetch(url, { ...defaultOptions, ...options });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
}

/**
 * 获取申请列表
 * GET /in/apply/transportation_fee/get
 */
export async function getApplicationList(
  token: string,
  query?: ApplicationListQuery
): Promise<GetApplicationListResponse> {
  try {
    let endpoint = TRANSPORTATION_ENDPOINTS.APPLICATION.GET_LIST;
    
    // 如果有查询参数，添加到URL
    if (query) {
      const queryParams = new URLSearchParams();
      Object.entries(query).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
      
      if (queryParams.toString()) {
        endpoint += `?${queryParams.toString()}`;
      }
    }

    const response = await apiRequest<GetApplicationListResponse>(endpoint, {
      method: 'GET',
      headers: {
        'Authorization': token,
      },
    });

    return response;
  } catch (error) {
    console.error('Get application list error:', error);
    throw new Error('获取申请列表失败');
  }
}

/**
 * 新建申请
 * POST /in/apply/transportation_fee/add
 */
export async function createApplication(
  token: string,
  data: CreateApplicationRequest
): Promise<CreateApplicationResponse> {
  try {
    const response = await apiRequest<CreateApplicationResponse>(
      TRANSPORTATION_ENDPOINTS.APPLICATION.CREATE,
      {
        method: 'POST',
        headers: {
          'Authorization': token,
        },
        body: JSON.stringify(data),
      }
    );

    return response;
  } catch (error) {
    console.error('Create application error:', error);
    throw new Error('创建申请失败');
  }
}

/**
 * 更新申请
 * POST /in/apply/transportation_fee/update
 */
export async function updateApplication(
  token: string,
  data: UpdateApplicationRequest
): Promise<UpdateApplicationResponse> {
  try {
    const response = await apiRequest<UpdateApplicationResponse>(
      TRANSPORTATION_ENDPOINTS.APPLICATION.UPDATE,
      {
        method: 'POST',
        headers: {
          'Authorization': token,
        },
        body: JSON.stringify(data),
      }
    );

    return response;
  } catch (error) {
    console.error('Update application error:', error);
    throw new Error('更新申请失败');
  }
}

/**
 * 删除申请
 * POST /in/apply/transportation_fee/delete
 */
export async function deleteApplication(
  token: string,
  data: DeleteApplicationRequest
): Promise<DeleteApplicationResponse> {
  try {
    const response = await apiRequest<DeleteApplicationResponse>(
      TRANSPORTATION_ENDPOINTS.APPLICATION.DELETE,
      {
        method: 'POST',
        headers: {
          'Authorization': token,
        },
        body: JSON.stringify(data),
      }
    );

    return response;
  } catch (error) {
    console.error('Delete application error:', error);
    throw new Error('删除申请失败');
  }
}

// 导出所有交通费申请相关的API函数
export const transportationApplicationApi = {
  getApplicationList,
  createApplication,
  updateApplication,
  deleteApplication,
};

export default transportationApplicationApi;
