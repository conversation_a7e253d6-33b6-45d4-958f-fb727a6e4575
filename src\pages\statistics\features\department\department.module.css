/* 响应式部门统计页面样式 - 使用全局CSS变量 */

/* 响应式容器样式 */
.container {
  padding: var(--spacing-xl);
  background: var(--bg-secondary);
  min-height: 100vh;
  width: 100%;
  max-width: none; /* 移除最大宽度限制，确保完全显示 */
  box-sizing: border-box;
}

.hide {
  pointer-events: none;
  opacity: 0.5;
  transition: var(--transition-normal);
}

/* 响应式日系页面标题 */
.page_header {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg) 0;
  border-bottom: 3px solid var(--border-color);
  position: relative;
  flex-shrink: 0;
}

.page_header::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 120px;
  height: 3px;
  background: var(--bg-gradient-primary);
  border-radius: 2px;
}

.page_title {
  font-size: calc(var(--font-size-xl) + 2px);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  letter-spacing: 1px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.title_icon {
  font-size: calc(var(--font-size-xl) + 4px);
  color: var(--color-primary);
  filter: drop-shadow(0 2px 4px rgba(24, 144, 255, 0.2));
}

/* 移动端页面标题优化 */
@media (max-width: 768px) {
  .container {
    padding: var(--spacing-lg);
  }

  .page_header {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md) 0;
  }

  .page_title {
    font-size: var(--font-size-lg);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .title_icon {
    font-size: var(--font-size-lg);
  }
}

@media (max-width: 480px) {
  .container {
    padding: var(--spacing-md);
  }

  .page_header {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm) 0;
  }

  .page_title {
    font-size: var(--font-size-md);
  }
}

/* 响应式日系月份选择卡片 */
.month_selector_card {
  margin-bottom: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  overflow: hidden;
  padding: var(--spacing-lg);
}

.month_selector_header {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid var(--border-color);
}

.month_selector_header h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  letter-spacing: 0.5px;
}

.month_list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: var(--spacing-xs);
}

.month_item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: var(--transition-normal);
  background: var(--bg-primary);
  min-width: 110px;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-secondary);
  letter-spacing: 0.3px;
  box-shadow: var(--shadow-sm);
  flex-shrink: 0;
  white-space: nowrap;
}

/* 响应式月份项悬停效果 - 蓝白渐变 */
.month_item:hover {
  border-color: var(--color-primary-light);
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  color: var(--color-primary);
}

/* 响应式月份项选中效果 - 符合整体UI风格 */
.month_item_active {
  border-color: var(--color-primary);
  background: var(--bg-gradient-primary);
  color: #ffffff;
  box-shadow: var(--shadow-lg);
  font-weight: 600;
}

.month_item_active:hover {
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

.month_icon {
  font-size: var(--font-size-md);
  flex-shrink: 0;
}

.month_text {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

/* 移动端月份选择器优化 */
@media (max-width: 768px) {
  .month_selector_card {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
  }

  .month_selector_header {
    margin-bottom: var(--spacing-md);
  }

  .month_selector_header h3 {
    font-size: var(--font-size-md);
  }

  .month_list {
    gap: var(--spacing-sm);
    justify-content: flex-start;
  }

  .month_item {
    min-width: 90px;
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-xs);
  }

  .month_icon {
    font-size: var(--font-size-sm);
  }

  .month_text {
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 480px) {
  .month_selector_card {
    padding: var(--spacing-sm);
  }

  .month_list {
    gap: var(--spacing-xs);
  }

  .month_item {
    min-width: 80px;
    padding: var(--spacing-xs);
    font-size: 10px;
  }

  .month_icon {
    font-size: 12px;
  }

  .month_text {
    font-size: 10px;
  }
}

/* 响应式表格卡片 - 确保列表项完全显示 */
.table_card {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 644px; /* 调整高度：60(标题) + 60(表头) + 8*48(8行数据) + 60(分页) + 32(边距) */
  position: relative;
  width: 100%;
  min-width: 0; /* 确保可以收缩 */
}

/* 响应式表格标题区域 */
.table_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
  flex-shrink: 0;
  z-index: 20;
  position: relative;
}

.table_header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 80px;
  height: 2px;
  background: var(--bg-gradient-primary);
  border-radius: 1px;
}

.table_title h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  letter-spacing: 0.5px;
}

.table_actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

/* 响应式导出按钮 - 符合整体UI风格 */
.export_btn {
  background: var(--bg-gradient-primary);
  border: none;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  height: 36px;
  padding: 0 var(--spacing-lg);
  font-size: var(--font-size-sm);
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  color: #ffffff;
  white-space: nowrap;
}

.export_btn:hover {
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

/* 表格容器 - 支持横向滚动确保列表项完全显示 */
.table_container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.table_scroll_wrapper {
  flex: 1;
  overflow-x: auto;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary-light) var(--bg-secondary);
}

.table_scroll_wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table_scroll_wrapper::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 4px;
}

.table_scroll_wrapper::-webkit-scrollbar-thumb {
  background: var(--color-primary-light);
  border-radius: 4px;
}

.table_scroll_wrapper::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

/* 移动端表格优化 - 参考交通费一览画面 */
@media (max-width: 768px) {
  .table_card {
    height: auto;
    min-height: 400px;
    display: flex;
    flex-direction: column;
  }

  .table_header {
    padding: var(--spacing-md);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
    flex-shrink: 0;
  }

  .table_title h3 {
    font-size: var(--font-size-md);
  }

  .table_actions {
    width: 100%;
    justify-content: flex-start;
    gap: var(--spacing-sm);
  }

  .export_btn {
    height: 40px;
    padding: 0 var(--spacing-md);
    font-size: var(--font-size-xs);
  }

  /* 移动端表格主容器 */
  .table_main_container {
    flex: 1;
    height: auto;
    min-height: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  /* 移动端隐藏固定表头 */
  .fixed_header_container {
    display: none !important;
  }

  /* 移动端表格内容区域 */
  .table_body_container {
    flex: 1;
    height: auto;
    min-height: 0;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
  }

  /* 移动端表格内容包装器 */
  .table_content_wrapper {
    padding-top: 0;
    width: 100%;
  }

  /* 移动端分页容器 */
  .pagination_container {
    position: relative;
    flex-shrink: 0;
    height: auto;
    padding: var(--spacing-md);
  }

  /* 移动端隐藏MobileTable自带的表头 */
  :global(.department-statistics-table .ant-table-thead) {
    display: none !important;
  }

  :global(.department-statistics-table .ant-table-header) {
    display: none !important;
  }

  /* 移动端body滚动控制 */
  body {
    overflow: hidden !important;
  }

  /* 移动端表格区域滚动 */
  .table_body_container {
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }
}

@media (max-width: 480px) {
  .table_header {
    padding: var(--spacing-sm);
  }

  .table_title h3 {
    font-size: var(--font-size-sm);
  }

  .export_btn {
    height: 44px; /* 移动端更大的触摸目标 */
    width: 100%;
    justify-content: center;
  }
}

/* 数据表格 - 日系风格 */
.data_table {
  border-radius: var(--border-radius-md);
  overflow: hidden;
  background: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8e8e8;
}

/* 表格列宽度配置 */
.data_table :global(.col-work-no) {
  width: 140px;
  min-width: 120px;
}

.data_table :global(.col-department) {
  width: 120px;
  min-width: 100px;
}

.data_table :global(.col-absence) {
  width: 80px;
  min-width: 70px;
}

.data_table :global(.col-work-days) {
  width: 90px;
  min-width: 80px;
}

.data_table :global(.col-compensatory-hours) {
  width: 100px;
  min-width: 90px;
}

.data_table :global(.col-personal-leave-hours) {
  width: 100px;
  min-width: 90px;
}

.data_table :global(.col-sick-leave-hours) {
  width: 100px;
  min-width: 90px;
}

.data_table :global(.col-weekday-overtime) {
  width: 90px;
  min-width: 80px;
}

.data_table :global(.col-weekend-overtime) {
  width: 90px;
  min-width: 80px;
}

.data_table :global(.col-holiday-overtime) {
  width: 90px;
  min-width: 80px;
}

.data_table :global(.col-other-info) {
  width: 300px;
  min-width: 200px;
}

.data_table :global(.col-actions) {
  width: 80px;
  min-width: 70px;
}

/* 表格主体容器 - 调整高度显示8个项目 */
.table_main_container {
  height: 524px; /* 调整高度：644px - 60px(标题) - 60px(分页) */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #ffffff;
  position: relative;
}

/* 表格包装器 - 包含表头和内容 */
.table_wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #ffffff;
  min-height: 0; /* 重要：允许flex子元素收缩 */
  width: 100%; /* 保持宽度不变 */
  position: relative;
}

/* 固定表头容器 - 考虑滚动条宽度 */
.fixed_header_container {
  height: 60px;
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0;
  z-index: 15;
  overflow: hidden;
  border-radius: 12px 12px 0 0; /* 左上和右上圆角 */
  position: absolute;
  top: 0;
  left: 0;
  right: 17px; /* 为滚动条预留17px空间 */
}

/* 表格内容滚动区域 - 调整高度显示8个项目 */
.table_body_container {
  height: 464px; /* 调整高度：524px - 60px(表头) = 464px，可显示8个48px高的项目 */
  overflow-y: auto;
  overflow-x: hidden; /* 暂时隐藏水平滚动，避免对齐问题 */
  background: #ffffff;
  position: relative;
}

/* 滚动条样式优化 - 右侧对齐 */
.table_body_container::-webkit-scrollbar {
  width: 17px; /* 标准滚动条宽度 */
}

.table_body_container::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 0;
  border-left: 1px solid #e2e8f0;
}

.table_body_container::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 8px;
  border: 3px solid #f8f9fa;
}

.table_body_container::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.table_body_container::-webkit-scrollbar-corner {
  background: #f8f9fa;
}

/* 表格内容包装器 - 与表头对齐 */
.table_content_wrapper {
  background: #ffffff;
  width: 100%; /* 减去滚动条宽度，与表头对齐 */
  padding-top: 60px; /* 为固定表头预留空间 */
}

/* 表格滚动配置 */
.table_scroll_config {
  width: 100%;
  min-width: 1200px;
}

/* 分页区域 - 固定底部 */
.pagination_container {
  height: 60px;
  padding: 16px 20px;
  border-top: 1px solid #e2e8f0;
  background: #ffffff;
  border-radius: 0 0 12px 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

/* 分页组件样式 - 日系设计风格 */
.pagination_container :global(.ant-pagination) {
  font-size: 14px;
}

/* 分页项基础样式 */
.pagination_container :global(.ant-pagination-item) {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s ease;
  margin: 0 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.pagination_container :global(.ant-pagination-item:hover) {
  border-color: #40a9ff;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.15);
}

.pagination_container :global(.ant-pagination-item a) {
  color: #4a5568;
  font-weight: 500;
  transition: color 0.3s ease;
}

.pagination_container :global(.ant-pagination-item:hover a) {
  color: #1890ff;
}

/* 当前选中页样式 - 蓝白渐变替代紫蓝色 */
.pagination_container :global(.ant-pagination-item-active) {
  border-color: #1890ff;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25);
  transform: translateY(-1px);
}

.pagination_container :global(.ant-pagination-item-active a) {
  color: #ffffff !important;
  font-weight: 600;
}

.pagination_container :global(.ant-pagination-item-active:hover) {
  border-color: #1890ff;
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.3);
}

/* 上一页/下一页按钮样式 */
.pagination_container :global(.ant-pagination-prev),
.pagination_container :global(.ant-pagination-next) {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s ease;
  margin: 0 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.pagination_container :global(.ant-pagination-prev:hover),
.pagination_container :global(.ant-pagination-next:hover) {
  border-color: #40a9ff;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.15);
}

.pagination_container :global(.ant-pagination-prev .ant-pagination-item-link),
.pagination_container :global(.ant-pagination-next .ant-pagination-item-link) {
  color: #4a5568;
  transition: color 0.3s ease;
}

.pagination_container :global(.ant-pagination-prev:hover .ant-pagination-item-link),
.pagination_container :global(.ant-pagination-next:hover .ant-pagination-item-link) {
  color: #1890ff;
}

/* 禁用状态样式 */
.pagination_container :global(.ant-pagination-disabled) {
  border-color: #f0f0f0 !important;
  background: #fafafa !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

.pagination_container :global(.ant-pagination-disabled .ant-pagination-item-link) {
  color: #bfbfbf !important;
}

/* 快速跳转输入框样式 */
.pagination_container :global(.ant-pagination-options-quick-jumper input) {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.pagination_container :global(.ant-pagination-options-quick-jumper input:focus) {
  border-color: #40a9ff;
  background: #ffffff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

/* 总数显示样式 */
.pagination_container :global(.ant-pagination-total-text) {
  color: #4a5568;
  font-weight: 500;
  margin-right: 16px;
}

/* 响应式滚动配置 */
@media (max-width: var(--breakpoint-xl)) {
  .table_card {
    height: 60vh;
    min-height: 400px;
  }

  .table_scroll_config {
    min-width: 900px;
  }
}

@media (max-width: var(--breakpoint-md)) {
  .table_card {
    height: 50vh;
    min-height: 350px;
  }

  .table_scroll_config {
    min-width: 800px;
  }

  .custom_table_header {
    font-size: 12px;
  }

  .custom_table_header_cell {
    padding: 12px 8px;
  }
}

/* 数据表格样式 - 只显示数据行 */
.data_table :global(.ant-table) {
  border: none !important;
  background: #ffffff !important;
  width: 100% !important;
  margin: 0 !important;
  table-layout: fixed !important;
}

.data_table :global(.ant-table-container) {
  border: none !important;
  background: #ffffff !important;
  width: 100% !important;
  overflow: visible !important;
}

.data_table :global(.ant-table-content) {
  background: #ffffff !important;
  width: 100% !important;
  overflow: visible !important;
}

.data_table :global(.ant-table-body) {
  overflow: visible !important;
}

/* 隐藏默认表头和测量行 */
.data_table :global(.ant-table-thead) {
  display: none !important;
}

/* 隐藏Ant Design的测量行 */
.data_table :global(.ant-table-measure-row) {
  display: none !important;
  height: 0 !important;
  visibility: hidden !important;
}

.data_table :global(.ant-table-measure-row > td) {
  display: none !important;
  height: 0 !important;
  padding: 0 !important;
  border: none !important;
}

/* 自定义表头样式 - 精确对齐 */
.custom_table_header {
  display: table;
  width: 100%;
  table-layout: fixed;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: #ffffff;
  font-weight: 600;
  font-size: 13px;
  text-align: center;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  border-bottom: 2px solid #1890ff;
  min-width: 1200px;
}

.custom_table_header_row {
  display: table-row;
}

.custom_table_header_cell {
  display: table-cell;
  padding: 16px 12px;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  vertical-align: middle;
  text-align: center;
}

.custom_table_header_cell:last-child {
  border-right: none;
}

/* 表头列宽度定义 - 与Table列宽度完全一致 */
.header_work_no { width: 14%; }
.header_department { width: 12%; }
.header_absence { width: 10%; }
.header_work_days { width: 10%; }
.header_commuter_pass_total { width: 14%; }
.header_single_ticket_total { width: 14%; }
.header_total_transportation_expense { width: 14%; }
.header_other { width: 12%; }

.data_table :global(.ant-table-thead) {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

.data_table :global(.ant-table-thead > tr) {
  width: 100% !important;
  display: table-row !important;
  margin: 0 !important;
  padding: 0 !important;
}

.data_table :global(.ant-table-tbody) {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 表格行样式 - 精确48px高度，显示8个完整项目 */
.data_table :global(.ant-table-tbody > tr > td) {
  padding: 14px 12px !important; /* 调整padding确保总高度48px */
  border-bottom: 1px solid #f0f0f0 !important;
  border-right: 1px solid #f5f5f5 !important;
  font-size: 13px !important;
  color: #2d3748 !important;
  background: #ffffff !important;
  transition: all 0.2s ease !important;
  line-height: 1.2 !important; /* 调整行高 */
  text-align: center !important;
  vertical-align: middle !important;
  height: 48px !important; /* 确保每行48px高度 */
  max-height: 48px !important;
  min-height: 48px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  box-sizing: border-box !important;
}

.data_table :global(.ant-table-tbody > tr > td:last-child) {
  border-right: none !important;
}

/* 确保表格行高度 */
.data_table :global(.ant-table-tbody > tr) {
  height: 48px !important;
  max-height: 48px !important;
  min-height: 48px !important;
}

.data_table :global(.ant-table-thead > tr > th:last-child) {
  border-right: none !important;
}

/* 表头装饰线 */
.data_table :global(.ant-table-thead > tr > th::after) {
  content: '' !important;
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%) !important;
}

/* 日系风格表格行 - 增强优先级 */
.data_table :global(.ant-table-tbody > tr > td) {
  padding: 14px 12px !important;
  border-bottom: 1px solid #f5f5f5 !important;
  border-right: 1px solid #fafafa !important;
  font-size: 13px !important;
  color: #2d3748 !important;
  background: #ffffff !important;
  transition: all 0.2s ease !important;
  line-height: 1.5 !important;
  border-top: none !important;
  border-left: none !important;
}

.data_table :global(.ant-table-tbody > tr > td:last-child) {
  border-right: none !important;
}

/* 清爽的行间距 */
.data_table :global(.ant-table-tbody > tr) {
  border-bottom: 1px solid #e2e8f0 !important;
}

.data_table :global(.ant-table-tbody > tr:last-child) {
  border-bottom: 2px solid #e2e8f0 !important;
}

/* 日系风格表格行样式 - 增强优先级 */
.data_table :global(.ant-table-tbody > tr.table_row_even > td) {
  background: linear-gradient(180deg, #fafbfc 0%, #f8f9fa 100%) !important;
  transition: all 0.3s ease !important;
}

.data_table :global(.ant-table-tbody > tr.table_row_odd > td) {
  background: #ffffff !important;
  transition: all 0.3s ease !important;
}

/* 保留原有类名以防其他地方使用 */
.table_row_even {
  background: linear-gradient(180deg, #fafbfc 0%, #f8f9fa 100%) !important;
  transition: all 0.3s ease !important;
}

.table_row_odd {
  background: #ffffff !important;
  transition: all 0.3s ease !important;
}

/* 日系悬停效果 - 柔和的蓝紫色 */
.data_table :global(.ant-table-tbody > tr:hover > td) {
  background: linear-gradient(135deg, #e8f2ff 0%, #f0f4ff 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
  border-color: #d6e3ff !important;
  transition: all 0.3s ease;
}

/* 选中行效果 */
.data_table :global(.ant-table-tbody > tr.ant-table-row-selected > td) {
  background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%) !important;
  border-color: #a5b4fc !important;
}

/* 行分割线增强 */
.data_table :global(.ant-table-tbody > tr:nth-child(5n)) {
  border-bottom: 2px solid #e2e8f0;
}

/* 日系员工信息样式 */
.employee_info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 2px 0;
}

.employee_text {
  font-weight: 500;
  color: #2d3748;
  font-size: 13px;
  letter-spacing: 0.3px;
  line-height: 1.4;
}

/* 日系详情按钮样式 - 蓝白渐变 */
.detail_btn {
  color: #1890ff;
  font-weight: 500;
  padding: 4px 12px;
  height: auto;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s ease;
  font-size: 12px;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.detail_btn:hover {
  color: #096dd9;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%);
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.15);
}

.detail_btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: var(--breakpoint-xl)) {
  .container {
    padding: var(--spacing-lg);
  }

  .month_list {
    gap: var(--spacing-sm);
  }

  .month_item {
    min-width: 100px;
    padding: 10px var(--spacing-lg);
  }
}

@media (max-width: var(--breakpoint-md)) {
  .container {
    padding: var(--spacing-md);
  }

  .page_title {
    font-size: 24px;
  }

  .month_list {
    gap: var(--spacing-xs);
  }

  .month_item {
    min-width: 80px;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 12px;
  }

  .table_header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .export_btn {
    width: 100%;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .container {
    background: #141414;
  }

  .month_selector_card,
  .table_card {
    background: #1f1f1f;
    border-color: #303030;
  }

  .page_title {
    color: #1890ff;
  }

  .month_item {
    background: #262626;
    border-color: #404040;
    color: #fff;
  }

  .month_item:hover {
    background: #303030;
  }
}