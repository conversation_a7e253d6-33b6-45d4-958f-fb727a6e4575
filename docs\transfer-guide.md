# 交通费报销API Postman测试指南

###目前所有权限验证只有001

###数据库
mysql://root:hyronjs2011@10.6.1.129:3307

### 获取token
```
GET /in/login/check?user_account=JS1873&user_password=Song13@14


### 获取申请列表
```
GET /in/apply/transportation_fee/get
Authorization: [token]

返回值：
{
    "status": "OK",
    "apply_list": [
        {
            "code": "ba8dfe8a519411f0bc5bc025a5c7be3d1fff86691b38438897df489676e518b4",
            "user_id": 553,
            "name": "朱国威",
            "start_day": "2024-06-01",
            "end_day": "2024-06-29",
            "route_from": "住所A",
            "route_to": "出勤地B",
            "fee_amount_single": 100.0,    #单次票的价格
            "fee_amount_monthly": 3000.0,  #月票的价格
            "reason": "测试更新交通费报销申请"
        }
    ]
}
```
### 新建申请
```
POST /in/apply/transportation_fee/add
Authorization: [token]
{
   "start_day": "2024-06-01",
   "end_day": "2024-06-29",
   "route_from": "住所A",
   "route_to": "出勤地B",
   "fee_amount_single": "100.0",
   "fee_amount_monthly": 3000.0,
   "reason": "测试提交交通费报销申请"
}

返回：
{
    "status": "OK",
    "message": "交通费报销申请创建成功",
    "code": "141bbda653f611f0bf06c025a5c7be3d09494c9c5c9a48ff9cb480a4862d6aba"
}


### 更新申请
```
POST /in/apply/transportation_fee/update
Authorization: [token]
{
    "code": "ba8dfe8a519411f0bc5bc025a5c7be3d1fff86691b38438897df489676e518b4",
    "start_day": "2024-06-01",
    "end_day": "2024-06-29",
    "route_from": "住所A",
    "route_to": "出勤地B",
    "fee_amount_single": "100.0",    
    "fee_amount_monthly": 3000.0,
    "reason": "测试更新交通费报销申请"
}


返回：
{
    "status": "OK",
    "message": "交通费报销申请更新成功",
}

```

### 删除申请
```
POST /in/apply/transportation_fee/delete
Authorization: [token]
Body: {
    "code": "申请代码"
}

### 获取申请列表
```
GET /in/apply/transportation_fee/get
Authorization: [token]

返回值：
{
    "status": "OK",
    "apply_list": [
        {
            "code": "ba8dfe8a519411f0bc5bc025a5c7be3d1fff86691b38438897df489676e518b4",
            "user_id": 553,
            "name": "朱国威",
            "start_day": "2024-06-01",
            "end_day": "2024-06-29",
            "route_from": "住所A",
            "route_to": "出勤地B",
            "fee_amount_single": 1000.0,
            "fee_amount_monthly": 3000.0,
            "reason": "测试更新交通费报销申请"
        }
    ]
}
```
### 新建申请
```
POST /in/apply/transportation_fee/add
Authorization: [token]
{
   "start_day": "2024-06-01",
   "end_day": "2024-06-29",
   "route_from": "住所A",
   "route_to": "出勤地B",
   "fee_amount_single": "100.0",
   "fee_amount_monthly": 3000.0,
   "reason": "测试提交交通费报销申请"
}


### 更新申请
```
POST /in/apply/transportation_fee/update
Authorization: [token]
{
    "code": "ba8dfe8a519411f0bc5bc025a5c7be3d1fff86691b38438897df489676e518b4",
    "start_day": "2024-06-01",
    "end_day": "2024-06-29",
    "route_from": "住所A",
    "route_to": "出勤地B",
    "fee_amount_single": "1000.0",
    "fee_amount_monthly": 3000.0,
    "reason": "测试更新交通费报销申请"
}
```

### 个人交通费报销集计（按照月份 YYYY-MM）
```
POST /in/record/transportation/personal
Authorization: [token]
{
  "date": "2025-06"
}

返回值：
{
    "status": "OK",
    "records": [
        {
            "date": "2025-06-01",
            "clock_in_time": "09:00",
            "clock_out_time": "18:00",
            "attendance_location": "日本大阪平野现场1",      #打卡点
            "applied_from": "住所A",                        #申请住宿点
            "applied_to": "出勤地B",                        #申请出勤点
            "reimbursement_method": {
                "monthly": 3000.0
            }
        },
        {
            "date": "2025-06-02",
            "clock_in_time": "09:00",
            "clock_out_time": "18:00",
            "attendance_location": "日本大阪平野现场1",
            "applied_from": "住所A",
            "applied_to": "出勤地B",
            "reimbursement_method": {
                "monthly": 3000.0
            }
        },
        、、、
        {
            "date": "2025-06-19",
            "clock_in_time": "08:47",
            "clock_out_time": "18:40",
            "attendance_location": "日本大阪平野现场1",
            "applied_from": "住所A",
            "applied_to": "出勤地C",
            "reimbursement_method": {
                "single": 20.0
            }
        },
        、、、
    ]
}
```

### 总交通费报销集计（按照自然月，如果选择的当前月，只计算到当前时间 YYYY-MM）
```
POST /in/record/transportation/collective
Authorization: [token]
{
  "date": "2025-06"
}

#测试时代码中全体员工数组只存放了两个数据
# 获取所有员工信息
    # db.execute("""
    #     SELECT USER_ID, WORK_NO, NAME
    #     FROM STAFF_MASTER
    #     WHERE ENABLE_FLAG = 1
    #     ORDER BY WORK_NO
    # """)
    # staff_records = db.fetchall()
    staff_records = [
        (721, 'JS1873', '宋柳叶'),
        (553, 'JS1640', '朱国威')
    ]

返回值（全体员工）：
{
    "status": "OK",
    "records": [
        {
            "work_no": "JS1873",
            "name": "宋柳叶",
            "actual_work_days": 17,      #实际出勤天数
            "absent_days": 2,            #缺勤时长
            "attendance_locations": [    #实际打卡的所有地点
                "日本大阪平野现场1",
                "日本大阪平野现场2"
            ],
            "reimbursement_method": {
                "single": 260.0,
                "monthly": 3000.0
            },
            "reimbursement_days": {
                "2025-06-01": {
                    "type": "monthly",
                    "amount": 3000.0,
                    "route_from": "住所A",   #申请住宿点
                    "route_to": "出勤地B"    #申请出勤点
                },
                "2025-06-02": {
                    "type": "monthly",
                    "amount": 3000.0,
                    "route_from": "住所A",
                    "route_to": "出勤地B"
                },
                、、、
                 "2025-06-19": {
                    "type": "single",
                    "amount": 20.0,          #单次票价格*2
                    "route_from": "住所A",
                    "route_to": "出勤地C"
                },
                "2025-06-20": {
                    "type": "single",
                    "amount": 300.0,
                    "route_from": "住所A",
                    "route_to": "出勤地C"
                },
                、、、
            }
        },
        {
            "work_no": "JS1640",
            "name": "朱国威",
            "actual_work_days": 0,
            "absent_days": 19,
            "attendance_locations": [],
            "reimbursement_method": null,
            "reimbursement_days": {}
        }
    ],
    "query_period": "2025-6"
}


### 修改个人数据
```
POST /in/record/transportation/override
Authorization: [token]
{
  "date": "2025-06-19",
  "method_type": "single",    #单次券single，定期券monthly（只能是'monthly' or 'single'）
  "amount": 10.0,   #价格
  "route_from"      #可选
  "route_to"        #可选        
  #"user_id"        #可选，管理使用
}

返回：
{
    "status": "ok"
}
```