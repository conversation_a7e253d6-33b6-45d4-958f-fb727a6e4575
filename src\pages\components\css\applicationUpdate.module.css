/* 响应式申请更新对话框 */
.record_dialog {
  min-width: 320px;
  max-width: 90vw;
  width: 460px;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  background: var(--bg-primary);
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: var(--z-modal);
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.dialog_title {
  color: var(--color-primary);
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin: 0;
  text-align: center;
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.dialog_content_wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  flex: 1;
}

.dialog_remain {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-secondary);
  min-width: 80px;
}

.dialog_content {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  line-height: 1.6;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.dialog_row {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-sm) 0;
}

.dialog_btn {
  width: 100%;
  max-width: 200px;
  height: 40px;
  background: var(--bg-gradient-primary);
  border: none;
  border-radius: var(--border-radius-md);
  color: var(--text-white);
  font-size: var(--font-size-sm);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog_btn:hover {
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 移动端优化 */
@media (max-width: 768px) {
  .record_dialog {
    width: 95vw;
    max-width: none;
    padding: var(--spacing-lg);
    margin: var(--spacing-md);
  }

  .dialog_title {
    font-size: var(--font-size-md);
  }

  .dialog_row {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .dialog_remain {
    min-width: auto;
    font-weight: 600;
  }

  .dialog_btn {
    width: 100%;
    height: 44px; /* 更大的触摸目标 */
  }
}

@media (max-width: 480px) {
  .record_dialog {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    padding: var(--spacing-md);
  }

  .dialog_title {
    font-size: var(--font-size-sm);
    position: sticky;
    top: 0;
    background: var(--bg-primary);
    z-index: 1;
  }

  .dialog_content_wrapper {
    overflow-y: auto;
    flex: 1;
  }
}

.dialog_cancel_btn {
  position: absolute;
  left: 260px;
  top: 190px;
  width: 30%;
  background: -webkit-gradient(linear, 100% 0%, 0% 0%, from(#c9dbecaa), to(#838585))!important;
}

.update_type{
  width: 500px;
  float: left;
}

.update_type span{
  width: 200px;
  float: left;
}

.input_group{
  width:200px;
  float: left;
}

.input_group span{
  float: left;
}