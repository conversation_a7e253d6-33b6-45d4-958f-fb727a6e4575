.record_dialog {
  min-width: 460px;
  border-radius: 5px;
  box-shadow: 0 0 18px rgba(0, 0, 0, .5);
  background: #ffffff;
  width: 25%;
  height: 26%;
  position: absolute;
  top: 0;
  bottom: 80px;
  left: 0;
  right: 0;
  margin: auto;
}

.dialog_title {
  color: rgb(79, 129, 238);
  font-size: large;
  font-weight: bold;
  position: absolute;
  left: 25px;
  top: 15px;
}

.dialog_remain {
  width: 48px;
  position: absolute;
  left: 25px;
  top: 60px;
  line-height: 35px;
}

.dialog_content {
  width: 500px;
  position: absolute;
  left: 90px;
  top: 60px;
  line-height: 35px;
}

.dialog_btn {
  position: absolute;
  left: 70px;
  top: 190px;
  width: 30%;
  background: -webkit-gradient(linear, 100% 0%, 0% 0%, from(#85b8e7), to(#59C2FD))!important;
}

.dialog_cancel_btn {
  position: absolute;
  left: 260px;
  top: 190px;
  width: 30%;
  background: -webkit-gradient(linear, 100% 0%, 0% 0%, from(#c9dbecaa), to(#838585))!important;
}

.update_type{
  width: 500px;
  float: left;
}

.update_type span{
  width: 200px;
  float: left;
}

.input_group{
  width:200px;
  float: left;
}

.input_group span{
  float: left;
}