{"title": "申请", "leaveApplication": "请假申请", "overtimeApplication": "加班申请", "businessTripApplication": "出差申请", "confirmationApplication": "确认单申请", "common": {"applicationOverview": "申请一览", "newApplication": "新申请", "submit": "提交", "cancel": "取消", "save": "保存", "delete": "删除", "modify": "修改", "withdraw": "撤回申请", "confirm": "确认", "close": "关闭", "selectApprover": "请选择审批人", "pleaseSelect": "请选择", "required": "必填", "optional": "可选"}, "form": {"date": "日期", "startDate": "开始日期", "endDate": "结束日期", "startTime": "开始时间", "endTime": "结束时间", "reason": "事由", "reasonPlaceholder": "请详细说明申请原因", "timeSelection": "时间选择", "location": "地点", "applicant": "申请人", "approver": "审批人", "projectApprover": "项目审批", "departmentApprover": "部门审批", "approverSelection": "审批人选择", "selectProjectFirst": "请先选择项目", "status": "状态", "operation": "操作", "workNumber": "工号", "name": "姓名", "workNumberName": "工号/姓名"}, "leaveTypes": {"compensatoryLeave": "调休", "personalLeave": "事假", "sickLeave": "病假", "other": "其他"}, "overtimeTypes": {"weekday": "平时", "weekend": "周末", "holiday": "假日"}, "status": {"pending": "待审批", "approved": "已通过", "rejected": "已驳回", "withdrawn": "已撤回", "processing": "审批中", "submitted": "提出", "approval": "审批", "waiting": "等待", "abnormal": "状态异常", "hrCopy": "人事抄送"}, "messages": {"submitSuccess": "提交成功", "submitFailed": "提交失败", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "selectApproverFirst": "请选择审批人", "confirmDelete": "确认删除", "inputConfirmText": "输入'我确认'以确认删除", "attention": "注意", "loginExpired": "登录凭证失效", "modifyRequest": "修改申请", "originalTime": "原定时间", "modifyTime": "修改时间", "modifyNote": "注意：修改申请后需要重新审批", "confirmModify": "确认修改"}, "leave": {"title": "请假申请", "leaveType": "请假类型", "duration": "请假时长", "halfDay": "半天", "fullDay": "全天", "hours": "小时", "days": "天", "availableLeave": "可用假期", "compensatoryHours": "调休时间", "personalLeaveHours": "事假时间", "sickLeaveHours": "病假时间", "otherLeaveHours": "其他假期", "reminders": {"title": "提醒：", "compensatoryLeave": "1.【调休】需在当天10:00之前提出申请，半天为最小单位。（申请时间过近，需要向上级口头汇报）", "sickAndOtherLeave": "2.【病假】【其他假期】提交申请后，需要写书面（存放在申请理由），病假的三甲医院就诊证明（加盖医院印章公章）。【其它假】的过年时间过长管理部审核。", "deadline": "3.提交申请的截止日为当月结算日（20日）后第2个工作日中午前。", "example": "例：02/21-03/20期间的请假申请提交，截止至03/22中午12时。", "contact": "其他不明之处，请向管理部咨询。"}}, "overtime": {"title": "加班申请", "overtimeType": "加班类型", "overtimeHours": "加班时长", "project": "项目", "workContent": "工作内容", "expenseReimbursement": "报销车费", "transportationFee": "车费", "predictedTime": "预计加班时长", "weekdayOvertime": "平日加班", "weekendOvertime": "周六加班", "otherTime": "其他时段", "reminders": {"title": "申请提醒：", "weekdayRule": "1, 平日加班：当日19:30前", "weekendRule": "2, 周六加班：前一天19:30前", "otherRule": "3, 其他时段：请联系管理部别途申请"}}, "businessTrip": {"title": "出差申请", "destination": "出差地点", "purpose": "出差目的", "transportation": "交通方式", "accommodation": "住宿安排", "area": "出差地区", "locations": {"japan": "日本", "domestic": "国内", "other": "其他"}, "reminders": {"title": "申请提醒", "advance": "出差申请：请提前申请，确保行程安排合理。", "location": "地区选择：请根据实际出差地点选择对应地区。", "approval": "审批流程：请确保选择正确的审批人。"}}, "transportationExpense": {"title": "交通费路线", "departure": "出发地", "destination": "目的地", "regularPassAmount": "定期券费用 (日元)", "singleTripAmount": "单次费用 (日元)", "startDate": "交通开始日期", "endDate": "交通结束日期", "route": "路线", "autoApproval": "自动生效", "autoApprovalNote": "交通费申请提交后将自动生效，无需审批流程。", "submitApplication": "提交申请", "startDateTooltip": "该申请生效后将更新上一个申请的结束时间", "startDateRequired": "请选择交通开始日期", "startDatePlaceholder": "请选择交通开始日期", "endDatePlaceholder": "请选择交通结束日期（可选）", "regularPassAmountError": "定期券费用必须在0-100000之间", "regularPassAmountPlaceholder": "请输入定期券费用", "singleTripAmountError": "单次费用必须在0-50000之间", "singleTripAmountPlaceholder": "请输入单次费用", "departureRequired": "请输入出发地", "departurePlaceholder": "请输入出发地", "destinationRequired": "请输入目的地", "destinationPlaceholder": "请输入目的地", "reasonRequired": "请输入申请原因", "reasonMinLength": "申请原因至少5个字符", "reasonMaxLength": "申请原因不能超过200个字符", "reasonPlaceholder": "请详细说明交通费申请原因", "reminders": {"title": "交通费申请提醒", "accuracy": "请确保填写准确的出发地和目的地信息", "startDate": "交通开始时间：选择交通方式变更的开始时间，用于生成报表", "regularPass": "定期券费用：填写定期券的费用，最高不超过100,000日元", "singleTrip": "单次费用：填写单次交通的费用，最高不超过50,000日元", "receipt": "请保留相关交通费收据以备查验", "contact": "如有疑问，请联系管理部门"}}, "confirmation": {"title": "上下班确认单", "clockInTime": "上班打卡", "clockOutTime": "下班打卡", "clockInCorrection": "上班修正", "clockOutCorrection": "下班修正", "hasCardRecord": "有打卡记录", "noCardRecord": "无打卡记录", "confirmationDate": "确认日期", "confirmationTime": "确认时间", "cardTime": "打卡时间", "realTime": "实际时间", "originalCardRecord": "原始打卡记录", "correctedTime": "修正时间", "reminders": {"title": "申请提醒", "dateRange": "确认日期：只能选择过去15天内的日期。", "timeAccuracy": "时间准确：请确保填写的时间准确无误。", "reasonDetail": "原因详细：请详细说明需要确认的原因。", "accessControl": "门禁故障：因门禁设备故障原因导致漏记录刷卡时间，可申请校正时间。", "otherSituation": "其他情况：请联系管理部别途校正。"}}}