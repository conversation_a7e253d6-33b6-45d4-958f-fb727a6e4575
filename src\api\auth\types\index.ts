/**
 * 认证模块类型定义
 */

// 登录请求参数
export interface LoginRequest {
  user_account: string;
  user_password: string;
}

// 登录响应
export interface LoginResponse {
  status: string;
  token?: string;
  message?: string;
  user_info?: {
    user_id: number;
    user_account: string;
    name: string;
    work_no: string;
    department?: string;
    position?: string;
    email?: string;
  };
}

// Token验证请求
export interface TokenValidationRequest {
  token: string;
}

// Token验证响应
export interface TokenValidationResponse {
  status: string;
  valid: boolean;
  user_info?: {
    user_id: number;
    user_account: string;
    name: string;
    work_no: string;
    department?: string;
    position?: string;
    email?: string;
  };
  message?: string;
}

// API响应基础类型
export interface ApiResponse<T = any> {
  status: string;
  message?: string;
  data?: T;
}

// 错误响应类型
export interface ErrorResponse {
  status: string;
  message: string;
  error_code?: string;
}
