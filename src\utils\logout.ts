import { NextRouter } from 'next/router';
import { onLogout } from './cookies';

/**
 * 统一的登出处理函数
 * 确保所有登出操作都能正确清理数据并跳转
 */
export const performLogout = (router: NextRouter, dispatch?: any) => {
  try {
    // 1. 清除 cookies
    onLogout();
    
    // 2. 清除本地存储
    if (typeof window !== 'undefined') {
      localStorage.removeItem('login');
      // 可选：保留语言偏好
      // localStorage.removeItem('preferred-language');
    }
    
    // 3. 如果有 dispatch，触发相关状态更新
    if (dispatch) {
      // 这里可以添加需要的状态清理逻辑
      // dispatch(someAction());
    }
    
    // 4. 跳转到登录页面
    router.replace('/login');
    
    console.log('Logout completed successfully');
  } catch (error) {
    console.error('Logout error:', error);
    // 即使出错也要确保跳转到登录页面
    router.replace('/login');
  }
};

/**
 * 检查登录状态并在需要时执行登出
 */
export const checkAndLogout = (router: NextRouter, dispatch?: any) => {
  if (typeof window === 'undefined') return;
  
  try {
    const loginData = localStorage.getItem('login');
    if (!loginData) {
      performLogout(router, dispatch);
      return false;
    }
    
    // 可以添加更多的登录状态检查逻辑
    return true;
  } catch (error) {
    console.error('Login status check error:', error);
    performLogout(router, dispatch);
    return false;
  }
};
