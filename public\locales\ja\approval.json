{"pageTitle": {"overtimeApproval": "残業承認", "leaveApproval": "休暇承認", "evectionApproval": "出張承認", "confirmationApproval": "出退勤確認書承認", "approvalProcessSettings": "承認フロー"}, "tabs": {"applicationList": "申請一覧", "companyWideList": "全社一覧", "departmentList": "部門一覧", "all": "全て", "pending": "承認待ち", "approved": "承認済み", "rejected": "却下済み", "completedApprovals": "承認完了データ表", "pendingApprovals": "承認中データ表"}, "tableHeaders": {"employeeIdName": "社員番号/氏名", "startTime": "開始時間", "endTime": "終了時間", "duration": "予定時間", "businessTripLocation": "出張先", "confirmationDate": "確認日付", "clockInDuration": "打刻時間", "confirmationDuration": "確認時間", "reason": "理由", "status": "ステータス", "actions": "操作", "department": "部門"}, "buttons": {"approve": "承認", "reject": "却下", "batchApprove": "一括承認", "batchReject": "一括却下", "keywordSearch": "キーワード検索：", "startDateSearch": "開始日検索：", "cancel": "キャンセル", "confirm": "確定", "search": "検索", "endExtendOvertimeApplication": "終了/延長残業申請", "endExtend": "終了/延長", "endExtendBusinessTripApplication": "終了/延長出張申請"}, "status": {"waitingApproval": "上司の承認待ち", "approved": "承認済み", "rejected": "却下済み", "expired": "期限切れ", "inProgress": "承認中"}, "messages": {"rejectReason": "却下理由を入力してください:", "batchRejectReason": "却下理由を入力してください（一括却下の理由は下記の入力内容と同じになりますのでご注意ください）:", "selected": "{count}件選択中", "companyWideDataError": "全社一覧データエラー", "departmentDataError": "部門一覧データエラー", "success": "成功", "enterOvertimeEndTime": "残業終了時間を入力して提出してください", "selectStaffAndMonth": "エクスポートする従業員と対応する勤怠月を選択してください", "enterEndTimeDate": "終了時間の日付を入力してください", "enterEndTime": "終了時間を入力", "selectAttendanceMonth": "勤怠月を選択", "overtimeDataExport": "残業データエクスポート", "includeWorkdays": "平日を含む", "submit": "提出", "enterBusinessTripEndTime": "出張終了時間を入力して提出してください"}, "approvalProcess": {"approvalTarget": "承認対象", "approvalType": "承認タイプ", "applicationRole": "申請役割", "detailType": "詳細タイプ", "approvalFlow": "承認フロー", "submitApplication": "申請提出", "approvalEnd": "承認終了"}, "approvalTypes": {"leave": "休暇", "overtime": "残業", "businessTrip": "出張"}, "roles": {"projectMember": "プロジェクトメンバー", "projectLeader": "プロジェクトリーダー", "departmentHead": "部門責任者", "seniorManagement": "上級管理職"}, "leaveTypes": {"within3Days": "3日以内休暇", "over3Days": "3日以上休暇", "sickLeave": "病気休暇", "other": "その他"}, "overtimeTypes": {"weekday": "平日残業", "saturday": "土曜残業", "sunday": "日曜残業", "holiday": "祝日残業"}, "businessTripTypes": {"businessTrip": "出張"}, "workTypes": {"regular": "平日", "weekend": "週末", "holiday": "祝日"}, "tooltips": {"approvalProcessDetail": "承認フロー詳細"}}