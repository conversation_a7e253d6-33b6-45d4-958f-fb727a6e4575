import { useEffect } from 'react';

const TableStyleOverride = () => {
    useEffect(() => {
        // 动态注入样式以确保覆盖Ant Design默认样式
        const styleId = 'department-statistics-table-override';
        
        // 移除已存在的样式
        const existingStyle = document.getElementById(styleId);
        if (existingStyle) {
            existingStyle.remove();
        }
        
        // 创建新的样式元素
        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            /* 部门统计页面表格样式强制覆盖 - 新布局 */
            .department-statistics-table.ant-table {
                background: #ffffff !important;
                border: none !important;
                margin: 0 !important;
                padding: 0 !important;
                width: 100% !important;
            }

            .department-statistics-table .ant-table-container {
                border: none !important;
                background: #ffffff !important;
                margin: 0 !important;
                padding: 0 !important;
                width: 100% !important;
            }

            .department-statistics-table .ant-table-content {
                background: #ffffff !important;
                margin: 0 !important;
                padding: 0 !important;
                width: 100% !important;
            }

            /* 隐藏默认表头和测量行 */
            .department-statistics-table .ant-table-thead {
                display: none !important;
            }

            /* 隐藏Ant Design的测量行 */
            .department-statistics-table .ant-table-measure-row {
                display: none !important;
                height: 0 !important;
                visibility: hidden !important;
            }

            .department-statistics-table .ant-table-measure-row > td {
                display: none !important;
                height: 0 !important;
                padding: 0 !important;
                border: none !important;
            }

            /* 表格行样式 - 精确48px高度 */
            .department-statistics-table .ant-table-tbody > tr > td {
                border-bottom: 1px solid #f0f0f0 !important;
                border-right: 1px solid #f5f5f5 !important;
                padding: 14px 12px !important;
                background-color: #ffffff !important;
                font-size: 13px !important;
                line-height: 1.2 !important;
                color: #2d3748 !important;
                transition: all 0.2s ease !important;
                text-align: center !important;
                vertical-align: middle !important;
                height: 48px !important;
                max-height: 48px !important;
                min-height: 48px !important;
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                box-sizing: border-box !important;
            }

            /* 确保表格行高度 */
            .department-statistics-table .ant-table-tbody > tr {
                height: 48px !important;
                max-height: 48px !important;
                min-height: 48px !important;
            }

            .department-statistics-table .ant-table-tbody > tr > td:last-child {
                border-right: none !important;
            }

            /* 奇偶行样式 */
            .department-statistics-table .ant-table-tbody > tr:nth-child(even) > td {
                background: #fafbfc !important;
            }

            .department-statistics-table .ant-table-tbody > tr:nth-child(odd) > td {
                background: #ffffff !important;
            }

            /* 悬停效果 */
            .department-statistics-table .ant-table-tbody > tr:hover > td {
                background: #e6f7ff !important;
                transform: translateY(-1px) !important;
                box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1) !important;
                transition: all 0.3s ease !important;
            }

            /* 最后一行 */
            .department-statistics-table .ant-table-tbody > tr:last-child > td {
                border-bottom: 1px solid #e2e8f0 !important;
            }

            /* 强制表格容器不遮挡分页 */
            .department-statistics-table {
                overflow: visible !important;
            }

            .department-statistics-table .ant-table-container {
                overflow: visible !important;
            }

            .department-statistics-table .ant-table-content {
                overflow: visible !important;
            }

            .department-statistics-table .ant-table-body {
                overflow: visible !important;
            }
        `;
        
        // 添加到head中
        document.head.appendChild(style);
        
        // 清理函数
        return () => {
            const styleToRemove = document.getElementById(styleId);
            if (styleToRemove) {
                styleToRemove.remove();
            }
        };
    }, []);
    
    return null;
};

export default TableStyleOverride;
