namespace ApiUrlVars {
    const domain = "172.28.1.60:8085"
    // const domain = "10.6.1.84:8000"
    // WebSocket服务器地址
    // export const websocket_domain : string ="127.0.0.1:8000";
    export const websocket_domain : string = domain;

    // API 服务器地址
    // export const api_domain : string ="http://127.0.0.1:8000/in";
    export const api_domain : string ="http://" + domain + "/in";

    // LoginAPI 服务器地址
    // export const loginApi_domain : string ="http://127.0.0.1:8000/in";
    export const loginApi_domain : string ="http://" + domain + "/in";

    //#region URL详细
    // 考勤： 查询
    export const record_query: string = "/record/query"

    // 考勤： 更新
    export const record_update: string = "/record/update"
    // 考勤： 最大日期
    export const record_date_max: string = "/record/datem"
    // 考勤： 导入
    export const record_import: string = "/record/import"

    // 考勤： 居家
    export const record_upload_home: string = "/record/upload/home"

    // 考勤： 批量
    export const record_upload_any: string = "/record/upload/any"

    // 考勤： 居家模板下载
    export const record_download_temp: string = "/record/download/temp"
    // 考勤： 批量模板下载
    export const error_download_home: string = "/record/download/error"
    // 考勤： 触发每日检查处理
    export const record_check_result_trigger: string = "/record/recalc"

    // 获取任务一览
    export const tasks_list_get: string = "/task/list"

    // 获取登录信息
    export const login_get: string = "/login/check"

    // 用户密码重置
    export const reset_post: string = "/password/reset"

    // 用户密码修改
    export const password_change: string = "/password/change"

    //申请页面每页数据个数取得
    export const apply_day_get: string = "/setting/apply_day/get"

    //指定用户请假申请取得
    export const leaveList_get: string = "/apply/leave/get"

    //指定用户加班申请取得
    export const overtimeList_get: string = "/apply/overtime/get"

    //指定用户出差申请取得
    export const businessTripList_get: string = "/apply/business_trip/get"

    //指定用户上下班申请取得
    export const editStList_get: string = "/apply/edit_st_et/get"

    //新增事假请假申请
    export const leaveListNormalAdd_post: string = "/apply/leave/add/normal"

    //新增病假请假申请
    export const leaveListSickAdd_post: string = "/apply/leave/add/sick"

    //新增其他请假申请
    export const leaveListOtherAdd_post: string = "/apply/leave/add/other"

    //新增加班申请
    export const overtimeNewList_post: string = "/apply/overtime/add"

    //加班单
    export const overtime_data_post: string = "/apply/output/get"

    //新增出差申请
    export const businessTripAdd_post: string = "/apply/business_trip/add"

    //新增上下班确认单
    export const editAdd_post: string = "/apply/edit_st_et/add"

    //撤回请假申请
    export const leaveCancel_post: string = "/apply/leave/cancel"

    //撤回加班申请
    export const overtimeCancel_post: string = "/apply/overtime/cancel"

    //撤回出差申请
    export const businessTripCancel_post: string = "/apply/business_trip/cancel"

    //撤回上下班确认单申请
    export const confirmationCancel_post: string = "/apply/edit_st_et/cancel"

    //请求修改请假申请
    export const leaveChange_post: string = "/apply/leave/change"

    //请求修改出差申请
    export const businessTripChange_post: string = "/apply/business_trip/change"

    //加班申请报销数据
    export const overtime_expense_post: string = "/apply/overtime/expense"

    //指定审批人申请取得
    export const workflow_get: string = "/workflow/get"

    //指定审批人加班申请取得
    export const workflowOvertimeApprove_get: string = "/workflow/overtime/get"

    //指定审批人出差申请取得
    export const workflowBusinessTripApprove_get: string = "/workflow/business_trip/get"

    //指定审批人确认单申请取得
    export const workflowEditApprove_get: string = "/workflow/edit/get"

    //指定审批人请假申请取得
    export const workflowLeaveApprove_get: string = "/workflow/leave/get"

    //指定申请审批同意
    export const workflowApprove_post: string = "/workflow/approve"
    
    //指定申请审批通过 (批量)
    export const workflowApproveBatch_post: string = "/workflow/approve/batch"

    //指定申请审批驳回
    export const workflowReject_post: string = "/workflow/reject"

    //指定申请审批驳回 (批量)
    export const workflowRejectBatch_post: string = "/workflow/reject/batch"

    //请假时间修改请求提出
    export const workflowRequestAdd_post: string = "/workflow/request/add"

    //请假时间修改请求确认
    export const workflowRequestApprove_post: string = "/workflow/request/approve"

    //请假时间修改请求驳回
    export const workflowRequestReject_post: string = "/workflow/request/reject"

    //请假申请调休时长修改
    export const workflowLeaveUpdate_post: string = "/workflow/leave/update"

    //出差申请出差时间修改
    export const workflowBusinessTripUpdate_post: string = "/workflow/business_trip/update"

    //部门全部成员取得
    export const departMember_get: string = "/depart/member/get"

    //新增部门成员
    export const departMemberAdd_post: string = "/depart/member/add"

    //删除部门成员
    export const departMemberDelete_post: string = "/depart/member/delete"

    //修改部门成员角色
    export const departMemberChange_post: string = "/depart/member/role_change"

    //修改部门单个成员角色
    export const departMemberChangeSingle_post: string = "/depart/member/role_change_single"

    //组织全部成员取得
    export const staffMember_get: string = "/staff/member/get"

    //导出组织成员筛选表格
    export const staffMemberFilter_get: string = "/staff/member/filter/get"

    //新增组织成员
    export const staffMemberAdd_post: string = "/staff/member/add"

    //删除组织成员
    export const staffMemberDelete_post: string = "/staff/member/delete"

    //修改组织成员角色
    export const staffMemberChange_post: string = "/staff/member/change"

    //修改组织单个成员角色
    export const staffMemberChangeSingle_post: string = "/staff/member/change_single"

    //批量处理组织成员角色(发邮件)
    export const staffMemberBatch_post: string = "/staff/member/batch/do"

    //批量处理组织成员角色(不发邮件)
    export const staffMemberBatch0_post: string = "/staff/member/batch/od"

    //下载组织成员数据导入模板
    export const staffMemberExcel_get: string = "/staff/member/batch/download"

    //下载组织成员报错数据文件
    export const staffMemberError_get: string = "/staff/member/batch/download/error"

    //角色权限信息取得
    export const setting_roles_get: string = "/setting/roles/get"

    //角色权限信息修改
    export const setting_roles_post: string = "/setting/roles/edit"

    //节假日设定：新增单个节假日信息
    export const setting_holiday_add_post: string = "/setting/holiday/add"

    //节假日设定：删除单个节假日信息
    export const setting_holiday_delete_post: string = "/setting/holiday/delete"

    //节假日设定：取得特殊安排
    export const setting_special_list_get: string = "/setting/special/get"

    //节假日设定：新增特殊安排
    export const setting_special_add_post: string = "/setting/special/add"

    //节假日设定：删除特殊安排
    export const setting_special_delete_post: string = "/setting/special/delete"

    //节假日设定：取得审批流程
    export const setting_process_get: string = "/setting/workflow/get"

    //数据统计：周末/节假日加班申请
    export const summary_application_get: string = "/report/overtime/all"

    //数据统计：个人数据汇总
    export const summary_personal_info_get: string = "/report/personal/get"

    //数据统计：部门数据汇总
    export const summary_depart_info_get: string = "/report/depart/get"

    //数据统计：部门报销数据汇总
    export const summary_depart_info_expense_get: string = "/report/depart/expense/get"

    //数据统计：组织数据汇总
    export const summary_company_info_get: string = "/report/company/get"

    //数据统计：调休管理
    export const summary_compensatory_info_get: string = "/report/compen/get"

    // 考勤： 触发调休管理
    export const summary_compensatory_trigger: string = "/report/compen/recalc"

    //下载调休数据导入模板
    export const compensatoryExcel_get: string = "/report/compen/download"

    //下载调休数据报错文件
    export const compensatoryError_get: string = "/report/compen/download/error"

    //处理调休数据
    export const compensatoryExcel_post: string = "/report/compen/upload"

    //调休数据导出
    export const compensatoryExcel_data_list: string = "/report/compen/data/get"

    //数据统计: 判断数据导入是否完成
    export const summary_compensatory_isloading_get: string = "/report/compen/isLoading"

    //数据统计：调休保存
    export const summary_compensatory_info_save: string = "/report/compen/save"

    //部门结构：部门结构数据获取
    export const structure_info_get: string = "/structure/get"

    //部门结构：新增部门
    export const structure_departAdd_post: string = "/structure/add"

    //部门结构：删除部门
    export const structure_departDelete_post: string = "/structure/delete"

    //部门结构：修改部门数据
    export const structure_departChange_post: string = "/structure/change"

    //其他设定：取得考勤默认查询天数
    export const setting_search_day_get: string = "/setting/search_day/get"

    //其他设定：设置考勤默认查询天数
    export const setting_search_day_set: string = "/setting/search_day/set"

    //其他设定：取得开发人员列表
    export const setting_admin_list_get: string = "/setting/admin_list/get"

    //其他设定：删除所选日期之后的所有记录
    export const setting_delete_record_post: string = "/record/delete"

    //其他设定：修改考勤系统邮箱账号和密码
    export const setting_change_mail_post: string = "/setting/mail/save"

    //其他设定：更改门禁数据
    export const setting_change_door_get: string = "/setting/door_data/get"

    //其他设定：更改门禁数据
    export const setting_change_door_post: string = "/setting/door_data/change"

    //节假日设定：取得节假日信息
    export const setting_holiday_list_get: string = "/setting/holiday/get"

    //节假日设定：取得节假日信息
    export const setting_mail_get: string = "/setting/mail/get"

    //取得本人操作记录信息
    export const task_get: string = "/task/my"

    //取得他人操作记录信息
    export const task_all_get: string = "/task/list"

    //取得全部操作记录的天数限制
    export const record_limit_get: string = "/task/search_day/get"

    //共通：部门一览
    export const departs_get: string = "/common/departs"

    //送信：发给开发
    export const to_development: string = "/mail/dev"

    //人事：请假审批获取
    export const rs_leave_all_list_get: string = "/workflow_all/leave/get"

    //人事：审批中请假审批获取
    export const rs_leave_ing_list_get: string = "/workflow_ing/leave/get"

    //人事：其他申请结束时间提交
    export const rs_leave_other_end_time_post: string = "/workflow/other/end_time/change"

    //人事：加班审批获取
    export const rs_overtime_all_list_get: string = "/workflow_all/overtime/get"

    //人事：审批中加班审批获取
    export const rs_overtime_ing_list_get: string = "/workflow_ing/overtime/get"

    //人事：加班结束时间提交
    export const rs_overtime_end_time_post: string = "/workflow/overtime/end_time/change"

    //人事：员工加班明细下载
    export const rs_overtime_staff_download_post: string = "/workflow/overtime/staff_overtime/get"

    //人事：出差审批获取
    export const rs_trip_all_list_get: string = "/workflow_all/trip/get"

    //人事：审批中出差审批获取
    export const rs_trip_ing_list_get: string = "/workflow_ing/trip/get"

    //人事：出差回社时间提交
    export const rs_trip_back_time_post: string = "/workflow/trip/end_time/change"

    //人事：上下班确认单审批获取
    export const rs_edit_all_list_get: string = "/workflow_all/edit/get"

    //人事：审批中上下班确认单审批获取
    export const rs_edit_ing_list_get: string = "/workflow_ing/edit/get"

    //部门人事：部门内请假审批获取
    export const rs_leave_depart_list_get: string = "/workflow_depart/leave/get"

    //部门人事：部门内审批中请假审批获取
    export const rs_leave_depart_ing_list_get: string = "/workflow_depart_ing/leave/get"
 
    //部门人事：部门内加班审批获取
    export const rs_overtime_depart_list_get: string = "/workflow_depart/overtime/get"
 
    //部门人事：部门内审批中加班审批获取
    export const rs_overtime_depart_ing_list_get: string = "/workflow_depart_ing/overtime/get"
 
    //部门人事：部门内出差审批获取
    export const rs_trip_depart_list_get: string = "/workflow_depart/trip/get"
 
    //部门人事：部门内审批中出差审批获取
    export const rs_trip_depart_ing_list_get: string = "/workflow_depart_ing/trip/get"

    //部门人事：部门内上下班确认单审批获取
    export const rs_edit_depart_list_get: string = "/workflow_depart/edit/get"

    //部门人事：部门内审批中上下班确认单审批获取
    export const rs_edit_depart_ing_list_get: string = "/workflow_depart_ing/edit/get"

    //部门人事：加班导出
    export const overtime_depart_out: string = "/workflow/overtime/non_work/get"

    // 交通费申请：获取申请列表
    export const transportation_fee_get: string = "/apply/transportation_fee/get"

    // 交通费申请：新建申请
    export const transportation_fee_add: string = "/apply/transportation_fee/add"

    // 交通费申请：更新申请
    export const transportation_fee_update: string = "/apply/transportation_fee/update"

    // 交通费申请：删除申请
    export const transportation_fee_delete: string = "/apply/transportation_fee/delete"

    // 交通费记录：个人交通费报销集计
    export const transportation_record_personal: string = "/record/transportation/personal"

    // 交通费记录：总交通费报销集计
    export const transportation_record_collective: string = "/record/transportation/collective"

    // 交通费记录：修改个人数据
    export const transportation_record_override: string = "/record/transportation/override"

    //#endregion
}

export default ApiUrlVars;
