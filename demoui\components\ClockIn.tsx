import React, { useState, useEffect } from 'react';
import { Clock, Calendar, MapPin, AlertTriangle, ChevronLeft, ChevronRight } from 'lucide-react';

interface ClockRecord {
  time: string;
  date: string;
  location: string;
}

interface LocationState {
  hasPermission: boolean;
  currentLocation: string;
  isLoading: boolean;
  error: string | null;
}

const ClockIn: React.FC = () => {
  const [clockRecords, setClockRecords] = useState<ClockRecord[]>([]);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [selectedLocation, setSelectedLocation] = useState('');
  const [customLocation, setCustomLocation] = useState('');
  const [locationState, setLocationState] = useState<LocationState>({
    hasPermission: false,
    currentLocation: '',
    isLoading: false,
    error: null,
  });
  const [currentPage, setCurrentPage] = useState(1);
  const recordsPerPage = 5;

  const predefinedLocations = [
    '東京オフィス',
    '大阪オフィス',
    '在宅勤務',
    'カスタム',
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    getCurrentLocation();
  }, []);

  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      setLocationState({
        hasPermission: false,
        currentLocation: '',
        isLoading: false,
        error: 'お使いのブラウザは位置情報をサポートしていません',
      });
      return;
    }

    setLocationState(prev => ({ ...prev, isLoading: true, error: null }));

    navigator.geolocation.getCurrentPosition(
      (position) => {
        // 実際のプロジェクトでは、ここで逆ジオコーディングAPIを使用して住所を取得
        const mockAddress = `緯度: ${position.coords.latitude.toFixed(4)}, 経度: ${position.coords.longitude.toFixed(4)}`;
        setLocationState({
          hasPermission: true,
          currentLocation: mockAddress,
          isLoading: false,
          error: null,
        });
      },
      (error) => {
        let errorMessage = '';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = '位置情報の取得が拒否されました';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = '位置情報が利用できません';
            break;
          case error.TIMEOUT:
            errorMessage = '位置情報の取得がタイムアウトしました';
            break;
          default:
            errorMessage = '位置情報の取得に失敗しました';
            break;
        }
        setLocationState({
          hasPermission: false,
          currentLocation: '',
          isLoading: false,
          error: errorMessage,
        });
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000,
      }
    );
  };

  const handleClockIn = () => {
    const location = selectedLocation === 'カスタム' ? customLocation : selectedLocation;
    
    if (!location) {
      alert('打卡地点を選択してください');
      return;
    }

    const now = new Date();
    const newRecord: ClockRecord = {
      time: now.toLocaleTimeString('ja-JP', {
        hour: '2-digit',
        minute: '2-digit',
      }),
      date: now.toISOString(),
      location,
    };

    setClockRecords(prev => [newRecord, ...prev]);
    
    // 打卡成功后重置选择
    setSelectedLocation('');
    setCustomLocation('');
  };

  const getTodayRecords = () => {
    const today = new Date().toDateString();
    return clockRecords.filter(record => 
      new Date(record.date).toDateString() === today
    );
  };

  const todayRecords = getTodayRecords();
  
  // 分页逻辑
  const totalPages = Math.ceil(todayRecords.length / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentRecords = todayRecords.slice(startIndex, endIndex);

  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  return (
    <div className="p-8 h-full flex flex-col">
      {/* Current Time Display */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 mb-8 border border-blue-100">
        <div className="text-center">
          <h3 className="text-2xl font-bold text-slate-800 mb-4">
            現在時刻
          </h3>
          <div className="text-5xl font-bold text-slate-800 font-mono mb-2">
            {currentTime.toLocaleTimeString('ja-JP', {
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
            })}
          </div>
          <p className="text-slate-600">
            {currentTime.toLocaleDateString('ja-JP', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              weekday: 'long',
            })}
          </p>
        </div>
      </div>

      <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Clock In Section */}
        <div className="space-y-6">
          <h4 className="text-xl font-bold text-slate-800">打卡操作</h4>
          
          {/* Location Warning */}
          {locationState.error && (
            <div className="bg-amber-50 border border-amber-200 rounded-xl p-4 flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-amber-800 font-medium mb-1">位置情報の警告</p>
                <p className="text-amber-700 text-sm">{locationState.error}</p>
                <button
                  onClick={getCurrentLocation}
                  className="text-amber-600 hover:text-amber-800 text-sm underline mt-2"
                >
                  再試行
                </button>
              </div>
            </div>
          )}

          {/* Current Location Display */}
          {locationState.currentLocation && (
            <div className="bg-green-50 border border-green-200 rounded-xl p-4 flex items-start gap-3">
              <MapPin className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-green-800 font-medium mb-1">現在位置</p>
                <p className="text-green-700 text-sm">{locationState.currentLocation}</p>
              </div>
            </div>
          )}

          {/* Location Selection */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                打卡地点を選択
              </label>
              <select
                value={selectedLocation}
                onChange={(e) => setSelectedLocation(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-200"
              >
                <option value="">地点を選択してください</option>
                {predefinedLocations.map((location) => (
                  <option key={location} value={location}>
                    {location}
                  </option>
                ))}
              </select>
            </div>

            {selectedLocation === 'カスタム' && (
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  カスタム地点
                </label>
                <input
                  type="text"
                  value={customLocation}
                  onChange={(e) => setCustomLocation(e.target.value)}
                  placeholder="地点名を入力してください"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-200"
                />
              </div>
            )}
          </div>

          {/* Single Clock In Button */}
          <div>
            <button
              onClick={handleClockIn}
              disabled={!selectedLocation || (selectedLocation === 'カスタム' && !customLocation)}
              className="w-full bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed text-white py-8 px-8 rounded-2xl font-bold text-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none flex items-center justify-center gap-4"
            >
              <Clock className="w-8 h-8" />
              打卡
            </button>
            <p className="text-center text-sm text-slate-500 mt-3">
              毎日何度でも打卡できます
            </p>
          </div>
        </div>

        {/* Recent Records */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h4 className="text-xl font-bold text-slate-800">今日の記録</h4>
            {totalPages > 1 && (
              <div className="flex items-center gap-2">
                <button
                  onClick={() => goToPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="p-2 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeft className="w-4 h-4" />
                </button>
                <span className="text-sm text-slate-600">
                  {currentPage} / {totalPages}
                </span>
                <button
                  onClick={() => goToPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="p-2 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>
          
          <div className="bg-gray-50 rounded-2xl p-6 space-y-4 max-h-96 overflow-y-auto">
            {currentRecords.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">
                  {todayRecords.length === 0 ? 'まだ打刻記録がありません' : 'このページに表示する記録がありません'}
                </p>
              </div>
            ) : (
              currentRecords.map((record, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between bg-white p-4 rounded-xl shadow-sm border border-gray-100"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full flex items-center justify-center bg-pink-100 text-pink-600">
                      <Clock className="w-5 h-5" />
                    </div>
                    <div>
                      <p className="font-semibold text-slate-800">
                        打卡記録
                      </p>
                      <div className="flex items-center gap-2 text-sm text-slate-500">
                        <MapPin className="w-3 h-3" />
                        <span>{record.location}</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-slate-800 font-mono">
                      {record.time}
                    </p>
                    <p className="text-xs text-slate-500">
                      {new Date(record.date).toLocaleDateString('ja-JP')}
                    </p>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClockIn;