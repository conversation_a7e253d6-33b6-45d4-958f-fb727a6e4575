.record_dialog {
  border-radius: 5px;
  box-shadow: 0 0 18px rgba(0, 0, 0, .5);
  background: #ffffff;
  width: 25%;
  height: 29%;
  position: absolute;
  top: 0;
  bottom: 80px;
  left: 0;
  right: 0;
  margin: auto;
}

.dialog_title {
  color: rgb(79, 129, 238);
  font-size: large;
  font-weight: bold;
  position: absolute;
  left: 25px;
  top: 20px;
}

.dialog_remain {
  width: 85px;
  position: absolute;
  left: 40px;
  top: 60px;
  line-height: 32px;
}

.dialog_content {
  width: 170px;
  position: absolute;
  left: 130px;
  top: 60px;
  line-height: 32px;
  font-size: 15px;
}

.dialog_btn {
  position: absolute;
  left: 80px;
  top: 210px;
  width: 30%;
  background: -webkit-gradient(linear, 100% 0%, 0% 0%, from(#85b8e7), to(#59C2FD));
}

.dialog_cancel_btn {
  position: absolute;
  left: 240px;
  top: 210px;
  width: 30%;
  background: -webkit-gradient(linear, 100% 0%, 0% 0%, from(#c9dbecaa), to(#838585));
}

.calendar_area {
  width: 10%;
  float: left;
  min-width: 120px;
  margin-top: 6px;
}

.calendar_datePicker {
  height: 25px;
  width: 130px;
  top: -3px;
}

.hour_area {
  width: 10%;
  float: left;
  margin-left: 5px;
  margin-top: 6px;
}

.hour_select {
  height: 25px;
  min-width: 80px;
  top: -3px;
}