import { createSlice, PayloadAction, } from "@reduxjs/toolkit";
import { RootState } from "../store";
import { menuConfig } from "../utils/menulist";
import { format } from 'date-fns';
import { HYDRATE } from "next-redux-wrapper";
import ApiTasksTypes from "../api/common/task-types";
import ApiFetchVars from "../api/common/fetch-api-vars";
import ApiUrlVars from "../api/common/url-vars";
// import { createApiAsyncThunk } from "../api/fetch-api";
import dayjs from 'dayjs';
import * as XLSX from 'xlsx';
import { stat } from "fs";
export type SingleValueType = (string | number)[];
export type ValueType = SingleValueType | SingleValueType[];
const employees = [
    { id: 1, name: '<PERSON>' },
    { id: 2, name: '<PERSON>' },
    { id: 3, name: '<PERSON>' },
    { id: 4, name: '<PERSON>' },
    { id: 5, name: '<PERSON>' },
]

const pMonthInfos = [
    { key: 1, evection: '1', guonei: '0', japan: '1', other: '0', pregnent: '1', allAttendance: '22d', passAll: '20d', passAttendance: '20d', dayOff: '8h', forLeave: '3h', forSick: '5h', ordinary: '2h', weekend: '7h', holiday: '6h', forMeal: '5' },
]

const departmentList = [
    { key: 1, user: '张三-JS0000', department: 'DA04', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 2, user: '张三-JS0001', department: 'DA03', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 3, user: '张三-JS0002', department: 'DA02', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 4, user: '张三-JS0003', department: 'DA07', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 5, user: '张三-JS0004', department: 'DA05', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
]

const tissueList = [
    { key: 1, user: '张三-JS0000', department: 'DA04', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 2, user: '张三-JS0001', department: 'DA03', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 3, user: '张三-JS0002', department: 'DA02', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 4, user: '张三-JS0003', department: 'DA07', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 5, user: '张三-JS0004', department: 'DA05', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 6, user: '张三-JS0000', department: 'DA04', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 7, user: '张三-JS0001', department: 'DA03', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 8, user: '张三-JS0002', department: 'DA02', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 9, user: '张三-JS0003', department: 'DA07', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 10, user: '张三-JS0004', department: 'DA05', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 11, user: '张三-JS0000', department: 'DA04', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 12, user: '张三-JS0001', department: 'DA03', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 13, user: '张三-JS0002', department: 'DA02', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 14, user: '张三-JS0003', department: 'DA07', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
    { key: 15, user: '张三-JS0004', department: 'DA05', attendence: '22d', tiaoxiu: '3h', shijia: '3h', bingjia: '5h', ping: '3h', zhoumo: '8h', jiejia: '3h', chanjia: '30d (2023/10/12~2023/11/11)' },
]

const paidLeaveList = [
    { key: 1, user: '张三-JS0000', department: 'DA04', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 2, user: '张三-JS0001', department: 'DA03', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 3, user: '张三-JS0002', department: 'DA02', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 4, user: '张三-JS0003', department: 'DA07', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 5, user: '张三-JS0004', department: 'DA05', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 6, user: '张三-JS0005', department: 'DA04', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 7, user: '张三-JS0006', department: 'DA03', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 8, user: '张三-JS0007', department: 'DA02', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 9, user: '张三-JS0008', department: 'DA07', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 10, user: '张三-JS0009', department: 'DA05', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 12, user: '张三-JS0010', department: 'DA04', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 12, user: '张三-JS0011', department: 'DA03', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 13, user: '张三-JS0012', department: 'DA02', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 14, user: '张三-JS0013', department: 'DA07', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 15, user: '张三-JS0014', department: 'DA05', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 16, user: '张三-JS0015', department: 'DA04', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 17, user: '张三-JS0016', department: 'DA03', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 18, user: '张三-JS0017', department: 'DA02', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 19, user: '张三-JS0018', department: 'DA07', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
    { key: 20, user: '张三-JS0019', department: 'DA05', first: '80', second: '88', third: '96', forth: '104', fifth: '112', sixth: '104', isChange: false },
]

const moreInf={
    fifth: '112',
    fifthWorkTime: '168',
    fifthWorkTrueTime: '152',
    restTime: '16',
    sixth: '104',
    isChange: false,
}

interface DepartItem {
    id: number;
    Code: string;
    Name: string;
}


interface ICascaderItem {
    id: number;
    Name: string;
}

// 定义要保存到Store的数据格式
export interface statisticsState {
    status: string;
    // 详细
    open: boolean,
    targetUserId:string,
    // 周末&节假日申请
    employee: {},
    selected: string,
    dayList: [],
    selectedDayList: boolean[],
    allChecked: boolean,
    applyList:[],
    // 个人级
    targetMonth: string,
    pMonthInfos: [],
    // 部门级
    dMonthInfos: [],
    // 组织级
    cMonthInfos: [],
    selectedDeparts:[],
    //1: 个人(默认) 2：部门 3：组织
    targetType:number,
    selectedRowKey: number,
    // 组织
    showMonth: boolean,
    showYear: boolean,
    showDefined: boolean,
    cascaderCount: number,
    departList: any[],
    cascaderList: ICascaderItem[],
    cascaderContent: ValueType[],
    department: string,
    department3: string,
    //调休管理
    compensatory:{
        mons: {};
        users: {};
    },

    //调休表
    paidLeaveList: {
        key: number;
        user: string;
        department: string;
        first: string;
        second: string;
        third: string;
        forth: string;
        fifth: string;
        sixth: string;
        isChange: boolean;
    }[],
    paidLeaveTrueList:{
        key: number;
        user: string;
        department: string;
        first: string;
        second: string;
        third: string;
        forth: string;
        fifth: string;
        sixth: string;
        isChange: boolean;
    }[],
    moreInf:{
        fifth: string;
        fifthWorkTime: string;
        fifthWorkTrueTime: string;
        restTime: string;
        sixth: string;
        isChange: boolean;
    },
    devKey: string,
    clickCol: number,
    temText: string,
    leftTime: number,
    rightTime: number,
    isMore: boolean,
    
}

// 初始化数据
const initialState: statisticsState = {
    status: ApiFetchVars.todo,
    open: false,
    targetMonth: format(new Date(), 'yyyy-MM'),
    pMonthInfos: [],
    dMonthInfos: [],
    cMonthInfos: [],
    selectedDeparts:[],
    targetUserId:'',
    targetType: 1, 
    selectedRowKey: -1,
    showMonth: true,
    showYear: false,
    showDefined: false,
    cascaderCount: 0,
    cascaderList: [{ id: 1 , Name:''}],
    departList:[],
    cascaderContent: [],
    department: '',
    department3: '',
    employee: employees,
    selected: '',
    // 个人申请数据统计
    dayList: [],
    selectedDayList: [],
    allChecked: false,
    applyList:[],
    compensatory:{
        mons: {},
        users: {},
    },
    //调休表
    paidLeaveList: paidLeaveList,
    paidLeaveTrueList: paidLeaveList,
    moreInf: moreInf,
    devKey: '',
    clickCol: 0,
    temText: '',
    leftTime: -1,
    rightTime: -1,
    isMore: false,
};
const taskType = ApiTasksTypes.tasks_list_get
const summaryCompensatoryUrl = ApiUrlVars.loginApi_domain + ApiUrlVars.summary_compensatory_info_get
// export const reloadCompensatoryAsyncThunk = createApiAsyncThunk(taskType, summaryCompensatoryUrl, null, ApiFetchVars.get)

export const statisticsSlice = createSlice({
    name: 'statistics',
    initialState,
    // reducers 集合了reducer和action的功能
    // 每个case冒号之前的为action名
    // 每个case整体为reducer
    reducers: {
        // 使用PayloadAction来展示action.payload的详细内容
        selectDate: (state: statisticsState, action: PayloadAction<any>) => {
            state.selected = action.payload;
        },
        onTabChange: (state: statisticsState, action: PayloadAction<any>) => {
            state.status = ApiFetchVars.todo;
            state.targetMonth = action.payload;
        },        
        setAllCheckbox: (state: statisticsState, action: PayloadAction<any>) => {
            state.status = ApiFetchVars.todo;
            state.allChecked = action.payload;
            state.selectedDayList = action.payload ? Array(state.dayList?.length).fill(true) : Array(state.dayList?.length).fill(false);
        },        
        setCheckbox: (state: statisticsState, action: PayloadAction<any>) => {
            state.status = ApiFetchVars.todo;
            const { index, checked } = action.payload;
            state.selectedDayList[index] = checked;
            state.allChecked = state.selectedDayList.every(checkbox => checkbox === true);
        },
        handleShowPersonalDetailClick: (state: statisticsState, action: PayloadAction<any>) => {
            if(action.payload != null){
                state.targetUserId = action.payload.userId;
                state.targetType = action.payload.target;
            }
        },
        handleShowDetailClick: (state: statisticsState) => {
            state.open = true;
        },
        handleClose: (state: statisticsState) => {
            state.open = false;
        },
        // 组织
        handleExport: (state: statisticsState) => {
            // state.status = ApiFetchVars.todo;
            // const wb = XLSX.utils.book_new();
            // const ws = XLSX.utils.json_to_sheet(state.tissueStatistics);
            // XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
            // const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
            // const blob = new Blob([wbout], { type: 'application/octet-stream' });
            // const url = URL.createObjectURL(blob);
            // const link = document.createElement('a');
            // link.href = url;
            // link.download = 'tissueData.xlsx'; // 你可以设置下载的文件名  
            // link.click(); // 模拟点击下载链接，将文件保存到客户端浏览器中  
            // URL.revokeObjectURL(url); // 释放内存中的文件资源  
        },
        // 个人
        handleExportForPersonal: (state: statisticsState) => {
            // state.status = ApiFetchVars.todo;
            // const wb = XLSX.utils.book_new();
            // const ws = XLSX.utils.json_to_sheet(state.pMonthInfos);
            // XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
            // const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
            // const blob = new Blob([wbout], { type: 'application/octet-stream' });
            // const url = URL.createObjectURL(blob);
            // const link = document.createElement('a');
            // link.href = url;
            // link.download = 'personalData.xlsx'; // 你可以设置下载的文件名  
            // link.click(); // 模拟点击下载链接，将文件保存到客户端浏览器中  
            // URL.revokeObjectURL(url); // 释放内存中的文件资源  
        },
        // 部门
        handleExportForDepart: (state: statisticsState) => {
            // state.status = ApiFetchVars.todo;
            // const wb = XLSX.utils.book_new();
            // const ws = XLSX.utils.json_to_sheet(state.departmentStatistics);
            // XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
            // const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
            // const blob = new Blob([wbout], { type: 'application/octet-stream' });
            // const url = URL.createObjectURL(blob);
            // const link = document.createElement('a');
            // link.href = url;
            // link.download = 'departmentData.xlsx'; // 你可以设置下载的文件名  
            // link.click(); // 模拟点击下载链接，将文件保存到客户端浏览器中  
            // URL.revokeObjectURL(url); // 释放内存中的文件资源  
        },
        selectedMonth: (state: statisticsState) => {
            state.showMonth = true;
            state.showYear = false;
            state.showDefined = false;

            state.selectedDeparts = [];
            let options = [...state.departList];
            options.map((item: any)=>{
                item["disabled"] = false;
                return item;
            });
            state.departList = options;
        },
        selectedYear: (state: statisticsState) => {
            state.showYear = true;
            state.showMonth = false;
            state.showDefined = false;

            state.selectedDeparts = [];
            let options = [...state.departList];
            options.map((item: any)=>{
                item["disabled"] = false;
                return item;
            });
            state.departList = options;
        },
        selectedDefined: (state: statisticsState) => {
            state.showDefined = true;
            state.showMonth = false;
            state.showYear = false;

            state.selectedDeparts = [];
            let options = [...state.departList];
            options.map((item: any)=>{
                item["disabled"] = false;
                return item;
            });
            state.departList = options;
        },
        handleCascaderContent: (state: statisticsState, action: PayloadAction<any>) => {
            state.cascaderContent[action.payload.number] = action.payload.value; 
        },
        handleSelectedDeparts: (state: statisticsState, action: PayloadAction<any>) => {
            const selectedValues = action.payload;
            let selectedIds = ''
            const selectedItems = selectedValues.map((item: any)=>{
                selectedIds += "$" + item[0]["id"] + "$";
                return item[0];
            });
            state.selectedDeparts = selectedItems;

            // ALL被选中 或者 超出3个, 则其他项目不可选 
            let options = [...state.departList]
            if(selectedIds?.indexOf('$0$') >= 0 || selectedItems?.length >= 3){
                options.map((item: any)=>{
                    if(selectedIds?.indexOf("$" + item["id"] + "$") >= 0){
                        item["disabled"] = false
                    }else{
                        item["disabled"] = true;
                    }
                    return item;
                });
            } else {
                options.map((item: any)=>{
                    item["disabled"] = false;
                    return item;
                });
            }
            state.departList = options;
        },
        change: (state: statisticsState, action: PayloadAction<any>) => {
            state.clickCol = action.payload;
        },
        initialHolidayApplys: (state: statisticsState, action: PayloadAction<any>) => {
            state.dayList = action.payload.data_list;
            state.applyList = action.payload.apply_list;
        },
        initialPersonalMonthInfos: (state: statisticsState, action: PayloadAction<any>) => {
            state.pMonthInfos = action.payload.data_list;
        },
        initialDepartMonthInfos: (state: statisticsState, action: PayloadAction<any>) => {
            state.dMonthInfos = action.payload.data_list;
        },
        initialCompanyMonthInfos: (state: statisticsState, action: PayloadAction<any>) => {
            state.cMonthInfos = action.payload.data_list;
        },
        initialDeparts: (state: statisticsState, action: PayloadAction<any>) => {
            state.departList = action.payload.data_list.map((item: any)=>{
                item.disabled = false;
                return item;
            });
        },
        initialCompensatory: (state: statisticsState, action: PayloadAction<any>) => {
            state.compensatory = action.payload.data_list;
        },
    },
});

//以下内容必须要有
export const { actions: statisticsActions } = statisticsSlice;

export default statisticsSlice.reducer;

//state 后面的为store中数据名称
export const statisticsData = (state: RootState) => state.statistics;
