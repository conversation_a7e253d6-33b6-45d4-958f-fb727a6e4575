/* 响应式工具类 */

/* 显示/隐藏工具类 */
.show-xs { display: block !important; }
.show-sm { display: none !important; }
.show-md { display: none !important; }
.show-lg { display: none !important; }
.show-xl { display: none !important; }

.hide-xs { display: none !important; }
.hide-sm { display: block !important; }
.hide-md { display: block !important; }
.hide-lg { display: block !important; }
.hide-xl { display: block !important; }

/* 文本对齐工具类 */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

/* 响应式文本对齐 */
.text-xs-left { text-align: left !important; }
.text-xs-center { text-align: center !important; }
.text-xs-right { text-align: right !important; }

/* 间距工具类 */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.m-5 { margin: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }

/* 方向性间距 */
.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mt-5 { margin-top: var(--spacing-xl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: var(--spacing-xs) !important; }
.ml-2 { margin-left: var(--spacing-sm) !important; }
.ml-3 { margin-left: var(--spacing-md) !important; }
.ml-4 { margin-left: var(--spacing-lg) !important; }
.ml-5 { margin-left: var(--spacing-xl) !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: var(--spacing-xs) !important; }
.mr-2 { margin-right: var(--spacing-sm) !important; }
.mr-3 { margin-right: var(--spacing-md) !important; }
.mr-4 { margin-right: var(--spacing-lg) !important; }
.mr-5 { margin-right: var(--spacing-xl) !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: var(--spacing-xs) !important; }
.pt-2 { padding-top: var(--spacing-sm) !important; }
.pt-3 { padding-top: var(--spacing-md) !important; }
.pt-4 { padding-top: var(--spacing-lg) !important; }
.pt-5 { padding-top: var(--spacing-xl) !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: var(--spacing-xs) !important; }
.pb-2 { padding-bottom: var(--spacing-sm) !important; }
.pb-3 { padding-bottom: var(--spacing-md) !important; }
.pb-4 { padding-bottom: var(--spacing-lg) !important; }
.pb-5 { padding-bottom: var(--spacing-xl) !important; }

.pl-0 { padding-left: 0 !important; }
.pl-1 { padding-left: var(--spacing-xs) !important; }
.pl-2 { padding-left: var(--spacing-sm) !important; }
.pl-3 { padding-left: var(--spacing-md) !important; }
.pl-4 { padding-left: var(--spacing-lg) !important; }
.pl-5 { padding-left: var(--spacing-xl) !important; }

.pr-0 { padding-right: 0 !important; }
.pr-1 { padding-right: var(--spacing-xs) !important; }
.pr-2 { padding-right: var(--spacing-sm) !important; }
.pr-3 { padding-right: var(--spacing-md) !important; }
.pr-4 { padding-right: var(--spacing-lg) !important; }
.pr-5 { padding-right: var(--spacing-xl) !important; }

/* Flexbox 工具类 */
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-block { display: block !important; }
.d-inline-block { display: inline-block !important; }
.d-none { display: none !important; }

.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-start { justify-content: flex-start !important; }
.justify-end { justify-content: flex-end !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }

.align-start { align-items: flex-start !important; }
.align-end { align-items: flex-end !important; }
.align-center { align-items: center !important; }
.align-baseline { align-items: baseline !important; }
.align-stretch { align-items: stretch !important; }

.flex-1 { flex: 1 !important; }
.flex-auto { flex: auto !important; }
.flex-none { flex: none !important; }

/* 宽度工具类 */
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }

/* 高度工具类 */
.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

/* 位置工具类 */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* 溢出工具类 */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

.overflow-x-auto { overflow-x: auto !important; }
.overflow-x-hidden { overflow-x: hidden !important; }
.overflow-x-scroll { overflow-x: scroll !important; }

.overflow-y-auto { overflow-y: auto !important; }
.overflow-y-hidden { overflow-y: hidden !important; }
.overflow-y-scroll { overflow-y: scroll !important; }

/* 响应式断点 - 小屏手机 (max-width: 480px) */
@media (max-width: 480px) {
  .show-xs { display: block !important; }
  .show-sm { display: none !important; }
  .show-md { display: none !important; }
  .show-lg { display: none !important; }
  .show-xl { display: none !important; }
  
  .hide-xs { display: none !important; }
  .hide-sm { display: block !important; }
  .hide-md { display: block !important; }
  .hide-lg { display: block !important; }
  .hide-xl { display: block !important; }
  
  .text-xs-left { text-align: left !important; }
  .text-xs-center { text-align: center !important; }
  .text-xs-right { text-align: right !important; }
  
  .flex-xs-column { flex-direction: column !important; }
  .flex-xs-row { flex-direction: row !important; }
}

/* 响应式断点 - 大屏手机 (min-width: 481px) */
@media (min-width: 481px) {
  .show-xs { display: none !important; }
  .show-sm { display: block !important; }
  .show-md { display: none !important; }
  .show-lg { display: none !important; }
  .show-xl { display: none !important; }
  
  .hide-xs { display: block !important; }
  .hide-sm { display: none !important; }
  .hide-md { display: block !important; }
  .hide-lg { display: block !important; }
  .hide-xl { display: block !important; }
}

/* 响应式断点 - 平板 (min-width: 769px) */
@media (min-width: 769px) {
  .show-xs { display: none !important; }
  .show-sm { display: none !important; }
  .show-md { display: block !important; }
  .show-lg { display: none !important; }
  .show-xl { display: none !important; }
  
  .hide-xs { display: block !important; }
  .hide-sm { display: block !important; }
  .hide-md { display: none !important; }
  .hide-lg { display: block !important; }
  .hide-xl { display: block !important; }
}

/* 响应式断点 - 桌面 (min-width: 1025px) */
@media (min-width: 1025px) {
  .show-xs { display: none !important; }
  .show-sm { display: none !important; }
  .show-md { display: none !important; }
  .show-lg { display: block !important; }
  .show-xl { display: none !important; }
  
  .hide-xs { display: block !important; }
  .hide-sm { display: block !important; }
  .hide-md { display: block !important; }
  .hide-lg { display: none !important; }
  .hide-xl { display: block !important; }
}

/* 响应式断点 - 大屏 (min-width: 1201px) */
@media (min-width: 1201px) {
  .show-xs { display: none !important; }
  .show-sm { display: none !important; }
  .show-md { display: none !important; }
  .show-lg { display: none !important; }
  .show-xl { display: block !important; }
  
  .hide-xs { display: block !important; }
  .hide-sm { display: block !important; }
  .hide-md { display: block !important; }
  .hide-lg { display: block !important; }
  .hide-xl { display: none !important; }
}
