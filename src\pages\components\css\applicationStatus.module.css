.application_status {
    float: left;
    margin-left: 20px;
}

.application_status ul {
    float: left;
    margin-left: 1rem;
    list-style-type: disc;
}

.application_status ul li {
    float: left;
}

.application_status_line {
    border-bottom: 1px solid rgb(150, 149, 149);
    /* width: 1480px; */
    width: 100%;
    margin-right: 10px;
    margin-left: 10px;
}

.color_pending {
    color: #FF0000;
    /* 红色 */
}

.color-submitted {
    color: #00FF00;
    /* 绿色 */
}

.color-approved {
    color: #0000FF;
    /* 蓝色 */
}

.color-rejected {
    color: #FFFF00;
    /* 黄绿色 */
}

.color-other {
    color: #000000;
    /* 黑色 */
}

/* Steps组件紧凑样式 */
.compact_steps {
    font-size: 12px !important;
}

.compact_steps :global(.ant-steps-item-title) {
    font-size: 12px !important;
    line-height: 1.4 !important;
    margin-bottom: 2px !important;
}

.compact_steps :global(.ant-steps-item-description) {
    font-size: 11px !important;
    line-height: 1.3 !important;
    color: #666 !important;
    margin-top: 2px !important;
}

.compact_steps :global(.ant-steps-item-icon) {
    width: 24px !important;
    height: 24px !important;
    line-height: 24px !important;
    font-size: 14px !important;
}

.compact_steps :global(.ant-steps-item-content) {
    margin-top: 4px !important;
}

.compact_steps :global(.ant-steps-item) {
    padding-bottom: 8px !important;
}

.compact_steps :global(.ant-steps-item-tail) {
    top: 12px !important;
}