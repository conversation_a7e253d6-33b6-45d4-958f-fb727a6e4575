/* 响应式申请状态组件 */
.application_status {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-left: var(--spacing-lg);
    width: 100%;
    max-width: none; /* 确保完全显示 */
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.application_status ul {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin: 0;
    padding: 0;
    list-style-type: disc;
    flex-wrap: wrap;
}

.application_status ul li {
    display: flex;
    align-items: center;
    white-space: nowrap;
    font-size: var(--font-size-sm);
}

.application_status_line {
    border-bottom: 1px solid var(--border-color);
    width: 100%;
    margin: 0 var(--spacing-sm);
    flex-shrink: 0;
}

/* 响应式状态颜色 - 使用日系配色 */
.color_pending {
    color: #ff4d4f;
    font-weight: 500;
}

.color-submitted {
    color: #52c41a;
    font-weight: 500;
}

.color-approved {
    color: var(--color-primary);
    font-weight: 500;
}

.color-rejected {
    color: #fa8c16;
    font-weight: 500;
}

.color-other {
    color: var(--text-primary);
    font-weight: 500;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .application_status {
        margin-left: var(--spacing-md);
        gap: var(--spacing-sm);
    }

    .application_status ul {
        gap: var(--spacing-sm);
    }

    .application_status ul li {
        font-size: var(--font-size-xs);
    }
}

@media (max-width: 480px) {
    .application_status {
        margin-left: var(--spacing-sm);
        flex-direction: column;
        align-items: flex-start;
    }

    .application_status ul {
        width: 100%;
        justify-content: flex-start;
    }
}

/* Steps组件紧凑样式 */
.compact_steps {
    font-size: 12px !important;
}

.compact_steps :global(.ant-steps-item-title) {
    font-size: 12px !important;
    line-height: 1.4 !important;
    margin-bottom: 2px !important;
}

.compact_steps :global(.ant-steps-item-description) {
    font-size: 11px !important;
    line-height: 1.3 !important;
    color: #666 !important;
    margin-top: 2px !important;
}

.compact_steps :global(.ant-steps-item-icon) {
    width: 24px !important;
    height: 24px !important;
    line-height: 24px !important;
    font-size: 14px !important;
}

.compact_steps :global(.ant-steps-item-content) {
    margin-top: 4px !important;
}

.compact_steps :global(.ant-steps-item) {
    padding-bottom: 8px !important;
}

.compact_steps :global(.ant-steps-item-tail) {
    top: 12px !important;
}