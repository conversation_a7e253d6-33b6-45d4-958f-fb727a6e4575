// 交通费审批API实现

import {
  PendingApproval,
  ApprovalRequest,
  BatchApprovalRequest,
  GetPendingApprovalsRequest,
  GetPendingApprovalsResponse,
  ApprovalResponse,
  BatchApprovalResponse,
  ApprovalStatistics,
  ApprovalStatus,
  ApprovalAction
} from '../types';

/**
 * 获取待审批申请列表
 * GET /in/approval/transportation_fee/pending
 */
export const getPendingApprovals = async (
  token: string,
  params?: GetPendingApprovalsRequest
): Promise<GetPendingApprovalsResponse> => {
  // TODO: 实现实际的API调用
  // const queryParams = new URLSearchParams();
  // if (params?.page) queryParams.append('page', params.page.toString());
  // if (params?.page_size) queryParams.append('page_size', params.page_size.toString());
  // if (params?.status) queryParams.append('status', params.status);
  // if (params?.department) queryParams.append('department', params.department);
  // if (params?.date_from) queryParams.append('date_from', params.date_from);
  // if (params?.date_to) queryParams.append('date_to', params.date_to);
  // if (params?.applicant_name) queryParams.append('applicant_name', params.applicant_name);
  
  // const response = await fetch(`/in/approval/transportation_fee/pending?${queryParams}`, {
  //   method: 'GET',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    total: 2,
    page: 1,
    page_size: 10,
    approvals: [
      {
        code: 'ba8dfe8a519411f0bc5bc025a5c7be3d1fff86691b38438897df489676e518b4',
        user_id: 553,
        name: '朱国威',
        start_day: '2024-06-01',
        end_day: '2024-06-29',
        route_from: '住所A',
        route_to: '出勤地B',
        fee_amount_single: 100.0,
        fee_amount_monthly: 3000.0,
        reason: '测试交通费报销申请',
        status: 'pending',
        submitted_at: '2024-06-01T09:00:00Z',
        applicant_department: '技术部',
        applicant_position: '开发工程师',
        priority: 'normal',
        approval_history: [],
        created_at: '2024-06-01T09:00:00Z',
        updated_at: '2024-06-01T09:00:00Z'
      },
      {
        code: '141bbda653f611f0bf06c025a5c7be3d09494c9c5c9a48ff9cb480a4862d6aba',
        user_id: 721,
        name: '宋柳叶',
        start_day: '2024-07-01',
        end_day: '2024-07-31',
        route_from: '住所B',
        route_to: '出勤地A',
        fee_amount_single: 150.0,
        fee_amount_monthly: 4000.0,
        reason: '月度交通费申请',
        status: 'pending',
        submitted_at: '2024-07-01T10:30:00Z',
        applicant_department: '人事部',
        applicant_position: '人事专员',
        priority: 'high',
        approval_history: [],
        created_at: '2024-07-01T10:30:00Z',
        updated_at: '2024-07-01T10:30:00Z'
      }
    ]
  };
};

/**
 * 审批申请
 * POST /in/approval/transportation_fee/approve
 */
export const approveApplication = async (
  token: string,
  data: ApprovalRequest
): Promise<ApprovalResponse> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/approval/transportation_fee/approve', {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  //   body: JSON.stringify(data),
  // });
  // return response.json();
  
  // 临时返回模拟数据
  const actionText = data.action === ApprovalAction.APPROVE ? '审批通过' : 
                    data.action === ApprovalAction.REJECT ? '审批拒绝' : '审批退回';
  
  return {
    status: 'OK',
    message: `交通费申请${actionText}成功`,
    approval_id: 'approval_' + Date.now()
  };
};

/**
 * 批量审批申请
 * POST /in/approval/transportation_fee/batch_approve
 */
export const batchApproveApplications = async (
  token: string,
  data: BatchApprovalRequest
): Promise<BatchApprovalResponse> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/approval/transportation_fee/batch_approve', {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  //   body: JSON.stringify(data),
  // });
  // return response.json();
  
  // 临时返回模拟数据
  const actionText = data.action === ApprovalAction.APPROVE ? '批量审批通过' : 
                    data.action === ApprovalAction.REJECT ? '批量审批拒绝' : '批量审批退回';
  
  return {
    status: 'OK',
    message: `${actionText}成功`,
    success_count: data.application_codes.length,
    failed_count: 0
  };
};

/**
 * 获取审批统计
 * GET /in/approval/transportation_fee/statistics
 */
export const getApprovalStatistics = async (
  token: string,
  date_from?: string,
  date_to?: string
): Promise<{ status: 'OK' | 'ERROR'; statistics: ApprovalStatistics; message?: string }> => {
  // TODO: 实现实际的API调用
  // const queryParams = new URLSearchParams();
  // if (date_from) queryParams.append('date_from', date_from);
  // if (date_to) queryParams.append('date_to', date_to);
  
  // const response = await fetch(`/in/approval/transportation_fee/statistics?${queryParams}`, {
  //   method: 'GET',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    statistics: {
      total_pending: 5,
      total_approved: 23,
      total_rejected: 2,
      avg_approval_time: 4.5,
      pending_by_department: {
        '技术部': 2,
        '人事部': 1,
        '财务部': 1,
        '市场部': 1
      },
      approval_rate: 92.0
    }
  };
};

/**
 * 获取审批历史
 * GET /in/approval/transportation_fee/history
 */
export const getApprovalHistory = async (
  token: string,
  application_code: string
): Promise<{ status: 'OK' | 'ERROR'; history: any[]; message?: string }> => {
  // TODO: 实现实际的API调用
  // const response = await fetch(`/in/approval/transportation_fee/history?code=${application_code}`, {
  //   method: 'GET',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    history: [
      {
        id: 'history_1',
        application_code: application_code,
        approver_id: 1001,
        approver_name: '管理员',
        action: 'approve',
        comment: '申请材料齐全，同意审批',
        approved_at: '2024-06-02T14:30:00Z',
        created_at: '2024-06-02T14:30:00Z'
      }
    ]
  };
};
