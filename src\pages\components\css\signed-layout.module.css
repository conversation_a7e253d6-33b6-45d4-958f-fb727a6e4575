/* 主布局容器 */
.main_layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
  background: #f5f5f5;
}

/* 左侧边栏 */
.sidebar {
  width: 280px;
  min-width: 280px;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1000;
}

/* 主内容区域 */
.main_content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #ffffff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main_layout {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    min-width: unset;
    height: auto;
    max-height: 60vh;
  }
  
  .main_content {
    flex: 1;
    overflow-y: auto;
  }
}

@media (max-width: 576px) {
  .sidebar {
    max-height: 50vh;
  }
}
