import React, { useState } from 'react';
import {Button, Input, Select, DatePicker, Cascader, Tag} from 'antd';

const InputGroup = Input.Group;
const { Option } = Select;

const options = [
    {
      value: 'zhejiang',
      label: 'Zhejiang',
      children: [
        {
          value: 'hangzhou',
          label: 'Hangzhou',
        },
      ],
    },
    {
      value: 'jiangsu',
      label: '<PERSON><PERSON>',
      children: [
        {
          value: 'nanjing',
          label: 'Nanjing',
        },
      ],
    },
  ];

const LeaveApplicationForms = (props:any) => {
  const [cascaderCount,setCascaderCount] = useState(1);

  return (
    <div>
        <div style={{width:'5%',backgroundColor:'white',color:'blue',marginTop:'40px',borderTopLeftRadius:'10px',borderTopRightRadius:'10px'}}>新建申请</div>
        <div style={{width:'70%',backgroundColor:'white',height:'400px',borderBottomLeftRadius:'10px',borderBottomRightRadius:'10px',borderTopRightRadius:'10px'}}>
            <div style={{marginTop:'15px',width:'100%',float:'left'}}>
                <div style={{width:'7%',float:'left'}}>申请人：</div>
                <InputGroup compact style={{width:'90%',float:'left'}}>
                    <Select defaultValue="Option1-1">
                        <Option value="Option1-1">Option1-1</Option>
                        <Option value="Option1-2">Option1-2</Option>
                    </Select>
                </InputGroup>
            </div>
            <br/>
            <div style={{marginTop:'15px',width:'100%',float:'left'}}>
                <div style={{width:'7%',float:'left'}}>请假类型：</div>
                <InputGroup compact>
                    <Select defaultValue="Option1-1" options={options}>
                    </Select>
                </InputGroup>
            </div>
            <div style={{marginTop:'15px',width:'100%',float:'left'}}>
                <div style={{width:'7%',float:'left'}}>请假事由：</div>
                <Input style={{width:'93%',float:'left'}}></Input>
            </div>
            <div style={{marginTop:'15px',width:'100%',float:'left'}}>
                <div style={{width:'7%',float:'left'}}>开始时间：</div>
                <InputGroup compact style={{width:'10%',float:'left'}}>
                    <DatePicker />
                </InputGroup>
                <InputGroup compact style={{width:'10%',float:'left',marginLeft:'20px'}}>
                    <Select defaultValue="Option1-1">
                        <Option value="Option1-1">Option1-1</Option>
                        <Option value="Option1-2">Option1-2</Option>
                    </Select>
                </InputGroup>
                <div style={{width:'7%',float:'left',marginLeft:'150px'}}>结束时间：</div>
                <InputGroup compact style={{width:'10%',float:'left'}}>
                    <DatePicker style={{ width:'100%' }} />
                </InputGroup>
                <InputGroup compact style={{width:'39%',float:'left',marginLeft:'20px'}}>
                    <Select defaultValue="Option1-1">
                        <Option value="Option1-1">Option1-1</Option>
                        <Option value="Option1-2">Option1-2</Option>
                    </Select>
                </InputGroup>
            </div>
            <div style={{marginTop:'15px',width:'100%',float:'left'}}>
                <div style={{float:'left',marginLeft:'0px'}}>请假时长：</div>
                <div style={{float:'left',marginLeft:'0px'}}>调休:8小时</div>
                <div style={{float:'left',marginLeft:'10px'}}>事假:8小时</div>
            </div>
            <div style={{marginTop:'15px',width:'100%',float:'left'}}>
                <div style={{float:'left',marginLeft:'0px',width:'7%'}}>项目审批：</div>
                <div style={{float:'left',marginLeft:'0px',width:'90%'}}>
                    <Button onClick={() => setCascaderCount(cascaderCount + 1)} style={{
                    float:'right',width:'4%',backgroundColor:'purple',marginRight:'30px'}}>+</Button>
                    {Array.from({ length: cascaderCount}).map((_, index) => (
                        <InputGroup compact key = {index} style={{marginLeft:'10px',marginTop:'5px',float:'left',width:'20%'}}>
                            <Cascader options={options} placeholder="Select people" />
                        </InputGroup>
                    ))}
                </div>
            </div>
            <div style={{marginTop:'15px',width:'100%',float:'left'}}>
                <div style={{float:'left',marginLeft:'0px'}}>部门审批：</div>
                <Tag style={{float:'left',marginLeft:'2px'}}>李四-JS0000</Tag>
            </div>
            <div style={{marginTop:'15px',width:'100%',float:'left'}}>
                <Button style={{marginLeft:'5%',width:'10%',float:'left'}}>提交</Button>
                <Button style={{marginLeft:'5%',width:'10%',float:'left'}}>取消</Button>
            </div>
        </div>    
    </div>
  );
};

export default LeaveApplicationForms