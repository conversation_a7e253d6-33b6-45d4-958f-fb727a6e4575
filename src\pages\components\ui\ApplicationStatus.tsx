import React from "react";
import { useApplicationSelector } from "@/hook/hooks";
import { Row, Col, Steps } from 'antd';
import { CheckCircleOutlined, ClockCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import styles from '../css/applicationStatus.module.css';
import dayjs from 'dayjs';
import { applicationData } from '@/slice/applicationSlice';
import { approvalData } from '@/slice/approvalSlice';
import { useTranslation } from '@/hooks/useTranslation';

interface status {
    code: string;
    type: string;
    page: string;
}
const ApplicationStatus = ({ code, type, page }: status) => {
    const { t } = useTranslation(['common', 'application']);
    const data = useApplicationSelector(applicationData);
    const approvalListData = useApplicationSelector(approvalData);
    
    let isLeader = 0;
    let isHighLeader = 0;
    let listStatus = data.overtimeApplication.workflow_list[code];
    let approvalListStatus: any = approvalListData.overtimeApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST;
    let listRejectReason:any = approvalListData.overtimeApplication.find(a => a.APPLY_CODE === code)?.reject_reason;
    if (type == 'overtime') {
        if(page == "application"){
            listRejectReason = data.overtimeApplication.currentData.find(a => a.code === code)?.reject_reason;
            data.overtimeApplication.workflow_list[code]?.map((item:any)=>{
                if(item.workflow_index == 1 && item.handle_user_id == -1){
                    isLeader = 1
                }
                if(item.workflow_index == 2 && item.handle_user_id == -2){
                    isHighLeader = 1
                }
            })
            if(isLeader == 1){
                listStatus = data.overtimeApplication.workflow_list[code].filter(
                            (item:any)=>item.workflow_index!=1||(item.workflow_index==1&&item.handle_user_id == -1)
                            )
                
            }else if(isHighLeader == 1){
                listStatus = data.overtimeApplication.workflow_list[code].filter(
                    (item:any)=>item.workflow_index!=2||(item.workflow_index==2&&item.handle_user_id == -2)
                    )
            }else{
                listStatus = data.overtimeApplication.workflow_list[code];
            }
        }
        isLeader = 0
        isHighLeader = 0
        if(page == "approve"){
            listRejectReason = approvalListData.overtimeApplication.find(a => a.APPLY_CODE === code)?.reject_reason;
            approvalListData.overtimeApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST.map((item:any)=>{
                if(item.workflow_index == 1 && item.handle_user_id == -1){
                    isLeader = 1
                }
                if(item.workflow_index == 2 && item.handle_user_id == -2){
                    isHighLeader = 1
                }
            })
            if(isLeader == 1){
                approvalListStatus = approvalListData.overtimeApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST.filter(
                            (item:any)=>item.workflow_index!=1||(item.workflow_index==1&&item.handle_user_id == -1)
                            )
                
            }else if(isHighLeader == 1){
                approvalListStatus = approvalListData.overtimeApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST.filter(
                    (item:any)=>item.workflow_index!=2||(item.workflow_index==2&&item.handle_user_id == -2)
                    )
            }else{
                approvalListStatus = approvalListData.overtimeApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST;
            }
        }
        
    } else if (type == 'leave') {
        if(page == "application"){
            listRejectReason = data.leaveApplication.currentData.find(a => a.code === code)?.reject_reason;
            data.leaveApplication.workflow_list[code]?.map((item:any)=>{
                if(item.workflow_index == 1 && item.handle_user_id == -1){
                    isLeader = 1
                }
                if(item.workflow_index == 2 && item.handle_user_id == -2){
                    isHighLeader = 1
                }
            })
            if(isLeader == 1){
                listStatus = data.leaveApplication.workflow_list[code].filter(
                            (item:any)=>item.workflow_index!=1||(item.workflow_index==1&&item.handle_user_id == -1)
                            )
                
            }else if(isHighLeader == 1){
                listStatus = data.leaveApplication.workflow_list[code].filter(
                    (item:any)=>item.workflow_index!=2||(item.workflow_index==2&&item.handle_user_id == -2)
                    )
            }else{
                listStatus = data.leaveApplication.workflow_list[code];
            }
        }
        isLeader = 0
        isHighLeader = 0
        if(page == "approve"){
            listRejectReason = approvalListData.leaveApplication.find(a => a.APPLY_CODE === code)?.reject_reason;
            approvalListData.leaveApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST.map((item:any)=>{
                if(item.workflow_index == 1 && item.handle_user_id == -1){
                    isLeader = 1
                }
                if(item.workflow_index == 2 && item.handle_user_id == -2){
                    isHighLeader = 1
                }
            })
            if(isLeader == 1){
                approvalListStatus = approvalListData.leaveApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST.filter(
                            (item:any)=>item.workflow_index!=1||(item.workflow_index==1&&item.handle_user_id == -1)
                            )
                
            }else if(isHighLeader == 1){
                approvalListStatus = approvalListData.leaveApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST.filter(
                    (item:any)=>item.workflow_index!=2||(item.workflow_index==2&&item.handle_user_id == -2)
                    )
            }else{
                approvalListStatus = approvalListData.leaveApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST;
            }
        }
    } else if (type == 'businessTrip') {
        if(page == "application"){
            listRejectReason = data.businessTripApplication.currentData.find(a => a.code === code)?.reject_reason;
            data.businessTripApplication.workflow_list[code]?.map((item:any)=>{
                if(item.workflow_index == 1 && item.handle_user_id == -1){
                    isLeader = 1
                }
                if(item.workflow_index == 2 && item.handle_user_id == -2){
                    isHighLeader = 1
                }
            })
            if(isLeader == 1){
                listStatus = data.businessTripApplication.workflow_list[code].filter(
                            (item:any)=>item.workflow_index!=1||(item.workflow_index==1&&item.handle_user_id == -1)
                            )
                
            }else if(isHighLeader == 1){
                listStatus = data.businessTripApplication.workflow_list[code].filter(
                    (item:any)=>item.workflow_index!=2||(item.workflow_index==2&&item.handle_user_id == -2)
                    )
            }else{
                listStatus = data.businessTripApplication.workflow_list[code];
            }
        }
        isLeader = 0
        isHighLeader = 0
        if(page == "approve"){
            listRejectReason = approvalListData.evectionApplication.find(a => a.APPLY_CODE === code)?.reject_reason;
            approvalListData.evectionApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST.map((item:any)=>{
                if(item.workflow_index == 1 && item.handle_user_id == -1){
                    isLeader = 1
                }
                if(item.workflow_index == 2 && item.handle_user_id == -2){
                    isHighLeader = 1
                }
            })
            if(isLeader == 1){
                approvalListStatus = approvalListData.evectionApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST.filter(
                            (item:any)=>item.workflow_index!=1||(item.workflow_index==1&&item.handle_user_id == -1)
                            )
                
            }else if(isHighLeader == 1){
                approvalListStatus = approvalListData.evectionApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST.filter(
                    (item:any)=>item.workflow_index!=2||(item.workflow_index==2&&item.handle_user_id == -2)
                    )
            }else{
                approvalListStatus = approvalListData.evectionApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST;
            }
        }
    } else if (type == 'confirmation') {
        if(page == "application"){
            listRejectReason = data.confirmationApplication.currentData.find(a => a.code === code)?.reject_reason;
            data.confirmationApplication.workflow_list[code]?.map((item:any)=>{
                if(item.workflow_index == 1 && item.handle_user_id == -1){
                    isLeader = 1
                }
                if(item.workflow_index == 2 && item.handle_user_id == -2){
                    isHighLeader = 1
                }
            })
            if(isLeader == 1){
                listStatus = data.confirmationApplication.workflow_list[code].filter(
                            (item:any)=>item.workflow_index!=1||(item.workflow_index==1&&item.handle_user_id == -1)
                            )
                
            }else if(isHighLeader == 1){
                listStatus = data.confirmationApplication.workflow_list[code].filter(
                    (item:any)=>item.workflow_index!=2||(item.workflow_index==2&&item.handle_user_id == -2)
                    )
            }else{
                listStatus = data.confirmationApplication.workflow_list[code];
            }
        }
        isLeader = 0
        isHighLeader = 0
        if(page == "approve"){
            listRejectReason = approvalListData.confirmationApplication.find(a => a.APPLY_CODE === code)?.reject_reason;
            approvalListData.confirmationApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST.map((item:any)=>{
                if(item.workflow_index == 1 && item.handle_user_id == -1){
                    isLeader = 1
                }
                if(item.workflow_index == 2 && item.handle_user_id == -2){
                    isHighLeader = 1
                }
            })
            if(isLeader == 1){
                approvalListStatus = approvalListData.confirmationApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST.filter(
                            (item:any)=>item.workflow_index!=1||(item.workflow_index==1&&item.handle_user_id == -1)
                            )
                
            }else if(isHighLeader == 1){
                approvalListStatus = approvalListData.confirmationApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST.filter(
                    (item:any)=>item.workflow_index!=2||(item.workflow_index==2&&item.handle_user_id == -2)
                    )
            }else{
                approvalListStatus = approvalListData.confirmationApplication.find(a => a.APPLY_CODE === code)?.WORKFLOW_LIST;
            }
        }
    }
    const dListStatus = listStatus ? Object.keys(listStatus) : [];
    const dApprovalListStatus = approvalListStatus ? Object.keys(approvalListStatus) : [];

    // 生成Steps数据的函数
    const generateStepsData = (statusList: any, statusKeys: any[], rejectReason: any) => {
        if (!statusList) return [];

        const steps = statusKeys.map((item: any, index: number) => {
            const currentStatus = statusList[item];
            let status: 'wait' | 'process' | 'finish' | 'error' = 'wait';
            let icon = <ClockCircleOutlined />;
            let title = '';
            let description = '';

            // 判断是否为第一步（提出申请）
            if (item === '0') {
                title = t('application:status.submitted', '提出');
                if (currentStatus.workflow_result === 1) {
                    status = 'finish';
                    icon = <CheckCircleOutlined style={{ fontSize: '14px' }} />;
                    description = dayjs(currentStatus.update_time).format('MM-DD HH:mm');
                } else {
                    status = 'process';
                    icon = <ClockCircleOutlined style={{ fontSize: '14px' }} />;
                    description = dayjs(currentStatus.update_time).format('MM-DD HH:mm');
                }
            } else {
                // 审批步骤
                title = currentStatus.workflow_role_name || t('application:status.approval', '审批');

                if (currentStatus.workflow_result === 1) {
                    // 审批通过
                    status = 'finish';
                    icon = <CheckCircleOutlined style={{ fontSize: '14px' }} />;
                    description = `${currentStatus.update_time ? dayjs(currentStatus.update_time).format('MM-DD HH:mm') : ''} ${currentStatus.handle_user_name || ''}`;
                } else if (currentStatus.workflow_result === 2) {
                    // 审批驳回
                    status = 'error';
                    icon = <CloseCircleOutlined style={{ fontSize: '14px' }} />;
                    description = `${dayjs(currentStatus.update_time).format('MM-DD HH:mm')} ${currentStatus.workflow_role_name || ''} - ${t('application:status.rejected', '驳回')}`;
                } else if (currentStatus.workflow_result === 0 || currentStatus.workflow_result === null) {
                    // 等待审批
                    status = 'process';
                    icon = <ClockCircleOutlined style={{ fontSize: '14px' }} />;
                    description = `${t('application:status.waiting', '等待')}${currentStatus.workflow_role_name || ''}`;
                } else {
                    // 状态异常
                    status = 'error';
                    icon = <ExclamationCircleOutlined style={{ fontSize: '14px' }} />;
                    description = `${dayjs(currentStatus.update_time).format('MM-DD HH:mm')} - ${t('application:status.abnormal', '状态异常')}`;
                }
            }

            return {
                title,
                description,
                status,
                icon
            };
        });

        // 添加人事抄送步骤
        const lastStatus = statusList[statusKeys[statusKeys.length - 1]];
        if (lastStatus) {
            steps.push({
                title: t('application:status.hrCopy', '人事抄送'),
                description: '',
                status: lastStatus.workflow_result === 1 ? 'finish' : 'wait' as 'wait' | 'process' | 'finish' | 'error',
                icon: lastStatus.workflow_result === 1 ?
                    <CheckCircleOutlined style={{ fontSize: '14px' }} /> :
                    <ClockCircleOutlined style={{ fontSize: '14px' }} />
            });
        }

        return steps;
    };

    return (
        <>
            <div style={{
                padding: '12px 16px',
                background: '#fafafa',
                borderRadius: '6px',
                minHeight: '60px'
            }}>
                {/* 申请页面的审批进度 */}
                {listStatus && page === "application" && (
                    <Steps
                        direction="horizontal"
                        size="small"
                        items={generateStepsData(listStatus, dListStatus, listRejectReason)}
                        style={{
                            marginBottom: '0',
                            fontSize: '12px'
                        }}
                        className={styles.compact_steps}
                    />
                )}

                {/* 审批页面的审批进度 */}
                {approvalListStatus && page === "approve" && (
                    <Steps
                        direction="horizontal"
                        size="small"
                        items={generateStepsData(approvalListStatus, dApprovalListStatus, listRejectReason)}
                        style={{
                            marginBottom: '0',
                            fontSize: '12px'
                        }}
                        className={styles.compact_steps}
                    />
                )}

                {/* 申请已通过的情况 */}
                {!listStatus && page === "application" && (
                    <div style={{ textAlign: 'center', padding: '8px 0' }}>
                        <CheckCircleOutlined style={{ fontSize: '16px', color: '#52c41a', marginRight: '6px' }} />
                        <span style={{ fontSize: '13px', color: '#52c41a', fontWeight: '600' }}>
                            {t('application:status.approved', '申请通过')}
                        </span>
                    </div>
                )}
            </div>

        </>
    );
};

export default ApplicationStatus;