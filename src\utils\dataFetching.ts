// 数据获取工具 - 替代Next.js的数据获取方法

import { getApi, postApi } from '../api/fetch-api'

// 通用数据获取函数
export async function fetchPageData<T>(
  apiUrl: string,
  params?: any
): Promise<T> {
  try {
    const response = await getApi(apiUrl, params)
    
    if (response.success) {
      return response.data
    } else {
      throw new Error(response.message || 'Failed to fetch data')
    }
  } catch (error) {
    console.error('Data fetching error:', error)
    throw error
  }
}

// 提交数据函数
export async function submitPageData<T>(
  apiUrl: string,
  data: any
): Promise<T> {
  try {
    const response = await postApi(apiUrl, data)
    
    if (response.success) {
      return response.data
    } else {
      throw new Error(response.message || 'Failed to submit data')
    }
  } catch (error) {
    console.error('Data submission error:', error)
    throw error
  }
}

// 批量数据获取
export async function fetchMultipleData(
  requests: Array<{ url: string; params?: any }>
): Promise<any[]> {
  try {
    const promises = requests.map(req => fetchPageData(req.url, req.params))
    return await Promise.all(promises)
  } catch (error) {
    console.error('Multiple data fetching error:', error)
    throw error
  }
}

// 缓存数据获取（简单内存缓存）
const dataCache = new Map<string, { data: any; timestamp: number }>()
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟

export async function fetchCachedData<T>(
  cacheKey: string,
  fetchFunction: () => Promise<T>,
  cacheDuration: number = CACHE_DURATION
): Promise<T> {
  const cached = dataCache.get(cacheKey)
  const now = Date.now()
  
  if (cached && (now - cached.timestamp) < cacheDuration) {
    return cached.data
  }
  
  const data = await fetchFunction()
  dataCache.set(cacheKey, { data, timestamp: now })
  
  return data
}

// 清除缓存
export function clearDataCache(cacheKey?: string) {
  if (cacheKey) {
    dataCache.delete(cacheKey)
  } else {
    dataCache.clear()
  }
}
