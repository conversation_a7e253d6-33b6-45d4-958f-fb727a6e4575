import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuthGuard } from '@/hooks/useAuthGuard'
import { routeConfig, shortUrlMapping } from '@/config/routes'

// 页面组件导入
import LoginPage from '@/pages/login/features/login/index'
import ResetPage from '@/pages/login/features/reset/index'
import UserInformation from '@/pages/my/features/information/index'
import MyAttendance from '@/pages/attendance/features/my/index'
import TransportationExpenseApplicationPage from '@/pages/application/features/transportationExpense/index'
import TransportationExpenseApprovalPage from '@/pages/approval/features/transportationExpense/index'
import DepartmentStatisticsPage from '@/pages/statistics/features/department/index'
import PaidLeavePage from '@/pages/my/features/paidLeave/index'
// 测试组件已删除

// 布局组件
import SignedLayout from '@/components/layout/SignedLayout'
import ProtectedRoute from '@/components/auth/ProtectedRoute'


function App() {
  // 使用认证守卫钩子
  useAuthGuard()

  return (
    <Routes>
      {/* 公开路由 */}
      <Route path="/login" element={<LoginPage />} />
      <Route path="/reset" element={<ResetPage />} />

      {/* 短链接重定向路由 - 动态生成 */}
      {Object.entries(shortUrlMapping).map(([shortUrl, fullUrl]) => (
        <Route
          key={shortUrl}
          path={shortUrl}
          element={<Navigate to={fullUrl} replace />}
        />
      ))}

      {/* 受保护的路由 */}
      <Route path="/" element={<ProtectedRoute><SignedLayout /></ProtectedRoute>}>
        <Route index element={<Navigate to="/my/information" replace />} />

        {/* 我的 */}
        <Route path="my/information" element={<UserInformation />} />

        {/* 考勤功能 */}
        <Route path="attendance/my" element={<MyAttendance />} />

        {/* 申请功能 */}
        <Route path="application/transportationExpense" element={<TransportationExpenseApplicationPage />} />

        {/* 审批承认 */}
        <Route path="approval/transportationExpense" element={<TransportationExpenseApprovalPage />} />

        {/* 数据统计 */}
        <Route path="statistics/department" element={<DepartmentStatisticsPage />} />

        {/* 保留的路由（向后兼容） */}
        <Route path="user/information" element={<Navigate to="/my/information" replace />} />
        <Route path="profile/paidLeave" element={<PaidLeavePage />} />
      </Route>

      {/* 404 重定向 */}
      <Route path="*" element={<Navigate to="/login" replace />} />
    </Routes>
  )
}

export default App
