import { RootState } from "@/store";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// 定义要保存到Store的数据格式
export interface informationState {
    old_password: string,
    new_password: string,
    new_pass_confirm: string,
    male: boolean,
    female: boolean,
    show: boolean
}

// 初始化数据
const initialState = {
    old_password: '',
    new_password: '',
    new_pass_confirm: '',
    male: true,
    female: false,
    show: false
};

export const informationSlice = createSlice({
    name: 'information',
    // reducers 集合了reducer和action的功能
    // 每个case冒号之前的为action名
    // 每个case整体为reducer
    initialState,
    reducers: {
        resetPass: (state: informationState, action: PayloadAction<any>) => {
            state.old_password = action.payload.old_password;
            state.new_password = action.payload.new_password;
            state.new_pass_confirm = action.payload.new_pass_confirm;
        },
        setMaleSex: (state: informationState) => {
            state.male = true;
            state.female = false;
            state.show = false;
        },
        setFemaleSex: (state: informationState) => {
            state.male = false;
            state.female = true;
            state.show = true;
        }
    }
});

export const { actions: informationActions } = informationSlice;

export default informationSlice.reducer;

//state 后面的为store中数据名称
export const informationData = (state: RootState) => state.information;