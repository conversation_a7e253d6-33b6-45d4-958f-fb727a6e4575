import { getApi, postApi } from '@/api/fetch-api'
import ApiUrlVars from '@/api/common/url-vars'
import { statusActions } from '@/slice/statusSlice'

/**
 * 申请类型枚举
 */
export enum ApplicationType {
  LEAVE = 'leave',
  OVERTIME = 'overtime',
  BUSINESS_TRIP = 'business_trip',
  TRANSPORTATION_EXPENSE = 'transportation_expense',
  EDIT_ST_ET = 'edit_st_et'
}

/**
 * 请假申请参数接口
 */
export interface LeaveApplicationParams {
  use_type: number
  user_id: string
  agent_user_id: number
  leave_type: string
  reason: string
  start_time: string
  end_time: string
  workflow_list: string
  half_day_type?: number
  related_work_date?: string
}

/**
 * 交通费申请参数接口
 */
export interface TransportationExpenseParams {
  use_type: number
  user_id: string
  agent_user_id: number
  departure: string
  destination: string
  regular_pass_amount: number
  single_trip_amount: number
  reason: string
  start_date: string
  workflow_list: string
}

/**
 * 加班申请参数接口
 */
export interface OvertimeApplicationParams {
  use_type: number
  user_id: string
  agent_user_id: number
  overtime_type: string
  reason: string
  start_time: string
  end_time: string
  workflow_list: string
}

/**
 * 出差申请参数接口
 */
export interface BusinessTripParams {
  use_type: number
  user_id: string
  agent_user_id: number
  destination: string
  purpose: string
  start_time: string
  end_time: string
  workflow_list: string
}

/**
 * 申请响应接口
 */
export interface ApplicationResponse {
  status: string
  message?: string
  data?: any
}

/**
 * 获取请假申请列表
 * @param userId 用户ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApplicationResponse>
 */
export async function getLeaveApplicationList(userId: string, dispatch: any): Promise<ApplicationResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.leaveList_get
    const params = { user_id: userId }
    const response = await getApi(url, params, dispatch)
    
    return {
      status: response.data.status || 'OK',
      data: response.data,
      message: response.data.message
    }
  } catch (error: any) {
    console.error('Get leave application list API error:', error)
    return {
      status: 'NG',
      message: error.message || '获取请假申请列表失败'
    }
  }
}

/**
 * 提交请假申请
 * @param params 请假申请参数
 * @param leaveType 请假类型 (normal, sick, other)
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApplicationResponse>
 */
export async function submitLeaveApplication(
  params: LeaveApplicationParams, 
  leaveType: 'normal' | 'sick' | 'other',
  dispatch: any
): Promise<ApplicationResponse> {
  try {
    let url: string
    switch (leaveType) {
      case 'normal':
        url = ApiUrlVars.api_domain + ApiUrlVars.leaveListNormalAdd_post
        break
      case 'sick':
        url = ApiUrlVars.api_domain + ApiUrlVars.leaveListSickAdd_post
        break
      case 'other':
        url = ApiUrlVars.api_domain + ApiUrlVars.leaveListOtherAdd_post
        break
      default:
        url = ApiUrlVars.api_domain + ApiUrlVars.leaveListNormalAdd_post
    }
    
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '提交失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '提交成功'
    }
  } catch (error: any) {
    console.error('Submit leave application API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 撤回请假申请
 * @param code 申请编号
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApplicationResponse>
 */
export async function cancelLeaveApplication(code: string, dispatch: any): Promise<ApplicationResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.leaveCancel_post
    const params = { code }
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '撤回失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '撤回成功'
    }
  } catch (error: any) {
    console.error('Cancel leave application API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 提交交通费申请
 * @param params 交通费申请参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApplicationResponse>
 */
export async function submitTransportationExpense(
  params: TransportationExpenseParams, 
  dispatch: any
): Promise<ApplicationResponse> {
  try {
    // TODO: 替换为实际的交通费申请API
    const url = ApiUrlVars.api_domain + ApiUrlVars.leaveListNormalAdd_post // 暂时使用leave的API
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '提交失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '提交成功'
    }
  } catch (error: any) {
    console.error('Submit transportation expense API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 撤回交通费申请
 * @param code 申请编号
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApplicationResponse>
 */
export async function cancelTransportationExpense(code: string, dispatch: any): Promise<ApplicationResponse> {
  try {
    // TODO: 替换为实际的交通费撤回API
    const url = ApiUrlVars.api_domain + ApiUrlVars.leaveCancel_post // 暂时使用leave的API
    const params = { code }
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '撤回失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '撤回成功'
    }
  } catch (error: any) {
    console.error('Cancel transportation expense API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取加班申请列表
 * @param userId 用户ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApplicationResponse>
 */
export async function getOvertimeApplicationList(userId: string, dispatch: any): Promise<ApplicationResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.overtimeList_get
    const params = { user_id: userId }
    const response = await getApi(url, params, dispatch)
    
    return {
      status: response.data.status || 'OK',
      data: response.data,
      message: response.data.message
    }
  } catch (error: any) {
    console.error('Get overtime application list API error:', error)
    return {
      status: 'NG',
      message: error.message || '获取加班申请列表失败'
    }
  }
}

/**
 * 提交加班申请
 * @param params 加班申请参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApplicationResponse>
 */
export async function submitOvertimeApplication(
  params: OvertimeApplicationParams, 
  dispatch: any
): Promise<ApplicationResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.overtimeNewList_post
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '提交失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '提交成功'
    }
  } catch (error: any) {
    console.error('Submit overtime application API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取申请页面每页数据个数
 * @param dispatch Redux dispatch函数
 * @returns Promise<ApplicationResponse>
 */
export async function getApplicationPageSize(dispatch: any): Promise<ApplicationResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.apply_day_get
    const response = await getApi(url, null, dispatch)
    
    return {
      status: response.data.status || 'OK',
      data: response.data,
      message: response.data.message
    }
  } catch (error: any) {
    console.error('Get application page size API error:', error)
    return {
      status: 'NG',
      message: error.message || '获取页面配置失败'
    }
  }
}
