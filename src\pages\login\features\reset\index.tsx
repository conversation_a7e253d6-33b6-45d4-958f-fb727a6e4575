import React, { useState } from 'react'
import { Form, Input, <PERSON><PERSON>, Card } from 'antd'
import { useNavigate } from 'react-router-dom'
import { MailOutlined, ArrowLeftOutlined, UserOutlined } from '@ant-design/icons'
import { useTranslation } from '@/hooks/useTranslation'
import toast, { Toaster } from 'react-hot-toast'
import LanguageSwitcher from '@/components/LanguageSwitcher'
import { resetPassword } from '../../api/loginApi'
import { useApplicationDispatch } from '@/hook/hooks'
import styles from './reset.module.css'

const Reset: React.FC = () => {
  const navigate = useNavigate()
  const { t } = useTranslation('login')
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const dispatch = useApplicationDispatch()

  const handleReset = async (values: any) => {
    try {
      setLoading(true)

      // 使用loginApi中的resetPassword方法
      const response = await resetPassword({
        work_no: values.workNo,
        mail: values.email
      }, dispatch)

      if (response.data.status === 'OK') {
        toast.success(t('reset.emailSent') || '重置邮件已发送')

        // 3秒后跳转到登录页面
        setTimeout(() => {
          navigate('/login')
        }, 3000)
      } else {
        toast.error(t('reset.error') || '重置失败，请重试')
      }

    } catch (error) {
      console.error('Reset error:', error)
      toast.error(t('reset.error') || '重置失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className={styles.container}>
      <Toaster position="top-center" />

      {/* 语言切换器 */}
      <div className={styles.languageSwitcher}>
        <LanguageSwitcher />
      </div>

      {/* 装饰元素 */}
      <div className={styles.decoration1}></div>
      <div className={styles.decoration2}></div>

      {/* 重置密码卡片 */}
      <Card className={styles.card} bordered={false}>
        {/* 卡片头部 */}
        <div className={styles.header}>
          <div className={styles.iconContainer}>
            <MailOutlined />
          </div>
          <h1 className={styles.title}>
            {t('reset.title') || '重置密码'}
          </h1>
          <p className={styles.subtitle}>
            {t('reset.subtitle') || '请输入您的邮箱地址'}
          </p>
        </div>

        {/* 表单内容 */}
        <div className={styles.formContainer}>
          <Form
            form={form}
            name="reset"
            onFinish={handleReset}
            autoComplete="off"
            layout="vertical"
            size="large"
          >
            <Form.Item
              name="workNo"
              label={t('workNo') || '工号'}
              rules={[
                { required: true, message: t('reset.workNoRequired') || '请输入工号' }
              ]}
              style={{ marginBottom: '20px' }}
            >
              <Input
                prefix={<UserOutlined style={{ color: '#999' }} />}
                placeholder={t('reset.workNoRequired') || '请输入工号'}
                className={styles.input}
              />
            </Form.Item>

            <Form.Item
              name="email"
              label={t('reset.emailLabel') || '邮箱地址'}
              rules={[
                { required: true, message: t('reset.emailRequired') || '请输入邮箱地址' },
                { type: 'email', message: t('reset.emailInvalid') || '请输入有效的邮箱地址' }
              ]}
              style={{ marginBottom: '24px' }}
            >
              <Input
                prefix={<MailOutlined style={{ color: '#999' }} />}
                placeholder={t('reset.emailPlaceholder') || '请输入您的邮箱地址'}
                className={styles.input}
              />
            </Form.Item>

            <Form.Item style={{ marginBottom: '16px' }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                size="large"
                className={styles.sendButton}
              >
                {t('reset.sendEmail') || '发送重置邮件'}
              </Button>
            </Form.Item>

            <Form.Item style={{ marginBottom: 0 }}>
              <Button
                type="link"
                onClick={() => navigate('/login')}
                block
                size="large"
                className={styles.backButton}
              >
                <ArrowLeftOutlined />
                {t('reset.backToLogin') || '返回登录'}
              </Button>
            </Form.Item>
          </Form>
        </div>
      </Card>
    </div>
  )
}

export default Reset
