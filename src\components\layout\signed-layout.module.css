/* 主布局容器 - 使用CSS变量和响应式设计 */
.main_layout {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background: var(--bg-tertiary);
  font-family: inherit;
}

/* 左侧边栏 - 优化响应式行为 */
.sidebar {
  width: 280px;
  min-width: 280px;
  background: var(--bg-gradient-secondary);
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: var(--z-fixed);
  transition: var(--transition-normal);
  border-right: 1px solid var(--border-color-dark);
}

/* 收起状态 */
.sidebar.collapsed {
  width: 0;
  min-width: 0;
  opacity: 0;
  transform: translateX(-100%);
  pointer-events: none;
}

/* 主内容区域 - 优化布局和滚动 */
.main_content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background: var(--bg-primary);
  transition: var(--transition-normal);
  height: 100vh;
  min-width: 0; /* 防止flex子项溢出 */
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

/* 响应式设计 - 手机端 (max-width: 480px) */
@media (max-width: 480px) {
  .main_layout {
    flex-direction: row; /* 保持左右布局 */
  }

  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: var(--z-modal);
    width: 260px;
    min-width: 260px;
    height: 100vh;
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(10px);
    border-right: 1px solid var(--border-color-dark);
  }

  .sidebar.collapsed {
    transform: translateX(-100%);
    opacity: 0;
    pointer-events: none;
  }

  .main_content {
    width: 100%;
    margin-left: 0;
    flex: 1;
  }
}

/* 响应式设计 - 平板端 (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .main_layout {
    flex-direction: row; /* 确保左右布局 */
  }

  .sidebar {
    position: relative; /* 不使用fixed定位 */
    width: 260px;
    min-width: 260px;
    height: 100vh;
  }

  .sidebar.collapsed {
    /* 平板端不允许收起 */
    width: 260px;
    min-width: 260px;
    opacity: 1;
    transform: none;
    pointer-events: auto;
  }

  .main_content {
    flex: 1;
    width: auto;
    margin-left: 0;
  }
}

/* 响应式设计 - 桌面端 (min-width: 769px) */
@media (min-width: 769px) {
  .main_layout {
    flex-direction: row;
  }

  .sidebar {
    position: relative;
    width: 280px;
    min-width: 280px;
    height: 100vh;
  }

  .sidebar.collapsed {
    /* 桌面端不允许收起 */
    width: 280px;
    min-width: 280px;
    opacity: 1;
    transform: none;
    pointer-events: auto;
  }

  .main_content {
    flex: 1;
    margin-left: 0;
  }
}

/* 加载容器 */
.loadingContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  gap: var(--spacing-lg);
  background: var(--bg-tertiary);
}

/* 错误容器 */
.errorContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: var(--bg-tertiary);
  padding: var(--spacing-lg);
}

.errorMessage {
  padding: var(--spacing-xl);
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  font-size: var(--font-size-lg);
  color: var(--color-error);
  border: 1px solid var(--border-color);
  max-width: 500px;
  text-align: center;
}
