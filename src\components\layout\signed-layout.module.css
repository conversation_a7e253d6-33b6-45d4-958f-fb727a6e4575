/* 主布局容器 */
.main_layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
  background: #f5f5f5;
}

/* 左侧边栏 */
.sidebar {
  width: 280px;
  min-width: 280px;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1000;
  transition: all 0.3s ease;
}

/* 收起状态 */
.sidebar.collapsed {
  width: 0;
  min-width: 0;
  opacity: 0;
  transform: translateX(-100%);
  pointer-events: none;
}

/* 主内容区域 */
.main_content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background: #ffffff;
  transition: all 0.3s ease;
  height: 100vh;
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

/* 平板端保持正常左右布局 */
@media (min-width: 481px) and (max-width: 768px) {
  .main_layout {
    display: flex;
    flex-direction: row; /* 确保左右布局 */
  }

  .sidebar {
    position: relative; /* 不使用fixed定位 */
    width: 280px;
    min-width: 280px;
    height: 100vh;
  }

  .sidebar.collapsed {
    /* 平板端不允许收起 */
    width: 280px;
    min-width: 280px;
    opacity: 1;
    transform: none;
    pointer-events: auto;
  }

  .main_content {
    flex: 1;
    width: auto;
    margin-left: 0;
  }
}

/* 响应式设计 - 手机端 */
@media (max-width: 480px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    width: 260px;
    height: 100vh;
    box-shadow: 2px 0 12px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
  }

  .sidebar.collapsed {
    transform: translateX(-100%);
    width: 260px;
    opacity: 0;
    pointer-events: none;
  }

  .main_content {
    width: 100%;
    margin-left: 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main_layout {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    min-width: 100%;
    height: auto;
    position: relative;
    z-index: 999;
  }

  .main_content {
    flex: 1;
    width: 100%;
    margin-top: 0;
  }
}

/* 加载容器 */
.loadingContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  gap: 16px;
}

/* 错误容器 */
.errorContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f5f5f5;
}

.errorMessage {
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 16px;
  color: #ff4d4f;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main_layout {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    min-width: unset;
    height: auto;
    max-height: 60vh;
  }
}
