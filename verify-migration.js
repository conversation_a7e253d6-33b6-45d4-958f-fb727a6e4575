#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('🔍 验证 Next.js 到 React + Vite 迁移...\n')

// 检查必要文件是否存在
const requiredFiles = [
  'vite.config.ts',
  'index.html',
  'src/main.tsx',
  'src/App.tsx',
  'src/config/routes.ts',
  'src/hooks/useAuthGuard.ts',
  'src/components/auth/ProtectedRoute.tsx',
  'src/components/layout/SignedLayout.tsx',
  'src/styles/globals.css',
  'src/styles/layout-fixes.css'
]

console.log('📁 检查必要文件...')
let missingFiles = []

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`)
  } else {
    console.log(`❌ ${file}`)
    missingFiles.push(file)
  }
})

// 检查package.json中的脚本
console.log('\n📦 检查 package.json 脚本...')
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  const scripts = packageJson.scripts || {}
  
  const expectedScripts = {
    'dev': 'vite',
    'build': 'tsc && vite build',
    'preview': 'vite preview'
  }
  
  Object.entries(expectedScripts).forEach(([script, expected]) => {
    if (scripts[script] === expected) {
      console.log(`✅ ${script}: ${scripts[script]}`)
    } else {
      console.log(`❌ ${script}: ${scripts[script]} (期望: ${expected})`)
    }
  })
} catch (error) {
  console.log('❌ 无法读取 package.json')
}

// 检查依赖
console.log('\n📚 检查关键依赖...')
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }
  
  const requiredDeps = [
    'react',
    'react-dom',
    'react-router-dom',
    'vite',
    '@vitejs/plugin-react'
  ]
  
  const removedDeps = [
    'next',
    'next-i18next',
    'next-redux-wrapper'
  ]
  
  requiredDeps.forEach(dep => {
    if (dependencies[dep]) {
      console.log(`✅ ${dep}: ${dependencies[dep]}`)
    } else {
      console.log(`❌ 缺少依赖: ${dep}`)
    }
  })
  
  console.log('\n🗑️  检查已移除的 Next.js 依赖...')
  removedDeps.forEach(dep => {
    if (!dependencies[dep]) {
      console.log(`✅ 已移除: ${dep}`)
    } else {
      console.log(`⚠️  仍存在: ${dep}: ${dependencies[dep]}`)
    }
  })
} catch (error) {
  console.log('❌ 无法检查依赖')
}

// 检查页面组件
console.log('\n📄 检查页面组件...')
const pageComponents = [
  'src/pages/login/LoginPage.tsx',
  'src/pages/my/features/information/UserInformationPage.tsx',
  'src/pages/attendance/features/my/AttendanceImport1Page.tsx',
  'src/pages/statistics/features/department/DepartmentStatisticsPage.tsx',
  'src/pages/application/transportationExpenseApplication/TransportationExpenseApplicationPage.tsx'
]

pageComponents.forEach(component => {
  if (fs.existsSync(component)) {
    console.log(`✅ ${component}`)
  } else {
    console.log(`❌ ${component}`)
  }
})

// 生成总结
console.log('\n📊 迁移验证总结:')
if (missingFiles.length === 0) {
  console.log('🎉 所有必要文件都已创建！')
} else {
  console.log(`⚠️  缺少 ${missingFiles.length} 个文件:`)
  missingFiles.forEach(file => console.log(`   - ${file}`))
}

console.log('\n🚀 下一步操作:')
console.log('1. 运行 npm run dev 启动开发服务器')
console.log('2. 访问 http://localhost:3000 测试应用')
console.log('3. 访问 http://localhost:3000/test/routes 测试路由')
console.log('4. 测试短链接: http://localhost:3000/apply/tf')

console.log('\n✨ 迁移完成！从 Next.js 到 React + Vite 的迁移已成功完成。')
