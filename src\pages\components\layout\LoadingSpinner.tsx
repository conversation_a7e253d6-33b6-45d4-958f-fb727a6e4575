import { Spin } from "antd";
import { useApplicationSelector } from "@/hook/hooks";
import { loginData } from '@/slice/authSlice';

export default function LoadingSpinner() {
  const data = useApplicationSelector(loginData);
  
  if (!data.loading) return null;
  
  return (
    <div className='pop-up' style={{backgroundColor: 'rgba(255, 255, 255, 0.5)'}}>
      <Spin 
        spinning={data.loading} 
        delay={100}
        style={{
          display: 'flex',
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100vh'
        }} 
      />
    </div>
  );
}
