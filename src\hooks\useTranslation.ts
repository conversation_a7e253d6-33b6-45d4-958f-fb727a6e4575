import { useTranslation as useI18nTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import { loadLanguageResources } from '../lib/i18n';

export const useTranslation = (namespace?: string) => {
  const [isClient, setIsClient] = useState(false);
  const [translationReady, setTranslationReady] = useState(false);
  const [initError, setInitError] = useState<Error | null>(null);

  // 安全地获取翻译函数
  let t: any, i18n: any;
  try {
    const translation = useI18nTranslation(namespace);
    t = translation.t;
    i18n = translation.i18n;
  } catch (error) {
    // 如果翻译初始化失败，提供默认函数
    console.warn('Translation initialization failed, using fallback:', error);
    setInitError(error as Error);
    t = (key: string) => {
      // 提供更智能的后备翻译
      const fallbackTranslations: Record<string, string> = {
        'permissions.noAccess': '您没有权限访问该页面',
        'permissions.insufficient': '权限不足',
        'authentication.expired': '登录凭证失效',
        'authentication.invalid': '无效token',
        'authentication.loginExpired': '登录失效',
        'authentication.pleaseRelogin': '请返回重新登录',
        'authentication.relogin': '重新登录',
        'buttons.cancel': '取消',
        'buttons.relogin': '重新登录'
      };
      return fallbackTranslations[key] || key;
    };
    i18n = {
      language: 'zh',
      changeLanguage: () => Promise.resolve(),
      hasResourceBundle: () => false
    };
  }

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsClient(true);

      let isCancelled = false;

      // 延迟设置翻译就绪状态，确保 i18n 完全初始化
      const timer = setTimeout(() => {
        if (!isCancelled) {
          setTranslationReady(true);

          // 确保当前语言的资源已加载
          if (i18n?.language && !initError && typeof loadLanguageResources === 'function') {
            loadLanguageResources(i18n.language).catch((error) => {
              if (!isCancelled) {
                console.error('Failed to load language resources:', error);
              }
            });
          }
        }
      }, 100); // 给 i18n 一些时间初始化

      return () => {
        isCancelled = true;
        clearTimeout(timer);
      };
    }
  }, [i18n?.language, initError]);

  const changeLanguage = async (lng: string) => {
    if (typeof window !== 'undefined') {
      try {
        await loadLanguageResources(lng);
        if (i18n?.changeLanguage) {
          await i18n.changeLanguage(lng);
        }
        // 保存语言偏好到本地存储
        localStorage.setItem('preferred-language', lng);
      } catch (error) {
        console.error('Failed to change language:', error);
        throw error;
      }
    }
  };

  return {
    t,
    currentLanguage: i18n?.language || 'zh',
    changeLanguage,
    isLoading: !translationReady,
    isClient: isClient && translationReady,
    hasError: !!initError,
    error: initError,
  };
};

// 获取保存的语言偏好
export const getStoredLanguage = (): string => {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('preferred-language');
    console.log('getStoredLanguage: Retrieved language preference:', stored);
    return stored || 'zh';
  }
  return 'zh';
};
