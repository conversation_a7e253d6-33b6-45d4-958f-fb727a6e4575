import { createSlice, PayloadAction, } from "@reduxjs/toolkit";
import { RootState } from "../store";

// 定义要保存到Store的数据格式
export interface statusSlice {
    status: any,
    text: any
}
   
// 初始化数据
const initialState: statusSlice = {
    status: 'OK',
    text: ''
};

export const statusSlice = createSlice({
    name: 'status',
    initialState,
    // reducers 集合了reducer和action的功能
    // 每个case冒号之前的为action名
    // 每个case整体为reducer
    reducers: {
        // 使用PayloadAction来展示action.payload的详细内容
        onTure: (state: statusSlice) => {
            state.status = 'OK',
            state.text = '';
        },
        onFalse: (state: statusSlice, action: PayloadAction<any>) => {
            state.status = 'NG',
            state.text = action.payload;
        },
    },
});

//以下内容必须要有
export const { actions: statusActions } = statusSlice;

export default statusSlice.reducer;

//state 后面的为store中数据名称
export const statusData = (state: RootState) => state.status;
