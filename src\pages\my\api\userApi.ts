import { getApi, postApi } from '@/api/fetch-api'
import ApiUrlVars from '@/api/common/url-vars'
import { statusActions } from '@/slice/statusSlice'

/**
 * 用户信息接口
 */
export interface UserInfo {
  id: string
  name: string
  email: string
  department: string
  position: string
  employeeId: string
  joinDate: string
  avatar?: string
  phone?: string
  userType?: string // 社员/长期出张者
}

/**
 * 有薪假期信息接口
 */
export interface PaidLeaveInfo {
  totalDays: number
  grantedDays: number
  usedDays: number
  remainingDays: number
  expiredDays: number
  invalidDays: number
  expirationDate: string
  legalRequiredDays: number
}

/**
 * 请假记录接口
 */
export interface LeaveRecord {
  id: string
  type: string
  startDate: string
  endDate: string
  days: number
  reason: string
  status: 'pending' | 'approved' | 'rejected' | 'cancelled'
  appliedDate: string
}

/**
 * 密码修改参数接口
 */
export interface PasswordChangeParams {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

/**
 * 用户响应接口
 */
export interface UserResponse {
  status: string
  message?: string
  data?: any
}

/**
 * 获取用户信息
 * @param userId 用户ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<UserResponse>
 */
export async function getUserInfo(userId: string, dispatch: any): Promise<UserResponse> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.login_get
    const params = { user_id: userId }
    const response = await getApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取用户信息失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get user info API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 更新用户信息
 * @param userInfo 用户信息
 * @param dispatch Redux dispatch函数
 * @returns Promise<UserResponse>
 */
export async function updateUserInfo(userInfo: Partial<UserInfo>, dispatch: any): Promise<UserResponse> {
  try {
    // TODO: 替换为实际的用户信息更新API，暂时使用staffMemberChange_post
    const url = ApiUrlVars.api_domain + ApiUrlVars.staffMemberChange_post
    const response = await postApi(url, userInfo, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '更新用户信息失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '更新成功'
    }
  } catch (error: any) {
    console.error('Update user info API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 修改密码
 * @param params 密码修改参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<UserResponse>
 */
export async function changePassword(params: PasswordChangeParams, dispatch: any): Promise<UserResponse> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.password_change
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '密码修改失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '密码修改成功'
    }
  } catch (error: any) {
    console.error('Change password API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取有薪假期信息
 * @param userId 用户ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<UserResponse>
 */
export async function getPaidLeaveInfo(userId: string, dispatch: any): Promise<UserResponse> {
  try {
    // TODO: 替换为实际的有薪假期信息API，暂时使用个人统计API
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.summary_personal_info_get
    const params = { user_id: userId }
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取有薪假期信息失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get paid leave info API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取请假记录
 * @param userId 用户ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<UserResponse>
 */
export async function getLeaveRecords(userId: string, dispatch: any): Promise<UserResponse> {
  try {
    const url = ApiUrlVars.api_domain + ApiUrlVars.leaveList_get
    const params = { user_id: userId }
    const response = await getApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取请假记录失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get leave records API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取个人任务记录
 * @param userId 用户ID
 * @param dispatch Redux dispatch函数
 * @returns Promise<UserResponse>
 */
export async function getPersonalTasks(userId: string, dispatch: any): Promise<UserResponse> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.task_get
    const params = { user_id: userId }
    const response = await getApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取个人任务失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get personal tasks API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 获取个人统计数据
 * @param params 查询参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<UserResponse>
 */
export async function getPersonalStatistics(params: any, dispatch: any): Promise<UserResponse> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.summary_personal_info_get
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '获取个人统计失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data
    }
  } catch (error: any) {
    console.error('Get personal statistics API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 上传用户头像
 * @param file 头像文件
 * @param dispatch Redux dispatch函数
 * @returns Promise<UserResponse>
 */
export async function uploadUserAvatar(file: File, dispatch: any): Promise<UserResponse> {
  try {
    // TODO: 实现头像上传API，暂时使用staffMemberChange_post
    const formData = new FormData()
    formData.append('avatar', file)

    const url = ApiUrlVars.api_domain + ApiUrlVars.staffMemberChange_post
    const response = await postApi(url, formData, dispatch)
    
    if (response.data.status === 'NG') {
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      return {
        status: 'NG',
        message: response.data.message || '头像上传失败'
      }
    }
    
    return {
      status: 'OK',
      data: response.data,
      message: response.data.message || '头像上传成功'
    }
  } catch (error: any) {
    console.error('Upload user avatar API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}
