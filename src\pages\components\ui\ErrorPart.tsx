import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from 'antd';
import { useApplicationDispatch } from "@/hook/hooks";
import { statusActions } from '@/slice/statusSlice';
import errStyles from '@/pages/components/css/error.module.css';
import { onLogout } from '@/utils/cookies';
import { useTranslation } from '@/hooks/useTranslation';

interface ErrorProps {
    type: string;
    message: string;
}

let dispatch: any = null
const ErrorPart = ({ type, message }: ErrorProps) => {
    const navigate = useNavigate()
    const [noPre, setNoPre] = useState(true);
    const { t } = useTranslation('errors');
    dispatch = useApplicationDispatch();

    //权限失效时点击返回到登录界面
    const logout = () => {
        try {
            dispatch(statusActions.onTure())
            onLogout();

            // 清除本地存储的用户数据
            if (typeof window !== 'undefined') {
                localStorage.removeItem('login');
            }

            // 立即跳转到登录页面
            navigate('/login');
        } catch (error) {
            console.error('Logout error:', error);
            // 即使出错也要跳转到登录页面
            navigate('/login');
        }
    }

    return (
        <>
            {type == 'NG' && message == t('permissions.insufficient') &&<div style={{fontSize:'13px'}}>
                <div className={errStyles.no_permission}>
                    <div className={errStyles.title}>{t('permissions.noAccess')}</div>
                </div>
            </div>}
            {type == 'NG' && (message == t('authentication.invalid') || message == t('authentication.expired')) &&<div style={{fontSize:'13px'}}>
                <div className={errStyles.no_permission}>
                    <div className={errStyles.title}>{t('authentication.expired')}</div>
                    {noPre&&<div className='pop-up'>
                        <div className={errStyles.area}>
                            <div className={errStyles.formTitle} style={{color:'red'}}>{t('authentication.loginExpired')}</div>
                            <div className={errStyles.formTitle} style={{marginLeft:'120px',marginTop:'20px',color:'red'}}>{t('authentication.pleaseRelogin')}</div>
                            <Button className={errStyles.button} style={{marginLeft:'20px',marginTop:'30px',height:'30px'}} onClick={() => logout()}>
                                {t('buttons.relogin')}
                            </Button>
                            <Button className={errStyles.button} style={{marginLeft:'40px',marginTop:'30px',height:'30px'}} onClick={()=>setNoPre(false)}>
                                {t('buttons.cancel')}
                            </Button>
                        </div>
                    </div>}
                </div>
            </div>}
        </>
    )
}
export default ErrorPart