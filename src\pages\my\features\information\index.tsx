

import { Input, But<PERSON>, Card, Avatar, Divider, Space, Tag, Modal } from 'antd';
import React, { useState, useEffect } from 'react';
import { useApplicationSelector, useApplicationDispatch } from "@/hook/hooks";
import { informationActions, informationData } from "@/slice/user/informationSlice";
import { setUserActions, loginData } from "@/slice/authSlice";
import { get_token_aes } from '@/utils/login';

import { changePassword } from '@/pages/my/api/userApi';

import styles from './information.module.css'
import toast, { Toaster } from 'react-hot-toast';
import { onLogout } from '@/utils/cookies'
import { useTranslation } from '@/hooks/useTranslation';
import { useNavigate } from 'react-router-dom';
import {
  UserOutlined,
  MailOutlined,
  IdcardOutlined,
  TeamOutlined,
  SafetyOutlined,
  EditOutlined,
  LockOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { tabActions } from '@/slice/tabSlice';
import { menuTypes } from '@/utils/menulist';

const UserInformation: React.FC = () => {
  const [oldPass, setOldPass] = useState('')
  const [newPass, setNewPass] = useState('')
  const [newPassConfirm, setNewPassConfirm] = useState('')
  const [isPasswordModalVisible, setIsPasswordModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const dispatch = useApplicationDispatch();
  const navigate = useNavigate();
  const { t } = useTranslation('user');

  // 初始化tab状态
  useEffect(() => {
    dispatch(tabActions.onTabChange(menuTypes.profile));
    dispatch(tabActions.onSubChange('1'));
  }, [dispatch]);
  
  async function updatePassBtn(params: any) {
    const result = await changePassword(params, dispatch)
    return result;
  }

  const showPasswordModal = () => {
    setIsPasswordModalVisible(true);
  };

  const handlePasswordModalCancel = () => {
    setIsPasswordModalVisible(false);
    setOldPass('');
    setNewPass('');
    setNewPassConfirm('');
  };

  const loginDataFromStorage = typeof window !== 'undefined' ? localStorage.getItem('login') : null
  const localStorageData = loginDataFromStorage? JSON.parse(loginDataFromStorage) : {}

  const name = localStorageData.name;
  const work_no = localStorageData.work_no;
  const mail = localStorageData.mail;
  const departmentName = localStorageData.depart_name;
  const roleName = localStorageData.role_name;
  const userSex = localStorageData.sex;
  const userType = localStorageData.user_type || 'employee'; // 新增用户类型字段

  // 获取用户类型的翻译文本
  const getUserTypeText = (type: string) => {
    switch(type) {
      case 'employee':
        return t('user:userTypes.employee');
      case 'longTermBusiness':
        return t('user:userTypes.longTermBusiness');
      default:
        return t('user:userTypes.employee');
    }
  };

  const handlePasswordSubmit = async () => {
    if (!oldPass || !newPass || !newPassConfirm) {
      toast.error(t('user:validation.allFieldsRequired'), {
        style: {
          borderRadius: '12px',
          background: 'linear-gradient(135deg, rgba(231, 76, 60, 0.95) 0%, rgba(192, 57, 43, 0.98) 100%)',
          color: '#ffffff',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
        },
      });
      return;
    }

    if (newPass !== newPassConfirm) {
      toast.error(t('user:validation.passwordMismatch'), {
        style: {
          borderRadius: '12px',
          background: 'linear-gradient(135deg, rgba(231, 76, 60, 0.95) 0%, rgba(192, 57, 43, 0.98) 100%)',
          color: '#ffffff',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
        },
      });
      return;
    }

    setLoading(true);
    try {
      const res = await updatePassBtn({
        old_password: oldPass,
        new_password: newPass,
        new_pass_confirm: newPassConfirm
      });

      if (res.data.status === 'OK') {
        toast.success(t('user:messages.passwordChangeSuccess'), {
          style: {
            borderRadius: '12px',
            background: 'linear-gradient(135deg, rgba(46, 204, 113, 0.95) 0%, rgba(39, 174, 96, 0.98) 100%)',
            color: '#ffffff',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            backdropFilter: 'blur(10px)',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
          },
        });

        setIsPasswordModalVisible(false);
        setOldPass('');
        setNewPass('');
        setNewPassConfirm('');

        // 延迟登出并跳转到登录页
        setTimeout(() => {
          onLogout();
          navigate('/login');
        }, 1500);
      } else {
        toast.error(res.data?.message || t('user:messages.passwordChangeFailed'), {
          style: {
            borderRadius: '12px',
            background: 'linear-gradient(135deg, rgba(231, 76, 60, 0.95) 0%, rgba(192, 57, 43, 0.98) 100%)',
            color: '#ffffff',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            backdropFilter: 'blur(10px)',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
          },
        });
      }
    } catch (error) {
      toast.error(t('user:messages.networkError'), {
        style: {
          borderRadius: '12px',
          background: 'linear-gradient(135deg, rgba(231, 76, 60, 0.95) 0%, rgba(192, 57, 43, 0.98) 100%)',
          color: '#ffffff',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
        },
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div style={{padding: '24px', background: '#f5f5f5', minHeight: '100vh', width: '100%'}}>
        <Toaster />

        {/* 页面标题 */}
        <div style={{
          marginBottom: '32px',
          padding: '20px 0',
          borderBottom: '3px solid #e2e8f0',
          position: 'relative'
        }}>
          <div style={{
            position: 'absolute',
            bottom: '-3px',
            left: '0',
            width: '120px',
            height: '3px',
            background: 'linear-gradient(90deg, #1890ff 0%, #40a9ff 100%)',
            borderRadius: '2px'
          }}></div>
          <h1 style={{
            fontSize: '26px',
            fontWeight: '600',
            color: '#2d3748',
            margin: '0',
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            letterSpacing: '1px'
          }}>
            <span style={{
              fontSize: '28px',
              color: '#667eea',
              filter: 'drop-shadow(0 2px 4px rgba(102, 126, 234, 0.2))'
            }}>👤</span>
            {t('user:title')}
          </h1>
        </div>

        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '24px',
          width: '100%',
          margin: '0 auto',
          padding: '0 16px'
        }}>
          {/* 用户头像和基本信息卡片 */}
          <Card
            className={styles.profile_card}
            style={{ width: '100%' }}
          >
            <div className={styles.profile_header}>
              <Avatar
                size={80}
                icon={<UserOutlined />}
                className={styles.user_avatar}
              />
              <div className={styles.profile_info}>
                <h2 className={styles.user_name}>{name}</h2>
                <div className={styles.user_meta}>
                  <Tag color="blue" className={styles.user_tag}>
                    <IdcardOutlined /> {work_no}
                  </Tag>
                  <Tag color="green" className={styles.user_tag}>
                    {userSex === 'M' ? t('user:gender.male') : t('user:gender.female')}
                  </Tag>
                  <Tag color="purple" className={styles.user_tag}>
                    {getUserTypeText(userType)}
                  </Tag>
                </div>
              </div>
            </div>
          </Card>

          {/* 详细信息卡片 */}
          <Card
            title={
              <span className={styles.card_title}>
                <IdcardOutlined className={styles.card_icon} />
                {t('user:sections.basicInfo')}
              </span>
            }
            className={styles.info_card}
            style={{ width: '100%' }}
          >
            <div className={styles.info_grid}>
              <div className={styles.info_item}>
                <div className={styles.info_label}>
                  <UserOutlined className={styles.info_icon} />
                  {t('user:fields.name')}
                </div>
                <div className={styles.info_value}>{name}</div>
              </div>

              <div className={styles.info_item}>
                <div className={styles.info_label}>
                  <IdcardOutlined className={styles.info_icon} />
                  {t('user:fields.workNo')}
                </div>
                <div className={styles.info_value}>{work_no}</div>
              </div>

              <div className={styles.info_item}>
                <div className={styles.info_label}>
                  <MailOutlined className={styles.info_icon} />
                  {t('user:fields.email')}
                </div>
                <div className={styles.info_value}>{mail}</div>
              </div>

              <div className={styles.info_item}>
                <div className={styles.info_label}>
                  <TeamOutlined className={styles.info_icon} />
                  {t('user:fields.department')}
                </div>
                <div className={styles.info_value}>{departmentName}</div>
              </div>

              <div className={styles.info_item}>
                <div className={styles.info_label}>
                  <SafetyOutlined className={styles.info_icon} />
                  {t('user:fields.role')}
                </div>
                <div className={styles.info_value}>{roleName}</div>
              </div>

              <div className={styles.info_item}>
                <div className={styles.info_label}>
                  <UserOutlined className={styles.info_icon} />
                  {t('user:fields.userType')}
                </div>
                <div className={styles.info_value}>
                  <Tag color={userType === 'employee' ? 'blue' : 'orange'} className={styles.type_tag}>
                    {getUserTypeText(userType)}
                  </Tag>
                </div>
              </div>
            </div>
          </Card>

          {/* 安全设置卡片 */}
          <Card
            title={
              <span className={styles.card_title}>
                <LockOutlined className={styles.card_icon} />
                {t('user:sections.security')}
              </span>
            }
            className={styles.security_card}
            style={{ width: '100%' }}
            extra={
              <Button
                type="primary"
                icon={<EditOutlined />}
                onClick={showPasswordModal}
                className={styles.change_password_btn}
              >
                {t('user:buttons.changePassword')}
              </Button>
            }
          >
            <div className={styles.security_info}>
              <div className={styles.security_item}>
                <LockOutlined className={styles.security_icon} />
                <div className={styles.security_text}>
                  <div className={styles.security_title}>{t('user:security.password')}</div>
                  <div className={styles.security_desc}>{t('user:security.passwordDesc')}</div>
                </div>
                <CheckCircleOutlined className={styles.security_status} />
              </div>
            </div>
          </Card>
        </div>

        {/* 密码修改模态框 */}
        <Modal
          title={
            <span className={styles.modal_title}>
              <LockOutlined className={styles.modal_icon} />
              {t('user:modal.changePassword')}
            </span>
          }
          open={isPasswordModalVisible}
          onCancel={handlePasswordModalCancel}
          footer={[
            <Button key="cancel" onClick={handlePasswordModalCancel}>
              {t('common:buttons.cancel')}
            </Button>,
            <Button
              key="submit"
              type="primary"
              loading={loading}
              onClick={handlePasswordSubmit}
              className={styles.modal_submit_btn}
            >
              {t('common:buttons.confirm')}
            </Button>,
          ]}
          className={styles.password_modal}
          destroyOnClose
        >
          <div className={styles.modal_content}>
            <div className={styles.modal_form}>
              <div className={styles.form_item}>
                <label className={styles.form_label}>
                  {t('user:fields.oldPassword')}
                </label>
                <Input.Password
                  value={oldPass}
                  onChange={e => setOldPass(e.target.value)}
                  placeholder={t('user:placeholders.oldPassword')}
                  className={styles.form_input}
                />
              </div>

              <div className={styles.form_item}>
                <label className={styles.form_label}>
                  {t('user:fields.newPassword')}
                </label>
                <Input.Password
                  value={newPass}
                  onChange={e => setNewPass(e.target.value)}
                  placeholder={t('user:placeholders.newPassword')}
                  className={styles.form_input}
                />
              </div>

              <div className={styles.form_item}>
                <label className={styles.form_label}>
                  {t('user:fields.confirmPassword')}
                </label>
                <Input.Password
                  value={newPassConfirm}
                  onChange={e => setNewPassConfirm(e.target.value)}
                  placeholder={t('user:placeholders.confirmPassword')}
                  className={styles.form_input}
                />
              </div>
            </div>

            <div className={styles.modal_notice}>
              <div className={styles.notice_item}>
                <CheckCircleOutlined className={styles.notice_icon_success} />
                {t('user:validation.passwordRequirements')}
              </div>
              <div className={styles.notice_item}>
                <CloseCircleOutlined className={styles.notice_icon_warning} />
                {t('user:validation.logoutAfterChange')}
              </div>
            </div>
          </div>
        </Modal>
      </div>
    </>
  )
};

export default UserInformation