import React from 'react';
import { But<PERSON>, Image } from 'antd';
import people from '@/public/image/icon/people.png';
import styles from '../css/statisticsDialog.module.css';
import { useApplicationSelector, useApplicationDispatch } from "@/hook/hooks";
import { statisticsData, statisticsActions } from '@/slice/statisticsSlice';
import ItemImage from './itemImage';
import { useTranslation } from '@/hooks/useTranslation';

const Dialog = () => {
  const { t } = useTranslation(['common']);
  const statistics = useApplicationSelector(statisticsData);
  const targetUserId = statistics.targetUserId;
  const targetType = statistics.targetType;
  let details = statistics.pMonthInfos ? statistics.pMonthInfos[(statistics.targetMonth as any)] : [];
  if(targetType == 2){
    const departInfo = statistics.dMonthInfos ? statistics.dMonthInfos[(statistics.targetMonth as any)] : [];
    details = departInfo ? departInfo[(targetUserId as any)] : [];
  }
  const leaveList = details ? details[("leave_list" as any)] : [];
  const overtimeList = details ? details[("overtime_list" as any)] : [];
  const dispatch = useApplicationDispatch();
  if (!statisticsActions.handleClose()) {
    return null;
  }

  return (
    <div className='pop-up'>
      <div className={styles.dialog_box}>
        <div className={styles.leave_title}>{t('statistics.leaveHours')}</div>
        <div className={styles.overtime_title}>{t('statistics.overtimeHours')}</div>
        <div className={styles.leave_dialog}>
          <ul>
            {leaveList?.map((item: any, index: number) => {
              return (
                <li key={index}>
                  <div>{item.dayStr}</div>
                  {item.compensatory > 0 &&
                   <><ItemImage type='调休' time=''></ItemImage>
                   <span>{item.compensatory}</span></>
                  }
                  {item.normal > 0 &&
                   <><ItemImage type='事假' time=''></ItemImage>
                   <span>{item.normal}</span></>
                  }
                  {item.stick > 0 &&
                   <><ItemImage type='病假' time=''></ItemImage>
                   <span>{item.stick}</span></>
                  }
                  {item.other > 0 &&
                   <><ItemImage type='其他' time=''></ItemImage>
                   <span>{item.other}</span></>
                  }
                  {item.trip > 0 &&
                   <><ItemImage type='出差' time=''></ItemImage>
                   <span>{item.trip}</span></>
                  }
                </li>
              )
            })}
          </ul>
        </div>
        <div className={styles.overtime_dialog}>
          <ul>
            {overtimeList?.map((item: any, index: number) => (
              <li key={index}>
                  <span>{item.dayStr}</span>
                  {item.start_time != null &&
                   <><ItemImage type='开始' time=''></ItemImage>
                   <span>{item.start_time}</span></>
                  }
                  {item.end_time != null &&
                   <><ItemImage type='结束' time=''></ItemImage>
                   <span>{item.end_time}</span></>
                  }
                  {item.weekday > 0 &&
                   <><ItemImage type='平时' time=''></ItemImage>
                   <span>{item.weekday}</span></>
                  }
                  {item.weekend > 0 &&
                   <><ItemImage type='周末' time=''></ItemImage>
                   <span>{item.weekend}</span></>
                  }
                  {item.holiday > 0 &&
                   <><ItemImage type='假日' time=''></ItemImage>
                   <span>{item.holiday}</span></>
                  }
              </li>
            ))}
          </ul>
        </div>
        <Button type='default' onClick={() => dispatch(statisticsActions.handleClose())} className={styles.dialog_btn}>关闭</Button>
      </div>
    </div>
  );
};

export default Dialog;