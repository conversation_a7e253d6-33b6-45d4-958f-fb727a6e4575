#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('🚀 开始 Next.js 到 React.js 迁移...')

// 1. 备份原始文件
function backupOriginalFiles() {
  console.log('📦 备份原始文件...')
  
  const filesToBackup = [
    'package.json',
    'next.config.js',
    'middleware.ts'
  ]
  
  filesToBackup.forEach(file => {
    if (fs.existsSync(file)) {
      fs.copyFileSync(file, `${file}.backup`)
      console.log(`✅ 已备份: ${file}`)
    }
  })
}

// 2. 更新 package.json
function updatePackageJson() {
  console.log('📝 更新 package.json...')
  
  if (fs.existsSync('package-react.json')) {
    fs.copyFileSync('package-react.json', 'package.json')
    console.log('✅ 已更新 package.json')
  }
}

// 3. 创建必要的目录结构
function createDirectoryStructure() {
  console.log('📁 创建目录结构...')
  
  const directories = [
    'src/pages/login',
    'src/pages/user/information',
    'src/pages/attendance/attendanceimport1',
    'src/pages/application/transportationExpenseApplication',
    'src/pages/statistics/departmentStatistics',
    'src/pages/profile/paidLeave',
    'src/pages/reset',
    'src/components/layout',
    'src/components/auth',
    'src/components/ui',
    'src/hooks',
    'src/utils',
    'src/config',
    'src/api',
    'src/slice',
    'src/store',
    'src/styles'
  ]
  
  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
      console.log(`✅ 创建目录: ${dir}`)
    }
  })
}

// 4. 复制现有的源文件
function copyExistingFiles() {
  console.log('📋 复制现有文件...')
  
  // 复制 src 目录下的文件（如果存在）
  if (fs.existsSync('src')) {
    const srcFiles = [
      'src/api',
      'src/components',
      'src/hooks',
      'src/slice',
      'src/store',
      'src/utils'
    ]
    
    srcFiles.forEach(srcPath => {
      if (fs.existsSync(srcPath)) {
        console.log(`✅ 保留现有: ${srcPath}`)
      }
    })
  }
  
  // 复制样式文件
  if (fs.existsSync('styles')) {
    if (!fs.existsSync('src/styles')) {
      fs.mkdirSync('src/styles', { recursive: true })
    }
    
    const styleFiles = fs.readdirSync('styles')
    styleFiles.forEach(file => {
      const srcPath = path.join('styles', file)
      const destPath = path.join('src/styles', file)
      
      if (fs.statSync(srcPath).isFile()) {
        fs.copyFileSync(srcPath, destPath)
        console.log(`✅ 复制样式: ${file}`)
      }
    })
  }
  
  // 复制 public 目录（保持不变）
  if (fs.existsSync('public')) {
    console.log('✅ public 目录保持不变')
  }
}

// 5. 生成页面组件模板
function generatePageTemplates() {
  console.log('🎨 生成页面组件模板...')
  
  const pageTemplates = [
    {
      path: 'src/pages/reset/ResetPage.tsx',
      content: generateResetPageTemplate()
    },
    {
      path: 'src/pages/attendance/attendanceimport1/AttendanceImport1Page.tsx',
      content: generateAttendancePageTemplate()
    },
    {
      path: 'src/pages/application/transportationExpenseApplication/TransportationExpenseApplicationPage.tsx',
      content: generateTransportationPageTemplate()
    },
    {
      path: 'src/pages/statistics/departmentStatistics/DepartmentStatisticsPage.tsx',
      content: generateStatisticsPageTemplate()
    },
    {
      path: 'src/pages/profile/paidLeave/PaidLeavePage.tsx',
      content: generatePaidLeavePageTemplate()
    }
  ]
  
  pageTemplates.forEach(template => {
    if (!fs.existsSync(template.path)) {
      fs.writeFileSync(template.path, template.content)
      console.log(`✅ 生成模板: ${template.path}`)
    }
  })
}

// 页面模板生成函数
function generateResetPageTemplate() {
  return `import React from 'react'
import { useTranslation } from '../../hooks/useTranslation'

const ResetPage: React.FC = () => {
  const { t } = useTranslation()

  return (
    <div>
      <h1>{t('reset.title')}</h1>
      {/* TODO: 从 pages/reset/index.tsx 迁移内容 */}
    </div>
  )
}

export default ResetPage`
}

function generateAttendancePageTemplate() {
  return `import React from 'react'
import { useTranslation } from '../../../hooks/useTranslation'

const AttendanceImport1Page: React.FC = () => {
  const { t } = useTranslation()

  return (
    <div>
      <h1>{t('attendance.title')}</h1>
      {/* TODO: 从 pages/attendance/attendanceimport1 迁移内容 */}
    </div>
  )
}

export default AttendanceImport1Page`
}

function generateTransportationPageTemplate() {
  return `import React from 'react'
import { useTranslation } from '../../../hooks/useTranslation'

const TransportationExpenseApplicationPage: React.FC = () => {
  const { t } = useTranslation()

  return (
    <div>
      <h1>{t('transportation.title')}</h1>
      {/* TODO: 从 pages/application/transportationExpenseApplication 迁移内容 */}
    </div>
  )
}

export default TransportationExpenseApplicationPage`
}

function generateStatisticsPageTemplate() {
  return `import React from 'react'
import { useTranslation } from '../../../hooks/useTranslation'

const DepartmentStatisticsPage: React.FC = () => {
  const { t } = useTranslation()

  return (
    <div>
      <h1>{t('statistics.title')}</h1>
      {/* TODO: 从 pages/statistics/departmentStatistics 迁移内容 */}
    </div>
  )
}

export default DepartmentStatisticsPage`
}

function generatePaidLeavePageTemplate() {
  return `import React from 'react'
import { useTranslation } from '../../../hooks/useTranslation'

const PaidLeavePage: React.FC = () => {
  const { t } = useTranslation()

  return (
    <div>
      <h1>{t('profile.paidLeave.title')}</h1>
      {/* TODO: 从 pages/profile/paidLeave 迁移内容 */}
    </div>
  )
}

export default PaidLeavePage`
}

// 6. 生成迁移报告
function generateMigrationReport() {
  console.log('📊 生成迁移报告...')
  
  const report = `# 迁移报告

## 已完成的自动迁移

✅ 备份原始文件
✅ 更新 package.json
✅ 创建新的目录结构
✅ 复制现有源文件
✅ 生成页面组件模板

## 需要手动完成的迁移

### 1. 页面组件迁移
- [ ] 从 pages/login/index.tsx 迁移到 src/pages/login/LoginPage.tsx
- [ ] 从 pages/user/information 迁移到 src/pages/user/information/UserInformationPage.tsx
- [ ] 从 pages/reset/index.tsx 迁移到 src/pages/reset/ResetPage.tsx
- [ ] 从 pages/attendance/* 迁移到 src/pages/attendance/*
- [ ] 从 pages/application/* 迁移到 src/pages/application/*
- [ ] 从 pages/statistics/* 迁移到 src/pages/statistics/*
- [ ] 从 pages/profile/* 迁移到 src/pages/profile/*

### 2. 组件迁移
- [ ] 检查 pages/components 下的组件
- [ ] 更新组件中的导入路径
- [ ] 移除 Next.js 特定的 API 调用

### 3. 样式迁移
- [ ] 检查 CSS 模块的导入路径
- [ ] 更新静态资源的引用路径

### 4. API 调用更新
- [ ] 检查所有 API 调用
- [ ] 确保客户端渲染兼容性

### 5. 测试
- [ ] 运行 npm install
- [ ] 运行 npm run dev
- [ ] 测试所有页面功能
- [ ] 测试路由跳转
- [ ] 测试认证流程

## 下一步操作

1. 运行 \`npm install\` 安装新的依赖
2. 复制环境变量: \`cp .env.example .env\`
3. 开始手动迁移页面组件
4. 运行 \`npm run dev\` 测试应用

生成时间: ${new Date().toLocaleString()}
`
  
  fs.writeFileSync('MIGRATION_REPORT.md', report)
  console.log('✅ 生成迁移报告: MIGRATION_REPORT.md')
}

// 主函数
function main() {
  try {
    backupOriginalFiles()
    updatePackageJson()
    createDirectoryStructure()
    copyExistingFiles()
    generatePageTemplates()
    generateMigrationReport()
    
    console.log('\n🎉 自动迁移完成!')
    console.log('📖 请查看 MIGRATION_REPORT.md 了解下一步操作')
    console.log('📚 详细指南请参考 MIGRATION_GUIDE.md')
  } catch (error) {
    console.error('❌ 迁移过程中出现错误:', error)
    process.exit(1)
  }
}

// 运行迁移
main()
