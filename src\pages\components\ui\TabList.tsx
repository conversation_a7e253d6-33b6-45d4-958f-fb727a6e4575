import React from "react";
import { useApplicationSelector } from "@/hook/hooks";
import { tabData } from "@/slice/tabSlice";
import ListItem from "./ListItem";
import styles from '../css/left-cornor.module.css';
// import { subPermissionIsGranted } from "@/utils/permissions";

const TabList = () =>  {
  const currentTab = useApplicationSelector(tabData);

  const list = currentTab.selectedTabMenu?.list?.map((item: any, index: number) => {
    // 临时禁用权限检查 - 显示所有菜单项
    // if(subPermissionIsGranted(item.pCode)){
      return <ListItem linkTo={item.linkTo} textContent={item.text} liIndex ={item.index} tabType={currentTab.tabType} key={index}/>
    // }
  })
  return (
    <ul className={styles.user_menu_list_ul}>
      {list}
    </ul>
    
  );
};

export default TabList;