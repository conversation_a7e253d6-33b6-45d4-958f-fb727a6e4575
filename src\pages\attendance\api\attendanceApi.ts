import { getApi, postApi } from '@/api/fetch-api'
import ApiUrlVars from '@/api/common/url-vars'
import { statusActions } from '@/slice/statusSlice'

/**
 * 考勤查询参数接口
 */
export interface AttendanceQueryParams {
  keyword?: string
  query_type?: number
  start_dt?: string
  end_dt?: string
  user_id?: string
  startDate?: string
  endDate?: string
}

/**
 * 考勤记录接口
 */
export interface AttendanceRecord {
  id: string
  date: string
  checkIn: string
  checkOut: string
  workHours: number
  status: string
  location: string
  user_id?: string
  work_start_time?: string
  work_end_time?: string
  real_start_time?: string
  real_end_time?: string
}

/**
 * 考勤响应接口
 */
export interface AttendanceResponse {
  status: string
  message?: string
  data?: AttendanceRecord[]
  total?: number
}

/**
 * 查询考勤记录
 * @param params 查询参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<AttendanceResponse>
 */
export async function queryAttendanceRecords(
  params: AttendanceQueryParams, 
  dispatch: any
): Promise<AttendanceResponse> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.record_query
    const response = await postApi(url, params, dispatch)
    
    if (response.data.status === 'OK') {
      return {
        status: 'OK',
        data: response.data.data || response.data,
        total: response.data.total
      }
    } else {
      // 处理登录凭证失效
      if (response.data.message === '登录凭证失效') {
        dispatch(statusActions.onFalse(response.data.message))
      }
      
      return {
        status: 'NG',
        message: response.data.message || '查询失败'
      }
    }
  } catch (error: any) {
    console.error('Query attendance records API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 更新考勤记录
 * @param params 更新参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<any>
 */
export async function updateAttendanceRecord(params: any, dispatch: any): Promise<any> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.record_update
    const response = await postApi(url, params, dispatch)
    return response
  } catch (error: any) {
    console.error('Update attendance record API error:', error)
    throw error
  }
}

/**
 * 获取考勤最大日期
 * @param dispatch Redux dispatch函数
 * @returns Promise<any>
 */
export async function getMaxAttendanceDate(dispatch: any): Promise<any> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.record_date_max
    const response = await getApi(url, {}, dispatch)
    return response
  } catch (error: any) {
    console.error('Get max attendance date API error:', error)
    throw error
  }
}

/**
 * 导入考勤记录
 * @param params 导入参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<any>
 */
export async function importAttendanceRecords(params: any, dispatch: any): Promise<any> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.record_import
    const response = await postApi(url, params, dispatch)
    return response
  } catch (error: any) {
    console.error('Import attendance records API error:', error)
    throw error
  }
}

/**
 * 上传居家考勤记录
 * @param params 上传参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<any>
 */
export async function uploadHomeAttendance(params: any, dispatch: any): Promise<any> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.record_upload_home
    const response = await postApi(url, params, dispatch)
    return response
  } catch (error: any) {
    console.error('Upload home attendance API error:', error)
    throw error
  }
}

/**
 * 批量上传考勤记录
 * @param params 上传参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<any>
 */
export async function uploadBatchAttendance(params: any, dispatch: any): Promise<any> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.record_upload_any
    const response = await postApi(url, params, dispatch)
    return response
  } catch (error: any) {
    console.error('Upload batch attendance API error:', error)
    throw error
  }
}

/**
 * 下载考勤模板
 * @param dispatch Redux dispatch函数
 * @returns Promise<any>
 */
export async function downloadAttendanceTemplate(dispatch: any): Promise<any> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.record_download_temp
    const response = await getApi(url, {}, dispatch)
    return response
  } catch (error: any) {
    console.error('Download attendance template API error:', error)
    throw error
  }
}

/**
 * 下载错误记录
 * @param dispatch Redux dispatch函数
 * @returns Promise<any>
 */
export async function downloadErrorRecords(dispatch: any): Promise<any> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.error_download_home
    const response = await getApi(url, {}, dispatch)
    return response
  } catch (error: any) {
    console.error('Download error records API error:', error)
    throw error
  }
}

/**
 * 触发每日检查处理
 * @param dispatch Redux dispatch函数
 * @returns Promise<any>
 */
export async function triggerDailyCheck(dispatch: any): Promise<any> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.record_check_result_trigger
    const response = await postApi(url, {}, dispatch)
    return response
  } catch (error: any) {
    console.error('Trigger daily check API error:', error)
    throw error
  }
}

/**
 * 创建考勤查询JSON参数
 * @param userId 用户ID
 * @param queryType 查询类型
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 查询参数对象
 */
export function createAttendanceQueryParams(
  userId: string,
  queryType: number = 1,
  startDate: string = '',
  endDate: string = ''
): AttendanceQueryParams {
  return {
    keyword: userId,
    query_type: queryType,
    start_dt: startDate,
    end_dt: endDate
  }
}
