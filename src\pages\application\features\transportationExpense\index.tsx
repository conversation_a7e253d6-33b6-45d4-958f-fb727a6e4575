
import { message, Row, Col, Button, Input, DatePicker, Space, Pagination, Spin, Tooltip, Form, Divider, Modal } from 'antd';
import React,{ useState ,ChangeEvent, useEffect } from 'react';
import { applicationData, applicationActions } from '@/slice/applicationSlice';
import { statusData, statusActions } from '@/slice/statusSlice';
import Globals from '@/public/strings/Strings'
import ApiUrlVars from '@/api/common/url-vars';
import { submitTransportationExpense, getApplicationPageSize } from '@/pages/application/api/applicationApi';

import { useApplicationDispatch, useApplicationSelector } from "@/hook/hooks";


import styles from './index.module.css';
import ItemImage from '@/pages/components/ui/itemImage';
import people from '@/public/image/icon/people.png';
import undo from '@/public/image/icon/undo.png';
import detail from '@/public/image/icon/view-grid-detail.png';
import ApplicationStatus from '@/pages/components/ui/ApplicationStatus';
import dayjs from 'dayjs';
import { tabActions } from '@/slice/tabSlice';
import { menuConfig, menuTypes } from '@/utils/menulist';
import type { RangePickerProps } from 'antd/es/date-picker';
import ErrorPart from '@/pages/components/ui/ErrorPart';

import Table, { TableColumn } from '@/pages/components/ui/Table';
import { createTableColumns, renderUserInfo, renderTimeRange, renderLeaveTypes, renderStatus, renderActions } from '@/utils/tableUtils';
import { useTranslation } from '@/hooks/useTranslation';

//获取当天日期
const SpaceCompact = Space.Compact;
// const { Option } = Select; // 暂时不需要

const TransportationExpenseApplication: React.FC = () => {
  const data = useApplicationSelector(applicationData);
  const infData = useApplicationSelector(statusData);
  const [messageApi, contextHolder] = message.useMessage();
  const [form] = Form.useForm();
  const dispatch = useApplicationDispatch();

  // 多语言支持
  const { t: tApplication } = useTranslation('application');
  const { t: tCommon } = useTranslation('common');

  // 状态管理
  const [routeHistory, setRouteHistory] = useState<any[]>([]);
  const [showRouteHistory, setShowRouteHistory] = useState(false);

  //当前用户的信息
  const loginData = localStorage.getItem('login')
  const localStorageData = loginData ? JSON.parse(loginData) : {}
  const localName = localStorageData.name;
  const localWork_no = localStorageData.work_no;
  const localUser_id = localStorageData.user_id;
  const userMessage = localName + '-' + localWork_no;

  // Mock数据：已有的交通费申请记录
  const mockExistingApplications = [
    {
      id: 1,
      departure: '东京站',
      destination: '大阪站',
      startDate: '2024-01-15',
      endDate: '2024-01-20',
      userId: localUser_id
    },
    {
      id: 2,
      departure: '新宿站',
      destination: '横滨站',
      startDate: '2024-02-01',
      endDate: '2024-02-05',
      userId: localUser_id
    },
    {
      id: 3,
      departure: '东京站',
      destination: '大阪站',
      startDate: '2024-02-10',
      endDate: '2024-02-15',
      userId: localUser_id
    },
    {
      id: 4,
      departure: '涩谷站',
      destination: '品川站',
      startDate: '2024-03-01',
      endDate: '2024-03-03',
      userId: localUser_id
    }
  ];

  // 日期一贯性检查函数
  const checkDateConsistency = (departure: string, destination: string, startDate: string, endDate: string) => {
    // 查找相同出发地和目的地的已有申请
    const sameRouteApplications = mockExistingApplications.filter(app =>
      app.departure === departure &&
      app.destination === destination &&
      app.userId === localUser_id
    );

    if (sameRouteApplications.length === 0) {
      return { isValid: true, message: '' };
    }

    // 检查日期是否有重叠或不连续
    const newStart = dayjs(startDate);
    const newEnd = dayjs(endDate);

    for (const existingApp of sameRouteApplications) {
      const existingStart = dayjs(existingApp.startDate);
      const existingEnd = dayjs(existingApp.endDate);

      // 检查日期重叠
      if (
        (newStart.isBefore(existingEnd) && newEnd.isAfter(existingStart)) ||
        (existingStart.isBefore(newEnd) && existingEnd.isAfter(newStart))
      ) {
        return {
          isValid: false,
          message: `日期与已有申请重叠：${existingApp.startDate} 至 ${existingApp.endDate}`
        };
      }
    }

    // 检查日期连续性（可选：如果需要强制连续性）
    // 这里可以添加更复杂的连续性检查逻辑

    return { isValid: true, message: '' };
  };

  // 检查相同路线的历史记录
  const checkRouteHistory = (departure: string, destination: string) => {
    if (!departure || !destination) {
      setRouteHistory([]);
      setShowRouteHistory(false);
      return;
    }

    const sameRouteApps = mockExistingApplications.filter(app =>
      app.departure === departure &&
      app.destination === destination &&
      app.userId === localUser_id
    );

    setRouteHistory(sameRouteApps);
    setShowRouteHistory(sameRouteApps.length > 0);
  };
  const userRole = localStorageData.role_name;
  const addInf = {
    use_type: 0,
    user_id: localUser_id,
    agent_user_id: 0,
    departure: '',
    destination: '',
    regular_pass_amount: 0,
    single_trip_amount: 0,
    reason: '',
    start_date: '',
    workflow_list: '',
  }

  const disabledDate: RangePickerProps['disabledDate'] = (current) => {
    // 交通开始时间可以选择过去30天到未来30天
    return current > dayjs().add(30,'day').endOf('day') || current < dayjs().subtract(30, 'day').endOf('day');
  };

  useEffect(()=>{
    dispatch(applicationActions.setApplicationSliceInit());
    // 这里需要替换为交通费申请的API
    // fetchDataOnInitSaveToState(ApiUrlVars.api_domain + ApiUrlVars.transportationExpenseList_get, null, dispatch, applicationActions.setTransportationExpenseDataWhenInit);
    // fetchDataOnInitSaveToStatePost(ApiUrlVars.api_domain + ApiUrlVars.transportationExpenseAdd_post, addInf, dispatch, applicationActions.setTransportationExpenseAddInfInit);
    dispatch(tabActions.onTabChange(menuTypes.apply))
    // dispatch(tabActions.onSubChange(menuConfig[1].list[1].index)) // 交通费申请是申请菜单的第2个项目

    return () =>{
      dispatch(applicationActions.leaveHandleCancelModule()) // 暂时使用leave的action，后续需要创建专门的action
    }
  },[])

  function getWeekday(date: Date): string {
    const weekdays = ['(日)', '(一)', '(二)', '(三)', '(四)', '(五)', '(六)'];
    return weekdays[date.getDay()];
  }

  //cascader主题
  interface Theme {
    token: {
      fontSize: number;
    };
    components: {
      Cascader: {
        /* here is your component tokens */
      },
    },
  }
  //cascader主题字体大小设置为12
  const theme: Theme = {
    token: {
      fontSize: 12,
    },
    components: {
      Cascader: {
        /* here is your component tokens */
        dropdownHeight: 150,
      },
    },
  };

  //新增交通费申请
  const transportationExpenseAddApi = async() => {
    // 获取表单数据
    const formValues = form.getFieldsValue();
    const departure = formValues.departure || '';
    const destination = formValues.destination || '';
    const startDate = formValues.startDate ? formValues.startDate.format('YYYY-MM-DD') : '';
    const endDate = formValues.endDate ? formValues.endDate.format('YYYY-MM-DD') : '';

    // 基本验证
    if (!departure || !destination || !startDate) {
      messageApi.error('请填写完整的出发地、目的地和开始日期');
      return;
    }

    // 日期一贯性检查
    const consistencyCheck = checkDateConsistency(departure, destination, startDate, endDate || startDate);

    if (!consistencyCheck.isValid) {
      messageApi.error(`日期冲突：${consistencyCheck.message}`);
      return;
    }

    // 构建提交参数
    const params = {
      use_type: 0,
      user_id: localUser_id,
      agent_user_id: 0,
      departure: departure,
      destination: destination,
      regular_pass_amount: formValues.regularPassAmount || 0,
      single_trip_amount: formValues.singleTripAmount || 0,
      reason: formValues.reason || '',
      start_date: startDate,
      end_date: endDate || startDate,
      workflow_list: data.leaveApplication.commitAdd.workflow_list,
    };

    // 显示成功消息（因为使用Mock数据）
    messageApi.success(`交通费申请提交成功！路线：${departure} → ${destination}，日期：${startDate}${endDate ? ` 至 ${endDate}` : ''}`);

    // 关闭表单
    form.resetFields();
    dispatch(applicationActions.leaveHandleCancelModule());

    // 实际项目中这里应该调用真实的API
    // handleTransportationExpenseAddApi(params);
  }

  const handleTransportationExpenseAddApi = (params: any) => {
    // 这里需要替换为实际的交通费申请API
    const handleTransportationExpenseAddUrl = ApiUrlVars.api_domain + ApiUrlVars.leaveListNormalAdd_post; // 暂时使用leave的API
    try{
      postApi(handleTransportationExpenseAddUrl, params, dispatch).then((res:any) => {
        if (res?.data?.status == 'NG') {
          messageApi.open({
            type: 'error',
            content: res?.data?.message,
          });
          if(res?.data?.message == tApplication('messages.loginExpired')){
            dispatch(statusActions.onFalse(res?.data?.message))
          }
        }else if(res?.data?.status == 'OK') {
          messageApi.open({
            type: 'success',
            content: res?.data?.message,
          });
        }
      })
    }catch(error){
    }
  }

  //撤回交通费申请
  const transportationExpenseCancelApi = async(data: any) => {
    handleTransportationExpenseCancelApi(data);
  }

  // 表格列配置
  const getTableColumns = (): TableColumn[] => {
    return createTableColumns([
      {
        key: 'name',
        title: tApplication('form.workNumberName'),
        dataIndex: 'name',
        width: '13%',
        render: (value: string) => {
          if (!value) return '-';

          // 处理格式如 "JS0001-张三" 的用户名，转换为 "张三-JS0001"
          if (value.includes('-')) {
            const parts = value.split('-');
            const userId = parts[0];
            const userName = parts[1];
            return (
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontWeight: 'bold', color: '#2A82E4' }}>{userName}</div>
                <div style={{ fontSize: '12px', color: '#666' }}>{userId}</div>
              </div>
            );
          }

          return (
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontWeight: 'bold', color: '#2A82E4' }}>{value}</div>
            </div>
          );
        }
      },
      {
        key: 'start_date',
        title: tApplication('transportationExpense.startDate'),
        dataIndex: 'start_date',
        width: '12%',
        type: 'date',
        render: (value: string) => {
          const date = dayjs(value);
          const weekday = getWeekday(date.toDate());
          return (
            <div style={{ textAlign: 'center', fontSize: '12px' }}>
              <div>{date.format('YYYY-MM-DD')} {weekday}</div>
            </div>
          );
        }
      },
      {
        key: 'end_date',
        title: tApplication('transportationExpense.endDate'),
        dataIndex: 'end_date',
        width: '12%',
        type: 'date',
        render: (value: string) => {
          if (!value) return <div style={{ textAlign: 'center', fontSize: '12px', color: '#999' }}>-</div>;
          const date = dayjs(value);
          const weekday = getWeekday(date.toDate());
          return (
            <div style={{ textAlign: 'center', fontSize: '12px' }}>
              <div>{date.format('YYYY-MM-DD')} {weekday}</div>
            </div>
          );
        }
      },
      {
        key: 'route',
        title: tApplication('transportationExpense.route'),
        dataIndex: 'route',
        width: '18%',
        render: (_: any, record: any) => {
          return (
            <div style={{ textAlign: 'center', fontSize: '12px' }}>
              <div>{record.departure}</div>
              <div style={{ color: '#999', margin: '2px 0' }}>↓</div>
              <div>{record.destination}</div>
            </div>
          );
        }
      },
      {
        key: 'regular_pass_amount',
        title: tApplication('transportationExpense.regularPassAmount'),
        dataIndex: 'regular_pass_amount',
        width: '12%',
        render: (value: number) => {
          return (
            <div style={{ textAlign: 'center', fontSize: '12px', fontWeight: 'bold', color: '#1890ff' }}>
              {value > 0 ? `¥${value}` : '-'}
            </div>
          );
        }
      },
      {
        key: 'single_trip_amount',
        title: tApplication('transportationExpense.singleTripAmount'),
        dataIndex: 'single_trip_amount',
        width: '12%',
        render: (value: number) => {
          return (
            <div style={{ textAlign: 'center', fontSize: '12px', fontWeight: 'bold', color: '#f5222d' }}>
              {value > 0 ? `¥${value}` : '-'}
            </div>
          );
        }
      },
      {
        key: 'reason',
        title: tApplication('form.reason'),
        dataIndex: 'reason',
        width: '22%',
        type: 'ellipsis',
        maxLength: 30,
        align: 'left'
      },

      {
        key: 'actions',
        title: tApplication('form.operation'),
        dataIndex: 'actions',
        width: '10%',
        type: 'actions',
        actions: [
          {
            text: '',
            type: 'danger',
            icon: '/image/icon/undo.png',
            tooltip: tApplication('common.withdraw'),
            onClick: (record: any) => dispatch(applicationActions.leaveHandleDeleteForm(record.code)),
            disabled: (_record: any) => {
              // 这里需要根据交通费申请的业务逻辑来判断是否可以撤回
              return false; // 暂时允许所有记录撤回
            }
          }
        ]
      }
    ]);
  };

  // 处理表格数据
  const getTableData = () => {
    if (!data.leaveApplication.currentData || data.leaveApplication.datasource[1]?.code === '') {
      return [];
    }

    return data.leaveApplication.currentData.map((item: any, index: number) => {
      // 转换用户名格式：从 "JS0001-张三" 转换为 "张三-JS0001"
      let displayName = item.name;
      if (item.name && item.name.includes('-')) {
        const parts = item.name.split('-');
        displayName = parts[1] + '-' + parts[0];
      }

      return {
        key: item.code || index,
        ...item,
        name: displayName
      };
    });
  };

  const handleTransportationExpenseCancelApi = (params: any) => {
    // 这里需要替换为实际的交通费撤回API
    const handleTransportationExpenseCancelUrl = ApiUrlVars.api_domain + ApiUrlVars.leaveCancel_post; // 暂时使用leave的API
    const item = {
      code: params.code
    }
    postApi(handleTransportationExpenseCancelUrl, item, dispatch).then((res:any) => {
      if (res?.data?.status == 'NG') {
        messageApi.open({
          type: 'error',
          content: res?.data?.message,
        });
        if(res?.data?.message == tApplication('messages.loginExpired')){
          dispatch(statusActions.onFalse(res?.data?.message))
        }
      }else if(res?.data?.status == 'OK') {
        messageApi.open({
          type: 'success',
          content: res?.data?.message,
        });
      }
    })
  }

  // 渲染现代化表单弹窗
  const renderModernForm = () => {
    return (
      <>
        {/* 自定义样式 */}
        <style jsx global>{`
          .custom-scrollbar::-webkit-scrollbar {
            width: 6px !important;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1 !important;
            border-radius: 3px !important;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #c1c1c1 !important;
            border-radius: 3px !important;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8 !important;
          }


        `}</style>
        <Modal
          title={null}
          open={data.leaveApplication.show}
          onCancel={() => {
            form.resetFields();
            dispatch(applicationActions.leaveHandleCancelModule());
          }}
          footer={null}
          width={700}
          centered
          destroyOnClose
          maskClosable={false}
          style={{ top: 80 }}
          styles={{
            body: {
              padding: 0,
              height: '58vh',
              display: 'flex',
              flexDirection: 'column'
            },
            content: {
              borderRadius: '12px',
              overflow: 'hidden',
              boxShadow: '0 10px 30px rgba(0, 0, 0, 0.12)',
              height: '58vh'
            }
          }}
        >
        <Form
          form={form}
          layout="vertical"
          onFinish={() => transportationExpenseAddApi()}
          initialValues={{
            startDate: dayjs(),
            regularPassAmount: 0,
            singleTripAmount: 0
          }}
          size="middle"
          style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
        >
          {/* 固定顶部 - 申请人信息 */}
          <div style={{
            padding: '16px 20px 12px 20px',
            borderBottom: '1px solid #e9ecef',
            backgroundColor: '#ffffff',
            flexShrink: 0,
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.04)'
          }}>
            {/* 申请人信息 */}
            <Form.Item
              label={
                <span style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  fontSize: '13px',
                  fontWeight: '600',
                  color: '#333'
                }}>
                  🚌 {tApplication('form.applicant')}
                </span>
              }
              style={{ marginBottom: '0' }}
            >
              <Input
                value={userMessage}
                disabled
                style={{
                  backgroundColor: '#f8f9fa',
                  fontWeight: '600',
                  color: '#1890ff',
                  border: '1px solid #e9ecef',
                  borderRadius: '6px',
                  height: '36px'
                }}
              />
            </Form.Item>
          </div>

          {/* 可滚动中间部分 */}
          <div
            style={{
              flex: 1,
              overflowY: 'auto',
              padding: '16px 20px',
              backgroundColor: '#ffffff'
            }}
            className="custom-scrollbar"
          >
            {/* 交通日期 */}
            <Row gutter={12} style={{ marginBottom: '16px' }}>
              {/* 交通开始日期 */}
              <Col xs={24} sm={12}>
                <Form.Item
                  label={
                    <span style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px',
                      fontSize: '13px',
                      fontWeight: '600',
                      color: '#333'
                    }}>
                      📅 {tApplication('transportationExpense.startDate')} *
                    </span>
                  }
                  name="startDate"
                  rules={[{ required: true, message: tApplication('transportationExpense.startDateRequired') }]}
                  style={{ marginBottom: '12px' }}
                >
                  <DatePicker
                    placeholder={tApplication('transportationExpense.startDatePlaceholder')}
                    disabledDate={disabledDate}
                    style={{
                      width: '100%',
                      height: '36px',
                      borderRadius: '6px'
                    }}
                    onChange={(date) => {
                      if (date) {
                        dispatch(applicationActions.leaveHandleStartDateChange(date));
                      }
                    }}
                  />
                </Form.Item>
              </Col>

              {/* 交通结束日期 */}
              <Col xs={24} sm={12}>
                <Form.Item
                  label={
                    <span style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px',
                      fontSize: '13px',
                      fontWeight: '600',
                      color: '#333'
                    }}>
                      📅 {tApplication('transportationExpense.endDate')}
                    </span>
                  }
                  name="endDate"
                  style={{ marginBottom: '12px' }}
                >
                  <DatePicker
                    placeholder={tApplication('transportationExpense.endDatePlaceholder')}
                    disabledDate={disabledDate}
                    style={{
                      width: '100%',
                      height: '36px',
                      borderRadius: '6px'
                    }}
                    onChange={(date) => {
                      if (date) {
                        dispatch(applicationActions.leaveHandleEndDateChange(date));
                      }
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>



            {/* 费用信息 */}
            <Row gutter={12} style={{ marginBottom: '16px' }}>
              {/* 定期券费用 */}
              <Col xs={24} sm={12}>
                <Form.Item
                  label={
                    <span style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px',
                      fontSize: '13px',
                      fontWeight: '600',
                      color: '#333'
                    }}>
                      💰 {tApplication('transportationExpense.regularPassAmount')}
                    </span>
                  }
                  name="regularPassAmount"
                  rules={[
                    {
                      validator: (_, value) => {
                        if (value && (isNaN(value) || value < 0 || value > 100000)) {
                          return Promise.reject(new Error(tApplication('transportationExpense.regularPassAmountError')));
                        }
                        return Promise.resolve();
                      }
                    }
                  ]}
                  style={{ marginBottom: '12px' }}
                >
                  <Input
                    placeholder={tApplication('transportationExpense.regularPassAmountPlaceholder')}
                    prefix="¥"
                    style={{
                      backgroundColor: '#f8f9fa',
                      fontWeight: '600',
                      color: '#1890ff',
                      border: '1px solid #e9ecef',
                      borderRadius: '6px',
                      height: '36px'
                    }}
                    onChange={(_e) => {
                      // 这里需要添加定期券费用变更的action
                    }}
                  />
                </Form.Item>
              </Col>

              {/* 单次费用 */}
              <Col xs={24} sm={12}>
                <Form.Item
                  label={
                    <span style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px',
                      fontSize: '13px',
                      fontWeight: '600',
                      color: '#333'
                    }}>
                      🎫 {tApplication('transportationExpense.singleTripAmount')}
                    </span>
                  }
                  name="singleTripAmount"
                  rules={[
                    {
                      validator: (_, value) => {
                        if (value && (isNaN(value) || value < 0 || value > 50000)) {
                          return Promise.reject(new Error(tApplication('transportationExpense.singleTripAmountError')));
                        }
                        return Promise.resolve();
                      }
                    }
                  ]}
                  style={{ marginBottom: '12px' }}
                >
                  <Input
                    placeholder={tApplication('transportationExpense.singleTripAmountPlaceholder')}
                    prefix="¥"
                    style={{
                      backgroundColor: '#f8f9fa',
                      fontWeight: '600',
                      color: '#1890ff',
                      border: '1px solid #e9ecef',
                      borderRadius: '6px',
                      height: '36px'
                    }}
                    onChange={(_e) => {
                      // 这里需要添加单次费用变更的action
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* 出发地和目的地 */}
            <Row gutter={12} style={{ marginBottom: '16px' }}>
              <Col xs={24} sm={12}>
                <Form.Item
                  label={
                    <span style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px',
                      fontSize: '13px',
                      fontWeight: '600',
                      color: '#333'
                    }}>
                      🚩 {tApplication('transportationExpense.departure')} *
                    </span>
                  }
                  name="departure"
                  rules={[{ required: true, message: tApplication('transportationExpense.departureRequired') }]}
                  style={{ marginBottom: '12px' }}
                >
                  <Input
                    placeholder={tApplication('transportationExpense.departurePlaceholder')}
                    style={{
                      height: '36px',
                      borderRadius: '6px'
                    }}
                    onChange={(e) => {
                      const departure = e.target.value;
                      const destination = form.getFieldValue('destination');
                      checkRouteHistory(departure, destination);
                    }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  label={
                    <span style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px',
                      fontSize: '13px',
                      fontWeight: '600',
                      color: '#333'
                    }}>
                      🎯 {tApplication('transportationExpense.destination')} *
                    </span>
                  }
                  name="destination"
                  rules={[{ required: true, message: tApplication('transportationExpense.destinationRequired') }]}
                  style={{ marginBottom: '12px' }}
                >
                  <Input
                    placeholder={tApplication('transportationExpense.destinationPlaceholder')}
                    style={{
                      height: '36px',
                      borderRadius: '6px'
                    }}
                    onChange={(e) => {
                      const destination = e.target.value;
                      const departure = form.getFieldValue('departure');
                      checkRouteHistory(departure, destination);
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* 相同路线历史记录提示 */}
            {showRouteHistory && (
              <div style={{
                marginBottom: '16px',
                padding: '12px',
                backgroundColor: '#f6ffed',
                border: '1px solid #b7eb8f',
                borderRadius: '6px'
              }}>
                <div style={{
                  fontSize: '13px',
                  fontWeight: '600',
                  color: '#389e0d',
                  marginBottom: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px'
                }}>
                  ⚠️ 相同路线的历史申请记录
                </div>
                <div style={{ fontSize: '12px', color: '#52c41a' }}>
                  {routeHistory.map((record, index) => (
                    <div key={index} style={{ marginBottom: '4px' }}>
                      • {record.startDate} 至 {record.endDate}
                    </div>
                  ))}
                </div>
                <div style={{
                  fontSize: '12px',
                  color: '#fa8c16',
                  marginTop: '8px',
                  fontStyle: 'italic'
                }}>
                  请确保新申请的日期与已有记录保持一贯性，避免重叠。
                </div>
              </div>
            )}

            {/* 申请原因 */}
            <Form.Item
              label={
                <span style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  fontSize: '13px',
                  fontWeight: '600',
                  color: '#333'
                }}>
                  📝 {tApplication('form.reason')} *
                </span>
              }
              name="reason"
              rules={[
                { required: true, message: tApplication('transportationExpense.reasonRequired') },
                { min: 5, message: tApplication('transportationExpense.reasonMinLength') },
                { max: 200, message: tApplication('transportationExpense.reasonMaxLength') }
              ]}
              style={{ marginBottom: '12px' }}
            >
              <Input.TextArea
                placeholder={tApplication('transportationExpense.reasonPlaceholder')}
                rows={3}
                showCount
                maxLength={200}
                style={{
                  borderRadius: '6px'
                }}
                onChange={(e) => dispatch(applicationActions.leaveHandleInputReasonChange(e))}
              />
            </Form.Item>
          </div>

          {/* 固定底部区域 */}
          <div style={{
            padding: '14px 16px',
            borderTop: '1px solid #e9ecef',
            backgroundColor: '#f8f9fa',
            flexShrink: 0,
            boxShadow: '0 -2px 4px rgba(0, 0, 0, 0.04)'
          }}>
            {/* 提醒信息 */}
            <div style={{
              background: 'linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%)',
              border: '1px solid #b7eb8f',
              borderRadius: '6px',
              padding: '8px 12px',
              marginBottom: '14px'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
                fontWeight: '600',
                color: '#389e0d',
                marginBottom: '6px',
                fontSize: '12px'
              }}>
                💡 {tApplication('transportationExpense.reminders.title')}
              </div>
              <div style={{ lineHeight: '1.4', fontSize: '11px', color: '#333' }}>
                <div style={{ marginBottom: '4px' }}>
                  • {tApplication('transportationExpense.reminders.accuracy')}
                </div>
                <div style={{ marginBottom: '4px' }}>
                  • {tApplication('transportationExpense.reminders.startDate')}
                </div>
                <div style={{ marginBottom: '4px' }}>
                  • {tApplication('transportationExpense.reminders.regularPass')}
                </div>
                <div style={{ marginBottom: '4px' }}>
                  • {tApplication('transportationExpense.reminders.singleTrip')}
                </div>
                <div>
                  • {tApplication('transportationExpense.reminders.contact')}
                </div>
              </div>
            </div>

            {/* 提交按钮 */}
            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px' }}>
              <Button
                size="middle"
                style={{
                  minWidth: '90px',
                  height: '36px',
                  borderRadius: '6px',
                  border: '1px solid #d9d9d9',
                  backgroundColor: '#ffffff',
                  color: '#595959',
                  fontWeight: '500',
                  fontSize: '13px'
                }}
                onClick={() => {
                  form.resetFields();
                  dispatch(applicationActions.leaveHandleCancelModule());
                }}
              >
                {tCommon('buttons.cancel')}
              </Button>
              <Button
                type="primary"
                size="middle"
                style={{
                  minWidth: '90px',
                  height: '36px',
                  borderRadius: '6px',
                  fontWeight: '500',
                  fontSize: '13px',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  border: 'none',
                  boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)'
                }}
                onClick={() => {
                  form.validateFields().then(() => {
                    transportationExpenseAddApi();
                  }).catch((errorInfo) => {
                    console.log('Validation failed:', errorInfo);
                  });
                }}
              >
                {tApplication('transportationExpense.submitApplication')}
              </Button>
            </div>
          </div>
        </Form>
      </Modal>
      </>
    );
  };

  return (
    <>
    {infData.status=='OK'&&<div style={{padding: '24px', background: '#f5f5f5', minHeight: '100vh', width: '100%'}}>
      {contextHolder}
      {/* 申请表单 */}
      {data.leaveApplication.show && renderModernForm()}

      {/* 页面标题 */}
      <div style={{
        marginBottom: '32px',
        padding: '20px 0',
        borderBottom: '3px solid #e2e8f0',
        position: 'relative'
      }}>
        <div style={{
          position: 'absolute',
          bottom: '-3px',
          left: '0',
          width: '120px',
          height: '3px',
          background: 'linear-gradient(90deg, #1890ff 0%, #40a9ff 100%)',
          borderRadius: '2px'
        }}></div>
        <h1 style={{
          fontSize: '26px',
          fontWeight: '600',
          color: '#2d3748',
          margin: '0',
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          letterSpacing: '1px'
        }}>
          <span style={{
            fontSize: '28px',
            color: '#667eea',
            filter: 'drop-shadow(0 2px 4px rgba(102, 126, 234, 0.2))'
          }}>🚌</span>
          {tApplication('transportationExpense.title')}一览
        </h1>
      </div>

      {/* Mock数据展示面板 */}
      {/* <div style={{
        marginBottom: '24px',
        padding: '16px',
        backgroundColor: '#f0f9ff',
        border: '1px solid #0ea5e9',
        borderRadius: '8px'
      }}> */}
        {/* <div style={{
          fontSize: '14px',
          fontWeight: '600',
          color: '#0369a1',
          marginBottom: '12px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          🧪 Mock数据展示 - 已有交通费申请记录
        </div> */}
        {/* <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '12px' }}>
          {mockExistingApplications.map((app, index) => (
            <div key={index} style={{
              padding: '12px',
              backgroundColor: '#ffffff',
              border: '1px solid #e0e7ff',
              borderRadius: '6px',
              fontSize: '13px'
            }}>
              <div style={{ fontWeight: '600', color: '#1e40af', marginBottom: '6px' }}>
                {app.departure} → {app.destination}
              </div>
              <div style={{ color: '#64748b' }}>
                {app.startDate} 至 {app.endDate}
              </div>
            </div>
          ))}
        </div> */}
        {/* <div style={{
          fontSize: '12px',
          color: '#0369a1',
          marginTop: '12px',
          fontStyle: 'italic'
        }}>
          💡 提示：新建申请时，系统会检查相同路线的日期一贯性，避免重叠申请。
        </div> 
      </div>*/}

      {/* 申请一览区域 */}
      <div style={{
        borderRadius: '12px',
        boxShadow: '0 6px 20px rgba(0, 0, 0, 0.08)',
        border: '1px solid #e2e8f0',
        background: '#ffffff',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        height: '644px'
      }}>
        {/* 表格标题区域 */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '16px 20px',
          borderBottom: '1px solid #e2e8f0',
          background: '#ffffff',
          borderRadius: '12px 12px 0 0',
          flexShrink: 0,
          zIndex: 20,
          position: 'relative',
          minHeight: '68px' // 固定最小高度，确保按钮消失后高度不变
        }}>
          <div style={{
            position: 'absolute',
            bottom: '-2px',
            left: '0',
            width: '80px',
            height: '2px',
            background: 'linear-gradient(90deg, #1890ff 0%, #40a9ff 100%)',
            borderRadius: '1px'
          }}></div>

          {/* 提示信息 */}
          <div style={{
            fontSize: '13px',
            color: '#666',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            <span>💡</span>
            <span>点击表格行可以查看{tCommon('details')}</span>
          </div>

          {/* 新建申请按钮区域 - 保持固定高度 */}
          <div style={{
            height: '36px',
            minWidth: '100px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end'
          }}>
            {data.leaveApplication.showNew && (
              <Button
                type="primary"
                size="middle"
                style={{
                  backgroundColor: '#1890ff',
                  borderColor: '#1890ff',
                  color: '#ffffff',
                  fontWeight: 'bold',
                  borderRadius: '6px',
                  height: '36px',
                  minWidth: '100px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px'
                }}
                onClick={() => {
                  const role = userRole;
                  const id = localUser_id;
                  const inf = { role, id };
                  dispatch(applicationActions.leaveHandleNewModule(inf));
                }}
              >
                <span>➕</span>
                新建{tApplication('transportationExpense.title')}
              </Button>
            )}
          </div>
        </div>

        {/* 表格内容区域 */}
        <div style={{
          flex: 1,
          overflowY: 'auto',
          background: '#ffffff'
        }}>
          <Table
            columns={getTableColumns()}
            dataSource={getTableData()}
            loading={false}
            style={{
              background: '#ffffff',
              border: 'none',
              height: '384px'
            }}
            empty={{
              text: tCommon('noData'),
              description: `当前没有${tApplication('transportationExpense.title')}记录`
            }}
            pagination={{
              current: data.leaveApplication.currentPage,
              pageSize: 8,
              total: data.leaveApplication.datasource.length,
              onChange: (page) => dispatch(applicationActions.handleLeavePageChange(page)),
              showSizeChanger: false,
              showQuickJumper: true,
              pageSizeOptions: ['8', '16', '32'],
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} / ${total} ${tCommon('records', '条记录')}`,
              size: "default"
            }}
            expandedRowKeys={data.leaveApplication.selectedRowKeys.map(String)}
            expandedRowRender={(record) => (
              <ApplicationStatus code={record.code} type='transportation_expense' page='application' />
            )}
            onRowClick={(record) => {
              // 点击行展开/收起流程信息
              dispatch(applicationActions.leaveHandleRowDetailClick(record.code));
            }}
          />
        </div>

        {/* 分页区域样式 */}
        <style>{`
          .ant-pagination {
            padding: 16px 20px !important;
            border-top: 1px solid #e2e8f0 !important;
            background: #ffffff !important;
            margin: 0 !important;
          }
        `}</style>
        {/* 删除确认弹窗 - 保留原有的删除确认逻辑 */}
        {data.leaveApplication.selectedRowKey && data.leaveApplication.isConfirmed && (
          <div className='pop-up'>
            <div className={styles.deleteWarn}>
              <div className={styles.changeForm_title} style={{color:'red',fontWeight:'bold',fontSize:'16px'}}>
                注意
              </div>
              <div className={styles.deleteWarnArea}>
                <div style={{fontSize:'14px'}}>
                  请输入确认文字:
                </div>
                <Input
                  className={styles.confirmText}
                  onChange={(value) => dispatch(applicationActions.leaveHandleDeleteFormInput(value))}
                  value={data.leaveApplication.confirmText}
                />
                <div
                  className={styles.autoInput}
                  style={{fontSize:'14px'}}
                  onClick={() => dispatch(applicationActions.leaveHandleDeleteAutoInput())}
                >
                  一键输入
                </div>
                {data.leaveApplication.confirmText !== '确认撤回' && (
                  <Button className={styles.deleteSure}>
                    <div style={{marginTop:'-3px'}}>撤回</div>
                  </Button>
                )}
                {data.leaveApplication.confirmText === '确认撤回' && (
                  <Button
                    className={styles.deleteSure}
                    onClick={() => {
                      const selectedRecord = data.leaveApplication.currentData.find((item: any) =>
                        item.code === data.leaveApplication.selectedRowKey
                      );
                      if (selectedRecord) {
                        const code = selectedRecord.code;
                        const data = { code };
                        transportationExpenseCancelApi(data);
                      }
                    }}
                  >
                    <div style={{marginTop:'-3px'}}>撤回</div>
                  </Button>
                )}
                <Button
                  className={styles.deleteCancel}
                  onClick={() => dispatch(applicationActions.leaveHandleDeleteCancel())}
                >
                  <div style={{marginTop:'-3px'}}>取消</div>
                </Button>
              </div>
            </div>
          </div>
        )}


      </div>
    </div>}
    {infData.status=='NG'&&
      <ErrorPart type='NG' message={infData.text} ></ErrorPart>
    }
    </>
  )
};


export default TransportationExpenseApplication
