/* 现代化表单样式 */

/* 表单容器 */
.form_container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin: 24px;
  overflow: hidden;
}

/* 表单标题 */
.form_title {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
  padding: 20px 24px;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form_title_icon {
  font-size: 20px;
}

/* 表单内容区域 */
.form_content {
  padding: 24px;
}

/* 表单项样式 */
.form_item {
  margin-bottom: 24px;
}

.form_item_label {
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
  display: block;
}

/* 输入框样式 */
.form_input {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.form_input:hover {
  border-color: #40a9ff;
}

.form_input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 选择器样式 */
.form_select {
  border-radius: 6px;
}

.form_select .ant-select-selector {
  border-radius: 6px !important;
  transition: all 0.3s ease;
}

.form_select:hover .ant-select-selector {
  border-color: #40a9ff !important;
}

.form_select.ant-select-focused .ant-select-selector {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 日期选择器样式 */
.form_datepicker {
  border-radius: 6px;
  width: 100%;
}

.form_datepicker .ant-picker {
  border-radius: 6px !important;
  transition: all 0.3s ease;
}

.form_datepicker .ant-picker:hover {
  border-color: #40a9ff !important;
}

.form_datepicker .ant-picker.ant-picker-focused {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 文本域样式 */
.form_textarea {
  border-radius: 6px;
  resize: vertical;
  min-height: 80px;
}

.form_textarea:hover {
  border-color: #40a9ff;
}

.form_textarea:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 标签样式 */
.form_tag {
  padding: 4px 12px;
  font-size: 14px;
  border-radius: 4px;
  margin: 2px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.form_tag_blue {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.form_tag_orange {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.form_tag_red {
  background: #fff2f0;
  color: #f5222d;
  border: 1px solid #ffccc7;
}

.form_tag_purple {
  background: #f9f0ff;
  color: #722ed1;
  border: 1px solid #d3adf7;
}

/* 级联选择器样式 */
.form_cascader {
  border-radius: 6px;
}

.form_cascader .ant-cascader-picker {
  border-radius: 6px !important;
}

.form_cascader .ant-cascader-picker:hover .ant-cascader-selector {
  border-color: #40a9ff !important;
}

.form_cascader .ant-cascader-picker.ant-cascader-picker-focused .ant-cascader-selector {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 按钮样式 */
.form_button_group {
  display: flex;
  gap: 12px;
  margin-top: 32px;
  justify-content: flex-start;
}

.form_button_primary {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
  border-radius: 6px;
  height: 40px;
  min-width: 120px;
  font-weight: bold;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.form_button_primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.form_button_secondary {
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  height: 40px;
  min-width: 120px;
  color: #595959;
  cursor: pointer;
  transition: all 0.3s ease;
}

.form_button_secondary:hover {
  border-color: #40a9ff;
  color: #1890ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 提醒信息样式 */
.form_alert {
  margin-top: 24px;
  border-radius: 8px;
  border: 1px solid #91d5ff;
  background: #f6ffed;
}

.form_alert_content {
  line-height: 1.6;
  color: #595959;
}

.form_alert_content p {
  margin-bottom: 8px;
}

.form_alert_content strong {
  color: #262626;
  font-weight: 600;
}

/* 审批人选择区域 */
.form_approver_section {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  border: 1px solid #f0f0f0;
}

.form_approver_title {
  font-weight: 600;
  color: #262626;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form_approver_empty {
  color: #8c8c8c;
  font-style: italic;
  padding: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form_container {
    margin: 12px;
    border-radius: 8px;
  }
  
  .form_content {
    padding: 16px;
  }
  
  .form_button_group {
    flex-direction: column;
  }
  
  .form_button_primary,
  .form_button_secondary {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .form_title {
    padding: 16px;
    font-size: 16px;
  }
  
  .form_content {
    padding: 12px;
  }
  
  .form_item {
    margin-bottom: 16px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form_container {
  animation: fadeIn 0.3s ease-out;
}

/* 表单验证样式 */
.form_error {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

.form_success {
  color: #52c41a;
  font-size: 12px;
  margin-top: 4px;
}

/* 加载状态 */
.form_loading {
  position: relative;
  overflow: hidden;
}

.form_loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
