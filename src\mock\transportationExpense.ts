/**
 * 交通费申请Mock数据
 */

import dayjs from 'dayjs';

// 交通费申请状态
export const TRANSPORTATION_STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  CANCELLED: 'cancelled'
} as const;

// 交通费申请类型
export const TRANSPORTATION_TYPE = {
  COMMUTER_PASS: 'commuter_pass',  // 通勤定期券
  SINGLE_TICKET: 'single_ticket'   // 单次票
} as const;

// 审批状态映射
export const STATUS_MAP = {
  [TRANSPORTATION_STATUS.PENDING]: '审批中',
  [TRANSPORTATION_STATUS.APPROVED]: '已批准',
  [TRANSPORTATION_STATUS.REJECTED]: '已拒绝',
  [TRANSPORTATION_STATUS.CANCELLED]: '已撤回'
};

// 交通费申请Mock数据
export const mockTransportationExpenseData = [
  {
    code: 'TE20241201001',
    user_id: 1001,
    name: 'JS0001-田中太郎',
    start_day: '2024-12-01',
    end_day: '2024-12-31',
    route_from: '新宿駅',
    route_to: '渋谷駅',
    fee_amount_single: 160,
    fee_amount_monthly: 4800,
    reason: '通勤交通费申请',
    status: TRANSPORTATION_STATUS.APPROVED,
    created_at: '2024-11-25 09:30:00',
    updated_at: '2024-11-26 14:20:00',
    transportation_type: TRANSPORTATION_TYPE.COMMUTER_PASS,
    workflow_list: [
      {
        step: 1,
        approver_name: '佐藤花子',
        approver_id: 2001,
        status: 'approved',
        comment: '承認します',
        approved_at: '2024-11-26 14:20:00'
      }
    ]
  },
  {
    code: 'TE20241201002',
    user_id: 1002,
    name: 'JS0002-山田花子',
    start_day: '2024-12-02',
    end_day: '2024-12-02',
    route_from: '東京駅',
    route_to: '品川駅',
    fee_amount_single: 160,
    fee_amount_monthly: 0,
    reason: '客先訪問のため',
    status: TRANSPORTATION_STATUS.PENDING,
    created_at: '2024-12-01 10:15:00',
    updated_at: '2024-12-01 10:15:00',
    transportation_type: TRANSPORTATION_TYPE.SINGLE_TICKET,
    workflow_list: [
      {
        step: 1,
        approver_name: '佐藤花子',
        approver_id: 2001,
        status: 'pending',
        comment: '',
        approved_at: null
      }
    ]
  },
  {
    code: 'TE20241201003',
    user_id: 1003,
    name: 'JS0003-鈴木一郎',
    start_day: '2024-12-01',
    end_day: '2024-12-31',
    route_from: '池袋駅',
    route_to: '新宿駅',
    fee_amount_single: 160,
    fee_amount_monthly: 4800,
    reason: '12月分通勤定期券',
    status: TRANSPORTATION_STATUS.APPROVED,
    created_at: '2024-11-28 16:45:00',
    updated_at: '2024-11-29 11:30:00',
    transportation_type: TRANSPORTATION_TYPE.COMMUTER_PASS,
    workflow_list: [
      {
        step: 1,
        approver_name: '佐藤花子',
        approver_id: 2001,
        status: 'approved',
        comment: '承認します',
        approved_at: '2024-11-29 11:30:00'
      }
    ]
  },
  {
    code: 'TE20241201004',
    user_id: 1004,
    name: 'JS0004-高橋美咲',
    start_day: '2024-11-30',
    end_day: '2024-11-30',
    route_from: '横浜駅',
    route_to: '東京駅',
    fee_amount_single: 290,
    fee_amount_monthly: 0,
    reason: '研修参加のため',
    status: TRANSPORTATION_STATUS.REJECTED,
    created_at: '2024-11-29 14:20:00',
    updated_at: '2024-11-30 09:15:00',
    transportation_type: TRANSPORTATION_TYPE.SINGLE_TICKET,
    workflow_list: [
      {
        step: 1,
        approver_name: '佐藤花子',
        approver_id: 2001,
        status: 'rejected',
        comment: '事前申請が必要です',
        approved_at: '2024-11-30 09:15:00'
      }
    ]
  },
  {
    code: 'TE20241201005',
    user_id: 1005,
    name: 'JS0005-伊藤健太',
    start_day: '2024-12-03',
    end_day: '2024-12-03',
    route_from: '秋葉原駅',
    route_to: '上野駅',
    fee_amount_single: 140,
    fee_amount_monthly: 0,
    reason: '会議出席のため',
    status: TRANSPORTATION_STATUS.PENDING,
    created_at: '2024-12-02 08:30:00',
    updated_at: '2024-12-02 08:30:00',
    transportation_type: TRANSPORTATION_TYPE.SINGLE_TICKET,
    workflow_list: [
      {
        step: 1,
        approver_name: '佐藤花子',
        approver_id: 2001,
        status: 'pending',
        comment: '',
        approved_at: null
      }
    ]
  },
  {
    code: 'TE20241201006',
    user_id: 1006,
    name: 'JS0006-中村雅子',
    start_day: '2024-12-01',
    end_day: '2024-12-31',
    route_from: '大手町駅',
    route_to: '銀座駅',
    fee_amount_single: 170,
    fee_amount_monthly: 5100,
    reason: '12月分通勤定期券申請',
    status: TRANSPORTATION_STATUS.APPROVED,
    created_at: '2024-11-27 13:15:00',
    updated_at: '2024-11-28 10:45:00',
    transportation_type: TRANSPORTATION_TYPE.COMMUTER_PASS,
    workflow_list: [
      {
        step: 1,
        approver_name: '佐藤花子',
        approver_id: 2001,
        status: 'approved',
        comment: '承認します',
        approved_at: '2024-11-28 10:45:00'
      }
    ]
  },
  {
    code: 'TE20241201007',
    user_id: 1007,
    name: 'JS0007-小林達也',
    start_day: '2024-12-04',
    end_day: '2024-12-04',
    route_from: '有楽町駅',
    route_to: '新橋駅',
    fee_amount_single: 140,
    fee_amount_monthly: 0,
    reason: '取引先訪問',
    status: TRANSPORTATION_STATUS.CANCELLED,
    created_at: '2024-12-01 15:20:00',
    updated_at: '2024-12-02 09:10:00',
    transportation_type: TRANSPORTATION_TYPE.SINGLE_TICKET,
    workflow_list: [
      {
        step: 1,
        approver_name: '佐藤花子',
        approver_id: 2001,
        status: 'cancelled',
        comment: '申請者により撤回',
        approved_at: '2024-12-02 09:10:00'
      }
    ]
  },
  {
    code: 'TE20241201008',
    user_id: 1008,
    name: 'JS0008-加藤真理',
    start_day: '2024-12-05',
    end_day: '2024-12-05',
    route_from: '恵比寿駅',
    route_to: '目黒駅',
    fee_amount_single: 140,
    fee_amount_monthly: 0,
    reason: 'プロジェクト会議参加',
    status: TRANSPORTATION_STATUS.PENDING,
    created_at: '2024-12-03 11:45:00',
    updated_at: '2024-12-03 11:45:00',
    transportation_type: TRANSPORTATION_TYPE.SINGLE_TICKET,
    workflow_list: [
      {
        step: 1,
        approver_name: '佐藤花子',
        approver_id: 2001,
        status: 'pending',
        comment: '',
        approved_at: null
      }
    ]
  },
  {
    code: 'TE20241201009',
    user_id: 1009,
    name: 'JS0009-松本和也',
    start_day: '2024-12-01',
    end_day: '2024-12-31',
    route_from: '五反田駅',
    route_to: '大崎駅',
    fee_amount_single: 140,
    fee_amount_monthly: 4200,
    reason: '12月分通勤定期券',
    status: TRANSPORTATION_STATUS.APPROVED,
    created_at: '2024-11-26 09:00:00',
    updated_at: '2024-11-27 15:30:00',
    transportation_type: TRANSPORTATION_TYPE.COMMUTER_PASS,
    workflow_list: [
      {
        step: 1,
        approver_name: '佐藤花子',
        approver_id: 2001,
        status: 'approved',
        comment: '承認します',
        approved_at: '2024-11-27 15:30:00'
      }
    ]
  },
  {
    code: 'TE20241201010',
    user_id: 1010,
    name: 'JS0010-森田優子',
    start_day: '2024-12-06',
    end_day: '2024-12-06',
    route_from: '表参道駅',
    route_to: '青山一丁目駅',
    fee_amount_single: 140,
    fee_amount_monthly: 0,
    reason: '外部研修受講',
    status: TRANSPORTATION_STATUS.PENDING,
    created_at: '2024-12-04 14:30:00',
    updated_at: '2024-12-04 14:30:00',
    transportation_type: TRANSPORTATION_TYPE.SINGLE_TICKET,
    workflow_list: [
      {
        step: 1,
        approver_name: '佐藤花子',
        approver_id: 2001,
        status: 'pending',
        comment: '',
        approved_at: null
      }
    ]
  },
  {
    code: 'TE20241201011',
    user_id: 1011,
    name: 'JS0011-佐々木健',
    start_day: '2024-12-01',
    end_day: '2024-12-31',
    route_from: '品川駅',
    route_to: '田町駅',
    fee_amount_single: 140,
    fee_amount_monthly: 4200,
    reason: '12月分通勤定期券',
    status: TRANSPORTATION_STATUS.APPROVED,
    created_at: '2024-11-25 10:00:00',
    updated_at: '2024-11-26 16:45:00',
    transportation_type: TRANSPORTATION_TYPE.COMMUTER_PASS,
    workflow_list: [
      {
        step: 1,
        approver_name: '佐藤花子',
        approver_id: 2001,
        status: 'approved',
        comment: '承認します',
        approved_at: '2024-11-26 16:45:00'
      }
    ]
  },
  {
    code: 'TE20241201012',
    user_id: 1012,
    name: 'JS0012-清水美香',
    start_day: '2024-12-07',
    end_day: '2024-12-07',
    route_from: '神田駅',
    route_to: '日本橋駅',
    fee_amount_single: 140,
    fee_amount_monthly: 0,
    reason: '顧客打合せ',
    status: TRANSPORTATION_STATUS.PENDING,
    created_at: '2024-12-05 09:15:00',
    updated_at: '2024-12-05 09:15:00',
    transportation_type: TRANSPORTATION_TYPE.SINGLE_TICKET,
    workflow_list: [
      {
        step: 1,
        approver_name: '佐藤花子',
        approver_id: 2001,
        status: 'pending',
        comment: '',
        approved_at: null
      }
    ]
  },
  {
    code: 'TE20241201013',
    user_id: 1013,
    name: 'JS0013-橋本雄一',
    start_day: '2024-11-28',
    end_day: '2024-11-28',
    route_from: '浜松町駅',
    route_to: '新橋駅',
    fee_amount_single: 140,
    fee_amount_monthly: 0,
    reason: '緊急対応',
    status: TRANSPORTATION_STATUS.REJECTED,
    created_at: '2024-11-27 18:30:00',
    updated_at: '2024-11-28 10:20:00',
    transportation_type: TRANSPORTATION_TYPE.SINGLE_TICKET,
    workflow_list: [
      {
        step: 1,
        approver_name: '佐藤花子',
        approver_id: 2001,
        status: 'rejected',
        comment: '事後申請は認められません',
        approved_at: '2024-11-28 10:20:00'
      }
    ]
  },
  {
    code: 'TE20241201014',
    user_id: 1014,
    name: 'JS0014-藤田智子',
    start_day: '2024-12-01',
    end_day: '2024-12-31',
    route_from: '上野駅',
    route_to: '御徒町駅',
    fee_amount_single: 140,
    fee_amount_monthly: 4200,
    reason: '12月分通勤定期券申請',
    status: TRANSPORTATION_STATUS.APPROVED,
    created_at: '2024-11-24 14:00:00',
    updated_at: '2024-11-25 11:30:00',
    transportation_type: TRANSPORTATION_TYPE.COMMUTER_PASS,
    workflow_list: [
      {
        step: 1,
        approver_name: '佐藤花子',
        approver_id: 2001,
        status: 'approved',
        comment: '承認します',
        approved_at: '2024-11-25 11:30:00'
      }
    ]
  },
  {
    code: 'TE20241201015',
    user_id: 1015,
    name: 'JS0015-野村拓也',
    start_day: '2024-12-08',
    end_day: '2024-12-08',
    route_from: '錦糸町駅',
    route_to: '両国駅',
    fee_amount_single: 140,
    fee_amount_monthly: 0,
    reason: 'システム保守作業',
    status: TRANSPORTATION_STATUS.PENDING,
    created_at: '2024-12-06 16:20:00',
    updated_at: '2024-12-06 16:20:00',
    transportation_type: TRANSPORTATION_TYPE.SINGLE_TICKET,
    workflow_list: [
      {
        step: 1,
        approver_name: '佐藤花子',
        approver_id: 2001,
        status: 'pending',
        comment: '',
        approved_at: null
      }
    ]
  }
];

// 获取Mock数据的函数
export const getMockTransportationExpenseData = () => {
  return {
    status: 'OK',
    message: '交通费申请数据获取成功',
    data: mockTransportationExpenseData,
    total: mockTransportationExpenseData.length
  };
};

// 根据状态筛选数据
export const getMockDataByStatus = (status?: string) => {
  if (!status) {
    return mockTransportationExpenseData;
  }
  return mockTransportationExpenseData.filter(item => item.status === status);
};

// 根据日期范围筛选数据
export const getMockDataByDateRange = (startDate?: string, endDate?: string) => {
  if (!startDate && !endDate) {
    return mockTransportationExpenseData;
  }
  
  return mockTransportationExpenseData.filter(item => {
    const itemDate = dayjs(item.start_day);
    const start = startDate ? dayjs(startDate) : null;
    const end = endDate ? dayjs(endDate) : null;
    
    if (start && end) {
      return itemDate.isBetween(start, end, 'day', '[]');
    } else if (start) {
      return itemDate.isAfter(start, 'day') || itemDate.isSame(start, 'day');
    } else if (end) {
      return itemDate.isBefore(end, 'day') || itemDate.isSame(end, 'day');
    }
    
    return true;
  });
};

export default {
  mockTransportationExpenseData,
  getMockTransportationExpenseData,
  getMockDataByStatus,
  getMockDataByDateRange,
  TRANSPORTATION_STATUS,
  TRANSPORTATION_TYPE,
  STATUS_MAP
};
