import { createSlice, PayloadAction, } from "@reduxjs/toolkit";
import { RootState } from "../store";
import dayjs from 'dayjs';

const myAttendanceList = [
  { key: 1, startDate: '2023/12/04(三)', startTime: '9:13', endDate: '2024/01/11(三) 21:13', attendence: '', overTime: '4h', abnormal: '4h', shijia: '', bingjia: '', tiaoxiu: '8h', maternity: false, weekend: '', holiday: '', exception: '迟到1h', remark: '' },
  { key: 2, startDate: '2023/12/05(三)', startTime: '8:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '3h', bingjia: '', tiaoxiu: '', maternity: false, weekend: '', holiday: '4h', exception: '早退1h', remark: '' },
  { key: 3, startDate: '2023/12/06(三)', startTime: '7:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '出勤时间不满8h', remark: '' },
  { key: 4, startDate: '2023/12/07(三)', startTime: '6:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '', abnormal: '', shijia: '', bingjia: '3h', tiaoxiu: '', maternity: false, weekend: '', holiday: '4h', exception: '', remark: '' },
  { key: 5, startDate: '2023/12/08(三)', startTime: '5:13', endDate: '2024/01/11(三)', attendence: '', overTime: '4h', abnormal: '4h', shijia: '3h', bingjia: '3h', tiaoxiu: '', maternity: false, weekend: '', holiday: '', exception: '早退1h', remark: '' },
  { key: 6, startDate: '2023/12/09(三)', startTime: '', endDate: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假1111111111111111111111111111111' },
  { key: 7, startDate: '2023/12/10(三)', startTime: '7:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '', remark: '居家办公' },
  { key: 8, startDate: '2023/12/11(三)', startTime: '', endDate: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { key: 9, startDate: '2023/12/12(三)', startTime: '', endDate: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { key: 10, startDate: '2023/12/13(三)', startTime: '7:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '迟到1h', remark: '' },
  { key: 11, startDate: '2023/12/14(三)', startTime: '6:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '3h', tiaoxiu: '', maternity: false, weekend: '', holiday: '4h', exception: '', remark: '' },
  { key: 12, startDate: '2023/12/15(三)', startTime: '5:13', endDate: '2024/01/11(三)', attendence: '', overTime: '4h', abnormal: '4h', shijia: '3h', bingjia: '3h', tiaoxiu: '', maternity: false, weekend: '', holiday: '', exception: '早退1h', remark: '' },
  { key: 13, startDate: '2023/12/16(三)', startTime: '', endDate: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假1111111111111111111111111111111' },
  { key: 14, startDate: '2023/12/17(三)', startTime: '7:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '', remark: '家办公' },
  { key: 15, startDate: '2023/12/18(三)', startTime: '', endDate: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { key: 16, startDate: '2023/12/19(三)', startTime: '', endDate: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { key: 17, startDate: '2023/12/20(三)', startTime: '7:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '迟到1h', remark: '' },
  { key: 18, startDate: '2023/12/21(三)', startTime: '7:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '', remark: '居家办公' },
  { key: 19, startDate: '2023/12/22(三)', startTime: '', endDate: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { key: 20, startDate: '2023/12/23(三)', startTime: '', endDate: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { key: 21, startDate: '2023/12/24(三)', startTime: '7:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '迟到1h', remark: '' },
]

const attendanceFindList = [
  { workNum: 'js1111', name: '张三', state: true, key: 1, startDate: '2023/12/04(三)', startTime: '9:13', endDate: '2024/01/11(三) 21:13', attendence: '', overTime: '4h', abnormal: '4h', shijia: '', bingjia: '', tiaoxiu: '8h', maternity: false, weekend: '', holiday: '', exception: '迟到1h', remark: '' },
  { workNum: 'js1111', name: '张三', state: true, key: 2, startDate: '2023/12/05(三)', startTime: '8:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '3h', bingjia: '', tiaoxiu: '', maternity: false, weekend: '', holiday: '4h', exception: '早退1h', remark: '' },
  { workNum: 'js1112', name: '张三', state: true, key: 3, startDate: '2023/12/06(三)', startTime: '7:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '出勤时间不满8h', remark: '' },
  { workNum: 'js1112', name: 'aaa', state: true, key: 4, startDate: '2023/12/07(三)', startTime: '6:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '', abnormal: '', shijia: '', bingjia: '3h', tiaoxiu: '', maternity: false, weekend: '', holiday: '4h', exception: '', remark: '' },
  { workNum: 'js1113', name: 'aaa', state: true, key: 5, startDate: '2023/12/08(三)', startTime: '5:13', endDate: '2024/01/11(三)', attendence: '', overTime: '4h', abnormal: '4h', shijia: '3h', bingjia: '3h', tiaoxiu: '', maternity: false, weekend: '', holiday: '', exception: '早退1h', remark: '' },
  { workNum: 'js1113', name: 'aaa', state: true, key: 6, startDate: '2023/12/09(三)', startTime: '', endDate: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假1111111111111111111111111111111' },
  { workNum: 'js1113', name: 'aaa', state: true, key: 7, startDate: '2023/12/10(三)', startTime: '7:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '', remark: '居家办公' },
  { workNum: 'js1113', name: 'aaa', state: true, key: 8, startDate: '2023/12/11(三)', startTime: '', endDate: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { workNum: 'js1113', name: 'aaa', state: true, key: 9, startDate: '2023/12/12(三)', startTime: '', endDate: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { workNum: 'js1143', name: '王五', state: true, key: 10, startDate: '2023/12/13(三)', startTime: '7:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '迟到1h', remark: '' },
  { workNum: 'js1141', name: '王五', state: true, key: 11, startDate: '2023/12/14(三)', startTime: '6:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '3h', tiaoxiu: '', maternity: false, weekend: '', holiday: '4h', exception: '', remark: '' },
  { workNum: 'js1141', name: '王五', state: true, key: 12, startDate: '2023/12/15(三)', startTime: '5:13', endDate: '2024/01/11(三)', attendence: '', overTime: '4h', abnormal: '4h', shijia: '3h', bingjia: '3h', tiaoxiu: '', maternity: false, weekend: '', holiday: '', exception: '早退1h', remark: '' },
  { workNum: 'js1141', name: '王五', state: true, key: 13, startDate: '2023/12/16(三)', startTime: '', endDate: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假1111111111111111111111111111111' },
  { workNum: 'js1141', name: '王五', state: true, key: 14, startDate: '2023/12/17(三)', startTime: '7:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '', remark: '家办公' },
  { workNum: 'js1141', name: '王五', state: true, key: 15, startDate: '2023/12/18(三)', startTime: '', endDate: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { workNum: 'js1511', name: '李四', state: true, key: 16, startDate: '2023/12/19(三)', startTime: '', endDate: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { workNum: 'js1511', name: '李四', state: true, key: 17, startDate: '2023/12/20(三)', startTime: '7:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '迟到1h', remark: '' },
  { workNum: 'js1511', name: '李四', state: true, key: 18, startDate: '2023/12/21(三)', startTime: '7:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '', remark: '居家办公' },
  { workNum: 'js1511', name: '李四', state: true, key: 19, startDate: '2023/12/22(三)', startTime: '', endDate: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { workNum: 'js1511', name: '李四', state: true, key: 20, startDate: '2023/12/23(三)', startTime: '', endDate: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { workNum: 'js1511', name: '李四', state: true, key: 21, startDate: '2023/12/24(三)', startTime: '7:13', endDate: '2024/01/11(三)', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '迟到1h', remark: '' },
]

const attendanceEditList = [
  { workNum: 'js1111', name: '张三', state: true, key: 1, startDate: '2023/12/04(三)', startTime: '9:13', endDate: '2024/01/11(三)', endTime: '21:13', attendence: '', overTime: '4h', abnormal: '4h', shijia: '', bingjia: '', tiaoxiu: '8h', maternity: false, weekend: '', holiday: '', exception: '迟到1h', remark: '' },
  { workNum: 'js1111', name: '张三', state: true, key: 2, startDate: '2023/12/05(三)', startTime: '8:13', endDate: '2024/01/11(三)', endTime: '21:13', attendence: '5h', overTime: '4h', abnormal: '', shijia: '3h', bingjia: '', tiaoxiu: '', maternity: false, weekend: '', holiday: '4h', exception: '早退1h', remark: '' },
  { workNum: 'js1112', name: '张三', state: true, key: 3, startDate: '2023/12/06(三)', startTime: '7:13', endDate: '2024/01/11(三)', endTime: '21:13', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '出勤时间不满8h', remark: '' },
  { workNum: 'js1112', name: 'aaa', state: true, key: 4, startDate: '2023/12/07(三)', startTime: '6:13', endDate: '2024/01/11(三)', endTime: '21:13', attendence: '5h', overTime: '', abnormal: '', shijia: '', bingjia: '3h', tiaoxiu: '', maternity: false, weekend: '', holiday: '4h', exception: '', remark: '' },
  { workNum: 'js1113', name: 'aaa', state: true, key: 5, startDate: '2023/12/08(三)', startTime: '5:13', endDate: '2024/01/11(三)', endTime: '21:13', attendence: '', overTime: '4h', abnormal: '4h', shijia: '3h', bingjia: '3h', tiaoxiu: '', maternity: false, weekend: '', holiday: '', exception: '早退1h', remark: '' },
  { workNum: 'js1113', name: 'aaa', state: true, key: 6, startDate: '2023/12/09(三)', startTime: '', endDate: '', endTime: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假1111111111111111111111111111111' },
  { workNum: 'js1113', name: 'aaa', state: true, key: 7, startDate: '2023/12/10(三)', startTime: '7:13', endDate: '2024/01/11(三)', endTime: '21:13', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '', remark: '居家办公' },
  { workNum: 'js1113', name: 'aaa', state: true, key: 8, startDate: '2023/12/11(三)', startTime: '', endDate: '', endTime: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { workNum: 'js1113', name: 'aaa', state: true, key: 9, startDate: '2023/12/12(三)', startTime: '', endDate: '', endTime: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { workNum: 'js1143', name: '王五', state: true, key: 10, startDate: '2023/12/13(三)', startTime: '7:13', endDate: '2024/01/11(三)', endTime: '21:13', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '迟到1h', remark: '' },
  { workNum: 'js1141', name: '王五', state: true, key: 11, startDate: '2023/12/14(三)', startTime: '6:13', endDate: '2024/01/11(三)', endTime: '21:13', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '3h', tiaoxiu: '', maternity: false, weekend: '', holiday: '4h', exception: '', remark: '' },
  { workNum: 'js1141', name: '王五', state: true, key: 12, startDate: '2023/12/15(三)', startTime: '5:13', endDate: '2024/01/11(三)', endTime: '21:13', attendence: '', overTime: '4h', abnormal: '4h', shijia: '3h', bingjia: '3h', tiaoxiu: '', maternity: false, weekend: '', holiday: '', exception: '早退1h', remark: '' },
  { workNum: 'js1141', name: '王五', state: true, key: 13, startDate: '2023/12/16(三)', startTime: '', endDate: '', endTime: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假1111111111111111111111111111111' },
  { workNum: 'js1141', name: '王五', state: true, key: 14, startDate: '2023/12/17(三)', startTime: '7:13', endDate: '2024/01/11(三)', endTime: '21:13', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '', remark: '家办公' },
  { workNum: 'js1141', name: '王五', state: true, key: 15, startDate: '2023/12/18(三)', startTime: '', endDate: '', endTime: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { workNum: 'js1511', name: '李四', state: true, key: 16, startDate: '2023/12/19(三)', startTime: '', endDate: '', endTime: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { workNum: 'js1511', name: '李四', state: true, key: 17, startDate: '2023/12/20(三)', startTime: '7:13', endDate: '2024/01/11(三)', endTime: '21:13', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '迟到1h', remark: '' },
  { workNum: 'js1511', name: '李四', state: true, key: 18, startDate: '2023/12/21(三)', startTime: '7:13', endDate: '2024/01/11(三)', endTime: '21:13', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '', remark: '居家办公' },
  { workNum: 'js1511', name: '李四', state: true, key: 19, startDate: '2023/12/22(三)', startTime: '', endDate: '', endTime: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { workNum: 'js1511', name: '李四', state: true, key: 20, startDate: '2023/12/23(三)', startTime: '', endDate: '', endTime: '', attendence: '', overTime: '', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '', maternity: true, weekend: '', holiday: '', exception: '', remark: '产假' },
  { workNum: 'js1511', name: '李四', state: true, key: 21, startDate: '2023/12/24(三)', startTime: '7:13', endDate: '2024/01/11(三)', endTime: '21:13', attendence: '5h', overTime: '4h', abnormal: '', shijia: '', bingjia: '', tiaoxiu: '3h', maternity: false, weekend: '6h', holiday: '', exception: '迟到1h', remark: '' },
]

function getWeekday(date: Date): string {
  const weekdays = ['(日)', '(一)', '(二)', '(三)', '(四)', '(五)', '(六)'];
  return weekdays[date.getDay()];
}

// 定义要保存到Store的数据格式
export interface attendanceImportState {
  status: string,
  btnClicked: boolean,
  myAttendance: {
    key: number;
    startDate: string;
    startTime: string;
    endDate: string;
    attendence: string;
    overTime: string;
    abnormal: string;
    shijia: string;
    bingjia: string;
    tiaoxiu: string;
    maternity: boolean;
    weekend: string;
    holiday: string;
    exception: string;
    remark: string;
  }[],
  currentPage1: number, 
  pageSize1: number,
  myCurrentData: {
    key: number;
    startDate: string;
    startTime: string;
    endDate: string;
    attendence: string;
    overTime: string;
    abnormal: string;
    shijia: string;
    bingjia: string;
    tiaoxiu: string;
    maternity: boolean;
    weekend: string;
    holiday: string;
    exception: string;
    remark: string;
  }[],
  startDate: any,
  endDate: any,
  workNum: string,
  name: string,
  searchType: string,
  selectedRowKey: number,
  findTrueList: {
    workNum: string;
    name: string;
    state: boolean;
    key: number;
    startDate: string;
    startTime: string;
    endDate: string;
    attendence: string;
    overTime: string;
    abnormal: string;
    shijia: string;
    bingjia: string;
    tiaoxiu: string;
    maternity: boolean;
    weekend: string;
    holiday: string;
    exception: string;
    remark: string;
  }[],
  attendanceFind: {
    workNum: string;
    name: string;
    state: boolean;
    key: number;
    startDate: string;
    startTime: string;
    endDate: string;
    attendence: string;
    overTime: string;
    abnormal: string;
    shijia: string;
    bingjia: string;
    tiaoxiu: string;
    maternity: boolean;
    weekend: string;
    holiday: string;
    exception: string;
    remark: string;
  }[],
  findCurrentData: {
    workNum: string;
    name: string;
    state: boolean;
    key: number;
    startDate: string;
    startTime: string;
    endDate: string;
    attendence: string;
    overTime: string;
    abnormal: string;
    shijia: string;
    bingjia: string;
    tiaoxiu: string;
    maternity: boolean;
    weekend: string;
    holiday: string;
    exception: string;
    remark: string;
  }[],
  currentPage2: number, 
  pageSize2: number,
  date: string,
  time: string,
  dateE: string,
  timeE: string,
  editTrueList: {
    workNum: string;
    name: string;
    state: boolean;
    key: number;
    startDate: string;
    startTime: string;
    endDate: string;
    endTime: string;
    attendence: string;
    overTime: string;
    abnormal: string;
    shijia: string;
    bingjia: string;
    tiaoxiu: string;
    maternity: boolean;
    weekend: string;
    holiday: string;
    exception: string;
    remark: string;
  }[],
  attendanceEdit: {
    workNum: string;
    name: string;
    state: boolean;
    key: number;
    startDate: string;
    startTime: string;
    endDate: string;
    endTime: string;
    attendence: string;
    overTime: string;
    abnormal: string;
    shijia: string;
    bingjia: string;
    tiaoxiu: string;
    maternity: boolean;
    weekend: string;
    holiday: string;
    exception: string;
    remark: string;
  }[],
  editCurrentData: {
    workNum: string;
    name: string;
    state: boolean;
    key: number;
    startDate: string;
    startTime: string;
    endDate: string;
    endTime: string;
    attendence: string;
    overTime: string;
    abnormal: string;
    shijia: string;
    bingjia: string;
    tiaoxiu: string;
    maternity: boolean;
    weekend: string;
    holiday: string;
    exception: string;
    remark: string;
  }[],
  currentPage3: number, 
  pageSize3: number,
  show: boolean,
}
// 初始化数据
const initialState: attendanceImportState = {
  status: "",
  btnClicked: false,
  myAttendance: myAttendanceList,
  currentPage1: 1, 
  pageSize1: 50,
  myCurrentData: myAttendanceList.slice(0,50),
  // maternity: false,
  startDate: '2023/12/24',
  endDate: '2024/01/11',
  workNum: '',
  name: '',
  searchType: 'id',
  selectedRowKey: -1,
  findTrueList: attendanceFindList,
  attendanceFind: attendanceFindList,
  findCurrentData: attendanceFindList.slice(0,50),
  currentPage2: 1, 
  pageSize2: 50,
  date: (dayjs().year() + '/' +
    ((Number(dayjs().month()) + 1) < 10 ? ('0' + (Number(dayjs().month()) + 1)) : (Number(dayjs().month()) + 1)) + '/' +
    (Number(dayjs().date()) < 10 ? ('0' + Number(dayjs().date())) : Number(dayjs().date())) + getWeekday(dayjs().toDate())).toString(),
  time: '09:00',
  dateE: (dayjs().year() + '/' +
    ((Number(dayjs().month()) + 1) < 10 ? ('0' + (Number(dayjs().month()) + 1)) : (Number(dayjs().month()) + 1)) + '/' +
    (Number(dayjs().date()) < 10 ? ('0' + Number(dayjs().date())) : Number(dayjs().date())) + getWeekday(dayjs().toDate())).toString(),
  timeE: '09:00',
  // startTime: dayjs(),
  editTrueList: attendanceEditList,
  attendanceEdit: attendanceEditList,
  editCurrentData: attendanceEditList.slice(0,50),
  currentPage3: 1, 
  pageSize3: 50,
  show: false,
};

export const attendanceImportSlice = createSlice({
  name: 'attendanceImport',
  initialState,
  // reducers 集合了reducer和action的功能
  // 每个case冒号之前的为action名
  // 每个case整体为reducer
  reducers: {
    handleSubmit: (state: attendanceImportState) => {
      state.status = "";
      state.btnClicked = true;
    },
    startDateChange: (state: attendanceImportState, action: PayloadAction<any>) => {
      state.startDate = action.payload.year() + '/' +
        ((Number(action.payload.month()) + 1) < 10 ? ('0' + (Number(action.payload.month()) + 1)) : (Number(action.payload.month()) + 1)) + '/' +
        (Number(action.payload.date()) < 10 ? ('0' + Number(action.payload.date())) : Number(action.payload.date()))
    },
    endDateChange: (state: attendanceImportState, action: PayloadAction<any>) => {
      state.endDate = action.payload.year() + '/' +
        ((Number(action.payload.month()) + 1) < 10 ? ('0' + (Number(action.payload.month()) + 1)) : (Number(action.payload.month()) + 1)) + '/' +
        (Number(action.payload.date()) < 10 ? ('0' + Number(action.payload.date())) : Number(action.payload.date()))
    },
    workNumChange: (state: attendanceImportState, action: PayloadAction<any>) => {
      state.workNum = action.payload;
    },
    nameChange: (state: attendanceImportState, action: PayloadAction<any>) => {
      state.name = action.payload;
    },
    searchType: (state: attendanceImportState, action: PayloadAction<any>) => {
      if (state.searchType == action.payload) {
        state.searchType = '';
      } else {
        state.searchType = action.payload;
      }
    },
    searchChange: (state: attendanceImportState, action: PayloadAction<any>) => {
      if(action.payload == 'attendanceimport3'){
        const attendanceEdit = state.editTrueList.filter((item) => {
          if (state.searchType == 'id') {
            if (state.workNum.length != 0 && item.workNum.includes(state.workNum) && (item.startDate.slice(0, 10) == state.startDate.toString()) && (item.endDate.slice(0, 10) == state.endDate.toString())) {
              return true;
            }
          }
          if (state.searchType == 'name') {
            if (state.name.length != 0 && item.name.includes(state.name) && (item.startDate.slice(0, 10) == state.startDate.toString()) && (item.endDate.slice(0, 10) == state.endDate.toString())) {
              return true;
            }
          }
          return false;
        })
        state.attendanceEdit = attendanceEdit;
        if (state.searchType == 'id' && state.workNum.length == 0) {
          state.attendanceEdit = state.editTrueList;
        }
        if (state.searchType == 'name' && state.name.length == 0) {
          state.attendanceEdit = state.editTrueList;
        }

        const startIndex = (state.currentPage3 - 1) * state.pageSize3;
        const endIndex = startIndex + state.pageSize3;
        state.editCurrentData = state.attendanceEdit.slice(startIndex, endIndex);
      }
      if(action.payload == 'attendanceimport2'){
        const attendanceFind = state.findTrueList.filter((item) => {
          if (state.searchType == 'id') {
            if (state.workNum.length != 0 && item.workNum.includes(state.workNum) && (item.startDate.slice(0, 10) == state.startDate.toString()) && (item.endDate.slice(0, 10) == state.endDate.toString())) {
              return true;
            }
          }
          if (state.searchType == 'name') {
            if (state.name.length != 0 && item.name.includes(state.name) && (item.startDate.slice(0, 10) == state.startDate.toString()) && (item.endDate.slice(0, 10) == state.endDate.toString())) {
              return true;
            }
          }
          return false;
        })
        state.attendanceFind = attendanceFind;
        if (state.searchType == 'id' && state.workNum.length == 0) {
          state.attendanceFind = state.findTrueList;
        }
        if (state.searchType == 'name' && state.name.length == 0) {
          state.attendanceFind = state.findTrueList;
        }

        const startIndex = (state.currentPage2 - 1) * state.pageSize2;
        const endIndex = startIndex + state.pageSize2;
        state.findCurrentData = state.attendanceFind.slice(startIndex, endIndex);
      }
    },
    //修改时间表格显示
    handleRowDetailClick: (state: attendanceImportState, action: PayloadAction<any>) => {
      if (state.selectedRowKey === action.payload) {
        state.show = !state.show;
      } else {
        state.selectedRowKey = action.payload;
        state.show = true;
      }
    },
    //修改时间表格隐藏
    dataUpdateHide: (state: attendanceImportState) => {
      state.show = false;
    },
    //修改时间表格的提交
    updateSubmit: (state: attendanceImportState) => {
      state.show = false;
      state.attendanceEdit = state.attendanceEdit.map(item => {
        if (item.key === state.selectedRowKey) {
          return {
            ...item,
            startDate: state.date,
            startTime: state.time,
            endDate: state.dateE,
            endTime: state.timeE,
          };
        }
        return item;
      });
    },
    // 处理开始日期变化
    handleStartDateChange: (state: attendanceImportState, action: PayloadAction<any>) => {
      if (action.payload) {
        const week = getWeekday(action.payload.toDate());
        state.date = action.payload.year() + '/' +
          ((Number(action.payload.month()) + 1) < 10 ? ('0' + (Number(action.payload.month()) + 1)) : (Number(action.payload.month()) + 1)) + '/' +
          (Number(action.payload.date()) < 10 ? ('0' + Number(action.payload.date())) : Number(action.payload.date())) + week
      }
    },
    // 处理开始小时变化
    handleStartHourChange: (state: attendanceImportState, action: PayloadAction<any>) => {
      if (action.payload) {
        state.time = (action.payload < 10) ? ('0' + action.payload) : action.payload + ':00';
      }
    },
    // 处理开始日期变化
    handleEndDateChange: (state: attendanceImportState, action: PayloadAction<any>) => {
      if (action.payload) {
        const week = getWeekday(action.payload.toDate());
        state.dateE = action.payload.year() + '/' +
          ((Number(action.payload.month()) + 1) < 10 ? ('0' + (Number(action.payload.month()) + 1)) : (Number(action.payload.month()) + 1)) + '/' +
          (Number(action.payload.date()) < 10 ? ('0' + Number(action.payload.date())) : Number(action.payload.date())) + week
      }
    },
    // 处理开始小时变化
    handleEndHourChange: (state: attendanceImportState, action: PayloadAction<any>) => {
      if (action.payload) {
        state.timeE = (action.payload < 10) ? ('0' + action.payload) : action.payload + ':00';
      }
    },
    //我的考勤分页翻页
    handleAttendance1PageChange: (state: attendanceImportState, action: PayloadAction<any>) => {
      state.currentPage1 = action.payload;
      const startIndex = (state.currentPage1 - 1) * state.pageSize1;
      const endIndex = startIndex + state.pageSize1;
      state.myCurrentData = state.myAttendance.slice(startIndex, endIndex);
    },
    //考勤查询分页翻页
    handleAttendance2PageChange: (state: attendanceImportState, action: PayloadAction<any>) => {
      state.currentPage2 = action.payload;
      const startIndex = (state.currentPage2 - 1) * state.pageSize2;
      const endIndex = startIndex + state.pageSize2;
      state.findCurrentData = state.attendanceFind.slice(startIndex, endIndex);
    },
    //考勤查询分页翻页
    handleAttendance3PageChange: (state: attendanceImportState, action: PayloadAction<any>) => {
      state.currentPage3 = action.payload;
      const startIndex = (state.currentPage3 - 1) * state.pageSize3;
      const endIndex = startIndex + state.pageSize3;
      state.editCurrentData = state.attendanceEdit.slice(startIndex, endIndex);
    },
  },
});


//以下内容必须要有
export const { actions: attendanceImportActions } = attendanceImportSlice;

export default attendanceImportSlice.reducer;

//state 后面的为store中数据名称
export const attendanceImportData = (state: RootState) => state.attendanceImport;