#!/usr/bin/env node

/**
 * 多语言支持初始化脚本
 * 自动创建必要的目录结构和基础配置文件
 */

const fs = require('fs');
const path = require('path');

// 颜色输出函数
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
};

console.log(colors.blue('🌐 多语言支持初始化脚本'));
console.log(colors.blue('================================'));

// 创建目录的函数
function createDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(colors.green(`✓ 创建目录: ${dirPath}`));
  } else {
    console.log(colors.yellow(`⚠ 目录已存在: ${dirPath}`));
  }
}

// 创建文件的函数
function createFile(filePath, content) {
  if (!fs.existsSync(filePath)) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(colors.green(`✓ 创建文件: ${filePath}`));
  } else {
    console.log(colors.yellow(`⚠ 文件已存在: ${filePath}`));
  }
}

// 1. 创建多语言资源目录结构
console.log(colors.blue('\n📁 创建目录结构...'));

const localesDirs = [
  'public/locales',
  'public/locales/zh',
  'public/locales/ja',
];

localesDirs.forEach(createDirectory);

// 2. 创建基础的翻译文件
console.log(colors.blue('\n📄 创建基础翻译文件...'));

const translationFiles = [
  'common.json',
  'menu.json', 
  'login.json',
  'attendance.json',
  'application.json',
  'approval.json',
  'members.json',
  'settings.json',
  'statistics.json',
  'structure.json',
  'records.json',
  'errors.json',
  'validation.json',
];

// 中文翻译文件内容模板
const zhTemplates = {
  'common.json': {
    "buttons": {
      "save": "保存",
      "cancel": "取消",
      "confirm": "确定",
      "delete": "删除",
      "edit": "编辑",
      "add": "添加",
      "search": "搜索",
      "reset": "重置",
      "submit": "提交",
      "back": "返回"
    },
    "labels": {
      "user": "用户",
      "department": "部门", 
      "email": "邮箱",
      "name": "姓名",
      "workNo": "工号",
      "role": "角色",
      "status": "状态",
      "date": "日期",
      "time": "时间"
    },
    "messages": {
      "loading": "加载中...",
      "noData": "暂无数据",
      "success": "操作成功",
      "error": "操作失败",
      "confirm": "确认要执行此操作吗？"
    }
  },
  
  'menu.json': {
    "attendance": {
      "title": "考勤",
      "myAttendance": "我的考勤",
      "attendanceQuery": "考勤查询", 
      "attendanceEdit": "考勤编辑",
      "attendanceImport": "考勤导入"
    },
    "application": {
      "title": "申请",
      "leaveApplication": "请假申请",
      "overtimeApplication": "加班申请",
      "businessTripApplication": "出差申请",
      "confirmationApplication": "确认单申请"
    },
    "approval": {
      "title": "审批",
      "leaveApproval": "请假审批",
      "overtimeApproval": "加班审批",
      "businessTripApproval": "出差审批",
      "confirmationApproval": "确认单审批"
    },
    "members": {
      "title": "成员管理",
      "departmentMembers": "部门成员",
      "organizationMembers": "组织成员"
    },
    "statistics": {
      "title": "数据统计",
      "applicationStatistics": "申请统计",
      "personalStatistics": "个人统计",
      "departmentStatistics": "部门统计",
      "organizationStatistics": "组织统计",
      "paidLeaveStatistics": "带薪调休统计"
    },
    "structure": {
      "title": "部门结构"
    },
    "records": {
      "title": "操作记录",
      "myRecords": "我的记录",
      "allRecords": "全部记录"
    },
    "settings": {
      "title": "系统设置",
      "holidaySettings": "节假日设定",
      "mailSettings": "邮件设定",
      "roleSettings": "角色权限设定",
      "approvalSettings": "审批流程设定",
      "otherSettings": "其他设定"
    }
  },
  
  'login.json': {
    "title": "立即登录",
    "subtitle": "考勤管理系统",
    "account": "账号",
    "password": "密码",
    "remember": "记住账号",
    "forgot": "重置密码",
    "login": "登录",
    "placeholders": {
      "account": "请输入工号/公司邮箱",
      "password": "请输入密码"
    },
    "messages": {
      "loginSuccess": "登录成功！",
      "loginFailed": "您输入的账号或密码不正确，请重新输入",
      "accountRequired": "请输入账号/工号/公司邮箱！",
      "passwordRequired": "请输入密码！"
    }
  }
};

// 日文翻译文件内容模板
const jaTemplates = {
  'common.json': {
    "buttons": {
      "save": "保存",
      "cancel": "キャンセル",
      "confirm": "確定",
      "delete": "削除",
      "edit": "編集",
      "add": "追加",
      "search": "検索",
      "reset": "リセット",
      "submit": "送信",
      "back": "戻る"
    },
    "labels": {
      "user": "ユーザー",
      "department": "部門",
      "email": "メール",
      "name": "氏名",
      "workNo": "社員番号",
      "role": "役割",
      "status": "ステータス",
      "date": "日付",
      "time": "時間"
    },
    "messages": {
      "loading": "読み込み中...",
      "noData": "データがありません",
      "success": "操作が成功しました",
      "error": "操作が失敗しました",
      "confirm": "この操作を実行しますか？"
    }
  },
  
  'menu.json': {
    "attendance": {
      "title": "勤怠",
      "myAttendance": "私の勤怠",
      "attendanceQuery": "勤怠照会",
      "attendanceEdit": "勤怠編集",
      "attendanceImport": "勤怠インポート"
    },
    "application": {
      "title": "申請",
      "leaveApplication": "休暇申請",
      "overtimeApplication": "残業申請",
      "businessTripApplication": "出張申請",
      "confirmationApplication": "確認書申請"
    },
    "approval": {
      "title": "承認",
      "leaveApproval": "休暇承認",
      "overtimeApproval": "残業承認",
      "businessTripApproval": "出張承認",
      "confirmationApproval": "確認書承認"
    },
    "members": {
      "title": "メンバー管理",
      "departmentMembers": "部門メンバー",
      "organizationMembers": "組織メンバー"
    },
    "statistics": {
      "title": "データ統計",
      "applicationStatistics": "申請統計",
      "personalStatistics": "個人統計",
      "departmentStatistics": "部門統計",
      "organizationStatistics": "組織統計",
      "paidLeaveStatistics": "有給調整統計"
    },
    "structure": {
      "title": "部門構造"
    },
    "records": {
      "title": "操作記録",
      "myRecords": "私の記録",
      "allRecords": "全記録"
    },
    "settings": {
      "title": "システム設定",
      "holidaySettings": "祝日設定",
      "mailSettings": "メール設定",
      "roleSettings": "役割権限設定",
      "approvalSettings": "承認フロー設定",
      "otherSettings": "その他設定"
    }
  },
  
  'login.json': {
    "title": "ログイン",
    "subtitle": "勤怠管理システム",
    "account": "アカウント",
    "password": "パスワード",
    "remember": "アカウントを記憶",
    "forgot": "パスワードリセット",
    "login": "ログイン",
    "placeholders": {
      "account": "社員番号/会社メールを入力",
      "password": "パスワードを入力"
    },
    "messages": {
      "loginSuccess": "ログイン成功！",
      "loginFailed": "入力されたアカウントまたはパスワードが正しくありません",
      "accountRequired": "アカウント/社員番号/会社メールを入力してください！",
      "passwordRequired": "パスワードを入力してください！"
    }
  }
};

// 创建中文翻译文件
translationFiles.forEach(fileName => {
  const zhContent = zhTemplates[fileName] || {};
  createFile(
    path.join('public/locales/zh', fileName),
    JSON.stringify(zhContent, null, 2)
  );
});

// 创建日文翻译文件
translationFiles.forEach(fileName => {
  const jaContent = jaTemplates[fileName] || {};
  createFile(
    path.join('public/locales/ja', fileName),
    JSON.stringify(jaContent, null, 2)
  );
});

// 3. 创建配置文件
console.log(colors.blue('\n⚙️ 创建配置文件...'));

// i18n 配置文件
const i18nConfig = `import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// 动态导入翻译资源
const loadResources = async (lng: string) => {
  const modules = await Promise.all([
    import(\`../../public/locales/\${lng}/common.json\`),
    import(\`../../public/locales/\${lng}/menu.json\`),
    import(\`../../public/locales/\${lng}/login.json\`),
    // 可以根据需要添加更多模块
  ]);

  return {
    common: modules[0].default,
    menu: modules[1].default,
    login: modules[2].default,
  };
};

i18n
  .use(initReactI18next)
  .init({
    lng: 'zh', // 默认语言
    fallbackLng: 'zh',
    debug: process.env.NODE_ENV === 'development',
    
    interpolation: {
      escapeValue: false, // React 已经处理了 XSS
    },
    
    // 命名空间
    ns: ['common', 'menu', 'login'],
    defaultNS: 'common',
    
    // 资源将通过动态导入加载
    resources: {},
  });

// 动态加载语言资源
export const loadLanguageResources = async (language: string) => {
  if (!i18n.hasResourceBundle(language, 'common')) {
    const resources = await loadResources(language);
    Object.keys(resources).forEach(ns => {
      i18n.addResourceBundle(language, ns, resources[ns]);
    });
  }
};

export default i18n;
`;

createDirectory('src/lib');
createFile('src/lib/i18n.ts', i18nConfig);

// 自定义翻译 Hook
const useTranslationHook = `import { useTranslation as useI18nTranslation } from 'react-i18next';
import { useEffect } from 'react';
import { loadLanguageResources } from '../lib/i18n';

export const useTranslation = (namespace?: string) => {
  const { t, i18n } = useI18nTranslation(namespace);
  
  useEffect(() => {
    // 确保当前语言的资源已加载
    loadLanguageResources(i18n.language);
  }, [i18n.language]);
  
  const changeLanguage = async (lng: string) => {
    await loadLanguageResources(lng);
    await i18n.changeLanguage(lng);
    // 保存语言偏好到本地存储
    localStorage.setItem('preferred-language', lng);
  };
  
  return {
    t,
    currentLanguage: i18n.language,
    changeLanguage,
    isLoading: false,
  };
};

// 获取保存的语言偏好
export const getStoredLanguage = (): string => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('preferred-language') || 'zh';
  }
  return 'zh';
};
`;

createDirectory('src/hooks');
createFile('src/hooks/useTranslation.ts', useTranslationHook);

// 4. 创建工具脚本
console.log(colors.blue('\n🔧 创建工具脚本...'));

const findChineseScript = `#!/usr/bin/env node

/**
 * 扫描项目中的硬编码中文字符串
 */

const fs = require('fs');
const path = require('path');

const chineseRegex = /[\u4e00-\u9fa5]+/g;
const excludeDirs = ['node_modules', '.next', 'dist', '.git', 'public/locales'];
const includeExts = ['.tsx', '.ts', '.js', '.jsx'];

function scanDirectory(dir, results = []) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !excludeDirs.includes(file)) {
      scanDirectory(filePath, results);
    } else if (stat.isFile() && includeExts.includes(path.extname(file))) {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\\n');
      
      lines.forEach((line, index) => {
        const matches = line.match(chineseRegex);
        if (matches) {
          results.push({
            file: filePath,
            line: index + 1,
            content: line.trim(),
            matches: matches
          });
        }
      });
    }
  }
  
  return results;
}

console.log('🔍 扫描硬编码中文字符串...');
const results = scanDirectory('.');

if (results.length > 0) {
  console.log(\`\\n找到 \${results.length} 处硬编码中文：\\n\`);
  results.forEach(result => {
    console.log(\`📁 \${result.file}:\${result.line}\`);
    console.log(\`   \${result.content}\`);
    console.log(\`   匹配: \${result.matches.join(', ')}\\n\`);
  });
} else {
  console.log('✅ 未找到硬编码中文字符串');
}
`;

createFile('scripts/find-hardcoded-chinese.js', findChineseScript);

console.log(colors.blue('\n✅ 初始化完成！'));
console.log(colors.green('\n下一步操作：'));
console.log('1. 运行 npm install 安装依赖');
console.log('2. 查看 docs/i18n-implementation-guide.md 了解详细实施方案');
console.log('3. 查看 docs/i18n-implementation-checklist.md 按清单执行');
console.log('4. 运行 node scripts/find-hardcoded-chinese.js 扫描硬编码中文');
console.log(colors.yellow('\n⚠️ 注意：请根据实际需求调整翻译内容和配置'));
