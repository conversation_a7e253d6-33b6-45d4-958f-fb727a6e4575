import React, { useState } from 'react'
import { Form, Input, Checkbox, Card, Button } from 'antd';
import { useApplicationSelector, useApplicationDispatch } from "@/hook/hooks";
import { setUserActions, loginData } from "@/slice/authSlice";
import { LockOutlined, UserOutlined, LoginOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import toast, { Toaster } from 'react-hot-toast';
import { useTranslation } from '@/hooks/useTranslation';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { loginUser } from '../../api/loginApi';
import styles from './login.module.css';

let dispatch: any = null

const Login: React.FC = () => {
  const [account, setAccount] = useState('');
  const [checked, setChecked] = useState(false);
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const data = useApplicationSelector(loginData);
  const { t } = useTranslation('login');
  dispatch = useApplicationDispatch()

  const rememberAccount = () => {
    setChecked(!checked);
  }

  const rememberAccountChange = (e: any) => {
    setAccount(e.target.value);
  }

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      const response = await loginUser({
        user_account: values.account,
        user_password: values.password
      }, dispatch);
      
      if (response.status === 'OK') {
        rememberAccount();

        toast.success(t('messages.loginSuccess') || '登录成功！', {
          style: {
            borderRadius: '8px',
            background: 'linear-gradient(135deg, #52c41a 0%, #73d13d 100%)',
            color: '#fff',
          },
        });

        //跳转
        navigate('/user/information')
      } else {
        toast.error(response.message || t('messages.loginFailed') || '登录失败', {
          style: {
            borderRadius: '8px',
            background: 'linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%)',
            color: '#fff',
          },
        });
      }
    } catch (error) {
      toast.error(t('messages.networkError') || '网络连接错误，请稍后重试', {
        style: {
          borderRadius: '8px',
          background: 'linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%)',
          color: '#fff',
        },
      });
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className={styles.container}>
      <Toaster position="top-center" />

      {/* 语言切换器 */}
      <div className={styles.languageSwitcher}>
        <LanguageSwitcher />
      </div>

      {/* 装饰元素 */}
      <div className={styles.decoration1}></div>
      <div className={styles.decoration2}></div>

      {/* 登录卡片 */}
      <Card className={styles.card} bordered={false}>
        {/* 卡片头部 */}
        <div className={styles.header}>
          <div className={styles.logoContainer}>
            <img
              src="/image/background/HYRON_LOGO_BLUE.png"
              alt="HYRON Logo"
              className={styles.logoImage}
            />
          </div>
          <h1 className={styles.title}>
            {t('title') || '考勤管理系统'}
          </h1>
          <p className={styles.subtitle}>
            {t('subtitle') || '请登录您的账户'}
          </p>
        </div>

        {/* 表单内容 */}
        <div className={styles.formContainer}>
          {/* 表单区域的小logo */}
          <div className={styles.smallLogoContainer}>
            <img
              src="/image/background/HYRON_LOGO_BLUE.png"
              alt="HYRON Logo"
              className={styles.smallLogo}
            />
          </div>

          <Form
            name="login"
            onFinish={handleSubmit}
            autoComplete="off"
            layout="vertical"
            size="large"
          >
            <Form.Item
              name="account"
              label={t('account') || '账号'}
              rules={[{ required: true, message: t('messages.accountRequired') || '请输入账号' }]}
              style={{ marginBottom: '20px' }}
            >
              <Input
                prefix={<UserOutlined style={{ color: '#999' }} />}
                placeholder={t('placeholders.account') || '请输入账号'}
                value={account}
                onChange={(e) => setAccount(e.target.value)}
                className={styles.input}
              />
            </Form.Item>

            <Form.Item
              name="password"
              label={t('password') || '密码'}
              rules={[{ required: true, message: t('messages.passwordRequired') || '请输入密码' }]}
              style={{ marginBottom: '20px' }}
            >
              <Input.Password
                prefix={<LockOutlined style={{ color: '#999' }} />}
                placeholder={t('placeholders.password') || '请输入密码'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className={styles.input}
              />
            </Form.Item>

            <div className={styles.rememberContainer}>
              <Checkbox
                checked={checked}
                onChange={rememberAccount}
                onClick={() => dispatch(setUserActions.setCheckbox())}
              >
                {t('remember') || '记住账号'}
              </Checkbox>
              <a
                href="/reset"
                className={styles.forgotLink}
              >
                {t('forgot') || '忘记密码？'}
              </a>
            </div>

            <Form.Item style={{ marginBottom: 0 }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                size="large"
                className={styles.loginButton}
              >
                {t('login') || '登录'}
              </Button>
            </Form.Item>
          </Form>
        </div>
      </Card>
    </div>
  )
}

export default Login
