import React, { useState, useEffect } from 'react';
import { Table, TableProps } from 'antd';
import styles from './ResponsiveTable.module.css';

interface ResponsiveTableProps extends TableProps<any> {
  mobileBreakpoint?: number;
  mobileCardView?: boolean;
  mobileColumns?: string[];
  className?: string;
}

/**
 * 响应式表格组件
 * 在移动端自动切换为卡片视图或横向滚动
 */
const ResponsiveTable: React.FC<ResponsiveTableProps> = ({
  mobileBreakpoint = 768,
  mobileCardView = false,
  mobileColumns = [],
  className = '',
  ...tableProps
}) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= mobileBreakpoint);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, [mobileBreakpoint]);

  // 移动端卡片视图渲染
  const renderMobileCards = () => {
    if (!tableProps.dataSource) return null;

    return (
      <div className={styles.mobileCards}>
        {tableProps.dataSource.map((record, index) => (
          <div key={record.key || index} className={styles.mobileCard}>
            {tableProps.columns?.map((column: any) => {
              if (mobileColumns.length > 0 && !mobileColumns.includes(column.key)) {
                return null;
              }
              
              const value = column.render 
                ? column.render(record[column.dataIndex], record, index)
                : record[column.dataIndex];

              return (
                <div key={column.key || column.dataIndex} className={styles.cardRow}>
                  <div className={styles.cardLabel}>{column.title}</div>
                  <div className={styles.cardValue}>{value}</div>
                </div>
              );
            })}
          </div>
        ))}
      </div>
    );
  };

  const tableClasses = [
    styles.responsiveTable,
    isMobile ? styles.mobile : styles.desktop,
    className
  ].filter(Boolean).join(' ');

  if (isMobile && mobileCardView) {
    return (
      <div className={tableClasses}>
        {renderMobileCards()}
      </div>
    );
  }

  return (
    <div className={tableClasses}>
      <div className={styles.tableWrapper}>
        <Table
          {...tableProps}
          scroll={{ x: isMobile ? 'max-content' : undefined, ...tableProps.scroll }}
          className={styles.table}
        />
      </div>
    </div>
  );
};

export default ResponsiveTable;
