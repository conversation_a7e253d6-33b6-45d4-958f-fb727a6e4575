/* 交通费精算个人承认画面样式 */
.container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 页面标题区域 */
.page_header {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: var(--border-width) solid var(--border-color);
}

.page_title {
  font-size: calc(var(--font-size-lg) + 8px);
  font-weight: 700;
  color: var(--primary-color);
  margin: 0 0 var(--spacing-sm) 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.page_description {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

/* 检索区域 */
.search_area {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: var(--radius-medium);
  margin-bottom: var(--spacing-lg);
  border: var(--border-width) solid var(--border-color);
}

.search_item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.search_label {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.month_picker,
.type_selector {
  width: 200px;
  height: 40px;
}

.search_actions {
  display: flex;
  gap: var(--spacing-md);
}

.search_button {
  height: 40px;
  padding: 0 var(--spacing-lg);
  border-radius: var(--radius-medium);
  font-weight: 600;
  box-shadow: var(--shadow-small);
}

/* 批量操作区域 */
.batch_actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-radius: var(--radius-medium);
  margin-bottom: var(--spacing-lg);
  border: var(--border-width) solid rgba(33, 150, 243, 0.3);
}

.batch_info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.selected_count {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--primary-color);
  background: rgba(33, 150, 243, 0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-medium);
}

.batch_buttons {
  display: flex;
  gap: var(--spacing-md);
}

.batch_confirm_btn {
  border-radius: var(--radius-medium);
  font-weight: 600;
  box-shadow: var(--shadow-small);
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
  border-color: #4caf50;
}

.batch_confirm_btn:hover {
  background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
  border-color: #66bb6a;
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

/* 表格容器 */
.table_container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: var(--radius-medium);
  border: var(--border-width) solid var(--border-color);
}

.data_table {
  height: 100%;
}

/* 员工信息样式 */
.employee_info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.employee_name {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
}

.employee_no {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  background: #f0f0f0;
  padding: 2px var(--spacing-sm);
  border-radius: var(--radius-medium);
  display: inline-block;
  width: fit-content;
}

/* 日期样式 */
.date_text {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  font-family: 'Courier New', monospace;
}

/* 路线样式 */
.route_text {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  line-height: 1.4;
  word-break: break-all;
}

/* 票券类型样式 */
.ticket_type {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-medium);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-align: center;
  min-width: 60px;
}

.ticket_type_commuter {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1976d2;
  border: var(--border-width) solid #2196f3;
}

.ticket_type_single {
  background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
  color: #f57c00;
  border: var(--border-width) solid #ff9800;
}

/* 金额样式 */
.amount_text {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--success-color);
  font-family: 'Courier New', monospace;
}

/* 操作按钮区域 */
.action_buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
}

/* 已确认状态样式 */
.confirmed_status {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-md);
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  color: var(--success-color);
  border-radius: var(--radius-medium);
  font-size: var(--font-size-xs);
  font-weight: 600;
  border: var(--border-width) solid var(--success-color);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    width: 98%;
    padding: var(--spacing-lg);
  }
  
  .search_area {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search_actions {
    align-self: flex-start;
  }
  
  .batch_actions {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
}

@media (max-width: 768px) {
  .container {
    width: 100%;
    margin: var(--spacing-sm) auto;
    padding: var(--spacing-md);
    border-radius: var(--spacing-xs);
  }

  .page_title {
    font-size: var(--font-size-lg);
  }

  .search_area {
    padding: var(--spacing-md);
  }

  .month_picker,
  .type_selector {
    width: 100%;
  }

  .employee_info {
    gap: 2px;
  }

  .employee_name {
    font-size: var(--font-size-sm);
  }

  .employee_no {
    font-size: 10px;
    padding: 1px var(--spacing-xs);
  }

  .batch_actions {
    padding: var(--spacing-sm);
  }

  .batch_buttons {
    width: 100%;
  }

  .batch_confirm_btn {
    width: 100%;
  }

  /* 移动端确认区域样式 - 上下布局 */
  :global(.confirmation-area) {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 16px !important;
  }

  :global(.confirmation-area) > div:first-child {
    order: 1;
  }

  :global(.confirmation-area) > div:last-child {
    order: 2;
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 12px !important;
  }

  :global(.confirmation-area) > div:last-child > div:first-child {
    align-items: flex-start !important;
  }

  :global(.confirmation-area) > div:last-child > button {
    width: 100% !important;
    min-width: auto !important;
  }
}

/* 加载状态样式 */
.loading_container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--text-secondary);
}

/* 表格行悬停效果 */
.data_table :global(.table_row:hover) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 状态指示器 */
.status_indicator {
  display: inline-block;
  width: var(--spacing-sm);
  height: var(--spacing-sm);
  border-radius: 50%;
  margin-right: var(--spacing-xs);
}

.status_pending {
  background: #ffa726;
}

.status_confirmed {
  background: var(--success-color);
}

/* 统计信息样式 */
.summary_info {
  display: flex;
  gap: var(--spacing-lg);
  padding: var(--spacing-md);
  background: #f8f9fa;
  border-radius: var(--radius-medium);
  margin-bottom: var(--spacing-lg);
}

.summary_item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.summary_label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-weight: 500;
}

.summary_value {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--primary-color);
}
