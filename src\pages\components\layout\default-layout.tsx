import { ReactElement } from "react"
import LeftCornor from "@/components/ui/left-cornor"

// 默认布局，没有左右区分
export default function DefaultLayout(children : ReactElement) {
    return (
      <>
        <div style={{ width: '20%', float:'left'}}><LeftCornor key='LeftCornor'/></div>
        <div style={{ width: '70%', float:'left', border:'solid 1px gray', padding: '10px'}}>{children}</div>     
      </>
    )
  }