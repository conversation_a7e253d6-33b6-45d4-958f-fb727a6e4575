import React, { useState } from 'react';
import styles from '../css/itemImage.module.css';
import { Button } from 'antd';
import { useTranslation } from '@/hooks/useTranslation';

interface ItemImageProps {
  type: string;
  time: string;
}

const colorMap: { [key: string]: string } = {
  // 根据type的变化，color变化成对应的值
  '出勤': '#9F5555',
  '部门': '#F89835',
  '国内': '#63B8FF',
  '日本': '#E0B1CC',
  '其它': '#63B8FF',
  '其他': 'blue',
  '无': 'gray',
  '产假': '#D89835',
  '开始': '#FF3030',
  '结束': '#76EEC6',
  '调休': '#63B8FF',
  '病假': 'orange',
  '事假': '#489835',
  '平时': '#4047B1',
  '周末': '#259445',
  '假日': '#F55500',
  '原因': '#FF6347',
  '状态': '#9F79EE',
  '确认日': '#FF3030',
  '实际': '#76EEC6',
  '更新为': '#63B8FF',
  '日期': '#999329',
  '异常': '#9F79EE',
  '出差': 'green'
};

const ItemImage = ({ type, time }: ItemImageProps) => {
  const { t } = useTranslation(['attendance', 'common']);

  // 翻译映射表
  const translationMap: { [key: string]: string } = {
    '调休': t('leaveTypes.compensatoryLeave', '调休'),
    '事假': t('leaveTypes.personalLeave', '事假'),
    '病假': t('leaveTypes.sickLeave', '病假'),
    '其他': t('leaveTypes.otherLeave', '其他'),
    '出差': t('leaveTypes.businessTrip', '出差'),
    '平时': t('overtimeTypes.weekday', '平时'),
    '周末': t('overtimeTypes.weekend', '周末'),
    '假日': t('overtimeTypes.holiday', '假日'),
    '开始': t('statistics.start', '开始'),
    '结束': t('statistics.end', '结束'),
    // 保持其他类型不变
    '出勤': '出勤',
    '部门': '部门',
    '国内': '国内',
    '日本': '日本',
    '其它': '其它',
    '无': '无',
    '产假': '产假',
    '原因': '原因',
    '状态': '状态',
    '确认日': '确认日',
    '实际': '实际',
    '更新为': '更新为',
    '日期': '日期',
    '异常': '异常'
  };

  const displayText = translationMap[type] || type;
  const color = colorMap[type] || '#000000';

  return (
    <>
      <div className={styles.btn} style={{ backgroundColor: color }}>
        {displayText}
      </div>
      <div className={styles.text}>
        {time}
      </div>
    </>
  );
};

export default ItemImage