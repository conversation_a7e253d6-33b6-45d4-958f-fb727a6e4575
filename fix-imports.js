const fs = require('fs')
const path = require('path')

// 递归查找所有 .tsx 和 .ts 文件
function findFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory() && !file.includes('node_modules') && !file.includes('.git')) {
      findFiles(filePath, fileList)
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      fileList.push(filePath)
    }
  })
  
  return fileList
}

// 修复导入路径
function fixImports(filePath) {
  let content = fs.readFileSync(filePath, 'utf8')
  let changed = false
  
  // 替换 @/src/ 为 @/
  if (content.includes('@/src/')) {
    content = content.replace(/@\/src\//g, '@/')
    changed = true
  }
  
  if (changed) {
    fs.writeFileSync(filePath, content)
    console.log(`Fixed: ${filePath}`)
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src')
  const files = findFiles(srcDir)
  
  console.log(`Found ${files.length} files to check...`)
  
  files.forEach(fixImports)
  
  console.log('Import paths fixed!')
}

main()
