import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getCookie, key_for_token, key_for_token_aes } from '@/utils/cookies';
import { is_login } from '@/utils/login';

//首页 - 根据登录状态自动重定向
const Home: React.FC = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // 检查登录状态并重定向
    const checkLoginAndRedirect = () => {
      try {
        const token = getCookie(key_for_token);
        const tokenAes = getCookie(key_for_token_aes);
        const isLoggedIn = is_login(token, tokenAes);

        if (isLoggedIn) {
          // 已登录，重定向到用户信息页面
          navigate('/user/information', { replace: true });
        } else {
          // 未登录，重定向到登录页面
          navigate('/login', { replace: true });
        }
      } catch (error) {
        console.error('Login check failed:', error);
        // 出错时重定向到登录页面
        router.replace('/login');
      }
    };

    checkLoginAndRedirect();
  }, [router]);

  // 显示加载状态
  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      fontSize: '18px',
      color: '#666'
    }}>
      正在跳转...
    </div>
  );
};

export default Home