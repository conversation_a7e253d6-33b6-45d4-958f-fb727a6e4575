import { createSlice, PayloadAction, } from "@reduxjs/toolkit";
import { RootState } from "../store";
import { menuConfig } from "../utils/menulist";
import { loginData, loginState } from '@/slice/authSlice';
import { useApplicationDispatch, useApplicationSelector } from "@/hook/hooks";

//allRecords
export interface allRecordsState {
    //申请表的信息
    datasource: {
        key: number;
        type: string;
        operation: string;
        operationPeople: string;
        state: string;
    }[],
    //确定当前点击删除的申请栏位置和申请表的位置对应
    selectedRowKeys: number[],
    //确定当前要添加的行的位置
    allSelectedRowKey: number,
    //确定申请进度是否显示
    showInput: boolean,
    //全部操作记录的天数限制
    searchDay: string,
}

// 初始化数据
const allRecordsState = {
    datasource: [],
    selectedRowKeys: [],
    allSelectedRowKey: -1,
    showInput: false,
    searchDay: "0",
};

//myRecords

export interface myRecordsState {
    //申请表的信息
    datasource: {
        key: number;
        type: string;
        operation: string;
        operationPeople: string;
        state: string;
    }[],
    //确定当前点击删除的申请栏位置和申请表的位置对应
    selectedRowKeys: number[],
    //确定当前要添加的行的位置
    selectedRowKey: number,
    //确定申请进度是否显示
    showInput: boolean,
}

// 初始化数据
const myRecordsState = {
    datasource: [],
    selectedRowKeys: [],
    selectedRowKey: -1,
    showInput: false,
};

export interface recordsState {
    allRecords: allRecordsState,
    myRecords: myRecordsState,
}

const initialState: recordsState = {
    allRecords: allRecordsState,
    myRecords: myRecordsState,
}

export const recordsSlice = createSlice({
    name: 'records',
    initialState,
    // reducers 集合了reducer和action的功能
    // 每个case冒号之前的为action名
    // 每个case整体为reducer
    reducers: {
        // 使用PayloadAction来展示action.payload的详细内容
        getMyRecordListWhenInit: (state: recordsState, action: PayloadAction<any>) => {
            state.myRecords.datasource = action.payload.task_inodes
        },
        getAllRecordListWhenInit: (state: recordsState, action: PayloadAction<any>) => {
            state.allRecords.datasource = action.payload.task_inodes
        },
        getAllRecordLimitWhenInit: (state: recordsState, action: PayloadAction<any>) => {
            const default_value = action.payload.search_days.toString()
            const currentDate = new Date();
            const defaultDaysAgo = new Date(currentDate.getTime() - default_value * 24 * 60 * 60 * 1000);
            // 格式化日期，这里使用YYYY-MM-DD格式  
            const formattedDate = `${defaultDaysAgo.getFullYear()}-${('0' + (defaultDaysAgo.getMonth() + 1)).slice(-2)}-${('0' + defaultDaysAgo.getDate()).slice(-2)}`;
            state.allRecords.searchDay = formattedDate
        },
        //allRecords
        //点击后显示详情
        allRecordsHandleRowDetailClick: (state: recordsState, action: PayloadAction<any>) => {
            if (state.allRecords.allSelectedRowKey === action.payload) {
                state.allRecords.showInput = !state.allRecords.showInput;
            } else {
                state.allRecords.allSelectedRowKey = action.payload;
                state.allRecords.showInput = true;
            }
        },

        //myRecords
        //点击后显示详情
        myRecordsHandleRowDetailClick: (state: recordsState, action: PayloadAction<any>) => {
            if (state.myRecords.selectedRowKey === action.payload) {
                state.myRecords.showInput = !state.myRecords.showInput;
            } else {
                state.myRecords.selectedRowKey = action.payload;
                state.myRecords.showInput = true;
            }
        },
    },
});

//以下内容必须要有
export const { actions: recordsActions } = recordsSlice;

export default recordsSlice.reducer;

//state 后面的为store中数据名称
export const recordsData = (state: RootState) => state.records;
