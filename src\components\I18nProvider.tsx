import React, { useEffect, useState, ReactNode } from 'react';
import { refreshLanguageListeners } from '@/utils/languageManager';

interface I18nProviderProps {
  children: ReactNode;
}

/**
 * 独立的 i18n 初始化组件
 * 将 i18n 初始化逻辑从 _app.tsx 中分离，避免冲突
 */
const I18nProvider: React.FC<I18nProviderProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // 立即初始化 i18n，确保语言偏好能及时生效
    const initI18n = async () => {
      try {
        // 动态导入 i18n 相关模块，避免在 _app.tsx 中直接导入
        const { default: i18n, loadLanguageResources } = await import('@/lib/i18n');
        const { getStoredLanguage } = await import('@/hooks/useTranslation');

        if (typeof window !== 'undefined') {
          const storedLang = getStoredLanguage();
          console.log('I18nProvider: Initializing with stored language:', storedLang);

          // 先加载语言资源
          if (typeof loadLanguageResources === 'function') {
            await loadLanguageResources(storedLang);
          }

          // 然后切换语言
          if (i18n && typeof i18n.changeLanguage === 'function') {
            if (i18n.language !== storedLang) {
              await i18n.changeLanguage(storedLang);
              console.log('I18nProvider: Language changed to:', storedLang);
            }
          }

          // 刷新语言管理器的监听器，确保所有组件都能收到初始语言
          setTimeout(() => {
            refreshLanguageListeners();
            console.log('I18nProvider: Language listeners refreshed');
          }, 100);
        }

        setIsInitialized(true);
      } catch (error) {
        console.warn('I18n initialization failed:', error);
        // 即使初始化失败，也要设置为已初始化，让应用继续运行
        setIsInitialized(true);
      }
    };

    // 立即执行，不延迟
    initI18n();
  }, []);

  // 在 i18n 初始化完成前，直接渲染子组件
  // 不显示加载状态，避免影响用户体验
  return <>{children}</>;
};

export default I18nProvider;
