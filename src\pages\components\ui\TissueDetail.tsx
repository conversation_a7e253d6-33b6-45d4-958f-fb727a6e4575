import React from 'react';
import { Mo<PERSON>, <PERSON><PERSON>, Card, Divider, Tag, Row, Col, Typography } from 'antd';
import { UserOutlined, TeamOutlined, CalendarOutlined, ClockCircleOutlined, CloseOutlined } from '@ant-design/icons';
import { useApplicationDispatch } from "@/hook/hooks";
import { statisticsActions } from '@/slice/statisticsSlice';
import { useTranslation } from '@/hooks/useTranslation';
import styles from '../css/statisticsDetail.module.css';

const { Title, Text } = Typography;

interface params {
  detail: any;
  workNo: string;
}

const Detail = ({ detail, workNo }: params) => {
  // 多语言支持
  const { t: tCommon } = useTranslation('common');
  const { t: tStatistics } = useTranslation('statistics');
  const userInfo = detail;
  const leaveList = userInfo ? userInfo.leave_list : [];
  const overtimeList = userInfo ? userInfo.overtime_list : [];

  const dispatch = useApplicationDispatch();

  // 获取请假类型的显示名称和CSS类
  const getLeaveTypeInfo = (type: string) => {
    const typeMap: { [key: string]: { name: string; className: string } } = {
      compensatory: { name: tCommon('compensatoryLeave'), className: styles.tag_compensatory },
      normal: { name: tCommon('personalLeave'), className: styles.tag_personal },
      stick: { name: tCommon('sickLeave'), className: styles.tag_sick },
      other: { name: tCommon('otherLeave'), className: styles.tag_other },
      trip: { name: tCommon('businessTrip'), className: styles.tag_trip }
    };
    return typeMap[type] || { name: type, className: '' };
  };

  // 获取加班类型的显示名称和CSS类
  const getOvertimeTypeInfo = (type: string) => {
    const typeMap: { [key: string]: { name: string; className: string } } = {
      weekday: { name: tStatistics('weekdayOvertime'), className: styles.tag_weekday_overtime },
      weekend: { name: tStatistics('weekendOvertime'), className: styles.tag_weekend_overtime },
      holiday: { name: tStatistics('holidayOvertime'), className: styles.tag_holiday_overtime }
    };
    return typeMap[type] || { name: type, className: '' };
  };

  return (
    <Modal
      title={
        <div className={styles.modal_title}>
          <UserOutlined className={styles.title_icon} />
          {tStatistics('detailInfo')}
        </div>
      }
      open={true}
      onCancel={() => dispatch(statisticsActions.handleClose())}
      footer={[
        <Button
          key="close"
          onClick={() => dispatch(statisticsActions.handleClose())}
          className={styles.close_btn}
        >
          {tStatistics('close')}
        </Button>
      ]}
      className={styles.detail_modal}
    >
      <div className={styles.modal_content}>
        {/* 用户基本信息 */}
        <Card className={styles.user_info_card} size="small">
          <Row gutter={24}>
            <Col span={12}>
              <div className={styles.info_item}>
                <UserOutlined className={styles.info_icon} />
                <Text strong>{tStatistics('employeeInfo')}: </Text>
                <Text>{workNo}{userInfo?.user_name ? `-${userInfo.user_name}` : ''}</Text>
              </div>
            </Col>
            <Col span={12}>
              <div className={styles.info_item}>
                <TeamOutlined className={styles.info_icon} />
                <Text strong>{tStatistics('department')}: </Text>
                <Text>{userInfo?.depart_name}</Text>
              </div>
            </Col>
          </Row>
        </Card>
        <Divider />

        {/* 请假记录 */}
        <Card
          title={
            <div className={styles.section_title}>
              <CalendarOutlined className={styles.section_icon} />
              {tStatistics('leaveRecords')}
            </div>
          }
          className={styles.leave_card}
          size="small"
          style={{ marginBottom: '16px' }}
        >
          {leaveList && leaveList.length > 0 ? (
            <div className={styles.leave_list}>
              {leaveList.map((item: any, index: number) => (
                <div key={index} className={styles.leave_item}>
                  <div className={styles.leave_date}>
                    <CalendarOutlined className={styles.date_icon} />
                    <Text strong>{item.dayStr}</Text>
                  </div>
                  <div className={styles.leave_types}>
                    {item.compensatory > 0 && (
                      <Tag className={`${styles.leave_tag} ${getLeaveTypeInfo('compensatory').className}`}>
                        {getLeaveTypeInfo('compensatory').name}: {item.compensatory}h
                      </Tag>
                    )}
                    {item.normal > 0 && (
                      <Tag className={`${styles.leave_tag} ${getLeaveTypeInfo('normal').className}`}>
                        {getLeaveTypeInfo('normal').name}: {item.normal}h
                      </Tag>
                    )}
                    {item.stick > 0 && (
                      <Tag className={`${styles.leave_tag} ${getLeaveTypeInfo('stick').className}`}>
                        {getLeaveTypeInfo('stick').name}: {item.stick}h
                      </Tag>
                    )}
                    {item.other > 0 && (
                      <Tag className={`${styles.leave_tag} ${getLeaveTypeInfo('other').className}`}>
                        {getLeaveTypeInfo('other').name}: {item.other}h
                      </Tag>
                    )}
                    {item.trip > 0 && (
                      <Tag className={`${styles.leave_tag} ${getLeaveTypeInfo('trip').className}`}>
                        {getLeaveTypeInfo('trip').name}: {item.trip}h
                      </Tag>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className={styles.no_data}>
              <Text type="secondary">{tStatistics('noLeaveRecords')}</Text>
            </div>
          )}
        </Card>

        {/* 加班记录 */}
        <Card
          title={
            <div className={styles.section_title}>
              <ClockCircleOutlined className={styles.section_icon} />
              {tStatistics('overtimeRecords')}
            </div>
          }
          className={styles.overtime_card}
          size="small"
        >
          {overtimeList && overtimeList.length > 0 ? (
            <div className={styles.overtime_list}>
              {overtimeList.map((item: any, index: number) => (
                <div key={index} className={styles.overtime_item}>
                  <div className={styles.overtime_header}>
                    <div className={styles.overtime_date}>
                      <CalendarOutlined className={styles.date_icon} />
                      <Text strong>{item.dayStr}</Text>
                    </div>
                    <div className={styles.overtime_time}>
                      {item.start_time && (
                        <Text className={styles.time_text}>
                          {tStatistics('start')}: {item.start_time}
                        </Text>
                      )}
                      {item.end_time && (
                        <Text className={styles.time_text}>
                          {tStatistics('end')}: {item.end_time}
                        </Text>
                      )}
                    </div>
                  </div>
                  <div className={styles.overtime_types}>
                    {item.weekday > 0 && (
                      <Tag className={`${styles.overtime_tag} ${getOvertimeTypeInfo('weekday').className}`}>
                        {getOvertimeTypeInfo('weekday').name}: {item.weekday}h
                      </Tag>
                    )}
                    {item.weekend > 0 && (
                      <Tag className={`${styles.overtime_tag} ${getOvertimeTypeInfo('weekend').className}`}>
                        {getOvertimeTypeInfo('weekend').name}: {item.weekend}h
                      </Tag>
                    )}
                    {item.holiday > 0 && (
                      <Tag className={`${styles.overtime_tag} ${getOvertimeTypeInfo('holiday').className}`}>
                        {getOvertimeTypeInfo('holiday').name}: {item.holiday}h
                      </Tag>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className={styles.no_data}>
              <Text type="secondary">{tStatistics('noOvertimeRecords')}</Text>
            </div>
          )}
        </Card>
      </div>
    </Modal>
  );
};

export default Detail;