// 交通费申请API实现

import {
  TransportationExpenseApplication,
  CreateApplicationRequest,
  UpdateApplicationRequest,
  DeleteApplicationRequest,
  GetApplicationListResponse,
  CreateApplicationResponse,
  UpdateApplicationResponse,
  DeleteApplicationResponse
} from '../types';

/**
 * 获取申请列表
 * GET /in/apply/transportation_fee/get
 */
export const getApplicationList = async (token: string): Promise<GetApplicationListResponse> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/apply/transportation_fee/get', {
  //   method: 'GET',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    apply_list: [
      {
        code: 'ba8dfe8a519411f0bc5bc025a5c7be3d1fff86691b38438897df489676e518b4',
        user_id: 553,
        name: '朱国威',
        start_day: '2024-06-01',
        end_day: '2024-06-29',
        route_from: '住所A',
        route_to: '出勤地B',
        fee_amount_single: 100.0,
        fee_amount_monthly: 3000.0,
        reason: '测试更新交通费报销申请',
        status: 'pending',
        created_at: '2024-06-01T09:00:00Z',
        updated_at: '2024-06-01T09:00:00Z'
      }
    ]
  };
};

/**
 * 新建申请
 * POST /in/apply/transportation_fee/add
 */
export const createApplication = async (
  token: string, 
  data: CreateApplicationRequest
): Promise<CreateApplicationResponse> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/apply/transportation_fee/add', {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  //   body: JSON.stringify(data),
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    message: '交通费报销申请创建成功',
    code: '141bbda653f611f0bf06c025a5c7be3d09494c9c5c9a48ff9cb480a4862d6aba'
  };
};

/**
 * 更新申请
 * POST /in/apply/transportation_fee/update
 */
export const updateApplication = async (
  token: string, 
  data: UpdateApplicationRequest
): Promise<UpdateApplicationResponse> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/apply/transportation_fee/update', {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  //   body: JSON.stringify(data),
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    message: '交通费报销申请更新成功'
  };
};

/**
 * 删除申请
 * POST /in/apply/transportation_fee/delete
 */
export const deleteApplication = async (
  token: string, 
  data: DeleteApplicationRequest
): Promise<DeleteApplicationResponse> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/apply/transportation_fee/delete', {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  //   body: JSON.stringify(data),
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    message: '交通费报销申请删除成功'
  };
};
