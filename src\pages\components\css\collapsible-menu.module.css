/* CSS变量定义 */
:root {
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --radius-small: 4px;
  --radius-medium: 8px;
  --radius-large: 12px;
  --border-width: 1px;
  --border-width-thick: 3px;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --icon-size-sm: 14px;
  --icon-size-md: 16px;
  --icon-size-lg: 20px;
  --primary-color: #3498db;
  --primary-color-light: #5dade2;
  --primary-color-dark: #2980b9;
  --secondary-color: #9b59b6;
  --accent-color: #e74c3c;
  --text-sidebar: #ecf0f1;
  --text-sidebar-secondary: #bdc3c7;
  --text-secondary: #95a5a6;
  --sidebar-border-color: rgba(255, 255, 255, 0.1);
  --divider-color: rgba(255, 255, 255, 0.1);
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-large: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* 可折叠菜单样式 - 日系风格 */
.collapsible_menu {
  width: 100%;
  margin: 0;
}

/* 折叠面板样式 */
.menu_collapse {
  background: transparent !important;
  border: none !important;
}

.menu_collapse :global(.ant-collapse-item) {
  border: none !important;
  margin-bottom: var(--spacing-sm);
}

/* 日系面板头部样式 - 深色主题，更好的背景融合 */
.menu_collapse :global(.ant-collapse-header) {
  padding: var(--spacing-md) var(--spacing-lg) !important;
  background: linear-gradient(135deg, rgba(52, 73, 94, 0.6) 0%, rgba(44, 62, 80, 0.8) 100%);
  border-radius: var(--radius-medium) !important;
  border: var(--border-width) solid rgba(189, 195, 199, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  color: var(--text-sidebar);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
}

/* 面板头部装饰线 */
.menu_collapse :global(.ant-collapse-header)::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: var(--spacing-xs);
  background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
  border-radius: 0 var(--radius-small) var(--radius-small) 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.menu_collapse :global(.ant-collapse-header:hover) {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.15) 0%, rgba(44, 62, 80, 0.9) 100%);
  box-shadow: var(--shadow-medium);
  transform: translateY(-1px) scale(1.01);
}

.menu_collapse :global(.ant-collapse-header:hover)::before {
  opacity: 1;
}

.menu_collapse :global(.ant-collapse-content) {
  border: none !important;
  background: transparent !important;
}

.menu_collapse :global(.ant-collapse-content-box) {
  padding: var(--spacing-sm) 0 !important;
}

/* 日系面板头部样式 */
.panel_header {
  display: flex;
  align-items: center;
  width: 100%;
  gap: var(--spacing-md);
}

.panel_icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--icon-size-lg);
  height: var(--icon-size-lg);
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.panel_title {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-sidebar);
  letter-spacing: 0.3px;
  flex: 1;
}

/* 日系展开图标样式 */
.expand_icon {
  color: var(--text-secondary);
  font-size: var(--icon-size-sm);
  transition: all 0.3s ease;
}

.menu_collapse :global(.ant-collapse-header:hover) .expand_icon {
  color: var(--primary-color);
  transform: scale(1.1);
}

.menu_collapse :global(.ant-collapse-header:hover) .panel_icon {
  transform: scale(1.1);
}

/* 日系菜单项容器 - 深色主题，更好的背景融合 */
.menu_items {
  padding: var(--spacing-sm) var(--spacing-md);
  background: linear-gradient(135deg, rgba(52, 73, 94, 0.4) 0%, rgba(44, 62, 80, 0.6) 100%);
  border-radius: var(--radius-medium);
  margin: var(--spacing-sm) var(--spacing-md);
  border: var(--border-width) solid rgba(189, 195, 199, 0.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(8px);
}

/* 日系菜单项样式 - 深色主题，调整高度 */
.menu_item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) var(--spacing-md);
  margin: var(--spacing-xs) 0;
  border-radius: var(--radius-medium);
  border-left: var(--border-width-thick) solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--text-sidebar);
  background-color: transparent;
  font-weight: 500;
  letter-spacing: 0.2px;
  position: relative;
  overflow: hidden;
  border: var(--border-width) solid transparent;
  min-height: calc(var(--spacing-lg) + var(--spacing-sm));
}

/* 菜单项悬停效果 - 深色主题，更好的背景适配 */
.menu_item:hover {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.12) 0%, rgba(44, 62, 80, 0.6) 100%);
  transform: translateX(var(--spacing-sm)) scale(1.02);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  border-left-color: var(--primary-color-light);
  color: #bdc3c7;
}

/* 选中的菜单项 - 深色主题，更好的背景适配 */
.menu_item_selected {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.2) 0%, rgba(44, 62, 80, 0.8) 100%);
  color: #5dade2;
  font-weight: 600;
  transform: translateX(var(--spacing-xs));
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(52, 152, 219, 0.4);
  border-left: var(--border-width-thick) solid var(--primary-color-light);
  border: var(--border-width) solid rgba(52, 152, 219, 0.3);
  border-radius: var(--radius-medium);
  position: relative;
  backdrop-filter: blur(5px);
}

/* 选中项的装饰元素 */
.menu_item_selected::before {
  content: '';
  position: absolute;
  top: 50%;
  right: var(--spacing-md);
  width: var(--spacing-sm);
  height: var(--spacing-sm);
  background: var(--primary-color);
  border-radius: 50%;
  transform: translateY(-50%);
  box-shadow: 0 0 8px rgba(107, 155, 210, 0.4);
}

/* 选中项悬停效果 - 深色主题，更好的背景适配 */
.menu_item_selected:hover {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.3) 0%, rgba(44, 62, 80, 0.9) 100%);
  transform: translateX(var(--spacing-xs)) scale(1.01);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(52, 152, 219, 0.5);
  color: #74b9ff;
}

.menu_item_text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.3s ease;
}

/* 选中项的文字样式 */
.menu_item_selected .menu_item_text {
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.menu_item_arrow {
  font-size: var(--font-size-lg);
  opacity: 0.6;
  transition: all 0.3s ease;
  font-weight: bold;
  margin-left: var(--spacing-sm);
}

.menu_item:hover .menu_item_arrow {
  opacity: 1;
  transform: translateX(var(--spacing-xs));
  color: var(--primary-color);
}

.menu_item_selected .menu_item_arrow {
  opacity: 1;
  color: var(--primary-color);
  font-size: var(--font-size-xs);
  animation: selectedDot 2s ease-in-out infinite;
}

@keyframes selectedDot {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

/* 激活状态的面板样式 - 日系风格 */
.menu_collapse :global(.ant-collapse-item-active) :global(.ant-collapse-header) {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-light) 100%);
  color: white;
  box-shadow: var(--shadow-medium);
  transform: scale(1.02);
}

.menu_collapse :global(.ant-collapse-item-active) :global(.ant-collapse-header)::before {
  opacity: 1;
  background: linear-gradient(180deg, var(--accent-color) 0%, white 100%);
}

.menu_collapse :global(.ant-collapse-item-active) .panel_title {
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.menu_collapse :global(.ant-collapse-item-active) .expand_icon {
  color: white;
}

.menu_collapse :global(.ant-collapse-item-active) .panel_icon {
  transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .menu_collapse :global(.ant-collapse-header) {
    padding: var(--spacing-sm) var(--spacing-md) !important;
    font-size: var(--font-size-sm);
  }

  .menu_item {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
  }

  .menu_items {
    padding-left: var(--spacing-md);
    margin-left: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .menu_collapse :global(.ant-collapse-header) {
    padding: var(--spacing-sm) var(--spacing-sm) !important;
    font-size: var(--font-size-xs);
  }

  .menu_item {
    padding: var(--spacing-sm) var(--spacing-sm);
    font-size: var(--font-size-xs);
  }

  .menu_items {
    padding-left: var(--spacing-sm);
    margin-left: var(--spacing-sm);
  }
}

/* 日系动画效果 */
.menu_collapse :global(.ant-collapse-content) {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu_item {
  animation: slideInFromLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(calc(-1 * var(--spacing-lg)));
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 面板展开动画 */
.menu_collapse :global(.ant-collapse-header) {
  animation: headerFloat 0.4s ease;
}

@keyframes headerFloat {
  from {
    transform: translateY(var(--spacing-xs));
    opacity: 0.8;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 基于菜单类型的主题色彩 - 改善对比度 */
.menu_item[data-tab-type="record"][data-selected="true"] {
  border-left-color: #1890ff;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 251, 255, 0.9) 100%);
  color: #1565c0;
  border: var(--border-width) solid rgba(24, 144, 255, 0.2);
}

.menu_item[data-tab-type="apply"][data-selected="true"] {
  border-left-color: #faad14;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 252, 245, 0.9) 100%);
  color: #e65100;
  border: var(--border-width) solid rgba(250, 173, 20, 0.2);
}

.menu_item[data-tab-type="approvel"][data-selected="true"] {
  border-left-color: #fa8c16;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 248, 240, 0.9) 100%);
  color: #d84315;
  border: var(--border-width) solid rgba(250, 140, 22, 0.2);
}

.menu_item[data-tab-type="member"][data-selected="true"] {
  border-left-color: #a0d911;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(249, 255, 240, 0.9) 100%);
  color: #689f38;
  border: var(--border-width) solid rgba(160, 217, 17, 0.2);
}

.menu_item[data-tab-type="report"][data-selected="true"] {
  border-left-color: #52c41a;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(246, 255, 237, 0.9) 100%);
  color: #388e3c;
  border: var(--border-width) solid rgba(82, 196, 26, 0.2);
}

.menu_item[data-tab-type="depart"][data-selected="true"] {
  border-left-color: #f5222d;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 241, 240, 0.9) 100%);
  color: #c62828;
  border: var(--border-width) solid rgba(245, 34, 45, 0.2);
}

.menu_item[data-tab-type="history"][data-selected="true"] {
  border-left-color: #722ed1;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 240, 252, 0.9) 100%);
  color: #512da8;
  border: var(--border-width) solid rgba(114, 46, 209, 0.2);
}

.menu_item[data-tab-type="setting"][data-selected="true"] {
  border-left-color: #eb2f96;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 240, 246, 0.9) 100%);
  color: #ad1457;
  border: var(--border-width) solid rgba(235, 47, 150, 0.2);
}

/* 主题色彩的装饰点 - 更深的颜色 */
.menu_item[data-tab-type="record"][data-selected="true"]::before { background: #1565c0; }
.menu_item[data-tab-type="apply"][data-selected="true"]::before { background: #e65100; }
.menu_item[data-tab-type="approvel"][data-selected="true"]::before { background: #d84315; }
.menu_item[data-tab-type="member"][data-selected="true"]::before { background: #689f38; }
.menu_item[data-tab-type="report"][data-selected="true"]::before { background: #388e3c; }
.menu_item[data-tab-type="depart"][data-selected="true"]::before { background: #c62828; }
.menu_item[data-tab-type="history"][data-selected="true"]::before { background: #512da8; }
.menu_item[data-tab-type="setting"][data-selected="true"]::before { background: #ad1457; }

/* 主题色彩的箭头 - 更深的颜色 */
.menu_item[data-tab-type="record"][data-selected="true"] .menu_item_arrow { color: #1565c0; }
.menu_item[data-tab-type="apply"][data-selected="true"] .menu_item_arrow { color: #e65100; }
.menu_item[data-tab-type="approvel"][data-selected="true"] .menu_item_arrow { color: #d84315; }
.menu_item[data-tab-type="member"][data-selected="true"] .menu_item_arrow { color: #689f38; }
.menu_item[data-tab-type="report"][data-selected="true"] .menu_item_arrow { color: #388e3c; }
.menu_item[data-tab-type="depart"][data-selected="true"] .menu_item_arrow { color: #c62828; }
.menu_item[data-tab-type="history"][data-selected="true"] .menu_item_arrow { color: #512da8; }
.menu_item[data-tab-type="setting"][data-selected="true"] .menu_item_arrow { color: #ad1457; }

/* 图标浮动动画 */
.panel_icon {
  animation: iconFloat 4s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(calc(-1 * var(--spacing-xs)));
  }
}
