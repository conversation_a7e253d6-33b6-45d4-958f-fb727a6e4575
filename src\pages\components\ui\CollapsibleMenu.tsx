import React, { useState, useEffect } from 'react';
import { Collapse } from 'antd';
import {
  DownOutlined,
  RightOutlined,
  UserOutlined,
  ClockCircleOutlined,
  FileAddOutlined,
  CheckCircleOutlined,
  TeamOutlined,
  BarChartOutlined,
  ApartmentOutlined,
  HistoryOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useApplicationSelector, useApplicationDispatch } from "@/hook/hooks";
import { tabData, tabActions } from "@/slice/tabSlice";
import { getMenuConfig, menuTypes } from '@/utils/menulist';
// import { permissionGroupIsGranted, subPermissionIsGranted, permissionsGroups } from '@/utils/permissions';
import { useTranslation } from '@/hooks/useTranslation';
import { Link } from 'react-router-dom';
import styles from '../css/collapsible-menu.module.css';

const { Panel } = Collapse;

interface MenuItemProps {
  linkTo: string;
  textContent: string;
  liIndex: string;
  tabType: string;
  isSelected: boolean;
  onSelect: () => void;
}

const MenuItem: React.FC<MenuItemProps> = ({
  linkTo,
  textContent,
  liIndex,
  tabType,
  isSelected,
  onSelect
}) => {

  return (
    <Link to={linkTo}>
      <div
        className={`${styles.menu_item} ${isSelected ? styles.menu_item_selected : ''}`}
        onClick={onSelect}
        data-tab-type={tabType}
        data-selected={isSelected}
      >
        <span className={styles.menu_item_text}>{textContent}</span>
        <span className={styles.menu_item_arrow}>
          {isSelected ? '●' : '›'}
        </span>
      </div>
    </Link>
  );
};

// 获取菜单图标组件
const getMenuIcon = (menuType: string, isActive: boolean) => {
  const iconStyle = {
    fontSize: '18px',
    color: isActive ? 'var(--primary-color-light)' : 'var(--text-sidebar)',
    transition: 'all 0.3s ease',
    filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3))',
    opacity: isActive ? 1 : 0.9
  };

  const iconMap = {
    [menuTypes.profile]: <UserOutlined style={iconStyle} />,
    [menuTypes.record]: <ClockCircleOutlined style={iconStyle} />,
    [menuTypes.apply]: <FileAddOutlined style={iconStyle} />,
    [menuTypes.approvel]: <CheckCircleOutlined style={iconStyle} />,
    [menuTypes.member]: <TeamOutlined style={iconStyle} />,
    [menuTypes.report]: <BarChartOutlined style={iconStyle} />,
    [menuTypes.depart]: <ApartmentOutlined style={iconStyle} />,
    [menuTypes.history]: <HistoryOutlined style={iconStyle} />,
    [menuTypes.setting]: <SettingOutlined style={iconStyle} />,
  };
  return iconMap[menuType] || <SettingOutlined style={iconStyle} />;
};

const CollapsibleMenu: React.FC = () => {
  const { t } = useTranslation('menu');
  const currentTab = useApplicationSelector(tabData);
  const dispatch = useApplicationDispatch();

  // 获取当前语言的菜单配置
  const menuConfig = getMenuConfig(t);
  
  // 活跃的面板keys
  const [activeKeys, setActiveKeys] = useState<string[]>([]);

  // 初始化时展开当前选中的菜单组
  useEffect(() => {
    if (currentTab.tabType) {
      setActiveKeys([currentTab.tabType]);
    }
  }, [currentTab.tabType]);

  // 处理面板展开/收起
  const handlePanelChange = (keys: string | string[]) => {
    const keyArray = Array.isArray(keys) ? keys : [keys];
    setActiveKeys(keyArray);
  };

  // 处理菜单项选择
  const handleMenuItemSelect = (tabType: string, subIndex: string) => {
    dispatch(tabActions.onTabChange(tabType));
    dispatch(tabActions.onSubChange(subIndex));
  };

  // 自定义展开图标
  const expandIcon = ({ isActive }: { isActive?: boolean }) => (
    <span className={styles.expand_icon}>
      {isActive ? <DownOutlined /> : <RightOutlined />}
    </span>
  );

  // 渲染菜单组
  const renderMenuGroups = () => {
    return menuConfig.map((menuGroup) => {
      // 临时禁用权限检查 - 显示所有菜单
      const hasGroupPermission = true;

      if (!hasGroupPermission) {
        return null;
      }

      // 显示所有菜单项（禁用权限过滤）
      const authorizedItems = menuGroup.list || [];

      if (authorizedItems.length === 0) {
        return null;
      }

      return (
        <Panel
          header={
            <div className={styles.panel_header}>
              <span className={styles.panel_icon}>
                {getMenuIcon(menuGroup.type, currentTab.tabType === menuGroup.type)}
              </span>
              <span className={styles.panel_title}>{menuGroup.title}</span>
            </div>
          }
          key={menuGroup.type}
          className={styles.menu_panel}
        >
          <div className={styles.menu_items}>
            {authorizedItems.map((item) => (
              <MenuItem
                key={item.index}
                linkTo={item.linkTo}
                textContent={item.text}
                liIndex={item.index}
                tabType={menuGroup.type}
                isSelected={
                  currentTab.tabType === menuGroup.type && 
                  currentTab.subTabIndex === item.index
                }
                onSelect={() => handleMenuItemSelect(menuGroup.type, item.index)}
              />
            ))}
          </div>
        </Panel>
      );
    }).filter(Boolean);
  };

  return (
    <div className={styles.collapsible_menu}>
      <Collapse
        activeKey={activeKeys}
        onChange={handlePanelChange}
        expandIcon={expandIcon}
        ghost
        className={styles.menu_collapse}
      >
        {renderMenuGroups()}
      </Collapse>
    </div>
  );
};

export default CollapsibleMenu;
