import { getApi, postApi } from '@/api/fetch-api'
import ApiUrlVars from '@/api/common/url-vars'
import { get_token_aes } from '@/utils/login'
import { onLogin } from '@/utils/cookies'
import { setUserActions } from '@/slice/authSlice'
import { authApi } from '@/api/auth'
import { LoginRequest, LoginResponse, LoginFormData, LoginApiResponse } from '../types'

/**
 * 用户登录 - 使用新的API架构
 * @param params 登录参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<LoginApiResponse>
 */
export async function loginUser(params: LoginFormData, dispatch: any): Promise<LoginApiResponse> {
  try {
    // 使用新的API架构进行登录
    const loginParams: LoginRequest = {
      user_account: params.user_account || params.username || '',
      user_password: params.user_password || params.password || ''
    };

    const response = await authApi.loginWithStateManagement(loginParams, dispatch);

    return {
      status: 'OK',
      data: response
    };
  } catch (error: any) {
    console.error('Login API error:', error);
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    };
  }
}

/**
 * 用户登录 - 传统方式（保持向后兼容）
 * @param params 登录参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<LoginApiResponse>
 */
export async function loginUserLegacy(params: LoginFormData, dispatch: any): Promise<LoginApiResponse> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.login_get
    const response = await getApi(url, params, dispatch)

    if (response.data.status === 'OK') {
      // 处理登录成功
      const token = response.data.token
      const token_aes = get_token_aes(token)

      // 保存到cookie
      onLogin(token, token_aes)

      // 保存到localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('login', JSON.stringify(response.data))
      }

      // 更新Redux状态
      dispatch(setUserActions.setUser(response.data))

      return {
        status: 'OK',
        data: response.data
      }
    } else {
      return {
        status: 'NG',
        message: response.data.message || '登录失败'
      }
    }
  } catch (error: any) {
    console.error('Login API error:', error)
    return {
      status: 'NG',
      message: error.message || '网络错误，请稍后重试'
    }
  }
}

/**
 * 重置密码
 * @param params 重置参数
 * @param dispatch Redux dispatch函数
 * @returns Promise<any>
 */
export async function resetPassword(params: any, dispatch: any): Promise<any> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.reset_post
    const response = await postApi(url, params, dispatch)
    return response
  } catch (error: any) {
    console.error('Reset password API error:', error)
    throw error
  }
}

/**
 * 检查登录状态
 * @param dispatch Redux dispatch函数
 * @returns Promise<any>
 */
export async function checkLoginStatus(dispatch: any): Promise<any> {
  try {
    const url = ApiUrlVars.loginApi_domain + ApiUrlVars.login_get
    const response = await getApi(url, {}, dispatch)
    return response
  } catch (error: any) {
    console.error('Check login status API error:', error)
    throw error
  }
}

/**
 * 登出处理
 * @param dispatch Redux dispatch函数
 * @returns Promise<any>
 */
export async function logoutUser(dispatch: any): Promise<any> {
  try {
    // 如果有登出API，在这里调用
    // const url = ApiUrlVars.loginApi_domain + ApiUrlVars.logout_post
    // const response = await postApi(url, {}, dispatch)
    
    // 清除本地存储
    if (typeof window !== 'undefined') {
      localStorage.removeItem('login')
    }
    
    return { status: 'OK' }
  } catch (error: any) {
    console.error('Logout API error:', error)
    throw error
  }
}
