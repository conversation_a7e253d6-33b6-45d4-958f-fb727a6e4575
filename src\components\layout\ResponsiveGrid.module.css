/* 响应式网格系统 */
.grid {
  display: grid;
  grid-template-columns: repeat(var(--grid-columns, 12), 1fr);
  width: 100%;
  box-sizing: border-box;
}

.gridItem {
  grid-column: span var(--item-span, 1);
  box-sizing: border-box;
}

/* 网格间距 */
.gap_sm {
  gap: var(--grid-gutter-sm);
}

.gap_md {
  gap: var(--grid-gutter);
}

.gap_lg {
  gap: var(--grid-gutter-lg);
}

/* 响应式断点 */
@media (max-width: 320px) {
  .grid {
    grid-template-columns: repeat(var(--grid-columns-xs, var(--grid-columns, 12)), 1fr);
  }
  
  .gridItem {
    grid-column: span var(--item-span-xs, var(--item-span, 1));
  }
}

@media (max-width: 480px) {
  .grid {
    grid-template-columns: repeat(var(--grid-columns-sm, var(--grid-columns, 12)), 1fr);
  }
  
  .gridItem {
    grid-column: span var(--item-span-sm, var(--item-span, 1));
  }
}

@media (max-width: 768px) {
  .grid {
    grid-template-columns: repeat(var(--grid-columns-md, var(--grid-columns, 12)), 1fr);
  }
  
  .gridItem {
    grid-column: span var(--item-span-md, var(--item-span, 1));
  }
}

@media (max-width: 1024px) {
  .grid {
    grid-template-columns: repeat(var(--grid-columns-lg, var(--grid-columns, 12)), 1fr);
  }
  
  .gridItem {
    grid-column: span var(--item-span-lg, var(--item-span, 1));
  }
}

@media (min-width: 1200px) {
  .grid {
    grid-template-columns: repeat(var(--grid-columns-xl, var(--grid-columns, 12)), 1fr);
  }
  
  .gridItem {
    grid-column: span var(--item-span-xl, var(--item-span, 1));
  }
}
