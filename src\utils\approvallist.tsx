import { getCurrentLanguage } from './languageManager';

// 获取翻译函数
const getTranslation = () => {
    const language = getCurrentLanguage();

    // 简单的翻译映射，实际项目中应该使用完整的翻译系统
    const translations: { [key: string]: { [key: string]: string } } = {
        zh: {
            'approval.tabs.all': '全部',
            'approval.tabs.pending': '待审批',
            'approval.tabs.approved': '已同意',
            'approval.tabs.rejected': '已驳回'
        },
        ja: {
            'approval.tabs.all': '全て',
            'approval.tabs.pending': '承認待ち',
            'approval.tabs.approved': '承認済み',
            'approval.tabs.rejected': '却下済み'
        }
    };

    return (key: string, fallback: string = '') => {
        return translations[language]?.[key] || fallback;
    };
};

// 获取审批配置的函数
export const getApprovalConfig = () => {
    const t = getTranslation();

    return {
        approvalAll: {
            title: t('approval.tabs.all', '全部')
        },
        approvalNull: {
            title: t('approval.tabs.pending', '待审批')
        },
        approvalFull: {
            title: t('approval.tabs.approved', '已同意')
        },
        approvalReject: {
            title: t('approval.tabs.rejected', '已驳回')
        },
    };
};

// 为了向后兼容，保留原有的 approvalConfig 导出（使用默认中文）
export const approvalConfig = {
    approvalAll: {
        title: "全部"
    },
    approvalNull: {
        title: "待审批"
    },
    approvalFull: {
        title: "已同意"
    },
    approvalReject: {
        title: "已驳回"
    },
};