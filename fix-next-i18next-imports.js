#!/usr/bin/env node

/**
 * 修复 next-i18next 导入的脚本
 * 将所有 next-i18next 的导入替换为我们自定义的 useTranslation hook
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 修复 next-i18next 导入...');
console.log('=====================================\n');

// 需要检查的文件扩展名
const includeExts = ['.tsx', '.ts'];
// 排除的目录
const excludeDirs = ['node_modules', '.next', 'dist', '.git', 'scripts', 'docs'];

// 递归查找文件
function findFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !excludeDirs.includes(file)) {
      findFiles(filePath, fileList);
    } else if (stat.isFile() && includeExts.includes(path.extname(file))) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// 修复单个文件
function fixFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let changed = false;
  
  // 检查是否包含 next-i18next 导入
  if (content.includes('next-i18next')) {
    console.log(`📁 修复文件: ${filePath}`);
    
    // 替换导入语句
    const oldImport = /import\s*{\s*useTranslation\s*}\s*from\s*['"]next-i18next['"];?/g;
    const newImport = "import { useTranslation } from '@/hooks/useTranslation';";
    
    if (oldImport.test(content)) {
      content = content.replace(oldImport, newImport);
      changed = true;
      console.log(`  ✅ 替换了 useTranslation 导入`);
    }
    
    // 检查其他可能的 next-i18next 导入
    const otherImports = /import\s*.*from\s*['"]next-i18next.*['"];?/g;
    if (otherImports.test(content)) {
      console.log(`  ⚠️  发现其他 next-i18next 导入，需要手动检查`);
    }
  }
  
  if (changed) {
    fs.writeFileSync(filePath, content);
    console.log(`  💾 文件已保存\n`);
    return true;
  }
  
  return false;
}

// 主函数
function main() {
  const files = findFiles('src');
  console.log(`🔍 找到 ${files.length} 个文件需要检查...\n`);
  
  let fixedCount = 0;
  
  files.forEach(file => {
    if (fixFile(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n📊 修复完成！`);
  console.log(`✅ 修复了 ${fixedCount} 个文件`);
  console.log(`📁 检查了 ${files.length} 个文件`);
  
  if (fixedCount > 0) {
    console.log(`\n🎉 所有 next-i18next 导入已修复为自定义 useTranslation hook`);
  } else {
    console.log(`\n✨ 没有发现需要修复的文件`);
  }
}

main();
