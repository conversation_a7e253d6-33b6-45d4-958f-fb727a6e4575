#!/usr/bin/env node

/**
 * 多语言错误修复脚本
 * 用于诊断和修复 "Should have a queue" 等 React 错误
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 多语言错误修复脚本');
console.log('=====================================\n');

// 检查关键文件是否存在
const criticalFiles = [
  'src/lib/i18n.ts',
  'src/hooks/useTranslation.ts',
  'pages/_app.tsx',
  'next.config.js',
  'public/locales/zh/errors.json',
  'public/locales/ja/errors.json'
];

console.log('📋 检查关键文件...');
let missingFiles = [];

criticalFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - 文件缺失`);
    missingFiles.push(file);
  }
});

if (missingFiles.length > 0) {
  console.log(`\n⚠️  发现 ${missingFiles.length} 个缺失文件，请先运行 npm run i18n:setup`);
  process.exit(1);
}

// 检查 package.json 中的依赖
console.log('\n📦 检查依赖包...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredDeps = {
  'react-i18next': '^13.5.0',
  'i18next': '^23.7.6',
  'next-i18next': '^15.2.0'
};

let missingDeps = [];
Object.entries(requiredDeps).forEach(([dep, version]) => {
  if (packageJson.dependencies[dep]) {
    console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
  } else {
    console.log(`❌ ${dep}: 缺失`);
    missingDeps.push(`${dep}@${version}`);
  }
});

if (missingDeps.length > 0) {
  console.log(`\n⚠️  发现缺失依赖，请运行: npm install ${missingDeps.join(' ')}`);
}

// 检查 next.config.js 配置
console.log('\n⚙️  检查 Next.js 配置...');
try {
  const nextConfigContent = fs.readFileSync('next.config.js', 'utf8');
  
  if (nextConfigContent.includes('reactStrictMode: false')) {
    console.log('✅ React Strict Mode 已禁用');
  } else {
    console.log('⚠️  建议禁用 React Strict Mode 以避免多语言初始化问题');
  }
  
  if (nextConfigContent.includes('i18n:')) {
    console.log('✅ 国际化配置已添加');
  } else {
    console.log('❌ 缺少国际化配置');
  }
} catch (error) {
  console.log('❌ 无法读取 next.config.js');
}

// 检查翻译文件完整性
console.log('\n🌐 检查翻译文件...');
const languages = ['zh', 'ja'];
const namespaces = ['common', 'menu', 'login', 'errors'];

languages.forEach(lang => {
  console.log(`\n  ${lang.toUpperCase()}:`);
  namespaces.forEach(ns => {
    const filePath = `public/locales/${lang}/${ns}.json`;
    if (fs.existsSync(filePath)) {
      try {
        const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        const keyCount = Object.keys(content).length;
        console.log(`    ✅ ${ns}.json (${keyCount} keys)`);
      } catch (error) {
        console.log(`    ❌ ${ns}.json - JSON 格式错误`);
      }
    } else {
      console.log(`    ❌ ${ns}.json - 文件缺失`);
    }
  });
});

// 提供修复建议
console.log('\n🛠️  修复建议:');
console.log('=====================================');
console.log('1. 确保所有依赖已正确安装: npm install');
console.log('2. 重启开发服务器: npm run dev');
console.log('3. 清除 Next.js 缓存: rm -rf .next');
console.log('4. 如果问题持续，尝试: npm run build && npm start');
console.log('5. 检查浏览器控制台是否有其他错误信息');
console.log('6. 测试登出功能: 访问 /test-logout 页面');
console.log('7. 测试多语言功能: 访问 /test-fix 页面');

// 生成诊断报告
const diagnosticReport = {
  timestamp: new Date().toISOString(),
  criticalFiles: criticalFiles.map(file => ({
    path: file,
    exists: fs.existsSync(file)
  })),
  dependencies: Object.keys(requiredDeps).map(dep => ({
    name: dep,
    required: requiredDeps[dep],
    installed: packageJson.dependencies[dep] || null
  })),
  nextConfig: {
    exists: fs.existsSync('next.config.js'),
    hasI18nConfig: fs.existsSync('next.config.js') && 
      fs.readFileSync('next.config.js', 'utf8').includes('i18n:'),
    strictModeDisabled: fs.existsSync('next.config.js') && 
      fs.readFileSync('next.config.js', 'utf8').includes('reactStrictMode: false')
  }
};

fs.writeFileSync('i18n-diagnostic-report.json', JSON.stringify(diagnosticReport, null, 2));
console.log('\n📊 诊断报告已保存到: i18n-diagnostic-report.json');

console.log('\n✨ 检查完成！');
