/* CSS变量定义 */
:root {
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --radius-small: 4px;
  --radius-medium: 8px;
  --radius-large: 12px;
  --border-width: 1px;
  --border-width-thick: 3px;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --primary-color: #3498db;
  --primary-color-light: #5dade2;
  --primary-color-dark: #2980b9;
  --secondary-color: #9b59b6;
  --accent-color: #e74c3c;
  --text-sidebar: #ecf0f1;
  --text-sidebar-secondary: #bdc3c7;
  --sidebar-border-color: rgba(255, 255, 255, 0.1);
  --divider-color: rgba(255, 255, 255, 0.1);
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-large: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* 侧边栏容器 */
.sidebar_container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

/* 收起状态 */
.sidebar_container.collapsed {
    width: 0;
    min-width: 0;
    opacity: 0;
    transform: translateX(-100%);
    pointer-events: none;
}

/* 响应式设计 - 针对移动端 */
@media (max-width: 768px) {
    .sidebar_container {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1000;
        width: 260px; /* 手机端稍微窄一些 */
        height: 100vh; /* 确保全屏高度 */
        box-shadow: 2px 0 12px rgba(0,0,0,0.2);
        backdrop-filter: blur(10px);
        overflow: hidden; /* 防止整个容器滚动 */
    }

    .sidebar_container.collapsed {
        transform: translateX(-100%);
        width: 260px; /* 保持宽度，只是移出视野 */
        opacity: 0;
        pointer-events: none;
    }

    /* 手机端菜单滚动优化 */
    .user_menu {
        margin: var(--spacing-sm);
        padding: var(--spacing-md);
        max-height: calc(100vh - 200px); /* 为头部和底部留出空间 */
        overflow-y: auto;
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: thin; /* Firefox */
        scrollbar-color: rgba(255,255,255,0.3) transparent; /* Firefox */
    }

    /* 手机端滚动条样式 */
    .user_menu::-webkit-scrollbar {
        width: 4px;
    }

    .user_menu::-webkit-scrollbar-track {
        background: transparent;
    }

    .user_menu::-webkit-scrollbar-thumb {
        background: rgba(255,255,255,0.3);
        border-radius: 2px;
    }

    .user_menu::-webkit-scrollbar-thumb:hover {
        background: rgba(255,255,255,0.5);
    }
}

/* 平板端保持正常布局 */
@media (min-width: 481px) and (max-width: 768px) {
    .sidebar_container {
        position: relative;
        width: 280px;
        min-width: 280px;
    }

    .sidebar_container.collapsed {
        width: 280px;
        min-width: 280px;
        opacity: 1;
        transform: none;
        pointer-events: auto;
    }
}

.sidebar_main_content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0;
    min-height: 0; /* 允许flex子项收缩 */
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

/* 日系欢迎标题 - 深色主题 */
.welcome_header {
    text-align: center;
    padding: var(--spacing-lg) var(--spacing-md);
    margin: var(--spacing-md) var(--spacing-md) 0;
    background: linear-gradient(135deg, rgba(52, 73, 94, 0.9) 0%, rgba(44, 62, 80, 0.8) 100%);
    border-radius: var(--radius-medium);
    box-shadow: var(--shadow-medium);
    border: var(--border-width) solid var(--sidebar-border-color);
    position: relative;
    overflow: hidden;
}

.welcome_header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: var(--border-width-thick);
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--accent-color) 100%);
}

.welcome_title {
    font-size: calc(var(--font-size-lg) + 2px);
    font-weight: 600;
    color: var(--text-sidebar);
    margin: 0 0 var(--spacing-xs) 0;
    letter-spacing: var(--border-width);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.welcome_subtitle {
    font-size: var(--font-size-xs);
    color: var(--text-sidebar-secondary);
    margin: 0;
    font-weight: 400;
    letter-spacing: 0.5px;
    opacity: 0.9;
}

/* 日系用户信息卡片 - 深色主题 */
.user_message{
    background: linear-gradient(135deg, rgba(52, 73, 94, 0.8) 0%, rgba(44, 62, 80, 0.9) 100%);
    margin: var(--spacing-md) var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-medium);
    box-shadow: var(--shadow-medium);
    border: var(--border-width) solid var(--sidebar-border-color);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(var(--spacing-sm));
}

/* 添加微妙的装饰元素 */
.user_message::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: var(--border-width-thick);
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
    border-radius: var(--radius-large) var(--radius-large) 0 0;
}

/* 抽屉模式下的样式调整 */
.menu-drawer .user_message {
    margin: var(--spacing-sm) var(--spacing-sm);
    box-shadow: none;
    border: none;
    background-color: transparent;
}

.menu-drawer .user_menu {
    margin: var(--spacing-sm) var(--spacing-sm);
    box-shadow: none;
    border: none;
    background-color: transparent;
}

.user_message:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    transform: translateY(-2px) scale(1.01);
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.2) 0%, rgba(44, 62, 80, 0.9) 100%);
}

.user_info {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-sidebar);
    padding: var(--spacing-xs) 0;
    border-radius: var(--radius-small);
    transition: all 0.3s ease;
}

.user_info:last-child {
    margin-bottom: 0;
}

.user_info:hover {
    background-color: rgba(52, 152, 219, 0.15);
    padding-left: var(--spacing-xs);
}

.user_info_icon {
    margin-right: var(--spacing-sm);
    flex-shrink: 0;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user_info:hover .user_info_icon {
    transform: scale(1.1);
}

.user_info_title {
    flex: 1;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    letter-spacing: calc(var(--border-width) * 0.3);
}

/* 日系菜单容器 - 深色主题，可独立滚动 */
.user_menu{
    background: linear-gradient(135deg, rgba(52, 73, 94, 0.7) 0%, rgba(44, 62, 80, 0.8) 100%);
    margin: var(--spacing-md) var(--spacing-md);
    padding: var(--spacing-lg);
    border-radius: var(--radius-medium);
    box-shadow: var(--shadow-medium);
    border: var(--border-width) solid var(--sidebar-border-color);
    flex: 1;
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
    backdrop-filter: blur(var(--spacing-sm));
    min-height: 0; /* 允许flex子项收缩 */
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

/* 菜单容器装饰 */
.user_menu::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: calc(var(--border-width) * 2);
    background: linear-gradient(90deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    opacity: 0.8;
    border-radius: var(--radius-medium) var(--radius-medium) 0 0;
}

/* 日系分隔线 */
.divider_section {
    display: flex;
    align-items: center;
    margin: var(--spacing-lg) var(--spacing-md);
    gap: var(--spacing-md);
}

.divider_line {
    flex: 1;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--primary-color) 50%, transparent 100%);
    opacity: 0.3;
}

.divider_text {
    font-size: 12px;
    color: var(--text-sidebar);
    font-weight: 500;
    letter-spacing: 1px;
    padding: var(--spacing-xs) 0;
    /* 移除背景、边框和圆角 */
}

/* 独立语言切换区域 - 深色主题 */
.language_section {
    margin: var(--spacing-md) var(--spacing-md);
    flex-shrink: 0;
}

.language_switcher_standalone {
    padding: var(--spacing-md);
    background: linear-gradient(135deg, rgba(52, 73, 94, 0.7) 0%, rgba(44, 62, 80, 0.8) 100%);
    border-radius: var(--radius-medium);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid var(--sidebar-border-color);
    backdrop-filter: blur(10px);
}

/* 保留原有的语言切换器容器样式（如果其他地方还在使用） */
.language_switcher_container {
    padding: var(--spacing-md);
    border-top: 1px solid var(--sidebar-border-color);
    margin-top: var(--spacing-md);
    background: linear-gradient(135deg, rgba(52, 73, 94, 0.4) 0%, rgba(44, 62, 80, 0.6) 100%);
    border-radius: var(--radius-medium);
}

.language_label {
    font-size: 11px;
    color: var(--text-sidebar-secondary);
    margin-bottom: var(--spacing-xs);
    text-align: center;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.user_menu_tab_ul{
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: var(--spacing-sm) 0;
    margin-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--divider-color);
    list-style: none;
    margin: 0 0 var(--spacing-md) 0;
}

.user_menu_tab_ul li{
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-sm);
    border-radius: var(--radius-medium);
    transition: all 0.3s ease;
    cursor: pointer;
}

.user_menu_tab_ul li:hover{
    background-color: rgba(52, 152, 219, 0.15);
    transform: translateY(-2px);
}

.user_menu_tab_ul li img{
    transition: all 0.3s ease;
}

.user_menu_list_ul{
    list-style: none;
    margin: 0;
    padding: 0;
    /* 移除高度限制，让菜单自然展开 */
}

.user_menu_list_ul li{
    margin: var(--spacing-sm) 0;
    padding: var(--spacing-md);
    border-radius: var(--radius-small);
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 15px; /* 恢复合适的字体大小 */
    line-height: 1.5;
}

.user_menu_list_ul li:hover{
    background-color: rgba(52, 152, 219, 0.15);
}

.user_menu_list_ul li img{
    float: right;
    width: 18px;
    height: 18px;
    transition: all 0.3s ease;
}

.menu_item_not_selected{
    color: var(--text-sidebar);
}

.menu_item_selected_blue{
    color: #5dade2;
    font-weight: 600;
}

.menu_item_selected_yellow{
    color: #f1c40f;
    font-weight: 600;
}

.menu_item_selected_orange{
    color: #e67e22;
    font-weight: 600;
}

.menu_item_selected_yellow_green{
    color: #2ecc71;
    font-weight: 600;
}

.menu_item_selected_green{
    color: #27ae60;
    font-weight: 600;
}

.menu_item_selected_red{
    color: #e74c3c;
    font-weight: 600;
}

.menu_item_selected_light_purple{
    color: #9b59b6;
    font-weight: 600;
}

.menu_item_selected_purple{
    color: #8e44ad;
    font-weight: 600;
}

.dynamic{
    background: linear-gradient(135deg, rgba(52, 73, 94, 0.6) 0%, rgba(44, 62, 80, 0.7) 100%);
    margin: var(--spacing-md) var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-medium);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid var(--sidebar-border-color);
    min-height: 80px; /* 进一步减少动态区域高度 */
    max-height: 120px; /* 限制最大高度 */
    flex: 1;
    overflow-y: auto; /* 内容过多时滚动 */
    backdrop-filter: blur(10px);
}

.dynamic_title{
    font-size: 16px;
    font-weight: 600;
    color: var(--text-sidebar);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--sidebar-border-color);
}

.dynamic_content{
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--radius-small);
    font-size: 14px;
    color: var(--text-sidebar);
    transition: all 0.3s ease;
    cursor: pointer;
}

.dynamic_content:hover{
    background-color: rgba(52, 152, 219, 0.15);
}

/* 日系按钮容器 - 固定在底部，深色主题 */
.user_btn {
    margin: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    flex-shrink: 0;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--sidebar-border-color);
}

/* 日系按钮样式 - 深色主题 */
.user_btn button{
    font-size: 13px;
    height: 40px;
    width: 100%;
    border-radius: var(--radius-medium);
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    letter-spacing: 0.3px;
    position: relative;
    overflow: hidden;

    /* 日系深色渐变 */
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-light) 100%);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* 按钮发光效果 */
.user_btn button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.user_btn button:hover::before {
    left: 100%;
}

.user_btn button:hover{
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color-dark) 100%);
}

.user_btn button:active{
    transform: translateY(-1px) scale(0.98);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.ant-tabs-ink-bar {
    visibility: hidden;
}

.dot {
    position: absolute;
    top: var(--spacing-xs);
    right: var(--spacing-xs);
    width: 8px;
    height: 8px;
    background-color: #ff4757;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: var(--shadow-light);
}

.logOff {
    margin-top: var(--spacing-xl) !important;
    background: linear-gradient(135deg, var(--accent-color) 0%, #c0392b 100%) !important;
}

.logOff:hover {
    background: linear-gradient(135deg, #c0392b 0%, var(--accent-color) 100%) !important;
}

.null {
    /* 保留用于特殊情况 */
}