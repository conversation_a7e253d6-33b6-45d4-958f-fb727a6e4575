// 批量处理加班审批页面多语言化的脚本
// 这个脚本用于记录需要替换的内容模式

const replacements = [
  // 查询相关
  {
    old: '关键字查询：',
    new: '{tApproval(\'buttons.keywordSearch\', \'关键字查询：\')}'
  },
  {
    old: '开始日期查询：',
    new: '{tApproval(\'buttons.startDateSearch\', \'开始日期查询：\')}'
  },
  
  // 批量操作
  {
    old: '已选中{data.overtimeKeyList.length}个',
    new: '{tApproval(\'messages.selected\', \'已选中{count}个\').replace(\'{count}\', data.overtimeKeyList.length.toString())}'
  },
  {
    old: '批量同意',
    new: '{tApproval(\'buttons.batchApprove\', \'批量同意\')}'
  },
  {
    old: '批量驳回',
    new: '{tApproval(\'buttons.batchReject\', \'批量驳回\')}'
  },
  
  // 表格头部
  {
    old: '工号/姓名',
    new: '{tApproval(\'tableHeaders.employeeIdName\', \'工号/姓名\')}'
  },
  {
    old: '开始时间',
    new: '{tApproval(\'tableHeaders.startTime\', \'开始时间\')}'
  },
  {
    old: '结束时间',
    new: '{tApproval(\'tableHeaders.endTime\', \'结束时间\')}'
  },
  {
    old: '预订时长',
    new: '{tApproval(\'tableHeaders.duration\', \'预订时长\')}'
  },
  {
    old: '原因',
    new: '{tApproval(\'tableHeaders.reason\', \'原因\')}'
  },
  {
    old: '状态',
    new: '{tApproval(\'tableHeaders.status\', \'状态\')}'
  },
  {
    old: '操作',
    new: '{tApproval(\'tableHeaders.actions\', \'操作\')}'
  },
  
  // 状态显示
  {
    old: '等待上级批准',
    new: '{tApproval(\'status.waitingApproval\', \'等待上级批准\')}'
  },
  {
    old: '已同意',
    new: '{tApproval(\'status.approved\', \'已同意\')}'
  },
  {
    old: '已驳回',
    new: '{tApproval(\'status.rejected\', \'已驳回\')}'
  },
  {
    old: '已失效',
    new: '{tApproval(\'status.expired\', \'已失效\')}'
  },
  
  // 操作按钮
  {
    old: '同意',
    new: '{tApproval(\'buttons.approve\', \'同意\')}'
  },
  {
    old: '驳回',
    new: '{tApproval(\'buttons.reject\', \'驳回\')}'
  },
  
  // 工具提示
  {
    old: '审批流程详情',
    new: '{tApproval(\'tooltips.approvalProcessDetail\', \'审批流程详情\')}'
  },
  
  // 标签页
  {
    old: 'case "待审批":',
    new: 'case tApproval(\'tabs.pending\', \'待审批\'):'
  },
  {
    old: 'case "已同意":',
    new: 'case tApproval(\'tabs.approved\', \'已同意\'):'
  },
  {
    old: 'case "已驳回":',
    new: 'case tApproval(\'tabs.rejected\', \'已驳回\'):'
  }
];

// 导出替换规则
module.exports = replacements;
