import { createSlice, PayloadAction, } from "@reduxjs/toolkit";
import { RootState } from "../store";
import ApiFetchVars from "../api/common/fetch-api-vars";
import dayjs from 'dayjs';

export type SingleValueType = (string | number)[];
export type ValueType = SingleValueType | SingleValueType[];
// 格式化日期为YYYY/mm/DD
function formatDate(date: dayjs.Dayjs): string {
    const year = date.year();
    const month = (date.month() + 1)<10?'0'+(date.month() + 1):(date.month() + 1);
    const day = date.date()<10?'0'+date.date():date.date();
    return `${year}/${month}/${day}`;
}
  
// 计算结束日期减去开始日期的时间
function subtractTime(type:string, restTime:number, startDate:string, endDate:string, startTime: number, endTime: number, day_type: any): number[] {
    const [startYear, startMonth, startDay] = startDate.split('/');
    const [endYear, endMonth, endDay] = endDate.split('/');
    //获取开始时间的属性
    let startType: number = 0;
    //获取结束时间的属性
    let endType: number = 0;
    //获取开始时间到结束时间之间的工作日
    let workDaySum: number = 0;
    let diff:number = 0;
    let relaxTime = 0;
    let workTime = 0;
    let illnessTime = 0;
    let pregnantTime = 0;
    var numlist:number[] = [0,0,0,0];

    day_type.map((item:any)=>{
        if(item.date.replaceAll('-','/').slice(0,10) == startDate){
            startType = item.type;
        }
        if(item.date.replaceAll('-','/').slice(0,10) == endDate){
            endType = item.type;
        }
        const date = new Date(item.date.replaceAll('T',' '));
        const start = new Date(startDate.replaceAll('/','-') + ' 00:00:00');
        const end = new Date(endDate.replaceAll('/','-') + ' 00:00:00');
        if(date > start && date < end && (item.type == 0 || item.type == 4)){
            workDaySum += 1;
        }
    })

    const hourDiff:number = workDaySum * 8;
    let hourPartDiff: number = 0;

    if(startDate == endDate){
        if(startType == 0 || startType == 4){
            hourPartDiff = endTime - startTime;
            if(startTime<=12&&endTime>=13){
                hourPartDiff = hourPartDiff - 1;
            }
            diff = hourDiff + hourPartDiff;
        }else{
            diff = 0;
        }
    }else{
        if((startType != 0 && startType != 4) && (endType != 0 && endType != 4)){
            hourPartDiff = 0
        }else if((startType != 0 && startType != 4) && (endType == 0 || endType == 4)){
            hourPartDiff = endTime - 9
            if(endTime>=13){
                hourPartDiff = hourPartDiff - 1;
            }else{
                hourPartDiff = hourPartDiff;
            }
        }else if((startType == 0 || endType == 4) && (endType != 0 && endType != 4)){
            hourPartDiff = 18 - startTime
            if(startTime<=12){
                hourPartDiff = hourPartDiff - 1;
            }else{
                hourPartDiff = hourPartDiff;
            }
        }else if((startType == 0 || endType == 4) && (endType == 0 || endType == 4)){
            hourPartDiff = 18 - startTime + endTime - 9;
            if(startTime<=12&&endTime>=13){
                hourPartDiff = hourPartDiff - 2;
            }else if(startTime<=12&&endTime<13){
                hourPartDiff = hourPartDiff - 1;
            }else if(startTime>12&&endTime<13){
                hourPartDiff = hourPartDiff;
            }else if(startTime>12&&endTime>=13){
                hourPartDiff = hourPartDiff - 1;
            }
        }
        diff = hourDiff + hourPartDiff;
    }
    // if(type=='调休/事假'){
    //     if(startDate == endDate){
    //         if(startType == 0 || startType == 4){
    //             if(startTime==9){
    //                 relaxTime = endTime<14 ? 0 : (endTime==14) ? 4 : (endTime<18) ? 4 : (endTime==18) ? 8 : 0;
    //                 relaxTime = relaxTime<=restTime ? relaxTime : (restTime - (restTime % 4));
    //                 workTime = diff - relaxTime;
    //             }
    //             if(startTime>9 && startTime<=14){
    //                 relaxTime = endTime == 18 ? 4 : 0;
    //                 relaxTime = relaxTime<=restTime ? relaxTime : (restTime - (restTime % 4));
    //                 workTime = diff - relaxTime;
    //             }
    //             if(startTime>14){
    //                 relaxTime = 0;
    //                 workTime = diff;
    //             }
    //         }else{
    //             relaxTime = 0;
    //             workTime = 0;
    //         }
    //     }
    //     if(startDate != endDate){
    //         if((startType == 0 || startType == 4)&&(endType == 0 || endType == 4)){
    //             if(startTime>14&&endTime<14){
    //                 relaxTime = hourDiff;
    //                 relaxTime = relaxTime<=restTime ? relaxTime : (restTime - (restTime % 4));
    //                 workTime = diff - relaxTime;
    //             }
    //             if(startTime>14&&endTime>=14){
    //                 relaxTime = hourDiff + 4;
    //                 relaxTime = relaxTime<=restTime ? relaxTime : (restTime - (restTime % 4));
    //                 workTime = diff - relaxTime;
    //             }
    //             if(startTime<=14&&endTime<14){
    //                 if (startTime != 9){
    //                     relaxTime = hourDiff + 4;
    //                 }else{
    //                     relaxTime = hourDiff + 8;
    //                 }
    //                 relaxTime = relaxTime<=restTime ? relaxTime : (restTime - (restTime % 4));
    //                 workTime = diff - relaxTime;
    //             }
    //             if(startTime<=14&&endTime>=14){
    //                 if(startTime == 9 && endTime == 18){
    //                     relaxTime = hourDiff + 16;
    //                     relaxTime = relaxTime<=restTime ? relaxTime : (restTime - (restTime % 4));
    //                     workTime = diff - relaxTime;
    //                 }else if(startTime == 9 && endTime != 18){
    //                     relaxTime = hourDiff + 12;
    //                     relaxTime = relaxTime<=restTime ? relaxTime : (restTime - (restTime % 4));
    //                     workTime = diff - relaxTime;
    //                 }else if(startTime != 9 && endTime == 18){
    //                     relaxTime = hourDiff + 12;
    //                     relaxTime = relaxTime<=restTime ? relaxTime : (restTime - (restTime % 4));
    //                     workTime = diff - relaxTime;
    //                 }else if(startTime != 9 && endTime != 18){
    //                     relaxTime = hourDiff + 8;
    //                     relaxTime = relaxTime<=restTime ? relaxTime : (restTime - (restTime % 4));
    //                     workTime = diff - relaxTime;
    //                 }
    //             }
    //         }else if((startType != 0 && startType != 4)&&(endType == 0 || endType == 4)){
    //             if(endTime<14){
    //                 relaxTime = hourDiff;
    //                 relaxTime = relaxTime<=restTime ? relaxTime : (restTime - (restTime % 4));
    //                 workTime = diff - relaxTime;
    //             }else{
    //                 if(endTime != 18){
    //                     relaxTime = hourDiff + 4;
    //                     relaxTime = relaxTime<=restTime ? relaxTime : (restTime - (restTime % 4));
    //                     workTime = diff - relaxTime;
    //                 }else if(endTime == 18){
    //                     relaxTime = hourDiff + 8;
    //                     relaxTime = relaxTime<=restTime ? relaxTime : (restTime - (restTime % 4));
    //                     workTime = diff - relaxTime;
    //                 }
    //             }
    //         }else if((startType == 0 || startType == 4)&&(endType != 0 && endType != 4)){
    //             if(startTime>=14){
    //                 relaxTime = hourDiff;
    //                 relaxTime = relaxTime<=restTime ? relaxTime : (restTime - (restTime % 4));
    //                 workTime = diff - relaxTime;
    //             }else{
    //                 if(startTime != 9){
    //                     relaxTime = hourDiff + 4;
    //                     relaxTime = relaxTime<=restTime ? relaxTime : (restTime - (restTime % 4));
    //                     workTime = diff - relaxTime;
    //                 }else if(startTime == 9){
    //                     relaxTime = hourDiff + 8;
    //                     relaxTime = relaxTime<=restTime ? relaxTime : (restTime - (restTime % 4));
    //                     workTime = diff - relaxTime;
    //                 }
    //             }
    //         }else{
    //             relaxTime = hourDiff;
    //             relaxTime = relaxTime<=restTime ? relaxTime : (restTime - (restTime % 4));
    //             workTime = diff - relaxTime;
    //         }
    //     }
    // }
    if(type=='调休'){
        workTime = 0;
        relaxTime = diff;
    }
    if(type=='事假'){
        workTime = diff;
        relaxTime = 0;
    }
    if(type=='病假'){
        illnessTime = diff;
    }
    if(type=='其他'){
        if(startDate == endDate&&(startType == 0 || startType == 4)){
            pregnantTime = 1;
        }else{
            pregnantTime = workDaySum;
            if(startType == 0 || startType == 4){
                pregnantTime = pregnantTime + 1;
            }
            if(endType == 0 || endType == 4){
                pregnantTime = pregnantTime + 1;
            }
        }
    }
    numlist[0] = relaxTime; 
    numlist[1] = workTime;
    numlist[2] = illnessTime;
    numlist[3] = pregnantTime;
    return numlist;
}
function getWeekday(date: Date): string {
    const weekdays = ['(日)', '(一)', '(二)', '(三)', '(四)', '(五)', '(六)'];
    return weekdays[date.getDay()];
    }

// 计算结束日期减去开始日期的时间
function overtimeSubtractTime(startDay: dayjs.Dayjs, endDay: dayjs.Dayjs, startHour: number, endHour: number, startMinute: number, endMinute: number, workdays: string[], weekdays: string[], holidays: string[]): (number[]) {
    //当起始日和结束日都是同一天时
    let weekendTime = 0;
    let workDayTime = 0;
    let holidayTime = 0;
    var numlist:number[] = [0,0,0];
    if(startDay <= endDay){
        if(startDay.toDate().getDay() == endDay.toDate().getDay()){
            if(!isHoliday(startDay.toDate(), holidays)){
            if(isWeekend(startDay.toDate(), weekdays)){
                    const point = (endMinute - startMinute)/60;
                    weekendTime = endHour - startHour + point;
                    if(startHour<=12&&endHour>=13){
                        weekendTime = weekendTime-1;
                    }
            }
            if(isWorkday(startDay.toDate(), workdays)){
                    const point = (endMinute - startMinute)/60;
                    workDayTime = endHour - startHour + point;
                    if(startHour<=12&&endHour>=13){
                        weekendTime = weekendTime-1;
                    }
                } 
            }else{
                const point = (endMinute - startMinute)/60;
                holidayTime = endHour - startHour + point;
            }
        }

        //当起始日和结束日不同时
        if(startDay.toDate().getDay() != endDay.toDate().getDay()){
            if(!isHoliday(startDay.toDate(), holidays)&&!isHoliday(endDay.toDate(), holidays)){
                if(isWeekend(startDay.toDate(), weekdays)){
                    if(isWeekend(endDay.toDate(), weekdays)){
                        weekendTime = (24 - (startHour + startMinute/60)) + (endHour + endMinute/60);
                    }
                    if(isWorkday(endDay.toDate(), workdays)){
                        weekendTime = 24 - (startHour + startMinute/60);
                        workDayTime = endHour + endMinute/60;
                    }
                }
                if(isWorkday(startDay.toDate(), workdays)){
                    if(isWeekend(endDay.toDate(), weekdays)){
                        workDayTime = 24 - (startHour + startMinute/60);
                        weekendTime = endHour + endMinute/60;
                    }
                    if(isWorkday(endDay.toDate(), workdays)){
                        workDayTime = (24 - (startHour + startMinute/60)) + (endHour + endMinute/60);
                    }
                }
            }else if(isHoliday(startDay.toDate(), holidays)&&!isHoliday(endDay.toDate(), holidays)){
                if(isWeekend(endDay.toDate(), weekdays)){
                    holidayTime = 24 - (startHour + startMinute/60);
                    weekendTime = endHour + endMinute/60;
                }
                if(isWorkday(endDay.toDate(), workdays)){
                    holidayTime = 24 - (startHour + startMinute/60);
                    workDayTime = endHour + endMinute/60;
                }
            }else if(!isHoliday(startDay.toDate(), holidays)&&isHoliday(endDay.toDate(), holidays)){
                if(isWeekend(startDay.toDate(), weekdays)){
                    weekendTime = 24 - (startHour + startMinute/60);
                    holidayTime = endHour + endMinute/60;
                }
                if(isWorkday(startDay.toDate(), workdays)){
                    workDayTime = 24 - (startHour + startMinute/60);
                    holidayTime = endHour + endMinute/60;
                }
            }else if(isHoliday(startDay.toDate(), holidays)&&isHoliday(endDay.toDate(), holidays)){
                holidayTime = (24 - (startHour + startMinute/60)) + (endHour + endMinute/60);
            }
        }
    }
    
    numlist[0] = Math.floor(weekendTime * 2) / 2; 
    numlist[1] = Math.floor(workDayTime * 2) / 2;
    numlist[2] = Math.floor(holidayTime * 2) / 2;
    return numlist;
  }


  //判断当前日期是否是双休
  function isWeekend(date: Date, weekdays: string[]): boolean {
    const day = dayjs(date).format('YYYY-MM-DD');
    const isday = weekdays.includes(day);
    return isday;
    // const day = date.getDay();
    // return day === 0 || day === 6;
  }
  
  function isWorkday(date: Date, workdays: string[]): boolean {
    const day = dayjs(date).format('YYYY-MM-DD');
    const isday = workdays.includes(day);
    return isday;
  }

  function isHoliday(date: Date, holidays: string[]): boolean {
    const day = dayjs(date).format('YYYY-MM-DD');
    const isHoliday = holidays.includes(day);
    return isHoliday;
  }

//   //项目审批人
//   const GroupList = [
//     {
//       value: 'DA04',
//       label: 'DA04',
//       children: [
//         {
//           value: 'JS0020刘文丰',
//           label: 'JS0020刘文丰',
//         },
//         {
//           value: 'JS0199欧金龙',
//           label: 'JS0199欧金龙',
//         },
//         {
//           value: 'JS0298聂建军',
//           label: 'JS0298聂建军',
//         },
//         {
//           value: 'JS0611袁娟',
//           label: 'JS0611袁娟',
//         }
//       ],
//     },
//     {
//       value: 'DA05',
//       label: 'DA05',
//       children: [
//         {
//           value: 'JS0100张三',
//           label: 'JS0100张三',
//         },
//         {
//           value: 'JS0101李四',
//           label: 'JS0101李四',
//         },
//         {
//           value: 'JS0102王五',
//           label: 'JS0102王五',
//         },
//         {
//           value: 'JS0103赵六',
//           label: 'JS0103赵六',
//         },
//       ],
//     },
//     {
//       value: 'DA06',
//       label: 'DA06',
//       children: [
//         {
//             value: 'JS0200张三',
//             label: 'JS0200张三',
//         },
//         {
//             value: 'JS0201李四',
//             label: 'JS0201李四',
//         },
//         {
//             value: 'JS0202王五',
//             label: 'JS0202王五',
//         },
//         {
//             value: 'JS0203赵六',
//             label: 'JS0203赵六',
//         },
//       ],
//     },
//   ];

//   //部门审批人
//   const developmentList = [{
//     value: 'DA04',
//     label: 'DA04',
//     children: [
//       {
//         value: 'JS0020刘文丰',
//         label: 'JS0020刘文丰',
//       },
//       {
//         value: 'JS0199欧金龙',
//         label: 'JS0199欧金龙',
//       },
//       {
//         value: 'JS0298聂建军',
//         label: 'JS0298聂建军',
//       },
//       {
//         value: 'JS0611袁娟',
//         label: 'JS0611袁娟',
//       }
//     ],
//   },
//   {
//     value: 'DA05',
//     label: 'DA05',
//     children: [
//       {
//         value: 'JS0100张三',
//         label: 'JS0100张三',
//       },
//       {
//         value: 'JS0101李四',
//         label: 'JS0101李四',
//       },
//       {
//         value: 'JS0102王五',
//         label: 'JS0102王五',
//       },
//       {
//         value: 'JS0103赵六',
//         label: 'JS0103赵六',
//       },
//     ],
//   },
//   {
//     value: 'DA06',
//     label: 'DA06',
//     children: [
//       {
//           value: 'JS0200张三',
//           label: 'JS0200张三',
//       },
//       {
//           value: 'JS0201李四',
//           label: 'JS0201李四',
//       },
//       {
//           value: 'JS0202王五',
//           label: 'JS0202王五',
//       },
//       {
//           value: 'JS0203赵六',
//           label: 'JS0203赵六',
//       },
//     ],
//   },
// ];

const userMessage = '张三-JS0000';
interface ListType{
    key: number;
    user: string;
    applicant: string;
    startDate: dayjs.Dayjs;
    endDate: dayjs.Dayjs;
    time: string;
    reason: string;
    state: string;
  };
  interface businessTripListType{
    key: number;
    user: string;
    applicant: string;
    startDate: string;
    endDate: string;
    reason: string;
    state: string;
  };
  interface overtimeListType{
    key: number;
    user: string;
    applicant: string;
    startDate: string;
    endDate: string;
    workDayTime: number,
    weekendTime: number,
    specialDayTime: number,
    reason: string;
    state: string;
  };
  interface confirmationListType{
    key: number;
    user: string;
    applicant: string;
    startDate: string;
    timeOriginal: string;
    timeChange: string;
    reason: string;
    state: string;
  };
interface ICascaderItem {
id: number;
}
// 请假申请
export interface leaveApplicationState {
    member: {
        user_id: any,
        name: any,
    }[],
    checkDay: any,
    //api发送过来的调休时长
    compensatory_init: any,
    //剩余调休时长
    compensatory_time: any,
    //每一页的数据量
    pageSize: number,
    //申请表的内容
    listValues: ListType,
    //请假的类型
    typeValues: string,
    //时间的处理
    startDay: dayjs.Dayjs,
    startHour: number,
    endDay: dayjs.Dayjs,
    endDayView: dayjs.Dayjs,
    subDay: number,
    endHour: number,
    relaxTime: number,
    workTime: number,
    illnessTime: number,
    pregnantTime: number,
    //项目审批的选择栏
    cascaderCount: number,
    cascaderList: ICascaderItem[],
    cascaderContent: ValueType[],
    //项目审批人
    options: {
        value: string;
        label: string;
        children: {
            value: string;
            label: string;
        }[];
    }[];
    //项目选择的所属部门
    optionChooseDev: {
        key: number;
        value: string[];
    }[];
    //项目审批人的选择
    selectedDeparts:any[],
    departList: any[],
    departListC: any[],
    //部门审批人
    depart_leader: {
        user_id: any,
        name: any,
        role_id: any,
        depart_id: any
    }[],
    depart_leaderE: {
        user_id: any,
        name: any,
        role_id: any,
        depart_id: any
    }[],
    commitAdd: {
        use_type: any,
        user_id: any,
        agent_user_id: any,
        leave_type: any,
        reason: any,
        start_time: any,
        end_time: any,
        workflow_list: any,
    },
    //部门审批人总表
    developments:  {
        value: string;
        label: string;
        children: {
            value: string;
            label: string;
        }[];
    }[];
    //当前用户的行政部门审批人
    developments_leaders: {}[];
    //部门审批人筛选表
    developmentsChoose:  {
        value: string;
        label: string;
        children: {
            value: string;
            label: string;
        }[];
    }[];
    //申请表的信息
    datasource: {
        code: any,
        user_id: any,
        name: any,
        agent_user_id: any,
        agent_name: any,
        start_time: any,
        end_time: any,
        //事假
        normal_hours: any,
        //调休
        compensatory_hours: any,
        //病假
        sick_hours: any,
        //其他
        other_days: any,
        //其他类型
        other_apply_type: any,
        reason: any,
        workflow_result: any,
        reject_reason: any,
    }[],
    currentData: {
        code: any,
        user_id: any,
        name: any,
        agent_user_id: any,
        agent_name: any,
        start_time: any,
        end_time: any,
        //事假
        normal_hours: any,
        //调休
        compensatory_hours: any,
        //病假
        sick_hours: any,
        //其他
        other_days: any,
        //其他类型
        other_apply_type: any,
        reason: any,
        workflow_result: any,
        reject_reason: any,
    }[],
    changeAsk: {
        use_type: any,
        code: any,
        user_id: any,
        agent_user_id: any,
        leave_type: any,
        start_time: any,
        end_time: any,
        t_hours: any,
        s_hours: any,
        hours: any,
        t_days: any,
        // workflow_list: any
    },
    changeTime: {
        c_ctime: any,
        c_wtime: any,
        w_ctime: any,
        w_wtime: any
    },
    realTime: {
        date: any,
        work_start_time: any,
        work_end_time: any,
    }[],
    realTimeSample: {
        date: any,
        work_start_time: any,
        work_end_time: any,
        differ_time_all: any
    },
    abnormal_code: {
        date: any,
        abnormal_code: any,
        differ_time: any
    }[],
    day_type: {
        date: any,
        type: any
    }[],
    currentPage: number,
    workflow_list: any,
    confirmText: string,
    //判断当前申请栏是否显示
    show: boolean,
    //判断当前应该是默认当前用户还是更改为代申请的下拉框
    showSelect: boolean,
    //判断当前新建申请按钮是否显示
    showNew: boolean,
    //确定当前点击删除的申请栏位置和申请表的位置对应
    selectedRowKeys: number[],
    //确定当前要添加的行的位置
    selectedRowKey: number,
    //确定申请进度是否显示
    showInput: boolean,
    //撤回栏是否显示
    isConfirmed: boolean,
    //申请修改请假时间的弹窗是否显示
    isChangeShow: boolean,
    //调休限制天数
    applyLimitDay: number,
    //系统小时数
    applyLimitPastTime: number,
    //有休关联单位
    annualLeaveUnit: string,
  }
  //出差申请
  export interface businessTripApplicationState {
    //申请表的内容
    listValues: businessTripListType,
    checkDay: any,
    //每一页的数据量
    pageSize: number,
    //请假的类型
    typeValues: string,
    //时间的处理
    startDay: dayjs.Dayjs,
    endDay: dayjs.Dayjs,
    //项目审批的选择栏
    cascaderCount: number,
    cascaderList: ICascaderItem[],
    cascaderContent: ValueType[],
    //项目审批人
    options: {
        value: string;
        label: string;
        children: {
            value: string;
            label: string;
        }[];
    }[];
    //项目选择的所属部门
    optionChooseDev: {
        key: number;
        value: string[];
    }[];
    //项目审批人的选择
    selectedDeparts:any[],
    departList: any[],
    departListC: any[],
    //部门审批人
    depart_leader: {
        user_id: any,
        name: any,
        role_id: any,
        depart_id: any
    }[],
    depart_leaderE: {
        user_id: any,
        name: any,
        role_id: any,
        depart_id: any
    }[],
    //当前用户的行政部门审批人
    developments_leaders: {}[];
    commitAdd: {
        use_type: any,
        user_id: any,
        agent_user_id: any,
        location: any,
        reason: any,
        start_time: any,
        end_time: any,
        workflow_list: any,
    }
    //请求修改出差申请
    changeAsk: {
        use_type: any
        code: any
        user_id: any
        agent_user_id: any
        start_time: any
        end_time: any
    }
    //申请表的信息
    datasource: {
        code: any,
        user_id: any,
        name: any,
        agent_user_id: any,
        agent_name: any,
        start_time: any,
        end_time: any,
        location: any,
        t_days: any,
        reason: any,
        workflow_result: any,
        reject_reason: any,
    }[],
    currentData: {
        code: any,
        user_id: any,
        name: any,
        agent_user_id: any,
        agent_name: any,
        start_time: any,
        end_time: any,
        location: any,
        t_days: any,
        reason: any,
        workflow_result: any,
        reject_reason: any,
    }[],
    day_type: {
        date: any,
        type: any
    }[],
    currentPage: number,
    workflow_list: any,
    confirmText: string,
    //判断当前申请栏是否显示
    show: boolean,
    //判断当前应该是默认当前用户还是更改为代申请的下拉框
    showSelect: boolean,
    //判断当前新建申请按钮是否显示
    showNew: boolean,
    //确定当前点击删除的申请栏位置和申请表的位置对应
    selectedRowKeys: number[],
    //确定当前要添加的行的位置
    selectedRowKey: number,
    //确定申请进度是否显示
    showInput: boolean,
    isConfirmed: boolean,
    //申请修改出差时间的弹窗是否显示
    isChangeShow: boolean,

  }  
  //加班申请
  export interface overtimeApplicationState {
    status: string,
    //每一页的数据量
    pageSize: number,
    checkDay: any,
    member: {
        user_id: any,
        name: any,
    }[],
    //申请表的内容
    listValues: overtimeListType,
    //工作日加班时间
    workDayTime: number,
    //周末加班时间
    weekendTime: number,
    //法定节假日加班时间
    specialDayTime: number,
    //时间的处理
    startDay: dayjs.Dayjs,
    startHour: number,
    startMinute: number,
    endDay: dayjs.Dayjs,
    endDayView: dayjs.Dayjs,
    endHour: number,
    endMinute: number,
    subDay: number,
    //项目审批人的选择
    selectedDeparts:any[],
    departList: any[],
    departListC: any[],
    //部门审批人
    depart_leader: {
        user_id: any,
        name: any,
        role_id: any,
        depart_id: any
    }[],
    depart_leaderE: {
        user_id: any,
        name: any,
        role_id: any,
        depart_id: any
    }[],
    //项目审批的选择栏
    cascaderCount: number,
    cascaderList: ICascaderItem[],
    cascaderContent: ValueType[],
    //项目审批人
    options: {
        user_id: any,
        name: any,
        role_id: any
    }[];
    //项目选择的所属部门
    optionChooseDev: {
        key: number;
        value: string[];
    }[];
    //部门审批人总表
    developments:  {
        value: string;
        label: string;
        children: {
            value: string;
            label: string;
        }[];
    }[];
    //当前用户的行政部门审批人
    developments_leaders: {}[];
    //部门审批人筛选表
    developmentsChoose:  {
        value: string;
        label: string;
        children: {
            value: string;
            label: string;
        }[];
    }[];
    //新增加班申请的表格内容
    commitAdd: {
        use_type: any,
        user_id: any,
        agent_user_id: any,
        reason: any,
        start_time: any,
        end_time: any,
        workflow_list: any,
    }
    //申请表的信息
    datasource: {
        code: any,
        user_id: any,
        name: any,
        agent_user_id: any,
        agent_name: any,
        start_time: any,
        end_time: any,
        hours: any,
        reason: any,
        workflow_result: any,
        expense: any,
        reject_reason: any,
    }[],
    currentData: {
        code: any,
        user_id: any,
        name: any,
        agent_user_id: any,
        agent_name: any,
        start_time: any,
        end_time: any,
        hours: any,
        reason: any,
        workflow_result: any,
        expense: any,
        reject_reason: any,
    }[],
    day_type: {
        date: any,
        type: any
    }[],
    currentPage: number,
    workflow_list: any,
    confirmText: string,
    cancelCode: string,
    //判断当前申请栏是否显示
    show: boolean,
    //判断当前应该是默认当前用户还是更改为代申请的下拉框
    showSelect: boolean,
    //判断当前新建申请按钮是否显示
    showNew: boolean,
    //确定当前点击删除的申请栏位置和申请表的位置对应
    selectedRowKeys: number[],
    //确定当前要添加的行的位置
    selectedRowKey: number,
    selectedExpenseKeys: number[],
    selectedExpenseKey: number,
    //确定申请进度是否显示
    showInput: boolean,
    isConfirmed: boolean,

  }
  //上下班确认单
  export interface confirmationApplicationState {
    //申请表的内容
    listValues: confirmationListType,
    checkDay: any,
    //每一页的数据量
    pageSize: number,
    //时间的处理
    startDay: any,
    startHour: string,
    endDay: any,
    endHour: string,
    endDayView: any,
    //项目审批的选择栏
    cascaderCount: number,
    cascaderList: ICascaderItem[],
    cascaderContent: ValueType[],
    //真实打卡时间
    worktime_list: {
        day: any,
        work_start_time: any,
        work_end_time: any,
    }[],
    chooseWorkTime: {
        day: any,
        work_start_time: any,
        work_end_time: any,
    },
    //项目审批人的选择
    selectedDeparts:any[],
    departList: any[],
    departListC: any[],
    //部门审批人
    depart_leader: {
        user_id: any,
        name: any,
        role_id: any,
        depart_id: any
    }[],
    depart_leaderE: {
        user_id: any,
        name: any,
        role_id: any,
        depart_id: any
    }[],
    commitAdd: {
        use_type: any,
        user_id: any,
        agent_user_id: any,
        day: any,
        reason: any,
        work_start_time: any,
        work_end_time: any,
        real_start_time: any,
        real_end_time: any,
        workflow_list: any,
    };
    //项目审批人
    options: {
        value: string;
        label: string;
        children: {
            value: string;
            label: string;
        }[];
    }[];
    //项目选择的所属部门
    optionChooseDev: {
        key: number;
        value: string[];
    }[];
    //部门审批人总表
    developments:  {
        value: string;
        label: string;
        children: {
            value: string;
            label: string;
        }[];
    }[];
    //当前用户的行政部门审批人
    developments_leaders: {}[];
    //部门审批人筛选表
    developmentsChoose:  {
        value: string;
        label: string;
        children: {
            value: string;
            label: string;
        }[];
    }[];
    //申请表的信息
    datasource: {
        code: any,
        user_id: any,
        name: any,
        agent_user_id: any,
        agent_name: any,
        day: any,
        work_start_time: any,
        work_end_time: any,
        real_start_time: any,
        real_end_time: any,
        reason: any,
        workflow_result: any,
        reject_reason: any,
    }[],
    currentData: {
        code: any,
        user_id: any,
        name: any,
        agent_user_id: any,
        agent_name: any,
        day: any,
        work_start_time: any,
        work_end_time: any,
        real_start_time: any,
        real_end_time: any,
        reason: any,
        workflow_result: any,
        reject_reason: any,
    }[],
    day_type: {
        date: any,
        type: any
    }[],
    currentPage: number,
    workflow_list: any,
    confirmText: string,
    //判断当前申请栏是否显示
    show: boolean,
    //判断当前应该是默认当前用户还是更改为代申请的下拉框
    showSelect: boolean,
    //判断当前新建申请按钮是否显示
    showNew: boolean,
    //确定当前点击删除的申请栏位置和申请表的位置对应
    selectedRowKeys: number[],
    //确定当前要添加的行的位置
    selectedRowKey: number,
    //确定申请进度是否显示
    showInput: boolean,
    //撤回栏是否显示
    isConfirmed: boolean,

  }

// 定义要保存到Store的数据格式
export interface applicationState {
    leaveApplication: leaveApplicationState,
    businessTripApplication: businessTripApplicationState,
    overtimeApplication: overtimeApplicationState,
    confirmationApplication: confirmationApplicationState,
}
   
// 初始化数据
const leaveApplicationState = {
    listValues: {
        key: 0,
        user: userMessage,
        applicant: userMessage,
        startDate: dayjs(),
        endDate: dayjs(),
        time: '',
        reason: '',
        state: '',
    },
    checkDay: '1999-01-01 00:00:00',
    compensatory_init: 0,
    compensatory_time: 0,
    pageSize: 50,
    member:[],
    typeValues: '年次有給休暇',
    startDay: dayjs(),
    startHour: 9,
    endDay: dayjs(),
    endDayView: dayjs(),
    endHour: 18,
    //调休
    relaxTime: 0,
    //事假
    workTime: 0,
    //病假
    illnessTime: 0,
    //其他
    pregnantTime: 0,
    subDay: 0,
    cascaderCount: 1,
    cascaderList: [{ id: 1 }],
    cascaderContent: [],
    //项目审批人
    options: [],
    //项目选择的所属部门
    optionChooseDev: [],
    //项目审批人的选择
    selectedDeparts:[],
    departList: [],
    departListC: [],
    //部门审批人
    depart_leader: [],
    depart_leaderE: [],
    commitAdd: {
        use_type: 1,
        user_id: 0,
        agent_user_id: 0,
        leave_type: '年次有給休暇',
        reason: '',
        start_time: dayjs().format('YYYY-MM-DD') + ' 09:00:00',
        end_time: dayjs().format('YYYY-MM-DD') + ' 18:00:00',
        workflow_list: '',
    },
    //部门审批人筛选表
    developmentsChoose: [],
    //部门审批人
    developments: [],
    //当前用户的行政部门审批人
    developments_leaders: [],
    currentData:  [],
    changeAsk: {
        use_type: 1,
        code: '',
        user_id: 0,
        agent_user_id: 0,
        leave_type: '',
        start_time: '',
        end_time: '',
        t_hours: 0,
        s_hours: 0,
        hours: 0,
        t_days: 0,
        // workflow_list: ''
    },
    changeTime: {
        c_ctime: 0,
        c_wtime: 0,
        w_ctime: 0,
        w_wtime: 0
    },
    realTime: [],
    realTimeSample: {
        date: '',
        work_start_time: '',
        work_end_time: '',
        differ_time_all: 0,
    },
    abnormal_code: [],
    day_type: [],
    currentPage: 1,
    datasource: [],
    workflow_list: {},
    confirmText: '',
    show: false,
    showSelect: false,
    showNew: true,
    selectedRowKeys: [],
    selectedRowKey: -1,
    showInput: false,
    isConfirmed: false,
    isChangeShow: false,
    applyLimitDay: 0,
    applyLimitPastTime: 0,
    annualLeaveUnit: '整日休',
};
const businessTripApplicationState = {
    listValues: {
        key: 0,
        user: userMessage,
        applicant: userMessage,
        startDate: '',
        endDate: '',
        reason: '',
        state: '',
    },
    checkDay: '1999-01-01 00:00:00',
    pageSize: 50,
    typeValues: '日本',
    startDay: dayjs(),
    endDay: dayjs(),
    cascaderCount: 1,
    cascaderList: [{ id: 1 }],
    cascaderContent: [],
    //项目审批人
    options: [],
    //项目选择的所属部门
    optionChooseDev: [],
    //部门审批人筛选表
    // developmentsChoose: [],
    // //部门审批人
    // developments: developmentList,
    //当前用户的行政部门审批人
    developments_leaders: [],
    commitAdd: {
        use_type: 1,
        user_id: 0,
        agent_user_id: 0,
        location: '日本',
        reason: '',
        start_time: '',
        end_time: '',
        workflow_list: '',
    },
    //请求修改出差申请
    changeAsk: {
        use_type: 1,
        code: '',
        user_id: '',
        agent_user_id: 0,
        start_time: '',
        end_time: ''
    },
    //项目审批人的选择
    selectedDeparts:[],
    departList: [],
    departListC: [],
    //部门审批人
    depart_leader: [],
    depart_leaderE: [],
    datasource: [],
    currentData:  [],
    day_type: [],
    currentPage: 1,
    workflow_list: {},
    confirmText: '',
    show: false,
    showSelect: false,
    showNew: true,
    selectedRowKeys: [],
    selectedRowKey: -1,
    showInput: false,
    isConfirmed: false,
    isChangeShow: false,
};
const overtimeApplicationState = {
    status: ApiFetchVars.todo,
    member:[],
    checkDay: '1999-01-01 00:00:00',
    listValues: {
        key: 0,
        user: userMessage,
        applicant: '',
        startDate: '',
        endDate: '',
        workDayTime: 0,
        weekendTime: 0,
        specialDayTime: 0,
        reason: '',
        state: '',
    },
    pageSize: 50,
    workDayTime: 0,
    weekendTime: 0,
    specialDayTime: 0,
    startDay: dayjs(),
    startHour: (() => {
        const dayOfWeek = dayjs().day(); // 0=Sunday, 6=Saturday
        return (dayOfWeek === 0 || dayOfWeek === 6) ? 9 : 18; // 周末9点，平时18点
    })(),
    startMinute: 0,
    endDay: dayjs(),
    endDayView: dayjs(),
    endHour: (() => {
        const dayOfWeek = dayjs().day(); // 0=Sunday, 6=Saturday
        return (dayOfWeek === 0 || dayOfWeek === 6) ? 18 : 22; // 周末18点，平时22点
    })(),
    endMinute: 0,
    subDay: 0,
    //项目审批人的选择
    selectedDeparts:[],
    departList: [],
    departListC: [],
    //部门审批人
    depart_leader: [],
    depart_leaderE: [],
    cascaderCount: 1,
    cascaderList: [{ id: 1 }],
    cascaderContent: [],
    datasource: [],
    currentData:  [],
    day_type: [],
    currentPage: 1,
    workflow_list: {},
    confirmText: '',
    //项目审批人
    options: [],
    //项目选择的所属部门
    optionChooseDev: [],
    //部门审批人筛选表
    developmentsChoose: [],
    //部门审批人
    developments: [],
    //当前用户的行政部门审批人
    developments_leaders: [],
    commitAdd: {
        use_type: 1,
        user_id: 0,
        agent_user_id: 0,
        reason: '',
        start_time: '',
        end_time: '',
        workflow_list: '',
    },
    cancelCode: '',
    show: false,
    showSelect: false,
    showNew: true,
    selectedRowKeys: [],
    selectedRowKey: -1,
    selectedExpenseKeys: [],
    selectedExpenseKey: -1,
    showInput: false,
    isConfirmed: false,
};
const confirmationApplicationState = {
    listValues: {
        key: 0,
        user: userMessage,
        applicant: userMessage,
        startDate: '',
        timeOriginal: '',
        timeChange: '',
        reason: '',
        state: '',
    },
    checkDay: '1999-01-01 00:00:00',
    pageSize: 50,
    startDay: dayjs().subtract(1,'day'),
    startHour: '09:00',
    endDay: dayjs().subtract(1,'day'),
    endHour: '18:00',
    endDayView: dayjs().subtract(1,'day'),
    cascaderCount: 1,
    cascaderList: [{ id: 1 }],
    cascaderContent: [],
    //真实打卡时间
    worktime_list: [],
    chooseWorkTime: {
        day: '',
        work_start_time: '',
        work_end_time: '',
    },
    //项目审批人的选择
    selectedDeparts:[],
    departList: [],
    departListC: [],
    //部门审批人
    depart_leader: [],
    depart_leaderE: [],
    commitAdd: {
        use_type: 1,
        user_id: 0,
        agent_user_id: 0,
        day: '',
        reason: '',
        work_start_time: '',
        work_end_time: '',
        real_start_time: dayjs().subtract(1,'day').format('YYYY-MM-DD') + ' 09:00:00',
        real_end_time: dayjs().subtract(1,'day').format('YYYY-MM-DD') + ' 18:00:00',
        workflow_list: '',
    },
    //项目审批人
    options: [],
    //项目选择的所属部门
    optionChooseDev: [],
    //部门审批人筛选表
    developmentsChoose: [],
    //部门审批人
    developments: [],
    //当前用户的行政部门审批人
    developments_leaders: [],
    datasource: [],
    currentData:  [],
    day_type: [],
    currentPage: 1,
    workflow_list: {},
    confirmText: '',
    show: false,
    showSelect: false,
    showNew: true,
    selectedRowKeys: [],
    selectedRowKey: -1,
    showInput: false,
    isConfirmed: false,
};

const initialState:  applicationState = {
    leaveApplication: leaveApplicationState,
    businessTripApplication: businessTripApplicationState,
    overtimeApplication: overtimeApplicationState,
    confirmationApplication: confirmationApplicationState,
}
//定义需要的变量
export const applicationSlice = createSlice({
    name: 'application',
    initialState,
    // reducers 集合了reducer和action的功能
    // 每个case冒号之前的为action名
    // 每个case整体为reducer
    reducers: {
        // 使用PayloadAction来展示action.payload的详细内容
        setApplicationSliceInit: (state: applicationState) => {
            state.leaveApplication = leaveApplicationState;
            state.businessTripApplication = businessTripApplicationState;

            // 为加班申请重新计算智能默认时间
            const today = dayjs();
            const dayOfWeek = today.day(); // 0=Sunday, 6=Saturday
            const smartOvertimeState = {
                ...overtimeApplicationState,
                startDay: today,
                endDay: today,
                endDayView: today,
                startHour: (dayOfWeek === 0 || dayOfWeek === 6) ? 9 : 18, // 周末9点，平时18点
                endHour: (dayOfWeek === 0 || dayOfWeek === 6) ? 18 : 22, // 周末18点，平时22点
            };
            state.overtimeApplication = smartOvertimeState;

            state.confirmationApplication = confirmationApplicationState;
        },
        setLeaveDataWhenInit: (state: applicationState, action: PayloadAction<any>) => {
            if(action.payload.apply_list!=0){
                state.leaveApplication.datasource = action.payload.apply_list;
                // state.leaveApplication.datasource = state.leaveApplication.datasource.map(item => {
                //     item.start_time = item.start_time.replace('T', ' ')
                //     item.end_time = item.end_time.replace('T', ' ')
                //     return item
                // })
                state.leaveApplication.workflow_list = action.payload.workflow_list; 
                const startIndex = (state.leaveApplication.currentPage - 1) * state.leaveApplication.pageSize;
                const endIndex = startIndex + state.leaveApplication.pageSize;
                state.leaveApplication.currentData = state.leaveApplication.datasource.slice(startIndex, endIndex);
            }else{
                state.leaveApplication.datasource = [{
                    code: '',
                    user_id: '',
                    name: '',
                    agent_user_id: '',
                    agent_name: '',
                    start_time: '',
                    end_time: '',
                    compensatory_hours: '',
                    normal_hours: '',
                    sick_hours: '',
                    other_days: '',
                    other_apply_type: '',
                    reason: '',
                    reject_reason: '',
                    workflow_result: '',
                },];
            }
            state.leaveApplication.isConfirmed = false
        },
        setLeaveAddInfInit: (state: applicationState, action: PayloadAction<any>) => {
            state.leaveApplication.developments_leaders = action.payload.user_depart_leader_inf;
            // 获取十点前的调休申请限制天数
            state.leaveApplication.applyLimitPastTime = action.payload.apply_limit_past_time[0].apply_limit_past_time;
            // 获取这个行政部门的部门负责人的user_id列表
            state.leaveApplication.departList = action.payload.team_leader.map((item: any)=>{
                item.disabled = false;
                return item;
            });
            state.leaveApplication.depart_leader = action.payload.depart_leader;
            state.leaveApplication.departListC = [];
            action.payload.recently_team_leader.map((item:any)=>{
                const user_id = [item.user_id]
                state.leaveApplication.departListC = [...state.leaveApplication.departListC, user_id]
            })
            let depart = ''
            state.leaveApplication.departListC.map((item:any)=>{
                state.leaveApplication.departList.map((value:any)=>{
                    if(item[0] == value.user_id){
                        depart += "$" + value.depart_id + "$"
                    }
                })
            })
            state.leaveApplication.depart_leaderE = [];
            state.leaveApplication.depart_leader.map((item:any)=>{
                if(depart.includes("$" + item.depart_id + "$")){
                    state.leaveApplication.depart_leaderE.push(item);
                }
            })

            state.leaveApplication.developments_leaders.map((item:any)=>{
                if(!state.leaveApplication.depart_leaderE.some((object:any)=>{return object.user_id===item.user_depart_leader_user_id})){
                    state.leaveApplication.depart_leaderE = [...state.leaveApplication.depart_leaderE, 
                        {
                            user_id:item.user_depart_leader_user_id, 
                            name: item.user_depart_leader_name,
                            role_id: "D003",
                            depart_id: item.user_depart_leader_depart_id,
                        }]
                }
            })
            let a = '';
            state.leaveApplication.departListC.map((value:any)=>{
                a += value + ","
            })
            a = a.replace(/,\s*$/, "");
            a += '&';
            state.leaveApplication.depart_leaderE.map((item:any)=>{
                a += item.user_id + ",";
            })
            //删除字符串最后的逗号
            state.leaveApplication.commitAdd.workflow_list = a.replace(/,\s*$/, "");
            //获取用户自己的剩余调休时间
            let overDay = dayjs().format('YYYY-MM-DD') + '-21 00:00:00';
            if(dayjs().format('YYYY-MM-DD') <= (dayjs().format('YYYY-MM') + '-21')){
                overDay = dayjs().subtract(1, 'month').format('YYYY-MM') + '-21 00:00:00';
            }else{
                overDay = dayjs().format('YYYY-MM') + '-21 00:00:00';
            }
            if(action.payload.compensatory_time.length>0){
                state.leaveApplication.compensatory_init = action.payload.compensatory_time[0]['compensatory_time'];
            }
            //获取用户自己一个考勤月内的打卡记录
            state.leaveApplication.realTime = action.payload.card_record;
            state.leaveApplication.abnormal_code = action.payload.abnormal_code;
            state.leaveApplication.day_type = action.payload.day_type;
            let checkDay = ''
            let count = 0
            let area = 0
            const day_type = state.leaveApplication.day_type;
            day_type.map((item, index)=>{
                const date = item.date.replace('T', ' ')
                if(date > overDay && (item.type==0 || item.type==4)){
                    count++
                }
                if(count == (Number(action.payload.apply_forbid_day[0]?.apply_forbid_day))){
                    area = index
                }
            })
            let checkTime = action.payload.apply_forbid_time[0]?.apply_forbid_time;
            checkDay = day_type[area].date.slice(0,11) + checkTime;

            state.leaveApplication.checkDay = checkDay;
            state.leaveApplication.applyLimitDay = action.payload.apply_limit_day[0].apply_limit_day;
        },
        // 判断是否选择代人申请
        leaveHandleSelectShow: (state: applicationState) => {
            state.leaveApplication.showSelect = true;
        },
        //撤销代人申请
        leaveHandleSelectShowBack: (state: applicationState) => {
            state.leaveApplication.showSelect = false;
        },
        // 处理开始日期变化
        leaveHandleStartDateChange: (state: applicationState, action: PayloadAction<any>) => {
            if (action.payload) {
                state.leaveApplication.startDay = action.payload;
                state.leaveApplication.endDayView = state.leaveApplication.startDay.add(state.leaveApplication.subDay,'day');
                state.leaveApplication.endDay = state.leaveApplication.endDayView;
                state.leaveApplication.commitAdd.start_time = state.leaveApplication.startDay.format('YYYY-MM-DD') + ' ' + 
                                                                (state.leaveApplication.startHour<10 ?
                                                                '0'+state.leaveApplication.startHour :
                                                                state.leaveApplication.startHour) + ':00:00';
                state.leaveApplication.commitAdd.end_time = state.leaveApplication.endDay.format('YYYY-MM-DD') + ' ' + 
                                                                (state.leaveApplication.endHour<10 ?
                                                                '0'+state.leaveApplication.endHour : 
                                                                state.leaveApplication.endHour) + ':00:00';
            }
            let numlist:number[] = [0,0,0,0];
            numlist = subtractTime(state.leaveApplication.typeValues,
                state.leaveApplication.compensatory_time,
                formatDate(state.leaveApplication.startDay), 
                formatDate(state.leaveApplication.endDayView), 
                state.leaveApplication.startHour, 
                state.leaveApplication.endHour,
                state.leaveApplication.day_type);
            state.leaveApplication.relaxTime = numlist[0];
            state.leaveApplication.workTime = numlist[1];
            state.leaveApplication.illnessTime = numlist[2];
            state.leaveApplication.pregnantTime = numlist[3];
        },
        // 处理开始小时变化
        leaveHandleStartHourChange: (state: applicationState, action: PayloadAction<any>) => {
            if (action.payload) {
                state.leaveApplication.startHour = action.payload;
                state.leaveApplication.commitAdd.start_time = state.leaveApplication.startDay.format('YYYY-MM-DD') + ' ' + 
                                                                (state.leaveApplication.startHour<10 ?
                                                                '0'+state.leaveApplication.startHour :
                                                                state.leaveApplication.startHour) + ':00:00';
            }
            let numlist:number[] = [0,0,0,0];
            numlist = subtractTime(state.leaveApplication.typeValues,
                state.leaveApplication.compensatory_time,
                formatDate(state.leaveApplication.startDay), 
                formatDate(state.leaveApplication.endDayView), 
                state.leaveApplication.startHour, 
                state.leaveApplication.endHour,
                state.leaveApplication.day_type);
            state.leaveApplication.relaxTime = numlist[0];
            state.leaveApplication.workTime = numlist[1];
            state.leaveApplication.illnessTime = numlist[2];
            state.leaveApplication.pregnantTime = numlist[3];
        },
        // 处理结束日期变化
        leaveHandleEndDateChange: (state: applicationState, action: PayloadAction<any>) => {
            if (action.payload) {
                state.leaveApplication.endDay = action.payload;
                state.leaveApplication.endDayView = action.payload;
                state.leaveApplication.subDay = state.leaveApplication.endDay.diff(state.leaveApplication.startDay,'day');
                state.leaveApplication.commitAdd.start_time = state.leaveApplication.startDay.format('YYYY-MM-DD') + ' ' + 
                                                                (state.leaveApplication.startHour<10 ?
                                                                '0'+state.leaveApplication.startHour :
                                                                state.leaveApplication.startHour) + ':00:00';
                state.leaveApplication.commitAdd.end_time = action.payload.format('YYYY-MM-DD') + ' ' + 
                                                                (state.leaveApplication.endHour<10 ?
                                                                '0'+state.leaveApplication.endHour : 
                                                                state.leaveApplication.endHour) + ':00:00';
            }
            let numlist:number[] = [0,0,0,0];
            numlist = subtractTime(state.leaveApplication.typeValues,
                state.leaveApplication.compensatory_time,
                formatDate(state.leaveApplication.startDay), 
                formatDate(state.leaveApplication.endDayView), 
                state.leaveApplication.startHour, 
                state.leaveApplication.endHour,
                state.leaveApplication.day_type);
            state.leaveApplication.relaxTime = numlist[0];
            state.leaveApplication.workTime = numlist[1];
            state.leaveApplication.illnessTime = numlist[2];
            state.leaveApplication.pregnantTime = numlist[3];
        },
        // 处理结束小时变化
        leaveHandleEndHourChange: (state: applicationState, action: PayloadAction<any>) => {
            if (action.payload) {
                state.leaveApplication.endHour = action.payload;
                state.leaveApplication.commitAdd.end_time = state.leaveApplication.endDay.format('YYYY-MM-DD') + ' ' + 
                                                                (state.leaveApplication.endHour<10 ?
                                                                '0'+state.leaveApplication.endHour :
                                                                state.leaveApplication.endHour) + ':00:00';
            }
            let numlist:number[] = [0,0,0,0];
            numlist = subtractTime(state.leaveApplication.typeValues,
                state.leaveApplication.compensatory_time,
                formatDate(state.leaveApplication.startDay), 
                formatDate(state.leaveApplication.endDayView), 
                state.leaveApplication.startHour, 
                state.leaveApplication.endHour,
                state.leaveApplication.day_type);
            state.leaveApplication.relaxTime = numlist[0];
            state.leaveApplication.workTime = numlist[1];
            state.leaveApplication.illnessTime = numlist[2];
            state.leaveApplication.pregnantTime = numlist[3];
        },
        // 选择项目负责人(新)
        handleLeaveSelectedDeparts: (state: applicationState, action: PayloadAction<any>) => {
            const selectedValues = action.payload;
            let selectedIds = ''
            const selectedItems = selectedValues.map((item: any)=>{
                selectedIds += "$" + item[0]["user_id"] + "$";
                return item[0];
            });
            state.leaveApplication.selectedDeparts = selectedItems;

            // ALL被选中 或者 超出3个, 则其他项目不可选 
            let options = [...state.leaveApplication.departList]
            if(selectedItems?.length >= 3){
                options.map((item: any)=>{
                    if(selectedIds?.indexOf("$" + item["user_id"] + "$") >= 0){
                        item["disabled"] = false
                    }else{
                        item["disabled"] = true;
                    }
                    return item;
                });
            } else {
                options.map((item: any)=>{
                    item["disabled"] = false;
                    return item;
                });
            }
            state.leaveApplication.departList = options;

            state.leaveApplication.commitAdd.workflow_list = '';

            let depart = ''
            selectedValues.map((item:any)=>{
                if(!depart.includes(item[0]["depart_id"]+'$')){
                    depart += "$" + item[0]["depart_id"] + "$";
                }
            })
            state.leaveApplication.depart_leaderE = [];
            state.leaveApplication.depart_leader.map((item:any)=>{
                if(depart.includes("$" + item.depart_id + "$")){
                    state.leaveApplication.depart_leaderE.push(item);
                }
            })
            state.leaveApplication.developments_leaders.map((item:any)=>{
                if(!state.leaveApplication.depart_leaderE.some((object:any)=>{return object.user_id===item.user_depart_leader_user_id})){
                    state.leaveApplication.depart_leaderE = [...state.leaveApplication.depart_leaderE, 
                        {
                            user_id:item.user_depart_leader_user_id, 
                            name: item.user_depart_leader_name,
                            role_id: "D003",
                            depart_id: item.user_depart_leader_depart_id,
                        }]
                }
            })
            let a = '';
            let b = '';
            state.leaveApplication.depart_leaderE.map((item:any)=>{
                a += item.user_id + ",";
            })
            state.leaveApplication.selectedDeparts.map((item:any)=>{
                b += item.user_id + ",";
            })
            const c = b.replace(/,\s*$/, "") + '&' + a;

            state.leaveApplication.commitAdd.workflow_list = c.replace(/,\s*$/, "");

        },
        //点击显示撤回栏
        leaveHandleDeleteForm: (state: applicationState, action: PayloadAction<any>) => {
            if (state.leaveApplication.selectedRowKey === action.payload) {
                state.leaveApplication.isConfirmed = !state.leaveApplication.isConfirmed;
                state.leaveApplication.selectedRowKeys = state.leaveApplication.selectedRowKeys.filter((item)=>item!==action.payload)
            } else {
                state.leaveApplication.selectedRowKeys.push(action.payload)
                state.leaveApplication.selectedRowKey = action.payload;
                state.leaveApplication.isConfirmed = true;
            }
        },
        //输入栏输入'我确认'
        leaveHandleDeleteFormInput: (state: applicationState, action: PayloadAction<any>) => {
            state.leaveApplication.confirmText = action.payload.target.value;
        },
        //一键输入'我确认'
        leaveHandleDeleteAutoInput: (state: applicationState) => {
            state.leaveApplication.confirmText = '我确认';
        },
        //点击撤回栏取消
        leaveHandleDeleteCancel: (state: applicationState) => {
            state.leaveApplication.confirmText = '';
            state.leaveApplication.isConfirmed = false;
        },
        //撤回当前申请
        leaveHandleDelete: (state: applicationState, action: PayloadAction<any>) => {
            const newData = state.leaveApplication.datasource.filter(item => item.code !== action.payload);
            state.leaveApplication.datasource = newData; 
            state.leaveApplication.isConfirmed = false;
            state.leaveApplication.confirmText = '';

            const startIndex = (state.leaveApplication.currentPage - 1) * state.leaveApplication.pageSize;
            const endIndex = startIndex + state.leaveApplication.pageSize;
            state.leaveApplication.currentData = state.leaveApplication.datasource.slice(startIndex, endIndex);
        },
        //点击后显示当前申请进度
        leaveHandleRowDetailClick: (state: applicationState, action: PayloadAction<any>) => {
            if(state.leaveApplication.selectedRowKeys.includes(action.payload)){
                state.leaveApplication.selectedRowKeys = state.leaveApplication.selectedRowKeys.filter((item)=>item!==action.payload)
            }else{
                state.leaveApplication.selectedRowKeys.push(action.payload)
                state.leaveApplication.selectedRowKey = action.payload;
            }
        },
        //申请表格的显示判断
        leaveHandleNewModule: (state: applicationState, action: PayloadAction<any>) => {
            state.leaveApplication.showNew = false;
            state.leaveApplication.show = true;
            let numlist:number[] = [0,0,0,0];
            //获取用户自己的剩余调休时间
            let overDay = dayjs().format('YYYY-MM-DD') + '-21 00:00:00';
            let overDayEnd = dayjs().format('YYYY-MM-DD') + '-21 00:00:00';
            if(dayjs().format('YYYY-MM-DD') <= (dayjs().format('YYYY-MM') + '-21')){
                overDay = dayjs().subtract(1, 'month').format('YYYY-MM') + '-21 00:00:00';
                overDayEnd = dayjs().format('YYYY-MM') + '-21 00:00:00';
            }else{
                overDay = dayjs().format('YYYY-MM') + '-21 00:00:00';
                overDayEnd = dayjs().add(1, 'month').format('YYYY-MM') + '-21 00:00:00';
            }
            let over_t_time = 0;
            state.leaveApplication.datasource.map((item:any)=>{
                if((item.start_time >= overDay && item.start_time < overDayEnd) && (item.workflow_result == 0 || item.workflow_result == 1)){
                    over_t_time = over_t_time + item.compensatory_hours;
                }
            })
            state.leaveApplication.compensatory_time = (state.leaveApplication.compensatory_init - over_t_time)<0?0:(state.leaveApplication.compensatory_init - over_t_time);
            numlist = subtractTime(state.leaveApplication.typeValues,
                state.leaveApplication.compensatory_time,
                formatDate(state.leaveApplication.startDay), 
                formatDate(state.leaveApplication.endDay), 
                state.leaveApplication.startHour, 
                state.leaveApplication.endHour,
                state.leaveApplication.day_type);
            state.leaveApplication.relaxTime = numlist[0];
            state.leaveApplication.workTime = numlist[1];
            state.leaveApplication.illnessTime = numlist[2];
            state.leaveApplication.pregnantTime = numlist[3];
            state.leaveApplication.commitAdd.user_id = action.payload.id;
            if(action.payload.role == '部门负责人' || action.payload.role == '高级管理层'){
                state.leaveApplication.commitAdd.workflow_list = action.payload.id.toString();
            }
            let limit_day = 0;
            if(dayjs().hour()<10){
                limit_day = 1-state.leaveApplication.applyLimitPastTime;
            }else{
                limit_day = 1-state.leaveApplication.applyLimitDay;
            }
            if (state.leaveApplication.typeValues == '调休'){
                state.leaveApplication.startDay = dayjs().add(limit_day>0?limit_day:0, 'day');
                state.leaveApplication.endDay = dayjs().add(limit_day>0?limit_day:0, 'day');
                state.leaveApplication.endDayView = dayjs().add(limit_day>0?limit_day:0, 'day');
                state.leaveApplication.commitAdd.start_time = dayjs().add(limit_day>0?limit_day:0, 'day').format('YYYY-MM-DD') + ' 09:00:00'
                state.leaveApplication.commitAdd.end_time = dayjs().add(limit_day>0?limit_day:0, 'day').format('YYYY-MM-DD') + ' 18:00:00'
            }
        },
        setLeaveNewHandle: (state: applicationState, action: PayloadAction<any>) => {

        },
        //有休关联单位变更
        leaveHandleAnnualLeaveUnitChange: (state: applicationState, action: PayloadAction<string>) => {
            state.leaveApplication.annualLeaveUnit = action.payload;
        },
        //代理人
        leaveHandleSelectPeopleChange: (state: applicationState, action: PayloadAction<any>) => {
            state.leaveApplication.listValues = {
                ...state.leaveApplication.listValues,
                // applicant: '(代：'+action.payload+')',
                applicant: action.payload,
            };
        },
        //请假类型显示
        leaveHandleVacationValue: (state: applicationState, action: PayloadAction<any>) => {
            state.leaveApplication.typeValues = action.payload;
            state.leaveApplication.commitAdd.leave_type = action.payload;
            let numlist:number[] = [0,0,0,0];
            numlist = subtractTime(state.leaveApplication.typeValues,
                state.leaveApplication.compensatory_time,
                formatDate(state.leaveApplication.startDay), 
                formatDate(state.leaveApplication.endDay), 
                state.leaveApplication.startHour, 
                state.leaveApplication.endHour,
                state.leaveApplication.day_type);
            state.leaveApplication.relaxTime = numlist[0];
            state.leaveApplication.workTime = numlist[1];
            state.leaveApplication.illnessTime = numlist[2];
            state.leaveApplication.pregnantTime = numlist[3];
        },
        //请假原由
        leaveHandleInputReasonChange: (state: applicationState, action: PayloadAction<any>) => {
            state.leaveApplication.listValues = {
                ...state.leaveApplication.listValues,
                reason: action.payload.target.value,
            }
            state.leaveApplication.commitAdd.reason = action.payload.target.value;
        },
        // //申请修改请假时间
        // leaveHandleApplicationChangeShow: (state: applicationState, action: PayloadAction<any>) => {
        //     let type = ''
        //     const start_time = state.leaveApplication.datasource.filter(item => item.code == action.payload.code)[0]?.start_time;
        //     const end_time = state.leaveApplication.datasource.filter(item => item.code == action.payload.code)[0]?.end_time;
        //     const date = start_time.slice(0,10);
        //     const dateEnd = end_time.slice(0,10);
        //     //计算这个申请中缺勤的总时长
        //     let differ_time_all = 0
        //     state.leaveApplication.abnormal_code.map(item => {
        //         if(item.abnormal_code == action.payload.code){
        //             differ_time_all = differ_time_all + item.differ_time;
        //         }
        //     })
        //     state.leaveApplication.realTimeSample = state.leaveApplication.realTime.filter(item => item.date.slice(0,10) == date)[0]?
        //                                             {
        //                                                 date: state.leaveApplication.realTime.filter(item => item.date.slice(0,10) == date)[0].date,
        //                                                 work_start_time: state.leaveApplication.realTime.filter(item => item.date.slice(0,10) == date)[0].work_start_time,
        //                                                 work_end_time: state.leaveApplication.realTime.filter(item => item.date.slice(0,10) == date)[0].work_end_time,
        //                                                 differ_time_all: differ_time_all
        //                                             }:
        //                                             {
        //                                                 date: date + ' ' + '00:00:00',
        //                                                 work_start_time: state.leaveApplication.datasource.filter(item => item.code == action.payload.code)[0]?.start_time,
        //                                                 work_end_time: state.leaveApplication.datasource.filter(item => item.code == action.payload.code)[0]?.end_time,
        //                                                 differ_time_all: differ_time_all
        //                                             };
        //     if(state.leaveApplication.datasource.filter(item => item.code == action.payload.code)[0]?.sick_hours != 0){
        //         type = '病假'
        //     }else if(state.leaveApplication.datasource.filter(item => item.code == action.payload.code)[0]?.other_days != 0){
        //         type = '其他'
        //     }else{
        //         type = '调休/事假'
        //     }
        //     if(state.leaveApplication.realTimeSample.work_start_time){
        //     }else{
        //         state.leaveApplication.realTimeSample.work_start_time = state.leaveApplication.datasource.filter(item => item.code == action.payload.code)[0]?.start_time;
        //     }
        //     if(state.leaveApplication.realTimeSample.work_end_time){
        //     }else{
        //         state.leaveApplication.realTimeSample.work_end_time = state.leaveApplication.datasource.filter(item => item.code == action.payload.code)[0]?.end_time;
        //     }
        //     state.leaveApplication.realTimeSample.date = state.leaveApplication.realTimeSample.date.replace('T',' ')
        //     state.leaveApplication.realTimeSample.work_start_time = state.leaveApplication.realTimeSample.work_start_time.replace('T',' ')
        //     state.leaveApplication.realTimeSample.work_end_time = state.leaveApplication.realTimeSample.work_end_time.replace('T',' ')

        //     //计算当前剩余调休时长加上这次申请的调休时长
        //     let compensatory_time = 0
        //     compensatory_time = state.leaveApplication.compensatory_time + state.leaveApplication.datasource.filter(item => item.code == action.payload.code)[0]?.compensatory_hours;

        //     //计算出修改后选择调休为主的调休时间和事假时间
        //     //调休时间
        //     let c_chours = 0
        //     //事假时间
        //     let c_whours = 0

        //     //计算出修改后选择事假为主的调休时间和事假时间
        //     //调休时间
        //     let w_chours = 0
        //     //事假时间
        //     let w_whours = differ_time_all

        //     //计算出修改后病假时间
        //     let s_hours = 0

        //     //计算出修改后其他时间
        //     let o_hours = 0

        //     //判断调休/事假申请的问题
        //     if(type == '调休/事假'){
        //         //计算修改后的申请
        //         if(date == dateEnd){
        //             //当申请的前后时间一致时
        //             if(differ_time_all == 8){
        //                 if(compensatory_time >= 8){
        //                     c_chours = 8
        //                     c_whours = 0
        //                 }else if(compensatory_time>=4){
        //                     c_chours = 4
        //                     c_whours = 4
        //                 }else{
        //                     c_chours = 0
        //                     c_whours = 8
        //                 }
        //             }else if(differ_time_all<8&&differ_time_all>=4){
        //                 if(compensatory_time >= 4){
        //                     c_chours = 4
        //                     c_whours = differ_time_all - c_chours
        //                 }else{
        //                     c_chours = 0
        //                     c_whours = differ_time_all
        //                 }
        //             }else{
        //                 c_chours = 0
        //                 c_whours = differ_time_all
        //             }
        //         }else{
        //             if(compensatory_time >= differ_time_all && differ_time_all >= 4){
        //                 c_chours = differ_time_all - (differ_time_all%4)
        //                 c_whours = differ_time_all - c_chours
        //             }else if(compensatory_time < differ_time_all && differ_time_all >= 4){
        //                 c_chours = compensatory_time - (compensatory_time%4)
        //                 c_whours = differ_time_all - c_chours
        //             }else{
        //                 c_chours = 0
        //                 c_whours = differ_time_all
        //             }
        //         }
        //     }else if(type == '病假'){
        //         s_hours = differ_time_all
        //     }else if(type == '其他'){
        //         o_hours = differ_time_all / 8
        //     }
            
        //     state.leaveApplication.changeTime = {
        //         c_ctime: c_chours,
        //         c_wtime: c_whours,
        //         w_ctime: w_chours,
        //         w_wtime: w_whours,
        //     }
        //     state.leaveApplication.changeAsk = {
        //         use_type: 1,
        //         code: action.payload.code,
        //         user_id: action.payload.user_id,
        //         agent_user_id: 0,
        //         leave_type: type,
        //         start_time: start_time,
        //         end_time: end_time,
        //         t_hours: c_chours,
        //         s_hours: c_whours,
        //         hours: s_hours,
        //         t_days: o_hours,
        //     }
        //     state.leaveApplication.isChangeShow = !state.leaveApplication.isChangeShow;

        //     state.leaveApplication.selectedRowKey = action.payload.code;
        //     state.leaveApplication.isChangeShow = true;
        // },
        // //申请修改请假时间的选择
        // leaveHandleApplicationChangeSelect: (state: applicationState, action: PayloadAction<any>) => {
        //     if(action.payload){
        //         state.leaveApplication.changeAsk.t_hours = state.leaveApplication.changeTime.c_ctime;
        //         state.leaveApplication.changeAsk.s_hours = state.leaveApplication.changeTime.c_wtime;
        //     }else{
        //         state.leaveApplication.changeAsk.t_hours = state.leaveApplication.changeTime.w_ctime;
        //         state.leaveApplication.changeAsk.s_hours = state.leaveApplication.changeTime.w_wtime;
        //     }
        // },
        // //申请修改请假时间的表格取消
        // leaveHandleApplicationChangeCancel: (state: applicationState) => {
        //     state.leaveApplication.isChangeShow = false;
        // },
        //申请修改请假时间的表格申请确认(临时)
        leaveHandleApplicationChangeSure: (state: applicationState) => {
            state.leaveApplication.isChangeShow = false;
        },
  
        //申请表格的提交(临时)
        leaveHandleSubmitModule: (state: applicationState, action: PayloadAction<any>) => {
            if(state.leaveApplication.startDay>state.leaveApplication.endDay){
                state.leaveApplication.endDay = state.leaveApplication.startDay;
            }
            if(state.leaveApplication.endDayView<state.leaveApplication.endDay){
                state.leaveApplication.endDay = state.leaveApplication.endDayView;
            }
            if(state.leaveApplication.endDay.diff(state.leaveApplication.startDay,'day')!=state.leaveApplication.subDay){
                state.leaveApplication.endDay = state.leaveApplication.startDay.add(state.leaveApplication.subDay,'day');
            }
            let newList = [...state.leaveApplication.datasource];
            if(newList[0]?.code==''){
                newList = [];
            }
            const formattedStart = formatDate(state.leaveApplication.startDay);
            const formattedEnd = formatDate(state.leaveApplication.endDay);
            let start = formattedStart + getWeekday(state.leaveApplication.startDay.toDate()) + (state.leaveApplication.startHour < 10?'0' + state.leaveApplication.startHour : state.leaveApplication.startHour) + ":00";
            let end = formattedEnd + getWeekday(state.leaveApplication.endDay.toDate()) + (state.leaveApplication.endHour < 10?'0' + state.leaveApplication.endHour : state.leaveApplication.endHour) + ":00";
            if(state.leaveApplication.typeValues=='其他'){
                start = formattedStart + getWeekday(state.leaveApplication.startDay.toDate());
                end = formattedEnd + getWeekday(state.leaveApplication.endDay.toDate());
            }
            let inputApplicant = state.leaveApplication.listValues.applicant;
            if(!state.leaveApplication.showSelect){
            inputApplicant = '';
            }
            if(1){
                newList.push(
                     {
                        code: newList.length + 1,
                        user_id: 0,
                        name: action.payload,
                        agent_user_id: 0,
                        agent_name: inputApplicant,
                        start_time: start,
                        end_time: end,
                        compensatory_hours: state.leaveApplication.relaxTime,
                        normal_hours: state.leaveApplication.workTime,
                        sick_hours: state.leaveApplication.illnessTime,
                        other_days: state.leaveApplication.pregnantTime,
                        other_apply_type: state.leaveApplication.pregnantTime>0?'其他':'',
                        reject_reason: '',
                        reason: state.leaveApplication.listValues.reason,
                        workflow_result: 0,
                    }
                )
            }
            state.leaveApplication.datasource = newList;
            state.leaveApplication.show = false;
            state.leaveApplication.showNew = true;
            state.leaveApplication.typeValues = '调休';
            const startIndex = (state.leaveApplication.currentPage - 1) * state.leaveApplication.pageSize;
            const endIndex = startIndex + state.leaveApplication.pageSize;
            state.leaveApplication.currentData = state.leaveApplication.datasource.slice(startIndex, endIndex);
        },
        //申请表格的取消(临时)
        leaveHandleCancelModule: (state: applicationState) => {
            state.leaveApplication.show = false;
            state.leaveApplication.showNew = true;
        },
        //分页翻页
        handleLeavePageChange: (state: applicationState, action: PayloadAction<any>) => {
            state.leaveApplication.currentPage = action.payload;
            const startIndex = (state.leaveApplication.currentPage - 1) * state.leaveApplication.pageSize;
            const endIndex = startIndex + state.leaveApplication.pageSize;
            state.leaveApplication.currentData = state.leaveApplication.datasource.slice(startIndex, endIndex);
        },
        applyLeaveAdd: (state: applicationState, action: PayloadAction<any>) => {
            state.leaveApplication.datasource.push({
                code: action.payload.apply_list.code,
                user_id: action.payload.apply_list.user_id,
                name: action.payload.apply_list.name,
                agent_user_id: action.payload.apply_list.agent_user_id,
                agent_name: action.payload.apply_list.agent_name,
                start_time: action.payload.apply_list.start_time,
                end_time: action.payload.apply_list.end_time,
                normal_hours: action.payload.apply_list.normal_hours,
                compensatory_hours: action.payload.apply_list.compensatory_hours,
                sick_hours: action.payload.apply_list.sick_hours,
                other_days: action.payload.apply_list.other_days,
                other_apply_type: action.payload.apply_list.other_apply_type,
                reason: action.payload.apply_list.reason,
                reject_reason: '',
                workflow_result: action.payload.apply_list.workflow_result,
            })

            state.leaveApplication.datasource = state.leaveApplication.datasource.filter(item=>item.code != '');

            state.leaveApplication.workflow_list[action.payload.apply_list.code] = (action.payload.workflow_list[action.payload.apply_list.code])

            const startIndex = (state.leaveApplication.currentPage - 1) * state.leaveApplication.pageSize;
            const endIndex = startIndex + state.leaveApplication.pageSize;
            state.leaveApplication.currentData = state.leaveApplication.datasource.slice(startIndex, endIndex);

            state.leaveApplication.commitAdd.reason = '';
            state.leaveApplication.commitAdd.leave_type = '调休';
            state.leaveApplication.commitAdd.start_time = dayjs().format('YYYY-MM-DD') + ' 09:00:00';
            state.leaveApplication.commitAdd.end_time = dayjs().format('YYYY-MM-DD') + ' 18:00:00';
            //计算已通过的和正在审批的调休时长
            let overDay = dayjs().format('YYYY-MM') + '-21 00:00:00';
            let overDayEnd = dayjs().format('YYYY-MM') + '-21 00:00:00';
            if(dayjs().format('YYYY-MM-DD') < (dayjs().format('YYYY-MM') + '-21')){
                overDay = dayjs().subtract(1, 'month').format('YYYY-MM') + '-21 00:00:00';
                overDayEnd = dayjs().format('YYYY-MM') + '-21 00:00:00';
            }else{
                overDay = dayjs().format('YYYY-MM') + '-21 00:00:00';
                overDayEnd = dayjs().add(1, 'month').format('YYYY-MM') + '-21 00:00:00';
            }
            let over_t_time = 0;
            state.leaveApplication.datasource.map((item:any)=>{
                if(item.start_time >= overDay && item.start_time < overDayEnd && (item.workflow_result == 0 || item.workflow_result == 1)){
                    over_t_time += item.compensatory_hours;
                }
            })

            // if(action.payload.compensatory_time){
            //     state.leaveApplication.compensatory_time = action.payload.compensatory_time - over_t_time;
            // }

            state.leaveApplication.confirmText = '';
            state.leaveApplication.typeValues = '调休';
            state.leaveApplication.startDay = dayjs();
            state.leaveApplication.startHour = 9;
            state.leaveApplication.endDay = dayjs();
            state.leaveApplication.endDayView = dayjs();
            state.leaveApplication.endHour = 18;

            state.leaveApplication.showSelect = false;
            state.leaveApplication.show = false;
            state.leaveApplication.showNew = true;
        },
        applyLeaveCancel: (state: applicationState, action: PayloadAction<any>) => {
            state.leaveApplication.datasource = state.leaveApplication.datasource.filter(item=> item.code != action.payload.apply_code);
            const startIndex = (state.leaveApplication.currentPage - 1) * state.leaveApplication.pageSize;
            const endIndex = startIndex + state.leaveApplication.pageSize;
            state.leaveApplication.currentData = state.leaveApplication.datasource.slice(startIndex, endIndex);
            //计算已通过的和正在审批的调休时长
            // let overDay = dayjs().format('YYYY-MM-DD') + '-21 00:00:00';
            // let overDayEnd = dayjs().format('YYYY-MM-DD') + '-21 00:00:00';
            // if(dayjs().format('YYYY-MM-DD') < (dayjs().format('YYYY-MM') + '-21')){
            //     overDay = dayjs().subtract(1, 'month').format('YYYY-MM') + '-21 00:00:00';
            //     overDayEnd = dayjs().format('YYYY-MM') + '-21 00:00:00';
            // }else{
            //     overDay = dayjs().format('YYYY-MM') + '-21 00:00:00';
            //     overDayEnd = dayjs().add(1, 'month').format('YYYY-MM') + '-21 00:00:00';
            // }
            // let over_t_time = 0;
            // state.leaveApplication.datasource.map((item:any)=>{
            //     if(item.start_time >= overDay && item.start_time < overDayEnd && (item.workflow_result == 0 || item.workflow_result == 1)){
            //         over_t_time += item.compensatory_hours;
            //     }
            // })
            // state.leaveApplication.compensatory_time = action.payload.compensatory_time - over_t_time;
            state.leaveApplication.confirmText = '';
            state.leaveApplication.isConfirmed = false;

            state.leaveApplication.showSelect = false;
            state.leaveApplication.show = false;
            state.leaveApplication.showNew = true;
        },
        applyLeaveChange: (state: applicationState, action: PayloadAction<any>) => {
            const apply_list = action.payload.apply_list;
            let target_code = ''
            if(apply_list){
                target_code = apply_list.code;
                state.leaveApplication.datasource = state.leaveApplication.datasource.filter(item=>item.code!=target_code);
                state.leaveApplication.datasource.push({
                    code: apply_list.code,
                    user_id: apply_list.user_id,
                    name: apply_list.name,
                    agent_user_id: apply_list.agent_user_id,
                    agent_name: apply_list.agent_name,
                    start_time: apply_list.start_time,
                    end_time: apply_list.end_time,
                    normal_hours: apply_list.normal_hours,
                    compensatory_hours: apply_list.compensatory_hours,
                    sick_hours: apply_list.sick_hours,
                    other_days: apply_list.other_days,
                    other_apply_type: apply_list.other_apply_type,
                    reason: apply_list.reason,
                    reject_reason: '',
                    workflow_result: apply_list.workflow_result,
                })
            }

            state.leaveApplication.isChangeShow = false;
            state.leaveApplication.changeAsk = {
                use_type: 1,
                code: '',
                user_id: 0,
                agent_user_id: 0,
                leave_type: '',
                start_time: '',
                end_time: '',
                t_hours: 0,
                s_hours: 0,
                hours: 0,
                t_days: 0,
            };       
            state.leaveApplication.confirmText = '';
        },



        //出差申请页面动作
        setBusinessTripDataWhenInit: (state: applicationState, action: PayloadAction<any>) => {
            if(action.payload.apply_list!=0){
                state.businessTripApplication.datasource = action.payload.apply_list;
                state.businessTripApplication.workflow_list = action.payload.workflow_list;
                const startIndex = (state.businessTripApplication.currentPage - 1) * state.businessTripApplication.pageSize;
                const endIndex = startIndex + state.businessTripApplication.pageSize;
                state.businessTripApplication.currentData = state.businessTripApplication.datasource.slice(startIndex, endIndex);
            }else{
                state.businessTripApplication.datasource = [{
                    code: '',
                    user_id: '',
                    name: '',
                    agent_user_id: '',
                    agent_name: '',
                    start_time: '',
                    end_time: '',
                    location: '',
                    t_days: '',
                    reason: '',
                    reject_reason: '',
                    workflow_result: '',
                }];
            }
            state.businessTripApplication.isConfirmed = false
        },
        setBusinessTripAddInfInit: (state: applicationState, action: PayloadAction<any>) => {
            state.businessTripApplication.developments_leaders = action.payload.user_depart_leader_inf;
            state.businessTripApplication.departList = action.payload.team_leader.map((item: any)=>{
                item.disabled = false;
                return item;
            });
            state.businessTripApplication.depart_leader = action.payload.depart_leader;
            state.businessTripApplication.departListC = [];
            action.payload.recently_team_leader.map((item:any)=>{
                const user_id = [item.user_id]
                state.businessTripApplication.departListC = [...state.businessTripApplication.departListC, user_id]
            })
            let depart = ''
            state.businessTripApplication.departListC.map((item:any)=>{
                state.businessTripApplication.departList.map((value:any)=>{
                    if(item[0] == value.user_id){
                        depart += "$" + value.depart_id + "$"
                    }
                })
            })
            state.businessTripApplication.depart_leaderE = [];
            state.businessTripApplication.depart_leader.map((item:any)=>{
                if(depart.includes("$" + item.depart_id + "$")){
                    state.businessTripApplication.depart_leaderE.push(item);
                }
            })
            state.businessTripApplication.developments_leaders.map((item:any)=>{
                if(!state.businessTripApplication.depart_leaderE.some((object:any)=>{return object.user_id===item.user_depart_leader_user_id})){
                    state.businessTripApplication.depart_leaderE = [...state.businessTripApplication.depart_leaderE, 
                        {
                            user_id:item.user_depart_leader_user_id, 
                            name: item.user_depart_leader_name,
                            role_id: "D003",
                            depart_id: item.user_depart_leader_depart_id,
                        }]
                }
            })
            let a = '';
            state.businessTripApplication.departListC.map((value:any)=>{
                a += value + ","
            })
            a = a.replace(/,\s*$/, "");
            a += '&';
            state.businessTripApplication.depart_leaderE.map((item:any)=>{
                a += item.user_id + ",";
            })
            let overDay = dayjs().format('YYYY-MM-DD') + '-21 00:00:00';
            let overDayEnd = dayjs().format('YYYY-MM-DD') + '-21 00:00:00';
            if(dayjs().format('YYYY-MM-DD') <= (dayjs().format('YYYY-MM') + '-21')){
                overDay = dayjs().subtract(1, 'month').format('YYYY-MM') + '-21 00:00:00';
                overDayEnd = dayjs().format('YYYY-MM') + '-21 00:00:00';
            }else{
                overDay = dayjs().format('YYYY-MM') + '-21 00:00:00';
                overDayEnd = dayjs().add(1, 'month').format('YYYY-MM') + '-21 00:00:00';
            }
            //删除字符串最后的逗号
            state.businessTripApplication.commitAdd.workflow_list = a.replace(/,\s*$/, "");
            state.businessTripApplication.day_type = action.payload.day_type;
            let checkDay = ''
            let count = 0
            let area = 0
            const day_type = state.businessTripApplication.day_type;
            day_type.map((item, index)=>{
                const date = item.date.replace('T', ' ')
                if(date > overDay && (item.type==0 || item.type==4)){
                    count++
                }
                if(count == (Number(action.payload.apply_forbid_day[0].apply_forbid_day))){
                    area = index
                }
            })
            let checkTime = action.payload.apply_forbid_time[0]?.apply_forbid_time;
            checkDay = day_type[area].date.slice(0,11) + checkTime;

            state.businessTripApplication.checkDay = checkDay;
        },
        businessTripHandleSelectShow: (state: applicationState) => {
            state.businessTripApplication.showSelect = true;
        },
        //撤销代人申请
        businessTripHandleSelectShowBack: (state: applicationState) => {
            state.businessTripApplication.showSelect = false;
        },
        // 处理开始日期变化
        businessTripHandleStartDateChange: (state: applicationState, action: PayloadAction<any>) => {
            if (action.payload) {
                state.businessTripApplication.startDay = action.payload;
                if(state.businessTripApplication.startDay > state.businessTripApplication.endDay){
                    state.businessTripApplication.endDay = state.businessTripApplication.startDay;
                    state.businessTripApplication.commitAdd.end_time = state.businessTripApplication.endDay.format('YYYY-MM-DD') + ' 00:00:00';
                }
                state.businessTripApplication.commitAdd.start_time = state.businessTripApplication.startDay.format('YYYY-MM-DD') + ' 00:00:00';
            }
        },
        // 处理结束日期变化
        businessTripHandleEndDateChange: (state: applicationState, action: PayloadAction<any>) => {
            if (action.payload) {
                state.businessTripApplication.endDay = action.payload;
                state.businessTripApplication.commitAdd.end_time = state.businessTripApplication.endDay.format('YYYY-MM-DD') + ' 00:00:00';
            }
        },
        // 选择项目负责人(新)
        handleBusinessSelectedDeparts: (state: applicationState, action: PayloadAction<any>) => {
            const selectedValues = action.payload;
            let selectedIds = ''
            const selectedItems = selectedValues.map((item: any)=>{
                selectedIds += "$" + item[0]["user_id"] + "$";
                return item[0];
            });
            state.businessTripApplication.selectedDeparts = selectedItems;

            // ALL被选中 或者 超出3个, 则其他项目不可选 
            let options = [...state.businessTripApplication.departList]
            if(selectedItems?.length >= 3){
                options.map((item: any)=>{
                    if(selectedIds?.indexOf("$" + item["user_id"] + "$") >= 0){
                        item["disabled"] = false
                    }else{
                        item["disabled"] = true;
                    }
                    return item;
                });
            } else {
                options.map((item: any)=>{
                    item["disabled"] = false;
                    return item;
                });
            }
            state.businessTripApplication.departList = options;

            state.businessTripApplication.commitAdd.workflow_list = '';
            let depart = ''
            selectedValues.map((item:any)=>{
                if(!depart.includes(item[0]["depart_id"]+'$')){
                    depart += "$" + item[0]["depart_id"] + "$";
                }
            })
            state.businessTripApplication.depart_leaderE = [];
            state.businessTripApplication.depart_leader.map((item:any)=>{
                if(depart.includes("$" + item.depart_id + "$")){
                    state.businessTripApplication.depart_leaderE.push(item);
                }
            })
            state.businessTripApplication.developments_leaders.map((item:any)=>{
                if(!state.businessTripApplication.depart_leaderE.some((object:any)=>{return object.user_id===item.user_depart_leader_user_id})){
                    state.businessTripApplication.depart_leaderE = [...state.businessTripApplication.depart_leaderE, 
                        {
                            user_id:item.user_depart_leader_user_id, 
                            name: item.user_depart_leader_name,
                            role_id: "D003",
                            depart_id: item.user_depart_leader_depart_id,
                        }]
                }
            })
            let a = '';
            let b = '';
            state.businessTripApplication.depart_leaderE.map((item:any)=>{
                a += item.user_id + ",";
            })
            state.businessTripApplication.selectedDeparts.map((item:any)=>{
                b += item.user_id + ",";
            })
            const c = b.replace(/,\s*$/, "") + '&' + a;

            state.businessTripApplication.commitAdd.workflow_list = c.replace(/,\s*$/, "");

        },
        //当前项目审批选择栏内容
        businessTripHandleCascaderContent: (state: applicationState, action: PayloadAction<any>) => {
            state.businessTripApplication.cascaderContent[action.payload.number] = action.payload.value; 
            let a = false;
            const devName = state.businessTripApplication.cascaderContent[action.payload.number][0].toString();
            state.businessTripApplication.optionChooseDev.map(item=>{
                if(item.key == action.payload.number){
                    a = true;
                }
            })
            if(a){
                state.businessTripApplication.optionChooseDev.forEach((item) => {
                    if(item.key == action.payload.number){
                        item.value[0] = devName;
                    }
                }); 
            }else{
                state.businessTripApplication.optionChooseDev.push({
                    key:action.payload.number,
                    value:action.payload.value,
                });
            }
            let v:{
                value: string;
                label: string;
                children: {
                    value: string;
                    label: string;
                }[];
            }[] = [];
            state.businessTripApplication.optionChooseDev.forEach((item) => {
                (state.businessTripApplication as any).developments.forEach((element:any)=> {
                    if(item.value[0] == element.value){
                        v.push(element);
                    }
                })
            })
            v = Array.from(new Set(v)) as any
            (state.businessTripApplication as any).developmentsChoose = v;
        },
        //当前项目审批选择栏的删除
        businessTripHandleDeleteCascader: (state: applicationState, action: PayloadAction<any>) => {
            state.businessTripApplication.cascaderContent.splice(action.payload-1,1);
            const newCascaderList = state.businessTripApplication.cascaderList.filter((item) => item.id !== action.payload);
            if(newCascaderList.length>0) {
                state.businessTripApplication.cascaderList = newCascaderList;
                state.businessTripApplication.cascaderCount = state.businessTripApplication.cascaderCount - 1;
                //更新id
                newCascaderList.forEach((item, index) => {
                    item.id = index + 1;
                });
            }
            state.businessTripApplication.optionChooseDev = state.businessTripApplication.optionChooseDev.filter((item)=>item.key!=action.payload-1);
            let v:{
                value: string;
                label: string;
                children: {
                    value: string;
                    label: string;
                }[];
            }[] = [];
            state.businessTripApplication.optionChooseDev.forEach((item) => {
                (state.businessTripApplication as any).developments.forEach((element:any)=> {
                    if(item.value[0] == element.value){
                        v.push(element);
                    }
                })
            })
            v = Array.from(new Set(v)) as any
            (state.businessTripApplication as any).developmentsChoose = v;
        },
        //项目审批的选择栏添加
        businessTripHandleAddCascader: (state: applicationState) => {
            const newCascaderList = [...state.businessTripApplication.cascaderList, { id: state.businessTripApplication.cascaderList.length + 1 }];
            state.businessTripApplication.cascaderList = newCascaderList;
            state.businessTripApplication.cascaderCount = state.businessTripApplication.cascaderCount + 1;
        },
        //点击显示撤回栏
        businessTripHandleDeleteForm: (state: applicationState, action: PayloadAction<any>) => {
            if (state.businessTripApplication.selectedRowKey === action.payload) {
                state.businessTripApplication.isConfirmed = !state.businessTripApplication.isConfirmed;
            } else {
                state.businessTripApplication.selectedRowKey = action.payload;
                state.businessTripApplication.isConfirmed = true;
            }
        },
        //输入栏输入'我确认'
        businessTripHandleDeleteFormInput: (state: applicationState, action: PayloadAction<any>) => {
            state.businessTripApplication.confirmText = action.payload.target.value;
        },
        //一键输入'我确认'
        businessTripHandleDeleteAutoInput: (state: applicationState) => {
            state.businessTripApplication.confirmText = '我确认';
        },
        //点击显示撤回栏
        businessTripHandleDeleteCancel: (state: applicationState) => {
            state.businessTripApplication.confirmText = '';
            state.businessTripApplication.isConfirmed = false;
        },
        //撤回当前申请
        businessTripHandleDelete: (state: applicationState, action: PayloadAction<any>) => {
            const newData = state.businessTripApplication.datasource.filter(item => item.code !== action.payload);
            state.businessTripApplication.datasource = newData; 
            state.businessTripApplication.isConfirmed = false;
            state.businessTripApplication.confirmText = '';

            const startIndex = (state.businessTripApplication.currentPage - 1) * state.businessTripApplication.pageSize;
            const endIndex = startIndex + state.businessTripApplication.pageSize;
            state.businessTripApplication.currentData = state.businessTripApplication.datasource.slice(startIndex, endIndex);
        },
        //点击后显示当前申请进度
        businessTripHandleRowDetailClick: (state: applicationState, action: PayloadAction<any>) => {
            if(state.businessTripApplication.selectedRowKeys.includes(action.payload)){
                state.businessTripApplication.selectedRowKeys = state.businessTripApplication.selectedRowKeys.filter((item)=>item!==action.payload)
            }else{
                state.businessTripApplication.selectedRowKeys.push(action.payload)
                state.businessTripApplication.selectedRowKey = action.payload;
            }
        },
        //申请表格的显示判断
        businessTripHandleNewModule: (state: applicationState, action: PayloadAction<any>) => {
            state.businessTripApplication.showNew = false;
            state.businessTripApplication.show = true;
            state.businessTripApplication.commitAdd.user_id = action.payload.id;
            if(action.payload.role == '部门负责人' || action.payload.role == '高级管理层'){
                state.businessTripApplication.commitAdd.workflow_list = action.payload.id.toString();
            }
            state.businessTripApplication.commitAdd.start_time = state.businessTripApplication.startDay.format('YYYY-MM-DD') + ' 00:00:00';
            state.businessTripApplication.commitAdd.end_time = state.businessTripApplication.endDay.format('YYYY-MM-DD') + ' 00:00:00';
        },
        //代理人
        businessTripHandleSelectPeopleChange: (state: applicationState, action: PayloadAction<any>) => {
            state.businessTripApplication.listValues = {
                ...state.businessTripApplication.listValues,
                applicant: action.payload,
            };
        },
        //请假类型显示
        businessTripHandleVacationValue: (state: applicationState, action: PayloadAction<any>) => {
            state.businessTripApplication.typeValues = action.payload;
            state.businessTripApplication.commitAdd.location = action.payload;
        },
        //申请修改出差时间
        businessTripHandleApplicationChangeShow: (state: applicationState, action: PayloadAction<any>) => {
            state.businessTripApplication.isChangeShow = true;
            state.businessTripApplication.selectedRowKey = action.payload.code;
            state.businessTripApplication.changeAsk.code = action.payload.code;
            state.businessTripApplication.changeAsk.user_id = action.payload.user_id;
            const sample = state.businessTripApplication.datasource.filter(item => item.code == action.payload.code)[0];
            if(sample!=null){
                state.businessTripApplication.changeAsk.start_time = sample.start_time;
                state.businessTripApplication.changeAsk.end_time = sample.end_time;
            }else{
                state.businessTripApplication.changeAsk.start_time = '';
                state.businessTripApplication.changeAsk.end_time = '';
            }
            state.businessTripApplication.changeAsk.start_time = state.businessTripApplication.changeAsk.start_time.replaceAll('T',' ');
        },
        //修改出差结束时间
        businessTripHandleApplicationChangeEndDay: (state: applicationState, action: PayloadAction<any>) => {
            state.businessTripApplication.changeAsk.start_time = state.businessTripApplication.changeAsk.start_time.replaceAll('T',' ');
            state.businessTripApplication.changeAsk.end_time = action.payload.format('YYYY-MM-DD') + ' 00:00:00';
        },
        //申请修改出差时间
        businessTripHandleApplicationChangeCancel: (state: applicationState) => {
            state.businessTripApplication.isChangeShow = false;
        },
        //申请修改出差时间
        businessTripHandleApplicationChangeSure: (state: applicationState, action: PayloadAction<any>) => {
            state.businessTripApplication.isChangeShow = false;
        },
        //请假原由
        businessTripHandleInputReasonChange: (state: applicationState, action: PayloadAction<any>) => {
            state.businessTripApplication.listValues = {
                ...state.businessTripApplication.listValues,
                reason: action.payload.target.value,
            }
            state.businessTripApplication.commitAdd.reason = action.payload.target.value;
        },
        //申请表格的取消(临时)
        businessTripHandleCancelModule: (state: applicationState) => {
            state.businessTripApplication.show = false;
            state.businessTripApplication.showNew = true;
        },
        //分页翻页
        handleBusinessTripPageChange: (state: applicationState, action: PayloadAction<any>) => {
            state.businessTripApplication.currentPage = action.payload;
            const startIndex = (state.businessTripApplication.currentPage - 1) * state.businessTripApplication.pageSize;
            const endIndex = startIndex + state.businessTripApplication.pageSize;
            state.businessTripApplication.currentData = state.businessTripApplication.datasource.slice(startIndex, endIndex);
        },
        applyBusinessTripAdd: (state: applicationState, action: PayloadAction<any>) => {
            state.businessTripApplication.datasource.push({
                code: action.payload.apply_list.code,
                user_id: action.payload.apply_list.user_id,
                name: action.payload.apply_list.name,
                agent_user_id: action.payload.apply_list.agent_user_id,
                agent_name: action.payload.apply_list.agent_name,
                start_time: action.payload.apply_list.start_time,
                end_time: action.payload.apply_list.end_time,
                location: action.payload.apply_list.location,
                t_days: action.payload.apply_list.t_days,
                reason: action.payload.apply_list.reason,
                reject_reason: '',
                workflow_result: action.payload.apply_list.workflow_result,
            })

            state.businessTripApplication.datasource = state.businessTripApplication.datasource.filter(item=>item.code != '');

            state.businessTripApplication.workflow_list[action.payload.apply_list.code] = (action.payload.workflow_list[action.payload.apply_list.code])

            const startIndex = (state.businessTripApplication.currentPage - 1) * state.businessTripApplication.pageSize;
            const endIndex = startIndex + state.businessTripApplication.pageSize;
            state.businessTripApplication.currentData = state.businessTripApplication.datasource.slice(startIndex, endIndex);

            state.businessTripApplication.commitAdd.reason = '';

            state.businessTripApplication.showSelect = false;
            state.businessTripApplication.show = false;
            state.businessTripApplication.showNew = true;
        },
        applyBusinessTripCancel: (state: applicationState, action: PayloadAction<any>) => {
            state.businessTripApplication.datasource = state.businessTripApplication.datasource.filter(item=> item.code != action.payload.apply_code);
            const startIndex = (state.businessTripApplication.currentPage - 1) * state.businessTripApplication.pageSize;
            const endIndex = startIndex + state.businessTripApplication.pageSize;
            state.businessTripApplication.currentData = state.businessTripApplication.datasource.slice(startIndex, endIndex);

            state.businessTripApplication.isConfirmed = false;
            state.businessTripApplication.confirmText = '';
        },
        applyBusinessTripChange: (state: applicationState, action: PayloadAction<any>) => {
            state.businessTripApplication.datasource = state.businessTripApplication.datasource.filter((item:any)=>item.code != action.payload?.apply_list?.code);
            state.businessTripApplication.datasource.push({
                code: action.payload.apply_list.code,
                user_id: action.payload.apply_list.user_id,
                name: action.payload.apply_list.name,
                agent_user_id: action.payload.apply_list.agent_user_id,
                agent_name: action.payload.apply_list.agent_name,
                start_time: action.payload.apply_list.start_time,
                end_time: action.payload.apply_list.end_time,
                location: action.payload.apply_list.location,
                t_days: action.payload.apply_list.t_days,
                reason: action.payload.apply_list.reason,
                reject_reason: '',
                workflow_result: action.payload.apply_list.workflow_result,
            })

            state.businessTripApplication.datasource = state.businessTripApplication.datasource.filter(item=>item.code != '');

            state.businessTripApplication.workflow_list[action.payload.apply_list.code] = (action.payload.workflow_list[action.payload.apply_list.code])

            const startIndex = (state.businessTripApplication.currentPage - 1) * state.businessTripApplication.pageSize;
            const endIndex = startIndex + state.businessTripApplication.pageSize;
            state.businessTripApplication.currentData = state.businessTripApplication.datasource.slice(startIndex, endIndex);

            state.businessTripApplication.changeAsk.start_time = '';
            state.businessTripApplication.changeAsk.end_time = '';
            state.businessTripApplication.changeAsk.code = '';

            state.businessTripApplication.isChangeShow = false;
            state.businessTripApplication.showSelect = false;
            state.businessTripApplication.show = false;
            state.businessTripApplication.showNew = true;
        },



        //加班申请

        setOvertimeDataWhenInit: (state: applicationState, action: PayloadAction<any>) => {
            if(action.payload.apply_list!=0){
                state.overtimeApplication.datasource = action.payload.apply_list;
                state.overtimeApplication.workflow_list = action.payload.workflow_list;
                const startIndex = (state.overtimeApplication.currentPage - 1) * state.overtimeApplication.pageSize;
                const endIndex = startIndex + state.overtimeApplication.pageSize;
                state.overtimeApplication.currentData = state.overtimeApplication.datasource.slice(startIndex,endIndex);
            }else{
                state.overtimeApplication.datasource = [{
                    code: '',
                    user_id: '',
                    name: '',
                    agent_user_id: '',
                    agent_name: '',
                    start_time: '',
                    end_time: '',
                    hours: '',
                    reason: '',
                    reject_reason: '',
                    workflow_result: '',
                    expense: '',
                }];
            }
            state.overtimeApplication.isConfirmed = false
        },
        setOvertimeAddInfInit: (state: applicationState, action: PayloadAction<any>) => {
            state.overtimeApplication.developments_leaders = action.payload.user_depart_leader_inf;
            state.overtimeApplication.departList = action.payload.team_leader.map((item: any)=>{
                item.disabled = false;
                return item;
            });
            state.overtimeApplication.depart_leader = action.payload.depart_leader;
            state.overtimeApplication.departListC = [];
            action.payload.recently_team_leader.map((item:any)=>{
                const user_id = [item.user_id]
                state.overtimeApplication.departListC = [...state.overtimeApplication.departListC, user_id]
            })
            let depart = ''
            state.overtimeApplication.departListC.map((item:any)=>{
                state.overtimeApplication.departList.map((value:any)=>{
                    if(item[0] == value.user_id){
                        depart += "$" + value.depart_id + "$"
                    }
                })
            })
            state.overtimeApplication.depart_leaderE = [];
            state.overtimeApplication.depart_leader.map((item:any)=>{
                if(depart.includes("$" + item.depart_id + "$")){
                    state.overtimeApplication.depart_leaderE.push(item);
                }
            })
            state.overtimeApplication.developments_leaders.map((item:any)=>{
                if(!state.overtimeApplication.depart_leaderE.some((object:any)=>{return object.user_id===item.user_depart_leader_user_id})){
                    state.overtimeApplication.depart_leaderE = [...state.overtimeApplication.depart_leaderE, 
                        {
                            user_id:item.user_depart_leader_user_id, 
                            name: item.user_depart_leader_name,
                            role_id: "D003",
                            depart_id: item.user_depart_leader_depart_id,
                        }]
                }
            })
            let a = '';
            state.overtimeApplication.departListC.map((value:any)=>{
                a += value + ","
            })
            a = a.replace(/,\s*$/, "");
            a += '&';
            state.overtimeApplication.depart_leaderE.map((item:any)=>{
                a += item.user_id + ",";
            })
            let overDay = dayjs().format('YYYY-MM-DD') + '-21 00:00:00';
            let overDayEnd = dayjs().format('YYYY-MM-DD') + '-21 00:00:00';
            if(dayjs().format('YYYY-MM-DD') <= (dayjs().format('YYYY-MM') + '-21')){
                overDay = dayjs().subtract(1, 'month').format('YYYY-MM') + '-21 00:00:00';
                overDayEnd = dayjs().format('YYYY-MM') + '-21 00:00:00';
            }else{
                overDay = dayjs().format('YYYY-MM') + '-21 00:00:00';
                overDayEnd = dayjs().add(1, 'month').format('YYYY-MM') + '-21 00:00:00';
            }
            //删除字符串最后的逗号
            state.overtimeApplication.commitAdd.workflow_list = a.replace(/,\s*$/, "");
            state.overtimeApplication.day_type = action.payload.day_type;

            // 智能默认时间逻辑：根据当天类型设置默认时间
            const today = dayjs();
            const todayStr = today.format('YYYY-MM-DD');

            // 设置默认日期为当天
            state.overtimeApplication.startDay = today;
            state.overtimeApplication.endDay = today;
            state.overtimeApplication.endDayView = today;

            // 判断当天是平时还是周末
            const todayType = state.overtimeApplication.day_type.find((item:any) =>
                item.date.toString().slice(0,10) === todayStr
            );

            // 根据当天类型设置默认时间
            if (todayType && (todayType.type === 1 || todayType.type === 3)) {
                // 周末：9点到18点
                state.overtimeApplication.startHour = 9;
                state.overtimeApplication.startMinute = 0;
                state.overtimeApplication.endHour = 18;
                state.overtimeApplication.endMinute = 0;
            } else {
                // 平时：18点到22点
                state.overtimeApplication.startHour = 18;
                state.overtimeApplication.startMinute = 0;
                state.overtimeApplication.endHour = 22;
                state.overtimeApplication.endMinute = 0;
            }

            let checkDay = ''
            let count = 0
            let area = 0
            const day_type = state.overtimeApplication.day_type;
            day_type.map((item, index)=>{
                const date = item.date.replace('T', ' ')
                if(date > overDay && (item.type==0 || item.type==4)){
                    count++
                }
                if(count == (Number(action.payload.apply_forbid_day[0]?.apply_forbid_day))){
                    area = index
                }
            })
            let checkTime = action.payload.apply_forbid_time[0]?.apply_forbid_time;
            checkDay = day_type[area].date.slice(0,11) + checkTime;

            state.overtimeApplication.checkDay = checkDay;
        },
        // 处理开始日期变化
        overtimeHandleStartDateChange: (state: applicationState, action: PayloadAction<any>) => {
            if (action.payload) {
                state.overtimeApplication.startDay = action.payload;
            }
            state.overtimeApplication.endDayView = state.overtimeApplication.startDay.add(state.overtimeApplication.subDay,'day');
            if(state.overtimeApplication.endDayView != state.overtimeApplication.endDay){
                state.overtimeApplication.endDay = state.overtimeApplication.endDayView;
            }
            // state.overtimeApplication.isSpacialDay = isHoliday(state.overtimeApplication.startDay.toDate());
            let work_day:any[] = []
            const workDays = state.overtimeApplication.day_type.filter((item:any)=>(item.type==0 || item.type==4));
            workDays.map((item:any)=>{
                work_day.push(item.date.toString().slice(0,10))
            })
            let week_day:any[] = []
            const weekDays = state.overtimeApplication.day_type.filter((item:any)=>(item.type==1 || item.type==3));
            weekDays.map((item:any)=>{
                week_day.push(item.date.toString().slice(0,10))
            })
            let special_day:any[] = []
            const specialDays = state.overtimeApplication.day_type.filter((item:any)=>item.type==2);
            specialDays.map((item:any)=>{
                special_day.push(item.date.toString().slice(0,10))
            })
            let numlist:number[] = [0,0];
            numlist = overtimeSubtractTime(state.overtimeApplication.startDay, state.overtimeApplication.endDayView, state.overtimeApplication.startHour,
                 state.overtimeApplication.endHour, state.overtimeApplication.startMinute, state.overtimeApplication.endMinute, work_day, week_day, special_day);
            state.overtimeApplication.weekendTime = numlist[0];
            state.overtimeApplication.workDayTime = numlist[1];
            state.overtimeApplication.specialDayTime = numlist[2];

           //传递给api的数据
           state.overtimeApplication.commitAdd.start_time = state.overtimeApplication.startDay.format('YYYY-MM-DD') + ' ' +
                                                                (state.overtimeApplication.startHour<10?
                                                                '0'+state.overtimeApplication.startHour:
                                                                state.overtimeApplication.startHour) + ':' + 
                                                                (state.overtimeApplication.startMinute<10?
                                                                '0'+state.overtimeApplication.startMinute:
                                                                state.overtimeApplication.startMinute) + ':00';
            state.overtimeApplication.commitAdd.end_time = state.overtimeApplication.endDay.format('YYYY-MM-DD') + ' ' + 
                                                                (state.overtimeApplication.endHour<10?
                                                                '0'+state.overtimeApplication.endHour:
                                                                state.overtimeApplication.endHour) + ':' + 
                                                                (state.overtimeApplication.endMinute<10?
                                                                '0'+state.overtimeApplication.endMinute:
                                                                state.overtimeApplication.endMinute) + ':00';
        },
        // 处理开始小时变化
        overtimeHandleStartHourChange: (state: applicationState, action: PayloadAction<any>) => {
            let time = '00:00';
            if (action.payload) {
                if(action.payload=='24:00'){
                    time = '00:00';
                }else{
                    time = action.payload;
                }
                state.overtimeApplication.startHour = Number(time.slice(0,2));
                state.overtimeApplication.startMinute = Number(time.slice(3,5));
                if(action.payload=='24:00'){
                    state.overtimeApplication.startDay = state.overtimeApplication.startDay.add(1,'days');
                }
                let numlist:number[] = [0,0];
                let work_day:any[] = []
                const workDays = state.overtimeApplication.day_type.filter((item:any)=>(item.type==0 || item.type==4));
                workDays.map((item:any)=>{
                    work_day.push(item.date.toString().slice(0,10))
                })
                let week_day:any[] = []
                const weekDays = state.overtimeApplication.day_type.filter((item:any)=>(item.type==1 || item.type==3));
                weekDays.map((item:any)=>{
                    week_day.push(item.date.toString().slice(0,10))
                })
                let special_day:any[] = []
                const specialDays = state.overtimeApplication.day_type.filter((item:any)=>item.type==2);
                specialDays.map((item:any)=>{
                    special_day.push(item.date.toString().slice(0,10))
                })
                numlist = overtimeSubtractTime(state.overtimeApplication.startDay, state.overtimeApplication.endDayView, state.overtimeApplication.startHour,
                     state.overtimeApplication.endHour, state.overtimeApplication.startMinute, state.overtimeApplication.endMinute, work_day, week_day, special_day);
                state.overtimeApplication.weekendTime = numlist[0];
                state.overtimeApplication.workDayTime = numlist[1];
                state.overtimeApplication.specialDayTime = numlist[2];
            }

             //传递给api的数据
             state.overtimeApplication.commitAdd.start_time = state.overtimeApplication.startDay.format('YYYY-MM-DD') + ' ' +
                                                                (state.overtimeApplication.startHour<10?
                                                                '0'+state.overtimeApplication.startHour:
                                                                state.overtimeApplication.startHour) + ':' + 
                                                                (state.overtimeApplication.startMinute<10?
                                                                '0'+state.overtimeApplication.startMinute:
                                                                state.overtimeApplication.startMinute) + ':00';
             state.overtimeApplication.commitAdd.end_time = state.overtimeApplication.endDay.format('YYYY-MM-DD') + ' ' + 
                                                                (state.overtimeApplication.endHour<10?
                                                                '0'+state.overtimeApplication.endHour:
                                                                state.overtimeApplication.endHour) + ':' + 
                                                                (state.overtimeApplication.endMinute<10?
                                                                '0'+state.overtimeApplication.endMinute:
                                                                state.overtimeApplication.endMinute) + ':00';
        },
        // 处理结束日期变化
        overtimeHandleEndDateChange: (state: applicationState, action: PayloadAction<any>) => {
            if (action.payload) {
                state.overtimeApplication.endDay = action.payload;
                state.overtimeApplication.endDayView = action.payload;
                state.overtimeApplication.subDay = state.overtimeApplication.endDay.diff(state.overtimeApplication.startDay,'day');
                let numlist:number[] = [0,0,0];
                let work_day:any[] = []
                const workDays = state.overtimeApplication.day_type.filter((item:any)=>(item.type==0 || item.type==4));
                workDays.map((item:any)=>{
                    work_day.push(item.date.toString().slice(0,10))
                })
                let week_day:any[] = []
                const weekDays = state.overtimeApplication.day_type.filter((item:any)=>(item.type==1 || item.type==3));
                weekDays.map((item:any)=>{
                    week_day.push(item.date.toString().slice(0,10))
                })
                let special_day:any[] = []
                const specialDays = state.overtimeApplication.day_type.filter((item:any)=>item.type==2);
                specialDays.map((item:any)=>{
                    special_day.push(item.date.toString().slice(0,10))
                })
                numlist = overtimeSubtractTime(state.overtimeApplication.startDay, state.overtimeApplication.endDayView, state.overtimeApplication.startHour, 
                    state.overtimeApplication.endHour, state.overtimeApplication.startMinute, state.overtimeApplication.endMinute, work_day, week_day, special_day);
                state.overtimeApplication.weekendTime = numlist[0];
                state.overtimeApplication.workDayTime = numlist[1];
                state.overtimeApplication.specialDayTime = numlist[2];
            }
            
             //传递给api的数据
             state.overtimeApplication.commitAdd.start_time = state.overtimeApplication.startDay.format('YYYY-MM-DD') + ' ' +
                                                                (state.overtimeApplication.startHour<10?
                                                                '0'+state.overtimeApplication.startHour:
                                                                state.overtimeApplication.startHour) + ':' + 
                                                                (state.overtimeApplication.startMinute<10?
                                                                '0'+state.overtimeApplication.startMinute:
                                                                state.overtimeApplication.startMinute) + ':00';
             state.overtimeApplication.commitAdd.end_time = state.overtimeApplication.endDay.format('YYYY-MM-DD') + ' ' + 
                                                                (state.overtimeApplication.endHour<10?
                                                                '0'+state.overtimeApplication.endHour:
                                                                state.overtimeApplication.endHour) + ':' + 
                                                                (state.overtimeApplication.endMinute<10?
                                                                '0'+state.overtimeApplication.endMinute:
                                                                state.overtimeApplication.endMinute) + ':00';
        },
        // 处理结束小时变化
        overtimeHandleEndHourChange: (state: applicationState, action: PayloadAction<any>) => {
            let time = '00:00';
            if (action.payload) {
                if(action.payload=='24:00'){
                    time = '00:00';
                }else{
                    time = action.payload;
                }
                state.overtimeApplication.endHour = Number(time.slice(0,2));
                state.overtimeApplication.endMinute = Number(time.slice(3,5));
                if(action.payload=='24:00'){
                    state.overtimeApplication.endDayView = state.overtimeApplication.endDayView.add(1,'days');
                    state.overtimeApplication.endDay = state.overtimeApplication.endDayView;
                }
                let numlist:number[] = [0,0,0];
                let work_day:any[] = []
                const workDays = state.overtimeApplication.day_type.filter((item:any)=>(item.type==0 || item.type==4));
                workDays.map((item:any)=>{
                    work_day.push(item.date.toString().slice(0,10))
                })
                let week_day:any[] = []
                const weekDays = state.overtimeApplication.day_type.filter((item:any)=>(item.type==1 || item.type==3));
                weekDays.map((item:any)=>{
                    week_day.push(item.date.toString().slice(0,10))
                })
                let special_day:any[] = []
                const specialDays = state.overtimeApplication.day_type.filter((item:any)=>item.type==2);
                specialDays.map((item:any)=>{
                    special_day.push(item.date.toString().slice(0,10))
                })
                numlist = overtimeSubtractTime(state.overtimeApplication.startDay, state.overtimeApplication.endDayView, state.overtimeApplication.startHour,
                     state.overtimeApplication.endHour, state.overtimeApplication.startMinute, state.overtimeApplication.endMinute, work_day, week_day, special_day);
                state.overtimeApplication.weekendTime = numlist[0];
                state.overtimeApplication.workDayTime = numlist[1];
                state.overtimeApplication.specialDayTime = numlist[2];
            }

             //传递给api的数据
            state.overtimeApplication.commitAdd.start_time = state.overtimeApplication.startDay.format('YYYY-MM-DD') + ' ' +
                                                                (state.overtimeApplication.startHour<10?
                                                                '0'+state.overtimeApplication.startHour:
                                                                state.overtimeApplication.startHour) + ':' + 
                                                                (state.overtimeApplication.startMinute<10?
                                                                '0'+state.overtimeApplication.startMinute:
                                                                state.overtimeApplication.startMinute) + ':00';
            state.overtimeApplication.commitAdd.end_time = state.overtimeApplication.endDay.format('YYYY-MM-DD') + ' ' + 
                                                                (state.overtimeApplication.endHour<10?
                                                                '0'+state.overtimeApplication.endHour:
                                                                state.overtimeApplication.endHour) + ':' + 
                                                                (state.overtimeApplication.endMinute<10?
                                                                '0'+state.overtimeApplication.endMinute:
                                                                state.overtimeApplication.endMinute) + ':00';
             
        },
        // 选择项目负责人(新)
        handleOvertimeSelectedDeparts: (state: applicationState, action: PayloadAction<any>) => {
            const selectedValues = action.payload;
            let selectedIds = ''
            const selectedItems = selectedValues.map((item: any)=>{
                selectedIds += "$" + item[0]["user_id"] + "$";
                return item[0];
            });
            state.overtimeApplication.selectedDeparts = selectedItems;

            // ALL被选中 或者 超出3个, 则其他项目不可选 
            let options = [...state.overtimeApplication.departList]
            if(selectedItems?.length >= 3){
                options.map((item: any)=>{
                    if(selectedIds?.indexOf("$" + item["user_id"] + "$") >= 0){
                        item["disabled"] = false
                    }else{
                        item["disabled"] = true;
                    }
                    return item;
                });
            } else {
                options.map((item: any)=>{
                    item["disabled"] = false;
                    return item;
                });
            }
            state.overtimeApplication.departList = options;

            state.overtimeApplication.commitAdd.workflow_list = '';
            let depart = ''
            selectedValues.map((item:any)=>{
                if(!depart.includes(item[0]["depart_id"]+'$')){
                    depart += "$" + item[0]["depart_id"] + "$";
                }
            })
            state.overtimeApplication.depart_leaderE = [];
            state.overtimeApplication.depart_leader.map((item:any)=>{
                if(depart.includes("$" + item.depart_id + "$")){
                    state.overtimeApplication.depart_leaderE.push(item);
                }
            })
            state.overtimeApplication.developments_leaders.map((item:any)=>{
                if(!state.overtimeApplication.depart_leaderE.some((object:any)=>{return object.user_id===item.user_depart_leader_user_id})){
                    state.overtimeApplication.depart_leaderE = [...state.overtimeApplication.depart_leaderE, 
                        {
                            user_id:item.user_depart_leader_user_id, 
                            name: item.user_depart_leader_name,
                            role_id: "D003",
                            depart_id: item.user_depart_leader_depart_id,
                        }]
                }
            })
            let a = '';
            let b = '';
            state.overtimeApplication.depart_leaderE.map((item:any)=>{
                a += item.user_id + ",";
            })
            state.overtimeApplication.selectedDeparts.map((item:any)=>{
                b += item.user_id + ",";
            })

            const c = b.replace(/,\s*$/, "") + '&' + a;

            state.overtimeApplication.commitAdd.workflow_list = c.replace(/,\s*$/, "");

        },
        //当前项目审批选择栏内容
        overtimeHandleCascaderContent: (state: applicationState, action: PayloadAction<any>) => {
            state.overtimeApplication.cascaderContent[action.payload.number] = action.payload.value; 
            let a = false;
            const devName = state.overtimeApplication.cascaderContent[action.payload.number][0].toString();
            state.overtimeApplication.optionChooseDev.map(item=>{
                if(item.key == action.payload.number){
                    a = true;
                }
            })
            if(a){
                state.overtimeApplication.optionChooseDev.forEach((item) => {
                    if(item.key == action.payload.number){
                        item.value[0] = devName;
                    }
                }); 
            }else{
                state.overtimeApplication.optionChooseDev.push({
                    key:action.payload.number,
                    value:action.payload.value,
                });
            }
            let v:{
                value: string;
                label: string;
                children: {
                    value: string;
                    label: string;
                }[];
            }[] = [];
            state.overtimeApplication.optionChooseDev.forEach((item) => {
                state.overtimeApplication.developments.forEach((element)=> {
                    if(item.value[0] == element.value){
                        v.push(element);
                    }
                })
            })
            v = Array.from(new Set(v))
            state.overtimeApplication.developmentsChoose = v;
        },
        //当前项目审批选择栏的删除
        overtimeHandleDeleteCascader: (state: applicationState, action: PayloadAction<any>) => {
            state.overtimeApplication.cascaderContent.splice(action.payload-1,1);
            const newCascaderList = state.overtimeApplication.cascaderList.filter((item) => item.id !== action.payload);
            if(newCascaderList.length>0) {
                state.overtimeApplication.cascaderList = newCascaderList;
                state.overtimeApplication.cascaderCount = state.overtimeApplication.cascaderCount - 1;
                //更新id
                newCascaderList.forEach((item, index) => {
                    item.id = index + 1;
                });
            }
            state.overtimeApplication.optionChooseDev = state.overtimeApplication.optionChooseDev.filter((item)=>item.key!=action.payload-1);
            let v:{
                value: string;
                label: string;
                children: {
                    value: string;
                    label: string;
                }[];
            }[] = [];
            state.overtimeApplication.optionChooseDev.forEach((item) => {
                state.overtimeApplication.developments.forEach((element)=> {
                    if(item.value[0] == element.value){
                        v.push(element);
                    }
                })
            })
            v = Array.from(new Set(v))
            state.overtimeApplication.developmentsChoose = v;
        },
        //项目审批的选择栏添加
        overtimeHandleAddCascader: (state: applicationState) => {
            const newCascaderList = [...state.overtimeApplication.cascaderList, { id: state.overtimeApplication.cascaderList.length + 1 }];
            state.overtimeApplication.cascaderList = newCascaderList;
            state.overtimeApplication.cascaderCount = state.overtimeApplication.cascaderCount + 1;
        },
        //点击显示撤回栏
        overtimeHandleDeleteForm: (state: applicationState, action: PayloadAction<any>) => {
            if (state.overtimeApplication.selectedRowKey === action.payload) {
                state.overtimeApplication.isConfirmed = !state.overtimeApplication.isConfirmed;
            } else {
                state.overtimeApplication.selectedRowKey = action.payload;
                state.overtimeApplication.isConfirmed = true;
            }
        },
        //输入栏输入'我确认'
        overtimeHandleDeleteFormInput: (state: applicationState, action: PayloadAction<any>) => {
            state.overtimeApplication.confirmText = action.payload.target.value;
        },
        //一键输入'我确认'
        overtimeHandleDeleteAutoInput: (state: applicationState) => {
            state.overtimeApplication.confirmText = '我确认';
        },
        //点击撤回栏取消
        overtimeHandleDeleteCancel: (state: applicationState) => {
            state.overtimeApplication.confirmText = '';
            state.overtimeApplication.isConfirmed = false;
        },
        //撤回当前申请
        overtimeHandleDelete: (state: applicationState, action: PayloadAction<any>) => {
            const newData = state.overtimeApplication.datasource.filter(item => item.code !== action.payload.code);
            state.overtimeApplication.datasource = newData; 
            state.overtimeApplication.isConfirmed = false;
            state.overtimeApplication.confirmText = '';

            state.overtimeApplication.cancelCode = action.payload.code;
            const startIndex = (state.overtimeApplication.currentPage - 1) * state.overtimeApplication.pageSize;
            const endIndex = startIndex + state.overtimeApplication.pageSize;
            state.overtimeApplication.currentData = state.overtimeApplication.datasource.slice(startIndex, endIndex);
        },
        //点击后显示当前申请进度
        overtimeHandleRowDetailClick: (state: applicationState, action: PayloadAction<any>) => {
            if(state.overtimeApplication.selectedRowKeys.includes(action.payload)){
                state.overtimeApplication.selectedRowKeys = state.overtimeApplication.selectedRowKeys.filter((item)=>item!==action.payload)
            }else{
                state.overtimeApplication.selectedRowKeys.push(action.payload)
                state.overtimeApplication.selectedRowKey = action.payload;
            }
        },
        //点击后显示报销车费输入栏
        overtimeExpense: (state: applicationState, action: PayloadAction<any>) => {
            if(state.overtimeApplication.selectedExpenseKeys.includes(action.payload)){
                state.overtimeApplication.selectedExpenseKeys = state.overtimeApplication.selectedExpenseKeys.filter((item)=>item!==action.payload)
            }else{
                state.overtimeApplication.selectedExpenseKeys.push(action.payload)
                state.overtimeApplication.selectedExpenseKey = action.payload;
            }
        },
        //申请表格的显示判断
        overtimeHandleNewModule: (state: applicationState, action: PayloadAction<any>) => {
            state.overtimeApplication.showNew = false;
            state.overtimeApplication.show = true;

            // 智能默认时间逻辑：日期默认是当天，根据当天类型设置时间
            const today = dayjs();
            const todayStr = today.format('YYYY-MM-DD');

            // 设置默认日期为当天
            state.overtimeApplication.startDay = today;
            state.overtimeApplication.endDay = today;
            state.overtimeApplication.endDayView = today;

            // 判断当天是平时还是周末
            const todayType = state.overtimeApplication.day_type.find((item:any) =>
                item.date.toString().slice(0,10) === todayStr
            );

            // 根据当天类型设置默认时间
            if (todayType && (todayType.type === 1 || todayType.type === 3)) {
                // 周末：9点到18点
                state.overtimeApplication.startHour = 9;
                state.overtimeApplication.startMinute = 0;
                state.overtimeApplication.endHour = 18;
                state.overtimeApplication.endMinute = 0;
            } else {
                // 平时：18点到22点
                state.overtimeApplication.startHour = 18;
                state.overtimeApplication.startMinute = 0;
                state.overtimeApplication.endHour = 22;
                state.overtimeApplication.endMinute = 0;
            }

            let numlist:number[] = [0,0];
            let work_day:any[] = []
            const workDays = state.overtimeApplication.day_type.filter((item:any)=>(item.type==0 || item.type==4));
            workDays.map((item:any)=>{
                work_day.push(item.date.toString().slice(0,10))
            })
            let week_day:any[] = []
            const weekDays = state.overtimeApplication.day_type.filter((item:any)=>(item.type==1 || item.type==3));
            weekDays.map((item:any)=>{
                week_day.push(item.date.toString().slice(0,10))
            })
            let special_day:any[] = []
            const specialDays = state.overtimeApplication.day_type.filter((item:any)=>item.type==2);
            specialDays.map((item:any)=>{
                special_day.push(item.date.toString().slice(0,10))
            })
            numlist = overtimeSubtractTime(state.overtimeApplication.startDay, state.overtimeApplication.endDay, state.overtimeApplication.startHour,
                 state.overtimeApplication.endHour, state.overtimeApplication.startMinute, state.overtimeApplication.endMinute, work_day, week_day, special_day);
            state.overtimeApplication.weekendTime = numlist[0];
            state.overtimeApplication.workDayTime = numlist[1];
            state.overtimeApplication.specialDayTime = numlist[2];
            state.overtimeApplication.commitAdd.user_id = action.payload.id;
            state.overtimeApplication.commitAdd.start_time = state.overtimeApplication.startDay.format('YYYY-MM-DD') + ' ' +
                                                                (state.overtimeApplication.startHour<10?
                                                                '0'+state.overtimeApplication.startHour:
                                                                state.overtimeApplication.startHour) + ':' + 
                                                                (state.overtimeApplication.startMinute<10?
                                                                '0'+state.overtimeApplication.startMinute:
                                                                state.overtimeApplication.startMinute) + ':00';
            state.overtimeApplication.commitAdd.end_time = state.overtimeApplication.endDay.format('YYYY-MM-DD') + ' ' + 
                                                                (state.overtimeApplication.endHour<10?
                                                                '0'+state.overtimeApplication.endHour:
                                                                state.overtimeApplication.endHour) + ':' + 
                                                                (state.overtimeApplication.endMinute<10?
                                                                '0'+state.overtimeApplication.endMinute:
                                                                state.overtimeApplication.endMinute) + ':00';
            if(action.payload.role == '部门负责人' || action.payload.role == '高级管理层'){
                state.overtimeApplication.commitAdd.workflow_list = action.payload.id.toString();
            }
        },
        //请假原由
        overtimeHandleInputReasonChange: (state: applicationState, action: PayloadAction<any>) => {
            state.overtimeApplication.listValues = {
                ...state.overtimeApplication.listValues,
                reason: action.payload.target.value,
            };
            state.overtimeApplication.commitAdd.reason = action.payload.target.value;
        },
        //申请表格的取消(临时)
        overtimeHandleCancelModule: (state: applicationState) => {
            state.overtimeApplication.show = false;
            state.overtimeApplication.showNew = true;
            state.overtimeApplication.listValues.applicant = '';
        },
        //分页翻页
        handleOvertimePageChange: (state: applicationState, action: PayloadAction<any>) => {
            state.overtimeApplication.currentPage = action.payload;
            const startIndex = (state.overtimeApplication.currentPage - 1) * state.overtimeApplication.pageSize;
            const endIndex = startIndex + state.overtimeApplication.pageSize;
            state.overtimeApplication.currentData = state.overtimeApplication.datasource.slice(startIndex, endIndex);
        },
        applyOvertimeAdd: (state: applicationState, action: PayloadAction<any>) => {
            state.overtimeApplication.datasource.push({
                code: action.payload.apply_list.code,
                user_id: action.payload.apply_list.user_id,
                name: action.payload.apply_list.name,
                agent_user_id: action.payload.apply_list.agent_user_id,
                agent_name: action.payload.apply_list.agent_name,
                start_time: action.payload.apply_list.start_time,
                end_time: action.payload.apply_list.end_time,
                hours: action.payload.apply_list.hours,
                reason: action.payload.apply_list.reason,
                reject_reason: '',
                workflow_result: action.payload.apply_list.workflow_result,
                expense: action.payload.apply_list.expense,
            })

            state.overtimeApplication.datasource = state.overtimeApplication.datasource.filter(item=>item.code != '');

            const code = action.payload.apply_list.code;
            state.overtimeApplication.workflow_list[code] = action.payload.workflow_list[code];

            const startIndex = (state.overtimeApplication.currentPage - 1) * state.overtimeApplication.pageSize;
            const endIndex = startIndex + state.overtimeApplication.pageSize;
            state.overtimeApplication.currentData = state.overtimeApplication.datasource.slice(startIndex, endIndex);

            state.overtimeApplication.commitAdd.reason = '';

            state.overtimeApplication.showSelect = false;
            state.overtimeApplication.show = false;
            state.overtimeApplication.showNew = true;
        },
        applyOvertimeCancel: (state: applicationState, action: PayloadAction<any>) => {
            state.overtimeApplication.datasource = state.overtimeApplication.datasource.filter(item=> item.code != action.payload.apply_code);
            const startIndex = (state.overtimeApplication.currentPage - 1) * state.overtimeApplication.pageSize;
            const endIndex = startIndex + state.overtimeApplication.pageSize;
            state.overtimeApplication.currentData = state.overtimeApplication.datasource.slice(startIndex, endIndex);
            state.overtimeApplication.confirmText = '';

            state.overtimeApplication.showSelect = false;
            state.overtimeApplication.show = false;
            state.overtimeApplication.showNew = true;
        },


        //上下班确认单申请
        setConfirmationDataWhenInit: (state: applicationState, action: PayloadAction<any>) => {
            if(action.payload.apply_list!=0){
                state.confirmationApplication.datasource = action.payload.apply_list;
                state.confirmationApplication.workflow_list = action.payload.workflow_list;
                const startIndex = (state.confirmationApplication.currentPage - 1) * state.confirmationApplication.pageSize;
                const endIndex = startIndex + state.confirmationApplication.pageSize;
                state.confirmationApplication.currentData = state.confirmationApplication.datasource.slice(startIndex, endIndex);
            }else{
                state.confirmationApplication.datasource = [{
                    code: '',
                    user_id: '',
                    name: '',
                    agent_user_id: '',
                    agent_name: '',
                    day: '',
                    work_start_time: '',
                    work_end_time: '',
                    real_start_time: '',
                    real_end_time: '',
                    reason: '',
                    reject_reason: '',
                    workflow_result: '',
                }];
            }
            state.confirmationApplication.isConfirmed = false
        },
        setConfirmationAddInfInit: (state: applicationState, action: PayloadAction<any>) => {
            state.confirmationApplication.developments_leaders = action.payload.user_depart_leader_inf;
            state.confirmationApplication.worktime_list = action.payload.clock_time;
            state.confirmationApplication.commitAdd.day = state.confirmationApplication.startDay.format('YYYY-MM-DD') + ' 00:00:00';
            state.confirmationApplication.departList = action.payload.team_leader.map((item: any)=>{
                item.disabled = false;
                return item;
            });
            state.confirmationApplication.depart_leader = action.payload.depart_leader;
            state.confirmationApplication.departListC = [];
            action.payload.recently_team_leader.map((item:any)=>{
                const user_id = [item.user_id]
                state.confirmationApplication.departListC = [...state.confirmationApplication.departListC, user_id]
            })
            let depart = ''
            state.confirmationApplication.departListC.map((item:any)=>{
                state.confirmationApplication.departList.map((value:any)=>{
                    if(item[0] == value.user_id){
                        depart += "$" + value.depart_id + "$"
                    }
                })
            })
            state.confirmationApplication.depart_leaderE = [];
            state.confirmationApplication.depart_leader.map((item:any)=>{
                if(depart.includes("$" + item.depart_id + "$")){
                    state.confirmationApplication.depart_leaderE.push(item);
                }
            })
            state.confirmationApplication.developments_leaders.map((item:any)=>{
                if(!state.confirmationApplication.depart_leaderE.some((object:any)=>{return object.user_id===item.user_depart_leader_user_id})){
                    state.confirmationApplication.depart_leaderE = [...state.confirmationApplication.depart_leaderE, 
                        {
                            user_id:item.user_depart_leader_user_id, 
                            name: item.user_depart_leader_name,
                            role_id: "D003",
                            depart_id: item.user_depart_leader_depart_id,
                        }]
                }
            })
            let a = '';
            state.confirmationApplication.departListC.map((value:any)=>{
                a += value + ","
            })
            a = a.replace(/,\s*$/, "");
            a += '&';
            state.confirmationApplication.depart_leaderE.map((item:any)=>{
                a += item.user_id + ",";
            })
            let overDay = dayjs().format('YYYY-MM-DD') + '-21 00:00:00';
            let overDayEnd = dayjs().format('YYYY-MM-DD') + '-21 00:00:00';
            if(dayjs().format('YYYY-MM-DD') <= (dayjs().format('YYYY-MM') + '-21')){
                overDay = dayjs().subtract(1, 'month').format('YYYY-MM') + '-21 00:00:00';
                overDayEnd = dayjs().format('YYYY-MM') + '-21 00:00:00';
            }else{
                overDay = dayjs().format('YYYY-MM') + '-21 00:00:00';
                overDayEnd = dayjs().add(1, 'month').format('YYYY-MM') + '-21 00:00:00';
            }
            //删除字符串最后的逗号
            state.confirmationApplication.commitAdd.workflow_list = a.replace(/,\s*$/, "");
            state.confirmationApplication.day_type = action.payload.day_type;
            let checkDay = ''
            let count = 0
            let area = 0
            const day_type = state.confirmationApplication.day_type;
            day_type.map((item, index)=>{
                const date = item.date.replace('T', ' ')
                if(date > overDay && (item.type==0 || item.type==4)){
                    count++
                }
                if(count == (Number(action.payload.apply_forbid_day[0].apply_forbid_day))){
                    area = index
                }
            })
            let checkTime = action.payload.apply_forbid_time[0]?.apply_forbid_time;
            checkDay = day_type[area].date.slice(0,11) + checkTime;

            state.confirmationApplication.checkDay = checkDay;
        },
        confirmationHandleSelectShow: (state: applicationState) => {
            state.confirmationApplication.showSelect = true;
        },
        //撤销代人申请
        confirmationHandleSelectShowBack: (state: applicationState) => {
            state.confirmationApplication.showSelect = false;
        },
        // 处理开始日期变化
        confirmationHandleStartDateChange: (state: applicationState, action: PayloadAction<any>) => {
            if (action.payload) {
                state.confirmationApplication.startDay = action.payload;
                state.confirmationApplication.endDayView = state.confirmationApplication.startDay;
                state.confirmationApplication.endDay = state.confirmationApplication.endDayView;
                state.confirmationApplication.chooseWorkTime = state.confirmationApplication.worktime_list.filter(item=>item.day == action.payload.format('YYYY-MM-DD') + 'T00:00:00')[0];
                state.confirmationApplication.commitAdd.day = action.payload.format('YYYY-MM-DD') + ' 00:00:00';
                state.confirmationApplication.worktime_list.map((item:any,index:any)=> {
                    
                    if(item.day == action.payload.format('YYYY-MM-DD') + 'T00:00:00'){
                        if(item.work_start_time!=null){
                            state.confirmationApplication.commitAdd.work_start_time = item.work_start_time.replace('T',' ');
                            state.confirmationApplication.commitAdd.real_start_time = item.work_start_time.slice(0,10) + ' ' + state.confirmationApplication.startHour + ':00';
                        }else{
                            state.confirmationApplication.commitAdd.work_start_time = '';
                            state.confirmationApplication.commitAdd.real_start_time = item.day.slice(0,10) + ' ' + state.confirmationApplication.startHour + ':00';
                        }
                        if(item.work_end_time!=null){
                            state.confirmationApplication.commitAdd.work_end_time = item.work_end_time?.replace('T',' ');
                            state.confirmationApplication.commitAdd.real_end_time = item.work_end_time?.slice(0,10) + ' ' + state.confirmationApplication.endHour + ':00';
                        }else{
                            state.confirmationApplication.commitAdd.work_end_time = '';
                            state.confirmationApplication.commitAdd.real_end_time = item.day?.slice(0,10) + ' ' + state.confirmationApplication.endHour + ':00';
                        }
                    }else{
                        state.confirmationApplication.commitAdd.real_start_time = state.confirmationApplication.startDay.format('YYYY-MM-DD') + ' ' + state.confirmationApplication.startHour + ':00';
                        state.confirmationApplication.commitAdd.real_end_time = state.confirmationApplication.endDay.format('YYYY-MM-DD') + ' ' + state.confirmationApplication.endHour + ':00';
                    }
                })
            }
        },
        // 处理开始小时变化
        confirmationHandleStartHourChange: (state: applicationState, action: PayloadAction<any>) => {
            if (action.payload) {
                const hour = Number(action.payload.hour())<10?'0'+action.payload.hour():action.payload.hour();
                const minute = Number(action.payload.minute())<10?'0'+action.payload.minute():action.payload.minute();
                state.confirmationApplication.startHour = hour + ':' + minute;
                state.confirmationApplication.commitAdd.real_start_time = state.confirmationApplication.startDay.format('YYYY-MM-DD') + ' ' + state.confirmationApplication.startHour + ':00';
            }
        },
        // 处理结束日期变化
        confirmationHandleEndDateChange: (state: applicationState, action: PayloadAction<any>) => {
            if (action.payload) {
                state.confirmationApplication.endDay = action.payload;
                state.confirmationApplication.endDayView = action.payload;
                state.confirmationApplication.commitAdd.real_end_time = action.payload.format('YYYY-MM-DD') + ' ' + state.confirmationApplication.endHour + ':00';
            }
        },
        // 处理结束小时变化
        confirmationHandleEndHourChange: (state: applicationState, action: PayloadAction<any>) => {
            if (action.payload) {
                const hour = Number(action.payload.hour())<10?'0'+action.payload.hour():action.payload.hour();
                const minute = Number(action.payload.minute())<10?'0'+action.payload.minute():action.payload.minute();
                state.confirmationApplication.endHour = hour + ':' + minute;
                state.confirmationApplication.commitAdd.real_end_time = state.confirmationApplication.commitAdd.real_end_time.slice(0,10) + ' ' + state.confirmationApplication.endHour + ':00';
            }
        },
        // 选择项目负责人(新)
        handleConfirmSelectedDeparts: (state: applicationState, action: PayloadAction<any>) => {
            const selectedValues = action.payload;
            let selectedIds = ''
            const selectedItems = selectedValues.map((item: any)=>{
                selectedIds += "$" + item[0]["user_id"] + "$";
                return item[0];
            });
            state.confirmationApplication.selectedDeparts = selectedItems;

            // 超出3个, 则其他项目不可选 
            let options = [...state.confirmationApplication.departList]
            if(selectedItems?.length >= 3){
                options.map((item: any)=>{
                    if(selectedIds?.indexOf("$" + item["user_id"] + "$") >= 0){
                        item["disabled"] = false
                    }else{
                        item["disabled"] = true;
                    }
                    return item;
                });
            } else {
                options.map((item: any)=>{
                    item["disabled"] = false;
                    return item;
                });
            }
            state.confirmationApplication.departList = options;

            state.confirmationApplication.commitAdd.workflow_list = '';
            let depart = ''
            selectedValues.map((item:any)=>{
                if(!depart.includes(item[0]["depart_id"]+'$')){
                    depart += "$" + item[0]["depart_id"] + "$";
                }
            })
            state.confirmationApplication.depart_leaderE = [];
            state.confirmationApplication.depart_leader.map((item:any)=>{
                if(depart.includes("$" + item.depart_id + "$")){
                    state.confirmationApplication.depart_leaderE.push(item);
                }
            })
            state.confirmationApplication.developments_leaders.map((item:any)=>{
                if(!state.confirmationApplication.depart_leaderE.some((object:any)=>{return object.user_id===item.user_depart_leader_user_id})){
                    state.confirmationApplication.depart_leaderE = [...state.confirmationApplication.depart_leaderE, 
                        {
                            user_id:item.user_depart_leader_user_id, 
                            name: item.user_depart_leader_name,
                            role_id: "D003",
                            depart_id: item.user_depart_leader_depart_id,
                        }]
                }
            })
            let a = '';
            let b = '';
            state.confirmationApplication.depart_leaderE.map((item:any)=>{
                a += item.user_id + ",";
            })
            state.confirmationApplication.selectedDeparts.map((item:any)=>{
                b += item.user_id + ",";
            })
            const c = b.replace(/,\s*$/, "") + '&' + a;

            state.confirmationApplication.commitAdd.workflow_list = c.replace(/,\s*$/, "");

        },
        //当前项目审批选择栏内容
        confirmationHandleCascaderContent: (state: applicationState, action: PayloadAction<any>) => {
            state.confirmationApplication.cascaderContent[action.payload.number] = action.payload.value; 
            let a = false;
            const devName = state.confirmationApplication.cascaderContent[action.payload.number][0].toString();
            state.confirmationApplication.optionChooseDev.map(item=>{
                if(item.key == action.payload.number){
                    a = true;
                }
            })
            if(a){
                state.confirmationApplication.optionChooseDev.forEach((item) => {
                    if(item.key == action.payload.number){
                        item.value[0] = devName;
                    }
                }); 
            }else{
                state.confirmationApplication.optionChooseDev.push({
                    key:action.payload.number,
                    value:action.payload.value,
                });
            }
            let v:{
                value: string;
                label: string;
                children: {
                    value: string;
                    label: string;
                }[];
            }[] = [];
            state.confirmationApplication.optionChooseDev.forEach((item) => {
                state.confirmationApplication.developments.forEach((element)=> {
                    if(item.value[0] == element.value){
                        v.push(element);
                    }
                })
            })
            v = Array.from(new Set(v))
            state.confirmationApplication.developmentsChoose = v;
        },
        //当前项目审批选择栏的删除
        confirmationHandleDeleteCascader: (state: applicationState, action: PayloadAction<any>) => {
            state.confirmationApplication.cascaderContent.splice(action.payload-1,1);
            const newCascaderList = state.confirmationApplication.cascaderList.filter((item) => item.id !== action.payload);
            if(newCascaderList.length>0) {
                state.confirmationApplication.cascaderList = newCascaderList;
                state.confirmationApplication.cascaderCount = state.confirmationApplication.cascaderCount - 1;
                //更新id
                newCascaderList.forEach((item, index) => {
                    item.id = index + 1;
                });
            }
            state.confirmationApplication.optionChooseDev = state.confirmationApplication.optionChooseDev.filter((item)=>item.key!=action.payload-1);
            let v:{
                value: string;
                label: string;
                children: {
                    value: string;
                    label: string;
                }[];
            }[] = [];
            state.confirmationApplication.optionChooseDev.forEach((item) => {
                state.confirmationApplication.developments.forEach((element)=> {
                    if(item.value[0] == element.value){
                        v.push(element);
                    }
                })
            })
            v = Array.from(new Set(v))
            state.confirmationApplication.developmentsChoose = v;
        },
        //项目审批的选择栏添加
        confirmationHandleAddCascader: (state: applicationState) => {
            const newCascaderList = [...state.confirmationApplication.cascaderList, { id: state.confirmationApplication.cascaderList.length + 1 }];
            state.confirmationApplication.cascaderList = newCascaderList;
            state.confirmationApplication.cascaderCount = state.confirmationApplication.cascaderCount + 1;
        },
        //点击显示撤回栏
        confirmationHandleDeleteForm: (state: applicationState, action: PayloadAction<any>) => {
            if (state.confirmationApplication.selectedRowKey === action.payload) {
                state.confirmationApplication.isConfirmed = !state.confirmationApplication.isConfirmed;
            } else {
                state.confirmationApplication.selectedRowKey = action.payload;
                state.confirmationApplication.isConfirmed = true;
            }
        },
        //输入栏输入'我确认'
        confirmationHandleDeleteFormInput: (state: applicationState, action: PayloadAction<any>) => {
            state.confirmationApplication.confirmText = action.payload.target.value;
        },
        //一键输入'我确认'
        confirmationHandleDeleteAutoInput: (state: applicationState) => {
            state.confirmationApplication.confirmText = '我确认';
        },
        //点击显示撤回栏
        confirmationHandleDeleteCancel: (state: applicationState) => {
            state.confirmationApplication.confirmText = '';
            state.confirmationApplication.isConfirmed = false;
        },
        //撤回当前申请
        confirmationHandleDelete: (state: applicationState, action: PayloadAction<any>) => {
            const newData = state.confirmationApplication.datasource.filter(item => item.code !== action.payload);
            state.confirmationApplication.datasource = newData; 
            state.confirmationApplication.isConfirmed = false;
            state.confirmationApplication.confirmText = '';

            const startIndex = (state.confirmationApplication.currentPage - 1) * state.confirmationApplication.pageSize;
            const endIndex = startIndex + state.confirmationApplication.pageSize;
            state.confirmationApplication.currentData = state.confirmationApplication.datasource.slice(startIndex, endIndex);
        },
        //点击后显示当前申请进度
        confirmationHandleRowDetailClick: (state: applicationState, action: PayloadAction<any>) => {
            if(state.confirmationApplication.selectedRowKeys.includes(action.payload)){
                state.confirmationApplication.selectedRowKeys = state.confirmationApplication.selectedRowKeys.filter((item)=>item!==action.payload)
            }else{
                state.confirmationApplication.selectedRowKeys.push(action.payload)
                state.confirmationApplication.selectedRowKey = action.payload;
            }
        },
        //申请表格的显示判断
        confirmationHandleNewModule: (state: applicationState, action: PayloadAction<any>) => {
            state.confirmationApplication.showNew = false;
            state.confirmationApplication.show = true;
            state.confirmationApplication.commitAdd.user_id = action.payload.id;
            state.confirmationApplication.worktime_list.map((item=>{
                if(item.day.slice(0,10) == state.confirmationApplication.startDay.format('YYYY-MM-DD')){
                    if(item.work_start_time?.replace('T', ' ')){
                        state.confirmationApplication.commitAdd.work_start_time = item.work_start_time?.replace('T', ' ');
                    }else{
                        state.confirmationApplication.commitAdd.work_start_time = ''
                    }
                    if(item.work_end_time?.replace('T', ' ')){
                        state.confirmationApplication.commitAdd.work_end_time = item.work_end_time?.replace('T', ' ');
                    }else{
                        state.confirmationApplication.commitAdd.work_end_time = ''
                    }
                }
            }))
            if(action.payload.role == '部门负责人' || action.payload.role == '高级管理层'){
                state.confirmationApplication.commitAdd.workflow_list = action.payload.id.toString();
            }
        },
        //代理人
        confirmationHandleSelectPeopleChange: (state: applicationState, action: PayloadAction<any>) => {
            state.confirmationApplication.listValues = {
                ...state.confirmationApplication.listValues,
                applicant: action.payload,
            };
        },
        //事由
        confirmationHandleInputReasonChange: (state: applicationState,  action: PayloadAction<any>) => {
            state.confirmationApplication.listValues = {
                ...state.confirmationApplication.listValues,
                reason: action.payload.target.value,
            }
            state.confirmationApplication.commitAdd.reason = action.payload.target.value;
        },
  
        //申请表格的提交(临时)
        confirmationHandleSubmitModule: (state: applicationState, action: PayloadAction<any>) => {
            let newList = [...state.confirmationApplication.datasource];
            if(newList[0]?.code==''){
                newList = [];
            }
            const formattedStart = formatDate(state.confirmationApplication.startDay);
            const formattedEnd = formatDate(state.confirmationApplication.endDay);
            const start = formattedStart + getWeekday(state.confirmationApplication.startDay.toDate());
            const end = formattedEnd + getWeekday(state.confirmationApplication.endDay.toDate());
            let secondDay:string = '';
            if(start==end){
                secondDay = '';
            }else{
                secondDay = end;
            }
            let inputApplicant = state.confirmationApplication.listValues.applicant;
            let inputReason = state.confirmationApplication.listValues.reason;
            if(!state.confirmationApplication.showSelect){
            inputApplicant = '';
            }
            if(1){
                newList.push(
                    {
                        code: newList.length + 1,
                        user_id: 0,
                        name: action.payload,
                        agent_user_id: 0,
                        agent_name: inputApplicant,
                        day: formattedStart.replaceAll('/','-') + "T00:00:00",
                        work_start_time: formattedStart.replaceAll('/','-') + "T09:00:00",
                        work_end_time: formattedStart.replaceAll('/','-') + "T18:00:00",
                        real_start_time: formattedStart.replaceAll('/','-') + "T" + state.confirmationApplication.startHour + ":00",
                        real_end_time: formattedEnd.replaceAll('/','-') + "T" + state.confirmationApplication.endHour + ":00",
                        reason: inputReason,
                        reject_reason: '',
                        workflow_result: 0,
                    }
                )
            }

            state.confirmationApplication.datasource = newList;
            state.confirmationApplication.show = false;
            state.confirmationApplication.showNew = true;

            const startIndex = (state.confirmationApplication.currentPage - 1) * state.confirmationApplication.pageSize;
            const endIndex = startIndex + state.confirmationApplication.pageSize;
            state.confirmationApplication.currentData = state.confirmationApplication.datasource.slice(startIndex, endIndex);
        },
        //申请表格的取消(临时)
        confirmationHandleCancelModule: (state: applicationState) => {
            state.confirmationApplication.show = false;
            state.confirmationApplication.showNew = true;
        },
        //分页翻页
        handleConfirmationPageChange: (state: applicationState, action: PayloadAction<any>) => {
            state.confirmationApplication.currentPage = action.payload;
            const startIndex = (state.confirmationApplication.currentPage - 1) * state.confirmationApplication.pageSize;
            const endIndex = startIndex + state.confirmationApplication.pageSize;
            state.confirmationApplication.currentData = state.confirmationApplication.datasource.slice(startIndex, endIndex);
        },

        applyEditAdd: (state: applicationState, action: PayloadAction<any>) => {
            state.confirmationApplication.datasource.push({
                code: action.payload.apply_list.code,
                user_id: action.payload.apply_list.user_id,
                name: action.payload.apply_list.name,
                agent_user_id: action.payload.apply_list.agent_user_id,
                agent_name: action.payload.apply_list.agent_name,
                day: action.payload.apply_list.day,
                work_start_time: action.payload.apply_list.work_start_time,
                work_end_time: action.payload.apply_list.work_end_time,
                real_start_time: action.payload.apply_list.real_start_time,
                real_end_time: action.payload.apply_list.real_end_time,
                reason: action.payload.apply_list.reason,
                reject_reason: action.payload.apply_list.reject_reason,
                workflow_result: action.payload.apply_list.workflow_result,
            })

            state.confirmationApplication.datasource = state.confirmationApplication.datasource.filter(item=>item.code != '');

            state.confirmationApplication.workflow_list[action.payload.apply_list.code] = (action.payload.workflow_list[action.payload.apply_list.code])

            const startIndex = (state.confirmationApplication.currentPage - 1) * state.confirmationApplication.pageSize;
            const endIndex = startIndex + state.confirmationApplication.pageSize;
            state.confirmationApplication.currentData = state.confirmationApplication.datasource.slice(startIndex, endIndex);

            state.confirmationApplication.commitAdd.reason = '';

            state.confirmationApplication.showSelect = false;
            state.confirmationApplication.show = false;
            state.confirmationApplication.showNew = true;
        },
        applyEditCancel: (state: applicationState, action: PayloadAction<any>) => {
            state.confirmationApplication.datasource = state.confirmationApplication.datasource.filter(item=> item.code != action.payload.apply_code);
            const startIndex = (state.confirmationApplication.currentPage - 1) * state.confirmationApplication.pageSize;
            const endIndex = startIndex + state.confirmationApplication.pageSize;
            state.confirmationApplication.currentData = state.confirmationApplication.datasource.slice(startIndex, endIndex);
            state.confirmationApplication.isConfirmed = false;
            state.confirmationApplication.confirmText = '';
        },
        handleLeaveWorkflow: (state: applicationState, action: PayloadAction<any>) => {
            const code = action.payload.APPLY_CODE;
            state.leaveApplication.datasource = state.leaveApplication.datasource.filter(item=>item.code!=code);
            state.leaveApplication.datasource.push({
                code: action.payload.apply_list.code,
                user_id: action.payload.apply_list.user_id,
                name: action.payload.apply_list.name,
                agent_user_id: action.payload.apply_list.agent_user_id,
                agent_name: action.payload.apply_list.agent_name,
                start_time: action.payload.apply_list.start_time,
                end_time: action.payload.apply_list.end_time,
                normal_hours: action.payload.apply_list.normal_hours,
                compensatory_hours: action.payload.apply_list.compensatory_hours,
                sick_hours: action.payload.apply_list.sick_hours,
                other_days: action.payload.apply_list.other_days,
                other_apply_type: action.payload.apply_list.other_apply_type,
                reason: action.payload.apply_list.reason,
                reject_reason: action.payload.apply_list.reject_reason,
                workflow_result: action.payload.apply_list.workflow_result,
            })
            const startIndex = (state.leaveApplication.currentPage - 1) * state.leaveApplication.pageSize;
            const endIndex = startIndex + state.leaveApplication.pageSize;
            state.leaveApplication.currentData = state.leaveApplication.datasource.slice(startIndex, endIndex);
            const workflow_list = action.payload.workflow_list[code];
            if(state.leaveApplication.workflow_list){
                state.leaveApplication.workflow_list[code] = workflow_list;
            }
        },
        handleOvertimeWorkflow: (state: applicationState, action: PayloadAction<any>) => {
            const code = action.payload.APPLY_CODE;
            state.overtimeApplication.datasource = state.overtimeApplication.datasource.filter(item=>item.code!=code);
            state.overtimeApplication.datasource.push({
                code: action.payload.apply_list.code,
                user_id: action.payload.apply_list.user_id,
                name: action.payload.apply_list.name,
                agent_user_id: action.payload.apply_list.agent_user_id,
                agent_name: action.payload.apply_list.agent_name,
                start_time: action.payload.apply_list.start_time,
                end_time: action.payload.apply_list.end_time,
                hours: action.payload.apply_list.hours,
                reason: action.payload.apply_list.reason,
                reject_reason: action.payload.apply_list.reject_reason,
                workflow_result: action.payload.apply_list.workflow_result,
                expense: action.payload.expense,
            })
            const startIndex = (state.overtimeApplication.currentPage - 1) * state.overtimeApplication.pageSize;
            const endIndex = startIndex + state.overtimeApplication.pageSize;
            state.overtimeApplication.currentData = state.overtimeApplication.datasource.slice(startIndex, endIndex);
            const workflow_list = action.payload.workflow_list[code];
            if(state.overtimeApplication.workflow_list){
                state.overtimeApplication.workflow_list[code] = workflow_list;
            }
        },
        handleTripWorkflow: (state: applicationState, action: PayloadAction<any>) => {
            const code = action.payload.APPLY_CODE;
            state.businessTripApplication.datasource = state.businessTripApplication.datasource.filter(item=>item.code!=code);
            state.businessTripApplication.datasource.push({
                code: action.payload.apply_list.code,
                user_id: action.payload.apply_list.user_id,
                name: action.payload.apply_list.name,
                agent_user_id: action.payload.apply_list.agent_user_id,
                agent_name: action.payload.apply_list.agent_name,
                start_time: action.payload.apply_list.start_time,
                end_time: action.payload.apply_list.end_time,
                location: action.payload.apply_list.location,
                t_days: action.payload.apply_list.t_days,
                reason: action.payload.apply_list.reason,
                reject_reason: action.payload.apply_list.reject_reason,
                workflow_result: action.payload.apply_list.workflow_result,
            })
            const startIndex = (state.businessTripApplication.currentPage - 1) * state.businessTripApplication.pageSize;
            const endIndex = startIndex + state.businessTripApplication.pageSize;
            state.businessTripApplication.currentData = state.businessTripApplication.datasource.slice(startIndex, endIndex);
            const workflow_list = action.payload.workflow_list[code];
            if(state.businessTripApplication.workflow_list){
                state.businessTripApplication.workflow_list[code] = workflow_list;
            }
        },
        handleEditWorkflow: (state: applicationState, action: PayloadAction<any>) => {
            const code = action.payload.APPLY_CODE;
            state.confirmationApplication.datasource = state.confirmationApplication.datasource.filter(item=>item.code!=code);
            state.confirmationApplication.datasource.push({
                code: action.payload.apply_list.code,
                user_id: action.payload.apply_list.user_id,
                name: action.payload.apply_list.name,
                agent_user_id: action.payload.apply_list.agent_user_id,
                agent_name: action.payload.apply_list.agent_name,
                day: action.payload.apply_list.day,
                work_start_time: action.payload.apply_list.work_start_time,
                work_end_time: action.payload.apply_list.work_end_time,
                real_start_time: action.payload.apply_list.real_start_time,
                real_end_time: action.payload.apply_list.real_end_time,
                reason: action.payload.apply_list.reason,
                reject_reason: action.payload.apply_list.reject_reason,
                workflow_result: action.payload.apply_list.workflow_result,
            })
            const startIndex = (state.confirmationApplication.currentPage - 1) * state.confirmationApplication.pageSize;
            const endIndex = startIndex + state.confirmationApplication.pageSize;
            state.confirmationApplication.currentData = state.confirmationApplication.datasource.slice(startIndex, endIndex);
            const workflow_list = action.payload.workflow_list[code];
            if(state.confirmationApplication.workflow_list){
                state.confirmationApplication.workflow_list[code] = workflow_list;
            }
        },
        //批量
        handleLeaveWorkflowList: (state: applicationState, action: PayloadAction<any>) => {
            const code_list = action.payload.code_list;
            if(state.leaveApplication.workflow_list){
                for(let i = 0; i<code_list.length; i++){
                    state.leaveApplication.workflow_list[code_list[i]] = action.payload.workflow_list[code_list[i]];
                }
            }
        },
        handleOvertimeWorkflowList: (state: applicationState, action: PayloadAction<any>) => {
            const code_list = action.payload.code_list;
            if(state.overtimeApplication.workflow_list){
                for(let i = 0; i<code_list.length; i++){
                    state.overtimeApplication.workflow_list[code_list[i]] = action.payload.workflow_list[code_list[i]];
                }
            }
        },
        handleTripWorkflowList: (state: applicationState, action: PayloadAction<any>) => {
            const code_list = action.payload.code_list;
            if(state.businessTripApplication.workflow_list){
                for(let i = 0; i<code_list.length; i++){
                    state.businessTripApplication.workflow_list[code_list[i]] = action.payload.workflow_list[code_list[i]];
                }
            }
        },
        handleEditWorkflowList: (state: applicationState, action: PayloadAction<any>) => {
            const code_list = action.payload.code_list;
            if(state.confirmationApplication.workflow_list){
                for(let i = 0; i<code_list.length; i++){
                    state.confirmationApplication.workflow_list[code_list[i]] = action.payload.workflow_list[code_list[i]];
                }
            }
        },
        applyOvertimeExpense: (state: applicationState, action: PayloadAction<any>) => {
            let code = '';
            let expense = '0.0'
            if(action.payload){
                code = action.payload.code;
                expense = action.payload.expense;
            }
            state.overtimeApplication.datasource = state.overtimeApplication.datasource.map((item:any)=>{
                if(item.code == code){
                    item.expense = expense;
                }
                return item
            })
            const startIndex = (state.overtimeApplication.currentPage - 1) * state.overtimeApplication.pageSize;
            const endIndex = startIndex + state.overtimeApplication.pageSize;
            state.overtimeApplication.currentData = state.overtimeApplication.datasource.slice(startIndex, endIndex);
        }
    },
});

//以下内容必须要有
export const { actions: applicationActions } = applicationSlice;

export default applicationSlice.reducer;

//state 后面的为store中数据名称
export const applicationData = (state: RootState) => state.application;