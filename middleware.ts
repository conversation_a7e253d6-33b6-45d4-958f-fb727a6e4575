import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { key_for_token, key_for_token_aes } from './src/utils/cookies'
import { is_login } from './src/utils/login'
import { redirectConfig } from './src/utils/redirectto'

// This function can be marked `async` if using `await` inside
export function middleware(request: NextRequest) {
    let redirect_to_value = redirectConfig.continue.value
    // 登录关联处理 开始

    //cookie登录判断
    let token = null
    const has_token = request.cookies.has(key_for_token)
    if (has_token) {
        token = request.cookies.get(key_for_token)?.value
    }
    let token_aes = null
    const has_token_aes = request.cookies.has(key_for_token_aes)
    if (has_token_aes) {
        token_aes = request.cookies.get(key_for_token_aes)?.value
    }

    const logined: boolean = is_login(token, token_aes)

    console.log(logined)
    console.log(request.nextUrl.pathname)
    if (!logined) {
        //当前没有登录 或 登录已失效
        if (redirectConfig.pass_forget.linkTo == request.nextUrl.pathname) {
            //进入重置密码页面
            redirect_to_value = redirectConfig.continue.value
        } else if (redirectConfig.login.linkTo != request.nextUrl.pathname) {
            //加载的页面不是Login画面，迁移到Login
            redirect_to_value = redirectConfig.login.value
        } else {
            // 继续加载
            redirect_to_value = redirectConfig.continue.value
        }
    } else {
        // 当前在登录有效期内
        if ('/' == request.nextUrl.pathname || redirectConfig.login.linkTo == request.nextUrl.pathname) {
            //加载的页面是Login画面，迁移到用户信息页面
            redirect_to_value = redirectConfig.user_info.value
        } else {
            // 继续加载
            redirect_to_value = redirectConfig.continue.value
        }
    }
    console.log(redirect_to_value)
    // 登录关联处理 结束

    //跳转 开始
    let response = null
    switch (redirect_to_value) {
        case redirectConfig.login.value:
            response = NextResponse.redirect(new URL(redirectConfig.login.linkTo, request.url))
            break
        case redirectConfig.user_info.value:
            response = NextResponse.redirect(new URL(redirectConfig.user_info.linkTo, request.url))
            break
        case redirectConfig.pass_forget.value:
            response = NextResponse.redirect(new URL(redirectConfig.pass_forget.linkTo, request.url))
            break
        case redirectConfig.continue.value:
        default:
            response = NextResponse.next()
    }

    return response

    //跳转 结束
}

export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - api (API routes)
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         */
        '/((?!api|image|_next/static|_next/image|favicon.ico).*)',
    ],
}