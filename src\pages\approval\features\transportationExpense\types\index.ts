// 交通费审批相关类型定义

import { TransportationExpenseApplication } from '../../../application/features/transportationExpense/types';

/**
 * 审批状态枚举
 */
export enum ApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled'
}

/**
 * 审批操作枚举
 */
export enum ApprovalAction {
  APPROVE = 'approve',
  REJECT = 'reject',
  RETURN = 'return'
}

/**
 * 审批记录
 */
export interface ApprovalRecord {
  id: string;
  application_code: string;        // 申请代码
  approver_id: number;             // 审批人ID
  approver_name: string;           // 审批人姓名
  action: ApprovalAction;          // 审批操作
  comment?: string;                // 审批意见
  approved_at: string;             // 审批时间
  created_at: string;              // 创建时间
}

/**
 * 待审批申请
 */
export interface PendingApproval extends TransportationExpenseApplication {
  submitted_at: string;            // 提交时间
  applicant_department: string;    // 申请人部门
  applicant_position: string;      // 申请人职位
  priority: 'low' | 'normal' | 'high'; // 优先级
  approval_history: ApprovalRecord[]; // 审批历史
}

/**
 * 审批请求参数
 */
export interface ApprovalRequest {
  application_code: string;
  action: ApprovalAction;
  comment?: string;
}

/**
 * 批量审批请求参数
 */
export interface BatchApprovalRequest {
  application_codes: string[];
  action: ApprovalAction;
  comment?: string;
}

/**
 * 获取待审批列表请求参数
 */
export interface GetPendingApprovalsRequest {
  page?: number;
  page_size?: number;
  status?: ApprovalStatus;
  department?: string;
  date_from?: string;
  date_to?: string;
  applicant_name?: string;
}

/**
 * 获取待审批列表响应
 */
export interface GetPendingApprovalsResponse {
  status: 'OK' | 'ERROR';
  approvals: PendingApproval[];
  total: number;
  page: number;
  page_size: number;
  message?: string;
}

/**
 * 审批响应
 */
export interface ApprovalResponse {
  status: 'OK' | 'ERROR';
  message: string;
  approval_id?: string;
}

/**
 * 批量审批响应
 */
export interface BatchApprovalResponse {
  status: 'OK' | 'ERROR';
  message: string;
  success_count: number;
  failed_count: number;
  failed_applications?: string[];
}

/**
 * 审批统计
 */
export interface ApprovalStatistics {
  total_pending: number;           // 待审批总数
  total_approved: number;          // 已审批总数
  total_rejected: number;          // 已拒绝总数
  avg_approval_time: number;       // 平均审批时间（小时）
  pending_by_department: Record<string, number>; // 各部门待审批数量
  approval_rate: number;           // 审批通过率
}

/**
 * 审批人信息
 */
export interface ApproverInfo {
  user_id: number;
  work_no: string;
  name: string;
  department: string;
  position: string;
  approval_level: number;          // 审批级别
  can_approve_departments: string[]; // 可审批的部门
}

/**
 * 审批流程配置
 */
export interface ApprovalWorkflow {
  id: string;
  name: string;
  department: string;
  steps: ApprovalStep[];
  is_active: boolean;
}

/**
 * 审批步骤
 */
export interface ApprovalStep {
  step_order: number;
  approver_role: string;
  approver_ids: number[];
  is_required: boolean;
  timeout_hours?: number;
}
