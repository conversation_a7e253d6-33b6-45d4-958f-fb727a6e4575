export interface User {
  id: string;
  name: string;
  nameKana: string;
  email: string;
  department: string;
  position: string;
  employeeId: string;
}

export interface LoginForm {
  employeeId: string;
  password: string;
}

export interface ForgotPasswordForm {
  employeeId: string;
  email: string;
}

export interface VerificationForm {
  code: string;
}

export interface ResetPasswordForm {
  newPassword: string;
  confirmPassword: string;
}

export type AuthStep = 'login' | 'forgot-password' | 'verification' | 'reset-password';