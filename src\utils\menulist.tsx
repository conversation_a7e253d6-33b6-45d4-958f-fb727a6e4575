import { permissionsGroups } from "./permissions";

// 定义翻译函数类型
type TFunction = (key: string) => string;

export const menuTypes = {
    profile: "profile",
    record: "record",
    apply: "apply",
    approvel: "approvel",
    member: "member",
    report: "report",
    depart: "depart",
    history: "history",
    setting: "setting",
}

// 定义菜单项 - 函数式配置，支持多语言
export const getMenuConfig = (t: TFunction) => [
    {
        title: t('menu:profile.title'),
        type: menuTypes.profile,
        list:[
            {index:"1", text: t('menu:profile.myInformation'), linkTo:"/my/information", pCode: permissionsGroups.GROUP_CARD_RECORDS.code + permissionsGroups.GROUP_CARD_RECORDS.persmissions[1].code},
        ]
    },
    {
        title: t('menu:attendance.title'),
        type: menuTypes.record,
        list:[
            {index:"1", text: t('menu:attendance.myAttendance'), linkTo:"/attendance/my", pCode: permissionsGroups.GROUP_CARD_RECORDS.code + permissionsGroups.GROUP_CARD_RECORDS.persmissions[1].code},
        ]
    },
    {
        title: t('menu:application.title'),
        type: menuTypes.apply,
        list:[
            {index:"1", text: t('menu:application.transportationExpense'), linkTo:"/application/transportationExpense", pCode: permissionsGroups.GROUP_APPLYS.code + permissionsGroups.GROUP_APPLYS.persmissions[1].code},
        ]
    },
    {
        title: t('menu:approval.title'),
        type: menuTypes.approvel,
        list:[
            {index:"1", text: t('menu:approval.transportationExpense'), linkTo:"/approval/transportationExpense", pCode: permissionsGroups.GROUP_APPROVAL.code + permissionsGroups.GROUP_APPROVAL.persmissions[1].code},
        ]
    },
    // {
    //     title: t('menu:members.title'),
    //     type: menuTypes.member,
    //     list:[
    //         {index:"1", text: t('menu:members.departmentMembers'), linkTo:"/mem/dep", pCode: permissionsGroups.GROUP_MEMBERS.code + permissionsGroups.GROUP_MEMBERS.persmissions[1].code},
    //         {index:"2", text: t('menu:members.organizationMembers'), linkTo:"/mem/org", pCode: permissionsGroups.GROUP_MEMBERS.code + permissionsGroups.GROUP_MEMBERS.persmissions[4].code},
    //     ]
    // },
    {
        title: t('menu:statistics.title'),
        type: menuTypes.report,
        list:[
            {index:"1", text: t('menu:statistics.departmentStatistics'), linkTo:"/statistics/department", pCode: permissionsGroups.GROUP_REPORTS.code + permissionsGroups.GROUP_REPORTS.persmissions[3].code},
        ]
    },
];

// 为了向后兼容，保留原有的 menuConfig 导出（使用默认中文）
export const menuConfig = getMenuConfig((key: string) => {
    // 简单的中文回退函数
    const fallbackTranslations: { [key: string]: string } = {
        'menu:profile.title': '我的',
        'menu:profile.myInformation': '我的信息',
        'menu:profile.myPaidLeave': '我的有休',
        'menu:attendance.title': '考勤',
        'menu:attendance.myAttendance': '我的考勤',
        'menu:application.title': '申请',
        'menu:application.transportationExpense': '交通费申请',
        'menu:approval.title': '审批',
        'menu:approval.transportationExpense': '交通费精算个人承认',
        'menu:members.title': '成员管理',
        'menu:members.departmentMembers': '部门成员',
        'menu:members.organizationMembers': '组织成员',
        'menu:statistics.title': '数据统计',
        'menu:statistics.departmentStatistics': '部门级数据统计',
        'menu:settings.title': '系统设定',
        'menu:settings.holidaySettings': '节假日设定',
        'menu:settings.otherSettings': '其他设定',
    };
    return fallbackTranslations[key] || key;
});

export function getTabMenuByType(type: string, t?: TFunction){
    const config = t ? getMenuConfig(t) : menuConfig;
    let tabMenu = {}
    for(let i = 0; i< config.length; i++){
        if(config[i].type == type){
            tabMenu = config[i]
            break
        }
    }
    return tabMenu
}