/// <reference types="vite/client" />

/**
 * Vite 环境变量类型定义
 */
interface ImportMetaEnv {
  readonly MODE: string
  readonly BASE_URL: string
  readonly PROD: boolean
  readonly DEV: boolean
  readonly SSR: boolean

  // 自定义环境变量
  readonly VITE_API_BASE_URL?: string
  readonly VITE_APP_ENV?: string
  readonly VITE_APP_TITLE?: string
  readonly VITE_DEBUG?: string
  readonly VITE_DEFAULT_LOCALE?: string
  readonly VITE_SUPPORTED_LOCALES?: string
  readonly VITE_ENABLE_MOCK?: string
  readonly VITE_MAX_FILE_SIZE?: string
  readonly VITE_SESSION_TIMEOUT?: string
  readonly VITE_ENABLE_PWA?: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// CSS Modules type declarations
declare module '*.module.css' {
  const classes: { [key: string]: string };
  export default classes;
}

declare module '*.module.scss' {
  const classes: { [key: string]: string };
  export default classes;
}

declare module '*.module.sass' {
  const classes: { [key: string]: string };
  export default classes;
}

declare module '*.module.less' {
  const classes: { [key: string]: string };
  export default classes;
}

declare module '*.module.styl' {
  const classes: { [key: string]: string };
  export default classes;
}

declare module '*.module.stylus' {
  const classes: { [key: string]: string };
  export default classes;
}
