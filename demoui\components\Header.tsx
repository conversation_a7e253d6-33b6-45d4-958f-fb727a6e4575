import React, { useState, useEffect } from 'react';
import { Calendar, Clock } from 'lucide-react';
import type { User } from '../types/auth';

interface HeaderProps {
  user: User;
}

const Header: React.FC<HeaderProps> = ({ user }) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long',
    });
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ja-JP', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  return (
    <header className="bg-white/80 backdrop-blur-sm border-b border-amber-100 px-8 py-6 shadow-sm">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-800 mb-1">
            おはようございます、{user.name}さん
          </h2>
          <p className="text-slate-600">今日も一日よろしくお願いします</p>
        </div>
        
        <div className="flex items-center gap-8">
          <div className="flex items-center gap-3 text-slate-700">
            <Calendar className="w-5 h-5 text-amber-600" />
            <span className="font-medium">{formatDate(currentTime)}</span>
          </div>
          
          <div className="flex items-center gap-3 bg-gradient-to-r from-amber-100 to-orange-100 px-6 py-3 rounded-xl">
            <Clock className="w-5 h-5 text-amber-700" />
            <span className="text-xl font-bold text-slate-800 font-mono">
              {formatTime(currentTime)}
            </span>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;