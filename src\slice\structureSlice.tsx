import { createSlice, PayloadAction, } from "@reduxjs/toolkit";
import { RootState } from "../store";
import { menuConfig } from "../utils/menulist";
import { format, addMonths, subMonths } from 'date-fns';

const sampleInfo = {
    code: '',
    name: '',
    parent_id: 3268,
};
// 定义要保存到Store的数据格式
export interface structureState {
    info: {
        id: number,
        code: string,
        name: string,
        parent_id: number,
        enable: any,
        update_name: string,
        update_time: string,
        update_page: string,
    }[],
    depart_member_count: {
        depart_id: number,
        countNum: number,
    }[],
    addInfo: {
        code: string;
        name: string;
        parent_id: any;
    },
    changeInfo: {
        id: any,
        code: any,
        name: any,
        parent_id: any,
        enable: any,
        update_name: any,
        update_time: any,
        update_page: any,
    },
    superiors: {
        value: any;
        label: any;
    }[],
    changeApiParams: {
        id: any;
        code: any;
        name: any;
        parent_id: any;
    }
    deleted: {
        deletedId: any;
        deletedCount: number;
        deletedMemCount: any;
    }
    confirmText: string;
    isChangeFormShow: boolean;
    isAddFormShow: boolean;
    isDeleteShow: boolean;
    isSureShow: boolean;
}

// 初始化数据
const initialState: structureState = {
    info: [],
    depart_member_count: [],
    addInfo: sampleInfo,
    changeInfo: {
        id: 0,
        code: '',
        name: '',
        parent_id: 0,
        enable: 0,
        update_name: '',
        update_time: '',
        update_page: '',
    },
    // deleteInfo: sampleInfo,
    superiors: [],
    changeApiParams: {
        id: 0,
        code: '',
        name: '',
        parent_id: 0,
    },
    deleted: {
        deletedId: 0,
        deletedCount: 0,
        deletedMemCount: 0,
    },
    confirmText: '',
    isChangeFormShow: false,
    isAddFormShow: false,
    isDeleteShow: false,
    isSureShow: false,
};

export const structureSlice = createSlice({
    name: 'structure',
    initialState,
    // reducers 集合了reducer和action的功能
    // 每个case冒号之前的为action名
    // 每个case整体为reducer
    reducers: {
        // 使用PayloadAction来展示action.payload的详细内容
        setStructureListWhenInit: (state: structureState, action: PayloadAction<any>) => {
            if(action.payload.structure_list!=0){
                state.info = action.payload.structure_list;
                state.depart_member_count = action.payload.depart_member_count;
                state.superiors = []
                state.info.map(( item:any ) => {
                    state.superiors.push({
                        value: item.id,
                        label: item.name,
                    })
                })
            }else{
                state.info = [{
                    id: 0,
                    code: '',
                    name: '',
                    parent_id: 0,
                    enable: '0',
                    update_name: '',
                    update_time: '',
                    update_page: '',
                }]
            }
        },
        infoAddShow: (state: structureState) => {
            state.isAddFormShow = true;
        },
        infoAddDepartCode: (state: structureState, action: PayloadAction<any>) => {
            state.addInfo.code = action.payload.target.value;
        },
        infoAddName: (state: structureState, action: PayloadAction<any>) => {
            state.addInfo.name = action.payload.target.value;
        },
        infoAddSuperior: (state: structureState, action: PayloadAction<any>) => {
            state.addInfo.parent_id = action.payload;
        },
        infoAddSave: (state: structureState) => {
            state.addInfo = {
                code: '',
                name: '',
                parent_id: 0,
            };
            state.isAddFormShow = false;
        },
        infoAddCancel: (state: structureState) => {
            state.addInfo = {
                code: '',
                name: '',
                parent_id: 0,
            };
            state.isAddFormShow = false;
        },
        infoAdd: (state: structureState, action: PayloadAction<any>) => {
            let isSame = 0
            state.info.map((item:any)=>{
                if(action.payload.id == item.id){
                    isSame = 1
                }
            })
            if(isSame == 0){
                state.info.push({
                    id: action.payload.id,
                    code: action.payload.code,
                    name: action.payload.name,
                    parent_id: action.payload.parent_id,
                    enable: action.payload.enable,
                    update_name: action.payload.update_name,
                    update_time: action.payload.update_time,
                    update_page: action.payload.update_page,
                })
            }
            
        },
        infoChangeList: (state: structureState, action: PayloadAction<any>) => {
            state.isChangeFormShow = true;
            state.changeInfo.id = action.payload.id;
            state.changeInfo.code = state.info.map((item) => ({ ...item})).find((item) => item.id === action.payload.id)?.code;
            state.changeInfo.name = state.info.map((item) => ({ ...item})).find((item) => item.id === action.payload.id)?.name;
            state.changeInfo.parent_id = state.info.map((item) => ({ ...item})).find((item) => item.id === action.payload.id)?.parent_id;
            state.changeInfo.enable = state.info.map((item) => ({ ...item})).find((item) => item.id === action.payload.id)?.enable;
            state.changeInfo.update_name = state.info.map((item) => ({ ...item})).find((item) => item.id === action.payload.id)?.update_name;
            state.changeInfo.update_time = state.info.map((item) => ({ ...item})).find((item) => item.id === action.payload.id)?.update_time;
            state.changeInfo.update_page = state.info.map((item) => ({ ...item})).find((item) => item.id === action.payload.id)?.update_page;

            state.changeApiParams.id = state.changeInfo.id;
            state.changeApiParams.code = state.changeInfo.code;
            state.changeApiParams.name = state.changeInfo.name;
            state.changeApiParams.parent_id = state.changeInfo.parent_id;
        },
        infoDepartCodeChange: (state: structureState, action: PayloadAction<any>) => {
            state.changeInfo.code = action.payload.target.value;
            state.changeApiParams.code = action.payload.target.value;
        },
        infoNameChange: (state: structureState, action: PayloadAction<any>) => {
            state.changeInfo.name = action.payload.target.value;
            state.changeApiParams.name = action.payload.target.value;
        },
        infoSuperiorChange: (state: structureState, action: PayloadAction<any>) => {
            state.changeInfo.parent_id = action.payload;
            state.changeApiParams.parent_id = action.payload;
        },
        infoChangeSave: (state: structureState) => {
            state.changeInfo = {
                id: 0,
                code: '',
                name: '',
                parent_id: 0,
                enable: 0,
                update_name: '',
                update_time: '',
                update_page: '',
            }
            state.isChangeFormShow = false;
        },
        infoChangeCancel: (state: structureState) => {
            state.isChangeFormShow = false;
            state.changeInfo = {
                id: 0,
                code: '',
                name: '',
                parent_id: 0,
                enable: 0,
                update_name: '',
                update_time: '',
                update_page: '',
            }
        },
        infoChange: (state: structureState, action: PayloadAction<any>) => {
            state.info = state.info.map(item => {
                if (item.id === action.payload.id) {
                  return {
                    ...item,
                    id: action.payload.id,
                    code: action.payload.code,
                    name: action.payload.name,
                    parent_id: action.payload.parent_id,
                    enable: action.payload.enable,
                    update_name: action.payload.update_name,
                    update_time: action.payload.update_time,
                    update_page: action.payload.update_page,
                  };
                }
                return item;
            });
            state.changeApiParams = {
                id: 0,
                code: '',
                name: '',
                parent_id: 0,
            }

        },
        infoDeleteShow: (state: structureState, action: PayloadAction<any>) => {
            state.isDeleteShow = true;
            state.deleted.deletedId = action.payload.id;
            state.info.map((item) => {
                if(item.id == action.payload.id){
                    state.deleted.deletedCount++;
                }
            })
            state.deleted.deletedCount--;
            state.deleted.deletedMemCount = state.depart_member_count.find(item => item.depart_id == action.payload.id)?.countNum;
            if(!state.deleted.deletedMemCount){
                state.deleted.deletedMemCount = 0;
            }
        },
        infoDeleteSure: (state: structureState) => {
            state.isDeleteShow = false;
            state.isSureShow = false;
            // state.info = state.info.filter((item) => item.key !== state.deleteInfo.key);
        },
        infoDeleteCancel: (state: structureState) => {
            state.isDeleteShow = false;
            state.deleted = {
                deletedId: 0,
                deletedCount: 0,
                deletedMemCount: 0,
            }
        },
        sureDeleteCancel: (state: structureState) => {
            state.isSureShow = false;
            state.confirmText = '';
        },
        //点击显示撤回栏
        leaveHandleDeleteForm: (state: structureState) => {
            state.isSureShow = true;
            state.confirmText = '';
        },
        //输入栏输入'我确认'
        leaveHandleDeleteFormInput: (state: structureState, action: PayloadAction<any>) => {
            state.confirmText = action.payload.target.value;
        },
        //一键输入'我确认'
        leaveHandleDeleteAutoInput: (state: structureState) => {
            state.confirmText = '我确认';
        },
        infoDelete: (state: structureState, action: PayloadAction<any>) => {
            state.info = state.info.map(item => {
                if(item.id === action.payload.id){
                    return {
                        ...item,
                        enable : '0'
                    }
                }
                return item
            });
        },
    },
});

//以下内容必须要有
export const { actions: structureActions } = structureSlice;

export default structureSlice.reducer;

//state 后面的为store中数据名称
export const structureData = (state: RootState) => state.structure;
