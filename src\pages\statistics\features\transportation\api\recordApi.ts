// 交通费统计记录API实现

import {
  PersonalTransportationRequest,
  CollectiveTransportationRequest,
  OverrideTransportationRequest,
  PersonalTransportationResponse,
  CollectiveTransportationResponse,
  OverrideTransportationResponse
} from '../types';

/**
 * 个人交通费报销集计（按照月份 YYYY-MM）
 * POST /in/record/transportation/personal
 */
export const getPersonalTransportationRecords = async (
  token: string,
  data: PersonalTransportationRequest
): Promise<PersonalTransportationResponse> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/record/transportation/personal', {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  //   body: JSON.stringify(data),
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    records: [
      {
        date: '2025-06-01',
        clock_in_time: '09:00',
        clock_out_time: '18:00',
        attendance_location: '日本大阪平野现场1',
        applied_from: '住所A',
        applied_to: '出勤地B',
        reimbursement_method: {
          monthly: 3000.0
        }
      },
      {
        date: '2025-06-02',
        clock_in_time: '09:00',
        clock_out_time: '18:00',
        attendance_location: '日本大阪平野现场1',
        applied_from: '住所A',
        applied_to: '出勤地B',
        reimbursement_method: {
          monthly: 3000.0
        }
      },
      {
        date: '2025-06-19',
        clock_in_time: '08:47',
        clock_out_time: '18:40',
        attendance_location: '日本大阪平野现场1',
        applied_from: '住所A',
        applied_to: '出勤地C',
        reimbursement_method: {
          single: 20.0
        }
      }
    ]
  };
};

/**
 * 总交通费报销集计（按照自然月 YYYY-MM）
 * POST /in/record/transportation/collective
 */
export const getCollectiveTransportationRecords = async (
  token: string,
  data: CollectiveTransportationRequest
): Promise<CollectiveTransportationResponse> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/record/transportation/collective', {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  //   body: JSON.stringify(data),
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    query_period: '2025-6',
    records: [
      {
        work_no: 'JS1873',
        name: '宋柳叶',
        actual_work_days: 17,
        absent_days: 2,
        attendance_locations: [
          '日本大阪平野现场1',
          '日本大阪平野现场2'
        ],
        reimbursement_method: {
          single: 260.0,
          monthly: 3000.0
        },
        reimbursement_days: {
          '2025-06-01': {
            type: 'monthly',
            amount: 3000.0,
            route_from: '住所A',
            route_to: '出勤地B'
          },
          '2025-06-02': {
            type: 'monthly',
            amount: 3000.0,
            route_from: '住所A',
            route_to: '出勤地B'
          },
          '2025-06-19': {
            type: 'single',
            amount: 20.0,
            route_from: '住所A',
            route_to: '出勤地C'
          },
          '2025-06-20': {
            type: 'single',
            amount: 300.0,
            route_from: '住所A',
            route_to: '出勤地C'
          }
        }
      },
      {
        work_no: 'JS1640',
        name: '朱国威',
        actual_work_days: 0,
        absent_days: 19,
        attendance_locations: [],
        reimbursement_method: null,
        reimbursement_days: {}
      }
    ]
  };
};

/**
 * 修改个人数据
 * POST /in/record/transportation/override
 */
export const overrideTransportationRecord = async (
  token: string,
  data: OverrideTransportationRequest
): Promise<OverrideTransportationResponse> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/record/transportation/override', {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  //   body: JSON.stringify(data),
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'ok'
  };
};
