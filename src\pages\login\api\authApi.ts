// 登录认证API实现 - 使用最新的API客户端架构

import { LoginRequest, LoginResponse, TokenValidationResponse } from '../types';
import { authApi } from '@/api/auth';

/**
 * 用户登录检查 - 使用新的API客户端
 * GET /in/login/check?user_account=JS1873&user_password=Song13@14
 */
export const checkLogin = async (params: LoginRequest): Promise<LoginResponse> => {
  try {
    // 使用统一的认证API
    return await authApi.login({
      user_account: params.user_account,
      user_password: params.user_password
    });
  } catch (error) {
    console.error('Login check error:', error);
    throw error;
  }
};

/**
 * 用户登录 - 带状态管理
 */
export const loginUser = async (
  params: LoginRequest,
  dispatch?: any
): Promise<LoginResponse> => {
  try {
    return await authApi.loginWithStateManagement({
      user_account: params.user_account,
      user_password: params.user_password
    }, dispatch);
  } catch (error) {
    console.error('Login user error:', error);
    throw error;
  }
};

/**
 * 验证Token有效性 - 使用新的API客户端
 */
export const validateToken = async (token: string): Promise<TokenValidationResponse> => {
  try {
    return await authApi.validateToken(token);
  } catch (error) {
    console.error('Token validation error:', error);
    throw error;
  }
};

/**
 * 用户登出 - 使用新的API客户端
 */
export const logout = async (token: string): Promise<{ status: string; message?: string }> => {
  try {
    const result = await authApi.logout(token);
    return {
      status: result.status,
      message: result.message || '登出成功'
    };
  } catch (error) {
    console.error('Logout error:', error);
    throw error;
  }
};
