// 登录认证API实现

import { LoginRequest, LoginResponse, TokenValidationResponse } from '../types';

/**
 * 用户登录检查
 * GET /in/login/check?user_account=JS1873&user_password=Song13@14
 */
export const checkLogin = async (params: LoginRequest): Promise<LoginResponse> => {
  // TODO: 实现实际的API调用
  // const response = await fetch(`/in/login/check?user_account=${params.user_account}&user_password=${params.user_password}`, {
  //   method: 'GET',
  //   headers: {
  //     'Content-Type': 'application/json',
  //   },
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    token: 'mock-token-' + Date.now(),
    user_info: {
      user_id: 553,
      work_no: params.user_account,
      name: '测试用户',
      department: '技术部',
      position: '开发工程师',
      email: '<EMAIL>'
    }
  };
};

/**
 * 验证Token有效性
 */
export const validateToken = async (token: string): Promise<TokenValidationResponse> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/auth/validate', {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    valid: true,
    user_info: {
      user_id: 553,
      work_no: 'JS1873',
      name: '测试用户',
      department: '技术部',
      position: '开发工程师',
      email: '<EMAIL>'
    }
  };
};

/**
 * 用户登出
 */
export const logout = async (token: string): Promise<{ status: string; message?: string }> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/auth/logout', {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    message: '登出成功'
  };
};
