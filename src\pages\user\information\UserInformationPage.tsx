import React, { useEffect, useState } from 'react'
import { Card, Descriptions, Avatar, Spin, Button } from 'antd'
import { UserOutlined, EditOutlined } from '@ant-design/icons'
import { useApplicationSelector } from '../../../hooks/hooks'
import { useTranslation } from '../../../hooks/useTranslation'
import { getApi } from '../../../api/fetch-api'
import ApiUrlVars from '../../../api/common/url-vars'
import styles from './user-information.module.css'

interface UserInfo {
  id: string
  name: string
  email: string
  department: string
  position: string
  employeeId: string
  joinDate: string
  avatar?: string
}

// 用户信息页面组件 - 从Next.js页面转换而来
const UserInformationPage: React.FC = () => {
  const { t } = useTranslation()
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const currentUser = useApplicationSelector(state => state.auth.user)

  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        setLoading(true)
        const response = await getApi(ApiUrlVars.userInfo)
        
        if (response.success) {
          setUserInfo(response.data)
        } else {
          console.error('Failed to fetch user info:', response.message)
        }
      } catch (error) {
        console.error('Error fetching user info:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchUserInfo()
  }, [])

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <Spin size="large" />
      </div>
    )
  }

  if (!userInfo) {
    return (
      <div className={styles.errorContainer}>
        <p>{t('userInfo.loadError')}</p>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>{t('userInfo.title')}</h1>
        <Button type="primary" icon={<EditOutlined />}>
          {t('userInfo.edit')}
        </Button>
      </div>

      <Card className={styles.userCard}>
        <div className={styles.userHeader}>
          <Avatar
            size={80}
            src={userInfo.avatar}
            icon={<UserOutlined />}
            className={styles.avatar}
          />
          <div className={styles.userBasic}>
            <h2>{userInfo.name}</h2>
            <p>{userInfo.position}</p>
            <p>{userInfo.department}</p>
          </div>
        </div>

        <Descriptions
          title={t('userInfo.basicInfo')}
          bordered
          column={2}
          className={styles.descriptions}
        >
          <Descriptions.Item label={t('userInfo.employeeId')}>
            {userInfo.employeeId}
          </Descriptions.Item>
          <Descriptions.Item label={t('userInfo.name')}>
            {userInfo.name}
          </Descriptions.Item>
          <Descriptions.Item label={t('userInfo.email')}>
            {userInfo.email}
          </Descriptions.Item>
          <Descriptions.Item label={t('userInfo.department')}>
            {userInfo.department}
          </Descriptions.Item>
          <Descriptions.Item label={t('userInfo.position')}>
            {userInfo.position}
          </Descriptions.Item>
          <Descriptions.Item label={t('userInfo.joinDate')}>
            {userInfo.joinDate}
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </div>
  )
}

export default UserInformationPage
