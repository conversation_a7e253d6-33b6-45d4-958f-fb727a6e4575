import Document, { Html, Head, Main, NextScript } from 'next/document'
import { StyleProvider, createCache, extractStyle } from '@ant-design/cssinjs'
const fontStyles = `
html,body {
    font-family: "Helvetica","Microsoft YaHei", "Helvetica Rounded", Arial, sans-serif;
  }
`;
type AppDocumentProps = {
  styles: React.ReactNode
}
export class AppDocument extends Document<AppDocumentProps> {
  render() {
    return (
      <Html lang="en">
        <Head>
          {this.props.styles}
          <style dangerouslySetInnerHTML={{ __html: fontStyles }} />
          <link rel="shortcut icon" href='/image/icon/favicon.ico'/>
        </Head>
		<body>
          <Main />
          <NextScript />
        </body>
      </Html>
    )
  }
}
AppDocument.getInitialProps = async (ctx) => {
  const originalRenderPage = ctx.renderPage
  const cache = createCache()
  ctx.renderPage = () =>
    originalRenderPage({
      enhanceApp: (App) =>
        function EnhanceApp(props) {
          return (
            <StyleProvider cache={cache}>
              <App {...props} />
            </StyleProvider>
          )
        },
    })
  const initialProps = await Document.getInitialProps(ctx)
  return {
    ...initialProps,
    styles: (
      <>
        {initialProps.styles}
        <style dangerouslySetInnerHTML={{ __html: extractStyle(cache)}} />
      </>
    ),
  }
}
export default AppDocument