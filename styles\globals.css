/* 全局CSS变量定义 */
:root {
  /* 响应式断点变量 */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1600px;
  
  /* 标准间距变量 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
  --spacing-xxl: 32px;
  
  /* 标准圆角变量 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  
  /* 标准阴影变量 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* 主题颜色变量 */
  --color-primary: #1890ff;
  --color-success: #52c41a;
  --color-warning: #fa8c16;
  --color-error: #ff4d4f;
  --color-info: #13c2c2;
  --color-purple: #722ed1;

  /* 背景颜色变量 */
  --bg-success: #f6ffed;
  --bg-warning: #fff7e6;
  --bg-error: #fff2f0;
  --bg-info: #e6fffb;
  --bg-purple: #f9f0ff;
  --bg-primary: #e6f7ff;

  /* 左侧导航栏专用变量 */
  --primary-color: #3498db;
  --primary-color-light: #5dade2;
  --primary-color-dark: #2980b9;
  --secondary-color: #9b59b6;
  --accent-color: #e74c3c;
  --text-sidebar: #ecf0f1;
  --text-sidebar-secondary: #bdc3c7;
  --sidebar-border-color: rgba(255, 255, 255, 0.1);
  --divider-color: rgba(255, 255, 255, 0.1);

  /* 圆角变量 */
  --radius-small: 4px;
  --radius-medium: 8px;
  --radius-large: 12px;

  /* 阴影变量 */
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.2);

  /* 字体大小变量 */
  --font-size-xs: 11px;
  --font-size-sm: 13px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;

  /* 边框变量 */
  --border-width: 1px;
  --border-width-thick: 2px;

  /* 图标尺寸变量 */
  --icon-size-xs: 12px;
  --icon-size-sm: 14px;
  --icon-size-md: 16px;
  --icon-size-lg: 20px;
  --icon-size-xl: 24px;

  /* 文本颜色变量 */
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-muted: #95a5a6;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

a {
  color: inherit;
  text-decoration: none;
}

/* 侧边栏容器布局样式 */
.sidebar-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.sidebar-main-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

.sidebar-bottom {
  flex-shrink: 0;
  margin-top: auto;
}

/* 弹窗样式 */
.pop-up {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 通用工具类 */
.null {
  /* 空样式类 */
}

.hide {
  display: none !important;
}

/* 列表容器通用样式 */
.list-container {
  width: 100%;
  height: calc(100vh - 120px);
  min-height: 600px;
  min-width: 1080px;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  padding: 20px;
}

/* 表格容器样式 */
.table-wrapper {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 修复Ant Design表格组件样式冲突 - 部门统计页面专用 */
.ant-table-wrapper {
  background: transparent !important;
}

/* 部门统计页面表格特殊样式 */
[class*="departmentStatistics"] .ant-table,
.ant-table.data_table {
  background: #fff !important;
  border: none !important;
  border-radius: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

[class*="departmentStatistics"] .ant-table-container,
.ant-table.data_table .ant-table-container {
  border: none !important;
  background: transparent !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 修复第一行间距 */
[class*="departmentStatistics"] .ant-table-tbody > tr:first-child > td,
.ant-table.data_table .ant-table-tbody > tr:first-child > td {
  border-top: none !important;
  margin-top: 0 !important;
  padding-top: 14px !important;
}

/* 隐藏Ant Design的测量行 */
[class*="departmentStatistics"] .ant-table-measure-row,
.ant-table.data_table .ant-table-measure-row {
  display: none !important;
  height: 0 !important;
  visibility: hidden !important;
}

[class*="departmentStatistics"] .ant-table-measure-row > td,
.ant-table.data_table .ant-table-measure-row > td {
  display: none !important;
  height: 0 !important;
  padding: 0 !important;
  border: none !important;
}

/* 部门统计页面表格头部样式 - 符合整体UI风格 */
[class*="departmentStatistics"] .ant-table-thead > tr > th,
.ant-table.data_table .ant-table-thead > tr > th {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  font-size: 13px !important;
  text-align: center !important;
  border-bottom: 2px solid #1890ff !important;
  border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-top: none !important;
  border-left: none !important;
  padding: 16px 12px !important;
  height: 50px !important;
  line-height: 1.4 !important;
  letter-spacing: 0.5px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
}

[class*="departmentStatistics"] .ant-table-thead > tr > th:last-child,
.ant-table.data_table .ant-table-thead > tr > th:last-child {
  border-right: none !important;
}

/* 部门统计页面表格行样式 - 日系风格 */
[class*="departmentStatistics"] .ant-table-tbody > tr > td,
.ant-table.data_table .ant-table-tbody > tr > td {
  border-bottom: 1px solid #f5f5f5 !important;
  border-right: 1px solid #fafafa !important;
  border-top: none !important;
  border-left: none !important;
  padding: 14px 12px !important;
  background-color: #ffffff !important;
  font-size: 13px !important;
  line-height: 1.5 !important;
  color: #2d3748 !important;
  transition: all 0.2s ease !important;
}

[class*="departmentStatistics"] .ant-table-tbody > tr > td:last-child,
.ant-table.data_table .ant-table-tbody > tr > td:last-child {
  border-right: none !important;
}

/* 部门统计页面表格行悬停效果 - 日系风格 */
[class*="departmentStatistics"] .ant-table-tbody > tr:hover > td,
.ant-table.data_table .ant-table-tbody > tr:hover > td {
  background: linear-gradient(135deg, #e8f2ff 0%, #f0f4ff 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1) !important;
  border-color: #d6e3ff !important;
  transition: all 0.3s ease !important;
}

/* 部门统计页面表格边框 */
[class*="departmentStatistics"] .ant-table-tbody,
.ant-table.data_table .ant-table-tbody {
  border: none !important;
}

/* 部门统计页面最后一行 */
[class*="departmentStatistics"] .ant-table-tbody > tr:last-child > td,
.ant-table.data_table .ant-table-tbody > tr:last-child > td {
  border-bottom: 2px solid #e2e8f0 !important;
}

/* 修复Tab组件样式 */
.ant-tabs {
  background: transparent !important;
}

.ant-tabs-content-holder {
  background: transparent !important;
}

.ant-tabs-tabpane {
  background: transparent !important;
}

/* 修复Input组件样式 */
.ant-input {
  background: #fff !important;
  border: 1px solid #d9d9d9 !important;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 修复Button组件样式 */
.ant-btn {
  background: #fff !important;
  border: 1px solid #d9d9d9 !important;
}

.ant-btn-primary {
  background: #1890ff !important;
  border-color: #1890ff !important;
  color: #fff !important;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* 修复DatePicker组件样式 */
.ant-picker {
  background: #fff !important;
  border: 1px solid #d9d9d9 !important;
}

.ant-picker:hover,
.ant-picker-focused {
  border-color: #40a9ff !important;
}

/* 修复Checkbox组件样式 */
.ant-checkbox-wrapper {
  color: inherit !important;
}

.ant-checkbox-inner {
  background: #fff !important;
  border: 1px solid #d9d9d9 !important;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background: #1890ff !important;
  border-color: #1890ff !important;
}

/* 修复Message组件样式 */
.ant-message {
  z-index: 1050 !important;
}

/* 修复Modal组件样式 */
.ant-modal {
  z-index: 1000 !important;
}

.ant-modal-mask {
  z-index: 1000 !important;
}

/* 修复Spin组件样式 */
.ant-spin {
  color: #1890ff !important;
}

/* 修复Select组件样式 */
.ant-select {
  background: transparent !important;
}

.ant-select-selector {
  background: #fff !important;
  border: 1px solid #d9d9d9 !important;
}

.ant-select-focused .ant-select-selector,
.ant-select-selector:focus,
.ant-select-selector:active,
.ant-select-open .ant-select-selector {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 修复页面布局问题 */
/* 确保页面容器正常显示 */
div[style*="width:100%"] {
  box-sizing: border-box !important;
}

div[style*="height:calc(100vh"] {
  box-sizing: border-box !important;
}

div[style*="minWidth"] {
  box-sizing: border-box !important;
}

/* 修复flex布局 */
div[style*="display:flex"],
div[style*="display: flex"] {
  box-sizing: border-box !important;
}

/* 修复内联样式的容器 */
div[style*="background:#fff"],
div[style*="background: #fff"],
div[style*="background:white"],
div[style*="background: white"] {
  box-sizing: border-box !important;
}

/* 修复表格容器 */
div[style*="overflow:hidden"],
div[style*="overflow: hidden"] {
  box-sizing: border-box !important;
}

/* 修复滚动容器 */
div[style*="overflow-y:auto"],
div[style*="overflow-y: auto"] {
  box-sizing: border-box !important;
}

/* 修复浮动布局 */
div[style*="float:left"],
div[style*="float: left"] {
  box-sizing: border-box !important;
}

/* 修复绝对定位 */
div[style*="position:absolute"],
div[style*="position: absolute"],
div[style*="position:fixed"],
div[style*="position: fixed"] {
  box-sizing: border-box !important;
}

/* 修复边距和内边距 */
div[style*="margin"],
div[style*="padding"] {
  box-sizing: border-box !important;
}

/* 确保图片正常显示 */
img {
  max-width: 100% !important;
  height: auto !important;
  box-sizing: border-box !important;
}

/* 修复按钮样式 */
button {
  box-sizing: border-box !important;
  cursor: pointer !important;
}

button:disabled {
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}

/* 修复输入框样式 */
input[type="file"] {
  box-sizing: border-box !important;
}

/* 修复表格样式 */
table {
  box-sizing: border-box !important;
  border-collapse: collapse !important;
}

td, th {
  box-sizing: border-box !important;
}

/* 修复链接样式 */
a {
  box-sizing: border-box !important;
  text-decoration: none !important;
}

a:hover {
  text-decoration: none !important;
}

/* 修复列表样式 */
ul, ol, li {
  box-sizing: border-box !important;
}

/* 修复文本样式 */
span, p, div {
  box-sizing: border-box !important;
}

/* 修复标题样式 */
h1, h2, h3, h4, h5, h6 {
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 修复表单样式 */
form {
  box-sizing: border-box !important;
}

label {
  box-sizing: border-box !important;
}

/* 修复滚动条样式 */
::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}

::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
  border-radius: 4px !important;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1 !important;
  border-radius: 4px !important;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1 !important;
}
