import { createSlice, PayloadAction, createAsyncThunk } from "@reduxjs/toolkit";
import { RootState } from "../store";
import axios from "axios";
import ApiTasksTypes from "../api/common/task-types";
import ApiFetchVars from "../api/common/fetch-api-vars";
import ApiUrlVars from "../api/common/url-vars";
// import { createApiAsyncThunk } from "../api/fetch-api";

// 定义要保存到Store的数据格式
export interface loginState {
    user_id: number;
    status: string;
    work_no: string;
    name: string;
    mail: string;
    admin: number;
    checked: boolean;
    isSubmitted: boolean;
    role_name: {}[];
    loading: boolean;
}

// 初始化数据
const initialState: loginState = {
    user_id: 0,
    status: ApiFetchVars.todo,
    work_no: '',
    name: '',
    mail: '',
    admin: 0,
    checked: false,
    isSubmitted: false,
    role_name: [],
    loading: false,
};

//定义需要的变量
const taskType = ApiTasksTypes.login_get

const url = ApiUrlVars.loginApi_domain + ApiUrlVars.login_get

// export const testAsyncThunk = createApiAsyncThunk(taskType, url, null, ApiFetchVars.get)

export const authSlice = createSlice({
    name: 'login',
    initialState,
    // reducers 集合了reducer和action的功能
    // 每个case冒号之前的为action名
    // 每个case整体为reducer
    reducers: {
        setUser: (state: loginState, action: PayloadAction<any>) => {
            state.user_id = action.payload.user_id;
            state.status = action.payload.status;
            state.work_no = action.payload.work_no;
            state.name = action.payload.name;
            state.mail = action.payload.mail;
            state.admin = action.payload.admin;
        },
        setCheckbox: (state: loginState) => {
            if (typeof window !== 'undefined') {
                state.checked = !localStorage.getItem('checkbox')?.valueOf()
            }
        },
        setUserMessage: (state: loginState, action: PayloadAction<any>) => {
            state.user_id = action.payload.user_id;
            state.status = action.payload.status;
            state.work_no = action.payload.work_no;
            state.name = action.payload.name;
            state.mail = action.payload.mail;
            state.admin = action.payload.admin;
            state.role_name = action.payload.role_name;
        },
        setLoading: (state: loginState, action: PayloadAction<any>) => {
            state.loading = action.payload;
        }
    },
});

export const { actions: setUserActions } = authSlice;

export default authSlice.reducer;

//state 后面的为store中数据名称
export const loginData = (state: RootState) => state.login;