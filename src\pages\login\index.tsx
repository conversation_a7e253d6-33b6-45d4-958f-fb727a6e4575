import React, { useState, useEffect } from 'react'
import { Form, Input, Checkbox } from 'antd';
import { useApplicationSelector, useApplicationDispatch } from "@/hook/hooks";
import { setUserActions, loginData } from "@/slice/authSlice";
import { getApi } from '@/api/fetch-api';
import ApiUrlVars from '@/api/common/url-vars';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { onLogin } from '@/utils/cookies';
import toast, { Toaster, } from 'react-hot-toast';
import { get_token_aes } from '@/utils/login';
import { useTranslation } from '@/hooks/useTranslation';
import LanguageSwitcher from '@/components/LanguageSwitcher';

let dispatch: any = null

// 日系深色主题样式
const loginStyles = {
  all: {
    background: 'linear-gradient(180deg, #34495e 0%, #2c3e50 100%)',
    minHeight: '100vh',
    width: '100%',
    position: 'relative' as const,
    overflow: 'hidden' as const,
    backgroundImage: `
      radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.03) 0%, transparent 50%)
    `
  },
  languageSwitcherContainer: {
    position: 'absolute' as const,
    top: '30px',
    right: '30px',
    zIndex: 1000
  },
  loginBox: {
    background: 'linear-gradient(135deg, rgba(52, 73, 94, 0.95) 0%, rgba(44, 62, 80, 0.98) 100%)',
    backdropFilter: 'blur(20px)',
    border: '1px solid rgba(189, 195, 199, 0.3)',
    boxShadow: `
      0 20px 40px rgba(0, 0, 0, 0.4),
      0 8px 16px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1)
    `,
    minHeight: '580px',
    width: '450px',
    position: 'absolute' as const,
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    borderRadius: '12px',
    overflow: 'visible' as const,
    paddingBottom: '32px'
  },
  loginImage: {
    backgroundImage: "url('/image/background/HYRON_LOGO_BLUE.png')",
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center',
    height: '64px',
    width: '280px',
    margin: '0 auto',
    marginTop: '50px',
    filter: 'brightness(1.8) contrast(1.4) saturate(1.3)',
    transition: 'all 0.3s ease'
  },
  loginTitle: {
    fontSize: '28px',
    fontWeight: 700,
    textAlign: 'center' as const,
    marginTop: '16px',
    color: '#ecf0f1',
    textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',
    letterSpacing: '1px'
  },
  loginRemain: {
    fontSize: '16px',
    fontWeight: 400,
    color: '#bdc3c7',
    textAlign: 'center' as const,
    marginTop: '6px',
    marginBottom: '8px',
    opacity: 0.9,
    letterSpacing: '0.5px'
  },
  loginComponent: {
    width: '85%',
    position: 'relative' as const,
    margin: '24px auto 0',
    padding: '0 24px'
  },
  inputTitle: {
    width: '100%',
    fontSize: '14px',
    fontWeight: 600,
    color: '#bdc3c7',
    marginBottom: '4px',
    paddingLeft: '8px',
    letterSpacing: '0.5px'
  },
  componentInput: {
    width: '100%',
    height: '48px',
    fontSize: '16px',
    background: 'linear-gradient(135deg, rgba(52, 73, 94, 0.8) 0%, rgba(44, 62, 80, 0.9) 100%)',
    border: '1px solid rgba(189, 195, 199, 0.3)',
    borderRadius: '12px',
    color: '#ecf0f1',
    padding: '0 16px',
    marginBottom: '16px',
    transition: 'all 0.3s ease',
    backdropFilter: 'blur(5px)',
    boxShadow: `
      0 2px 8px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1)
    `
  },
  loginButton: {
    background: 'linear-gradient(135deg, #3498db 0%, #2980b9 100%)',
    fontSize: '18px',
    fontWeight: 700,
    color: '#ffffff',
    height: '52px',
    width: '100%',
    border: 'none',
    borderRadius: '12px',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    marginTop: '16px',
    letterSpacing: '1px',
    textShadow: '0 1px 2px rgba(0, 0, 0, 0.2)',
    boxShadow: `
      0 4px 15px rgba(52, 152, 219, 0.4),
      0 2px 8px rgba(0, 0, 0, 0.2)
    `,
    position: 'relative' as const,
    overflow: 'hidden' as const
  }
}

function loginList(params: any) {
  const url = ApiUrlVars.loginApi_domain + ApiUrlVars.login_get
  const result = getApi(url, params, dispatch)
  return result;
}

const Login: React.FC = () => {
  const [account, setAccount] = useState('');
  const [checked, setChecked] = useState(false);
  const [password, setPassword] = useState('');
  const navigate = useNavigate();
  const data = useApplicationSelector(loginData);
  const { t } = useTranslation('login');
  dispatch = useApplicationDispatch()

  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (localStorage.getItem('account') != null && localStorage.getItem('checkbox')!= null) {
        setAccount((localStorage as any).getItem('account'))
        setChecked((localStorage as any).getItem('checkbox'))
      }
    }

    // handleResize();

    // function handleResize() {
    //   let targetScaleRatio = parseFloat((window.innerWidth / window.screen.width).toFixed(2));
    //   targetScaleRatio = targetScaleRatio >= 0.695 ? (targetScaleRatio <= 1 ? targetScaleRatio - 0.005 : 1) : 0.695;
    //   setScaleRatio(targetScaleRatio)
    //   setScreenHeight(960*targetScaleRatio)
    // }
    
    // window.addEventListener('resize', handleResize);
    // return () => window.removeEventListener('resize', handleResize);
  }, []);

  const rememberAccountChange = async (e: any) => {
    setAccount(e.target.value)
    if (typeof window !== 'undefined') {
      var lastAccount = (document.querySelector('#account') as any)?.value
      if (lastAccount !== localStorage.getItem('account')) {
        localStorage.removeItem('account')
        localStorage.setItem('account', lastAccount)
      }
    }
  }

  const rememberAccount = async () => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('account', (document.querySelector('#account') as any)?.value)
      localStorage.setItem('checkbox',(document.querySelector('#checkbox') as any)?.checked)
      setChecked((document.querySelector('#checkbox') as any)?.checked)
      if ((document.querySelector('#checkbox') as any)?.checked === false) {
        localStorage.removeItem('account')
        localStorage.removeItem('checkbox')
      }
    }
  }
  const handleSubmit = async (e: any) => {
    e.preventDefault()
    loginList({
      user_account: account,
      user_password: password
    }).then(res => {
      if (res.data.status == 'OK') {
        rememberAccount();
        dispatch(setUserActions.setUser(res.data))
        // 将token以及token md5保存在cookie中
        const token = res.data.token
        const token_aes = get_token_aes(token)
        onLogin(token, token_aes)
        if (typeof window !== 'undefined') {
          localStorage.setItem('login', JSON.stringify(res.data))
        }
        //跳转
        navigate('/record/my')
      } else {
        toast(res.data?.message,
          {
            icon: '❌',
            style: {
              marginTop: '20px',
              minWidth: '400px',
              borderRadius: '12px',
              background: 'linear-gradient(135deg, rgba(231, 76, 60, 0.95) 0%, rgba(192, 57, 43, 0.98) 100%)',
              color: '#ffffff',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              backdropFilter: 'blur(10px)',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
            },
          }
        );
      }
    }).catch(() => {
      toast(t('messages.networkError') || '网络连接错误，请稍后重试',
        {
          icon: '❌',
          style: {
            marginTop: '20px',
            minWidth: '400px',
            borderRadius: '12px',
            background: 'linear-gradient(135deg, rgba(231, 76, 60, 0.95) 0%, rgba(192, 57, 43, 0.98) 100%)',
            color: '#ffffff',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            backdropFilter: 'blur(10px)',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
          },
        }
      );
    })
  }

  return (
    <>
    {/* <Head>
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=0" />
      <style>
        {`body{
          transform: ${`scale(${scaleRatio})`};
        }
        html {
          height:  ${`${screenHeight}px`};
        }`
        }
      </style>
    </Head> */}
    <div className="login-page" style={{...loginStyles.all, width:'100%', height:'100vh'}}>
      <div><Toaster /></div>

      {/* 语言切换器 */}
      <div style={loginStyles.languageSwitcherContainer}>
        <LanguageSwitcher />
      </div>

      {/* 日系装饰背景元素 */}
      <div style={{
        position: 'absolute',
        top: '10%',
        left: '10%',
        width: '200px',
        height: '200px',
        background: 'radial-gradient(circle, rgba(52, 152, 219, 0.1) 0%, transparent 70%)',
        borderRadius: '50%',
        animation: 'float 8s ease-in-out infinite',
        zIndex: 1
      }}></div>

      <div style={{
        position: 'absolute',
        bottom: '15%',
        right: '15%',
        width: '150px',
        height: '150px',
        background: 'radial-gradient(circle, rgba(155, 89, 182, 0.08) 0%, transparent 70%)',
        borderRadius: '50%',
        animation: 'float 10s ease-in-out infinite reverse',
        zIndex: 1
      }}></div>

      <div style={loginStyles.loginBox}>
        {/* 日系装饰条 */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '4px',
          background: 'linear-gradient(90deg, #3498db 0%, #2980b9 50%, #3498db 100%)',
          borderRadius: '12px 12px 0 0'
        }}></div>

        <div style={loginStyles.loginImage}></div>
        <div style={loginStyles.loginTitle}>{t('title') || '考勤管理系统'}</div>

        {/* 日系分隔线 */}
        <div style={{
          width: '60px',
          height: '2px',
          background: 'linear-gradient(90deg, transparent 0%, #3498db 50%, transparent 100%)',
          margin: '16px auto',
          opacity: 0.7
        }}></div>

        <div style={loginStyles.loginRemain}>{t('subtitle') || '请登录您的账户'}</div>
        <div style={loginStyles.loginComponent}>
          <form onSubmit={handleSubmit}>
            <Form.Item>
              <p style={loginStyles.inputTitle}>{t('account') || '账号'}</p>
              <Input
                id='account'
                value={account}
                onChange={rememberAccountChange}
                style={loginStyles.componentInput}
                prefix={<UserOutlined className="site-form-item-icon" />}
                placeholder={t('placeholders.account') || '请输入账号'}
              />
            </Form.Item>
            <Form.Item>
              <p style={loginStyles.inputTitle}>{t('password') || '密码'}</p>
              <Input.Password
                value={password}
                onChange={e => setPassword(e.target.value)}
                style={loginStyles.componentInput}
                prefix={<LockOutlined className="site-form-item-icon" />}
                type="password"
                placeholder={t('placeholders.password') || '请输入密码'}
              />
            </Form.Item>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: 'var(--spacing-lg, 24px)'
            }}>
              <Checkbox
                id='checkbox'
                onChange={rememberAccount}
                checked={checked}
                onClick={() => dispatch(setUserActions.setCheckbox())}
                style={{
                  fontSize: '14px',
                  color: '#bdc3c7'
                }}
              >
                {t('remember') || '记住账号'}
              </Checkbox>
              <a className="login-form-forgot" href="reset" style={{
                color: '#5dade2',
                fontSize: '14px',
                textDecoration: 'none',
                transition: 'all 0.3s ease'
              }}>
                {t('forgot') || '忘记密码？'}
              </a>
            </div>
            <Form.Item>
              <button type="submit" style={loginStyles.loginButton}>
                {t('login') || '登录'}
              </button>
            </Form.Item>
          </form>
        </div>
      </div>
    </div >
    </>
  )
}

export default Login
