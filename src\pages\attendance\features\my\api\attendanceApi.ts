// 个人考勤API实现

import {
  AttendanceRecord,
  ClockRecord,
  GetAttendanceRecordsRequest,
  GetAttendanceRecordsResponse,
  ClockInOutRequest,
  ClockInOutResponse,
  AttendanceCorrectionRequest,
  AttendanceCorrectionResponse,
  MonthlyAttendanceSummary,
  AttendanceStatus,
  ClockType
} from '../types';

/**
 * 获取个人考勤记录
 * GET /in/attendance/my/records
 */
export const getMyAttendanceRecords = async (
  token: string,
  params: GetAttendanceRecordsRequest
): Promise<GetAttendanceRecordsResponse> => {
  // TODO: 实现实际的API调用
  // const queryParams = new URLSearchParams();
  // queryParams.append('date_from', params.date_from);
  // queryParams.append('date_to', params.date_to);
  // if (params.user_id) queryParams.append('user_id', params.user_id.toString());
  
  // const response = await fetch(`/in/attendance/my/records?${queryParams}`, {
  //   method: 'GET',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    records: [
      {
        id: 'att_001',
        user_id: 553,
        date: '2024-06-01',
        clock_in_time: '09:00',
        clock_out_time: '18:00',
        attendance_location: '日本大阪平野现场1',
        status: AttendanceStatus.PRESENT,
        work_hours: 8,
        overtime_hours: 0,
        late_minutes: 0,
        early_leave_minutes: 0,
        created_at: '2024-06-01T09:00:00Z',
        updated_at: '2024-06-01T18:00:00Z'
      },
      {
        id: 'att_002',
        user_id: 553,
        date: '2024-06-02',
        clock_in_time: '09:15',
        clock_out_time: '18:30',
        attendance_location: '日本大阪平野现场1',
        status: AttendanceStatus.LATE,
        work_hours: 8.25,
        overtime_hours: 0.5,
        late_minutes: 15,
        early_leave_minutes: 0,
        created_at: '2024-06-02T09:15:00Z',
        updated_at: '2024-06-02T18:30:00Z'
      }
    ],
    statistics: {
      total_work_days: 22,
      actual_work_days: 20,
      absent_days: 2,
      late_count: 1,
      early_leave_count: 0,
      total_work_hours: 160,
      total_overtime_hours: 5,
      attendance_rate: 90.9
    }
  };
};

/**
 * 打卡（上班/下班）
 * POST /in/attendance/my/clock
 */
export const clockInOut = async (
  token: string,
  data: ClockInOutRequest
): Promise<ClockInOutResponse> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/attendance/my/clock', {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  //   body: JSON.stringify(data),
  // });
  // return response.json();
  
  // 临时返回模拟数据
  const now = new Date();
  const clockRecord: ClockRecord = {
    id: 'clock_' + Date.now(),
    user_id: 553,
    type: data.type,
    timestamp: now.toISOString(),
    location: data.location,
    latitude: data.latitude,
    longitude: data.longitude,
    created_at: now.toISOString()
  };
  
  const attendanceRecord: AttendanceRecord = {
    id: 'att_' + Date.now(),
    user_id: 553,
    date: now.toISOString().split('T')[0],
    clock_in_time: data.type === ClockType.CLOCK_IN ? now.toTimeString().slice(0, 5) : undefined,
    clock_out_time: data.type === ClockType.CLOCK_OUT ? now.toTimeString().slice(0, 5) : undefined,
    attendance_location: data.location,
    status: AttendanceStatus.PRESENT,
    created_at: now.toISOString(),
    updated_at: now.toISOString()
  };
  
  return {
    status: 'OK',
    message: data.type === ClockType.CLOCK_IN ? '上班打卡成功' : '下班打卡成功',
    clock_record: clockRecord,
    attendance_record: attendanceRecord
  };
};

/**
 * 申请考勤修正
 * POST /in/attendance/my/correction
 */
export const requestAttendanceCorrection = async (
  token: string,
  data: AttendanceCorrectionRequest
): Promise<AttendanceCorrectionResponse> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/attendance/my/correction', {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  //   body: JSON.stringify(data),
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    message: '考勤修正申请提交成功，等待审批',
    correction_id: 'correction_' + Date.now()
  };
};

/**
 * 获取月度考勤汇总
 * GET /in/attendance/my/monthly_summary
 */
export const getMonthlyAttendanceSummary = async (
  token: string,
  month: string // YYYY-MM
): Promise<{ status: 'OK' | 'ERROR'; summary: MonthlyAttendanceSummary; message?: string }> => {
  // TODO: 实现实际的API调用
  // const response = await fetch(`/in/attendance/my/monthly_summary?month=${month}`, {
  //   method: 'GET',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  // });
  // return response.json();
  
  // 临时返回模拟数据
  return {
    status: 'OK',
    summary: {
      month: month,
      user_id: 553,
      work_no: 'JS1640',
      name: '朱国威',
      department: '技术部',
      statistics: {
        total_work_days: 22,
        actual_work_days: 20,
        absent_days: 2,
        late_count: 1,
        early_leave_count: 0,
        total_work_hours: 160,
        total_overtime_hours: 5,
        attendance_rate: 90.9
      },
      daily_records: [
        {
          id: 'att_001',
          user_id: 553,
          date: month + '-01',
          clock_in_time: '09:00',
          clock_out_time: '18:00',
          attendance_location: '日本大阪平野现场1',
          status: AttendanceStatus.PRESENT,
          work_hours: 8,
          overtime_hours: 0,
          created_at: month + '-01T09:00:00Z',
          updated_at: month + '-01T18:00:00Z'
        }
      ]
    }
  };
};

/**
 * 获取今日考勤状态
 * GET /in/attendance/my/today
 */
export const getTodayAttendanceStatus = async (
  token: string
): Promise<{ status: 'OK' | 'ERROR'; today_record?: AttendanceRecord; message?: string }> => {
  // TODO: 实现实际的API调用
  // const response = await fetch('/in/attendance/my/today', {
  //   method: 'GET',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'Authorization': token,
  //   },
  // });
  // return response.json();
  
  // 临时返回模拟数据
  const today = new Date().toISOString().split('T')[0];
  return {
    status: 'OK',
    today_record: {
      id: 'att_today',
      user_id: 553,
      date: today,
      clock_in_time: '09:00',
      attendance_location: '日本大阪平野现场1',
      status: AttendanceStatus.PRESENT,
      work_hours: 0,
      created_at: today + 'T09:00:00Z',
      updated_at: today + 'T09:00:00Z'
    }
  };
};
