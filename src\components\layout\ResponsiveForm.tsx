import React from 'react';
import { Form, FormProps } from 'antd';
import styles from './ResponsiveForm.module.css';

interface ResponsiveFormProps extends FormProps {
  layout?: 'horizontal' | 'vertical' | 'inline' | 'responsive';
  mobileBreakpoint?: number;
  className?: string;
}

/**
 * 响应式表单组件
 * 在移动端自动切换为垂直布局
 */
const ResponsiveForm: React.FC<ResponsiveFormProps> = ({
  layout = 'responsive',
  mobileBreakpoint = 768,
  className = '',
  ...formProps
}) => {
  const [isMobile, setIsMobile] = React.useState(false);

  React.useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= mobileBreakpoint);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, [mobileBreakpoint]);

  // 确定实际使用的布局
  const actualLayout = layout === 'responsive' 
    ? (isMobile ? 'vertical' : 'horizontal')
    : layout;

  const formClasses = [
    styles.responsiveForm,
    styles[`layout_${actualLayout}`],
    isMobile ? styles.mobile : styles.desktop,
    className
  ].filter(Boolean).join(' ');

  // 响应式标签列配置
  const labelCol = actualLayout === 'horizontal' 
    ? (isMobile ? { span: 24 } : { span: 6 })
    : undefined;

  const wrapperCol = actualLayout === 'horizontal'
    ? (isMobile ? { span: 24 } : { span: 18 })
    : undefined;

  return (
    <Form
      {...formProps}
      layout={actualLayout}
      labelCol={labelCol}
      wrapperCol={wrapperCol}
      className={formClasses}
    />
  );
};

export default ResponsiveForm;
