/* 响应式统计对话框 */
.dialog_box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  max-width: 1000px;
  height: 80vh;
  max-height: 500px;
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  z-index: var(--z-modal);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.leave_title {
  font-weight: 600;
  color: var(--color-primary);
  font-size: var(--font-size-lg);
  padding: var(--spacing-lg) var(--spacing-xl);
  margin: 0;
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  flex-shrink: 0;
}

.leave_dialog {
  flex: 1;
  padding: var(--spacing-md);
  border: none;
  border-radius: 0;
  margin: 0;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  background: var(--bg-primary);
}

.leave_dialog ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.leave_dialog ul li {
  margin: 0;
  padding: var(--spacing-sm) 0;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--border-color-light);
  transition: var(--transition-normal);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.leave_dialog ul li:hover {
  background: var(--bg-secondary);
  padding-left: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
}

.leave_dialog ul li:last-child {
  border-bottom: none;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .dialog_box {
    width: 95vw;
    height: 85vh;
    max-height: none;
  }

  .leave_title {
    font-size: var(--font-size-md);
    padding: var(--spacing-md);
  }

  .leave_dialog {
    padding: var(--spacing-sm);
  }

  .leave_dialog ul li {
    padding: var(--spacing-md) 0;
    font-size: var(--font-size-xs);
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .dialog_box {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    max-width: none;
    max-height: none;
  }

  .leave_title {
    font-size: var(--font-size-sm);
    padding: var(--spacing-sm);
    position: sticky;
    top: 0;
    z-index: 1;
  }

  .leave_dialog {
    flex: 1;
    padding: var(--spacing-xs);
  }
}

.leave_dialog ul li span {
  margin-left: 5px;
}

.leave_dialog ul li div {
  margin-left: 8px;
}

.overtime_title {
  font-weight: 800;
  color: #319cf3;
  font-size: large;
  margin-top: 10px;
  margin-left: 440px;
}

.overtime_dialog {
  padding-top: 5px;
  padding-left: 5px;
  padding-right: 5px;
  padding-bottom: 10px;
  position: absolute;
  height: 400px;
  width: 540px;
  border: 1px solid rgb(187, 186, 186);
  border-radius: 10px;
  margin-top: 10px;
  margin-left: 430px;
  margin-bottom: 20px;
  overflow-x: hidden;
}

.overtime_dialog ul li {
  margin-top: 3px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #79aadb7a;
  padding-bottom: 3px
}

.overtime_dialog ul li span {
  margin-left: 5px;
}

.overtime_dialog ul li div {
  margin-left: 10px;
}

.dialog_btn {
  width: 150px;
  background: #1E90FF!important;
  color: white!important;
  margin-top: 420px;
  margin-left: 765px;
}