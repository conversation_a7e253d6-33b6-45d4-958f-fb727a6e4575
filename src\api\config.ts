/**
 * API配置文件
 * 统一管理API相关的配置信息
 */

import ApiUrlVars from './common/url-vars';

// API基础配置
export const API_CONFIG = {
  // 基础域名
  BASE_DOMAIN: '172.28.1.60:8085',
  
  // API基础URL
  BASE_URL: ApiUrlVars.api_domain,
  
  // 登录API基础URL
  LOGIN_BASE_URL: ApiUrlVars.loginApi_domain,
  
  // WebSocket域名
  WEBSOCKET_DOMAIN: ApiUrlVars.websocket_domain,
  
  // 请求超时时间（毫秒）
  TIMEOUT: 30000,
  
  // 默认请求头
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
};

// 交通费相关API端点
export const TRANSPORTATION_ENDPOINTS = {
  // 申请相关
  APPLICATION: {
    GET_LIST: ApiUrlVars.transportation_fee_get,
    CREATE: ApiUrlVars.transportation_fee_add,
    UPDATE: ApiUrlVars.transportation_fee_update,
    DELETE: ApiUrlVars.transportation_fee_delete,
  },
  
  // 记录相关
  RECORDS: {
    PERSONAL: ApiUrlVars.transportation_record_personal,
    COLLECTIVE: ApiUrlVars.transportation_record_collective,
    OVERRIDE: ApiUrlVars.transportation_record_override,
  },
};

// 认证相关API端点
export const AUTH_ENDPOINTS = {
  LOGIN: ApiUrlVars.login_get,
  LOGOUT: '/auth/logout',
  VALIDATE: '/auth/validate',
  CURRENT_USER: '/user/current',
};

// 环境配置
export const ENV_CONFIG = {
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  apiBaseUrl: process.env.VITE_API_BASE_URL || API_CONFIG.BASE_URL,
};

// 错误码映射
export const ERROR_CODES = {
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  BAD_REQUEST: 400,
};

// 错误消息映射
export const ERROR_MESSAGES = {
  [ERROR_CODES.UNAUTHORIZED]: '登录凭证失效，请重新登录',
  [ERROR_CODES.FORBIDDEN]: '权限不足，无法访问该资源',
  [ERROR_CODES.NOT_FOUND]: '请求的资源不存在',
  [ERROR_CODES.INTERNAL_SERVER_ERROR]: '服务器内部错误，请稍后重试',
  [ERROR_CODES.BAD_REQUEST]: '请求参数错误',
  DEFAULT: '网络请求失败，请检查网络连接',
};

export default {
  API_CONFIG,
  TRANSPORTATION_ENDPOINTS,
  AUTH_ENDPOINTS,
  ENV_CONFIG,
  ERROR_CODES,
  ERROR_MESSAGES,
};
