/**
 * API配置文件
 * 统一管理API相关的配置信息
 */

import ApiUrlVars from './common/url-vars';

// API基础配置
export const API_CONFIG = {
  // 基础域名
  BASE_DOMAIN: '172.28.1.60:8085',

  // API基础URL
  BASE_URL: ApiUrlVars.api_domain,

  // 登录API基础URL
  LOGIN_BASE_URL: ApiUrlVars.loginApi_domain,

  // WebSocket域名
  WEBSOCKET_DOMAIN: ApiUrlVars.websocket_domain,

  // 请求超时时间（毫秒）
  TIMEOUT: 30000,

  // API版本
  VERSION: 'v1',

  // 重试次数
  RETRY_COUNT: 3,

  // 重试延迟（毫秒）
  RETRY_DELAY: 1000,

  // 默认请求头
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  },
};

// API端点配置
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: ApiUrlVars.login_get,
    LOGOUT: '/auth/logout',
    VALIDATE: '/auth/validate',
    REFRESH: '/auth/refresh'
  },

  // 交通费申请相关
  TRANSPORTATION_APPLICATION: {
    LIST: ApiUrlVars.transportation_fee_get,
    CREATE: ApiUrlVars.transportation_fee_add,
    UPDATE: ApiUrlVars.transportation_fee_update,
    DELETE: ApiUrlVars.transportation_fee_delete
  },

  // 交通费记录相关
  TRANSPORTATION_RECORD: {
    PERSONAL: ApiUrlVars.transportation_record_personal,
    COLLECTIVE: ApiUrlVars.transportation_record_collective,
    OVERRIDE: ApiUrlVars.transportation_record_override
  },

  // 审批相关
  APPROVAL: {
    PENDING: '/in/approval/transportation_fee/pending',
    APPROVE: '/in/approval/transportation_fee/approve',
    BATCH_APPROVE: '/in/approval/transportation_fee/batch_approve',
    STATISTICS: '/in/approval/transportation_fee/statistics',
    HISTORY: '/in/approval/transportation_fee/history'
  },

  // 考勤相关
  ATTENDANCE: {
    MY_RECORDS: '/in/attendance/my/records',
    CLOCK: '/in/attendance/my/clock',
    CORRECTION: '/in/attendance/my/correction',
    MONTHLY_SUMMARY: '/in/attendance/my/monthly_summary',
    TODAY: '/in/attendance/my/today'
  },

  // 个人信息相关
  PROFILE: {
    GET: '/in/my/profile',
    UPDATE: '/in/my/profile',
    CHANGE_PASSWORD: '/in/my/change_password',
    UPLOAD_AVATAR: '/in/my/upload_avatar',
    COMPLETE_PROFILE: '/in/my/complete_profile',
    SETTINGS: '/in/my/settings'
  }
};

// 保持向后兼容的别名
export const TRANSPORTATION_ENDPOINTS = API_ENDPOINTS.TRANSPORTATION_APPLICATION;

// 保持向后兼容的别名
export const AUTH_ENDPOINTS = API_ENDPOINTS.AUTH;

// 环境配置
export const ENV_CONFIG = {
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  apiBaseUrl: process.env.VITE_API_BASE_URL || API_CONFIG.BASE_URL,
};

// HTTP状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
} as const;

// API响应状态
export const API_STATUS = {
  SUCCESS: 'OK',
  ERROR: 'ERROR'
} as const;

// 错误码映射（保持向后兼容）
export const ERROR_CODES = {
  UNAUTHORIZED: HTTP_STATUS.UNAUTHORIZED,
  FORBIDDEN: HTTP_STATUS.FORBIDDEN,
  NOT_FOUND: HTTP_STATUS.NOT_FOUND,
  INTERNAL_SERVER_ERROR: HTTP_STATUS.INTERNAL_SERVER_ERROR,
  BAD_REQUEST: HTTP_STATUS.BAD_REQUEST,
};

// 错误消息映射
export const ERROR_MESSAGES = {
  [ERROR_CODES.UNAUTHORIZED]: '登录凭证失效，请重新登录',
  [ERROR_CODES.FORBIDDEN]: '权限不足，无法访问该资源',
  [ERROR_CODES.NOT_FOUND]: '请求的资源不存在',
  [ERROR_CODES.INTERNAL_SERVER_ERROR]: '服务器内部错误，请稍后重试',
  [ERROR_CODES.BAD_REQUEST]: '请求参数错误',
  DEFAULT: '网络请求失败，请检查网络连接',
};

// 开发环境配置
export const DEV_CONFIG = {
  // 是否启用模拟数据
  ENABLE_MOCK: ENV_CONFIG.isDevelopment,

  // 是否启用API日志
  ENABLE_LOGGING: ENV_CONFIG.isDevelopment,

  // 是否启用调试模式
  DEBUG_MODE: ENV_CONFIG.isDevelopment
};

export default {
  API_CONFIG,
  API_ENDPOINTS,
  TRANSPORTATION_ENDPOINTS,
  AUTH_ENDPOINTS,
  ENV_CONFIG,
  HTTP_STATUS,
  API_STATUS,
  ERROR_CODES,
  ERROR_MESSAGES,
  DEV_CONFIG,
};
