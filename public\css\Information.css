.all {
    background-image: url(../../public/background/22397179.png );
    height: 100%;
    width: 100%;
    background-repeat: norepeat;
    background-size: cover;
    background-position: center;
    position: absolute;
    left: 0;
    top: 0;
    min-width: 1500px;
}

.remain {
    position: absolute;
    top: 60px;
    right: 7%;
    padding-top: 20px;
    background: #fff;
    width: 68%;
    height: 6%;
    border-radius: 20px;
    min-width: 800px;
}

.information {
    padding-top: 20px;
    background: #fff;
    width: 35%;
    height: 48%;
    border-radius: 20px;
    position: absolute;
    top: 180px;
    right: 40%;
    min-width: 400px;
}

.update_password {
    padding-top: 20px;
    background: #fff;
    width: 30%;
    height: 48%;
    border-radius: 20px;
    position: absolute;
    top: 180px;
    right: 7%;
    min-width: 400px;
}

.remain_font {
    position: absolute;
    margin-top: 2px;
    margin-left: 10px;
    font-size: 22px;
    color: rgb(11, 223, 223);
}

.information_card {
    margin-left: 25px;
    background-color: rgb(65, 149, 233);
}

.information_card_font {
    color: #fff;
}

.update_password_card {
    margin-left: 25px;
    background-color: rgb(65, 149, 233);
}

.update_password_card_font {
    color: #fff;
}

.information_card_content {
    margin-top: 20px;
    margin-left: 70px;
    font-size: large;
}

.information_card_pic {
    width: 30px;
    height: 30px;
}

.information_card_personal {
    margin-top: 10px;
    margin-left: 160px;
    font-size: 25px;
}

.information_card_pic1 {
    width: 35px;
    height: 35px;
    margin-left: 70px;
    margin-right: 10px;
    float: left;
}

.information_card_id {
    margin-top: 28px;
}

.information_card_id1 {
    font-size: 23px;
    float: left;
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 10px;
    margin-right: 40px;
}

.information_card_input {
    width: 50%;
    height: 40px;
}

.update_password_card_remain {
    margin-top: 25px;
    margin-left: 25px;
    color: red;
    font-size: large;
}

.update_password_card_component {
    width: 80%;
    position: absolute;
    left: 10%;
    margin-top: 20px;
}

.component_button1 {
    background: -webkit-gradient(linear, 100% 0%, 0% 0%, from(#59C2FD), to(#7B68EE));
    font-size: 25px;
    font-weight: bolder;
    height: 50px;
    margin-left: 25px;
    width: 40%;
    background-color: #1E90FF;
    border-radius: 15px;
}

.component_button2 {
    background: -webkit-gradient(linear, 100% 0%, 0% 0%, from(#2B99FF), to(#59C2FD));
    font-size: 25px;
    font-weight: bolder;
    height: 50px;
    margin-left: 25px;
    width: 40%;
    background-color: #1E90FF;
    border-radius: 15px;
}

.component_input {
    font-size: 22px;
    border-radius: 10px;
    margin: auto;
}