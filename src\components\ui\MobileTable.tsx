import React from 'react';
import { Table, TableProps, Pagination } from 'antd';
import { ColumnType } from 'antd/es/table';
import styles from './MobileTable.module.css';

interface MobileTableProps<T = any> extends Omit<TableProps<T>, 'columns'> {
  columns: (ColumnType<T> & {
    mobileLabel?: string;
    mobileRender?: (value: any, record: T, index: number) => React.ReactNode;
    mobilePosition?: 'title' | 'subtitle' | 'content' | 'meta' | 'action';
  })[];
  dataSource?: T[];
  className?: string;
}

const MobileTable = <T extends Record<string, any>>({
  columns,
  dataSource = [],
  className = '',
  pagination,
  loading,
  locale,
  onRow,
  ...props
}: MobileTableProps<T>) => {
  // 检测是否为移动端
  const [isMobile, setIsMobile] = React.useState(false);

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 移动端自定义渲染
  const renderMobileCard = (record: T, index: number) => {
    // 按位置分组列
    const titleColumns = columns.filter(col => col.mobilePosition === 'title');
    const subtitleColumns = columns.filter(col => col.mobilePosition === 'subtitle');
    const contentColumns = columns.filter(col => col.mobilePosition === 'content');
    const metaColumns = columns.filter(col => col.mobilePosition === 'meta');
    const actionColumns = columns.filter(col => col.mobilePosition === 'action');

    // 获取列值的辅助函数
    const getColumnValue = (column: any) => {
      const value = record[column.dataIndex as string];
      if (column.mobileRender) {
        return column.mobileRender(value, record, index);
      }
      if (column.render) {
        return column.render(value, record, index);
      }
      return value;
    };

    return (
      <div
        key={record.key || index}
        className={styles.mobile_card}
        onClick={() => onRow?.(record, index)?.onClick?.()}
      >
        {/* 标题区域 */}
        {titleColumns.length > 0 && (
          <div className={styles.card_title}>
            {titleColumns.map((col, idx) => (
              <span key={idx} className={styles.title_text}>
                {getColumnValue(col)}
              </span>
            ))}
          </div>
        )}

        {/* 副标题区域 */}
        {subtitleColumns.length > 0 && (
          <div className={styles.card_subtitle}>
            {subtitleColumns.map((col, idx) => (
              <span key={idx} className={styles.subtitle_text}>
                {getColumnValue(col)}
              </span>
            ))}
          </div>
        )}

        {/* 内容区域 */}
        {contentColumns.length > 0 && (
          <div className={styles.card_content}>
            {contentColumns.map((col, idx) => (
              <div key={idx} className={styles.content_item}>
                <span className={styles.content_label}>
                  {col.mobileLabel || col.title}:
                </span>
                <span className={styles.content_value}>
                  {getColumnValue(col)}
                </span>
              </div>
            ))}
          </div>
        )}

        {/* 元信息区域 */}
        {metaColumns.length > 0 && (
          <div className={styles.card_meta}>
            {metaColumns.map((col, idx) => (
              <span key={idx} className={styles.meta_item}>
                {getColumnValue(col)}
              </span>
            ))}
          </div>
        )}

        {/* 操作区域 */}
        {actionColumns.length > 0 && (
          <div className={styles.card_actions}>
            {actionColumns.map((col, idx) => (
              <div key={idx} className={styles.action_item}>
                {getColumnValue(col)}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  // 移动端渲染
  if (isMobile) {
    return (
      <div className={`${styles.mobile_table_wrapper} ${className}`}>
        {loading && <div className={styles.loading}>加载中...</div>}

        <div className={styles.mobile_list}>
          {dataSource.length === 0 ? (
            <div className={styles.empty_state}>
              {locale?.emptyText || '暂无数据'}
            </div>
          ) : (
            dataSource.map((record, index) => renderMobileCard(record, index))
          )}
        </div>

        {pagination && (
          <div className={styles.mobile_pagination}>
            <Pagination {...pagination} />
          </div>
        )}
      </div>
    );
  }

  // PC端正常渲染
  const enhancedColumns = columns.map((column) => ({
    ...column,
    onCell: (record: T, index?: number) => {
      const originalOnCell = column.onCell?.(record, index) || {};
      return {
        ...originalOnCell,
        'data-label': column.mobileLabel || column.title,
      };
    },
  }));

  return (
    <div className={`${styles.mobile_table_wrapper} ${className}`}>
      <Table<T>
        {...props}
        columns={enhancedColumns}
        dataSource={dataSource}
        pagination={pagination}
        loading={loading}
        locale={locale}
        onRow={onRow}
        className={`${styles.mobile_table} ${props.className || ''}`}
      />
    </div>
  );
};

export default MobileTable;
