import React from 'react';
import { Table, TableProps } from 'antd';
import { ColumnType } from 'antd/es/table';
import styles from './MobileTable.module.css';

interface MobileTableProps<T = any> extends Omit<TableProps<T>, 'columns'> {
  columns: (ColumnType<T> & { mobileLabel?: string })[];
  dataSource?: T[];
  className?: string;
}

const MobileTable = <T extends Record<string, any>>({
  columns,
  dataSource = [],
  className = '',
  ...props
}: MobileTableProps<T>) => {
  // 为移动端添加data-label属性
  const enhancedColumns = columns.map((column) => ({
    ...column,
    onCell: (record: T, index?: number) => {
      const originalOnCell = column.onCell?.(record, index) || {};
      return {
        ...originalOnCell,
        'data-label': column.mobileLabel || column.title,
      };
    },
  }));

  return (
    <div className={`${styles.mobile_table_wrapper} ${className}`}>
      <Table<T>
        {...props}
        columns={enhancedColumns}
        dataSource={dataSource}
        className={`${styles.mobile_table} ${props.className || ''}`}
      />
    </div>
  );
};

export default MobileTable;
